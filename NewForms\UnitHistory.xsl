<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">

  <xsl:variable name="totalpages">
    <xsl:value-of select="number(/results/exemel/NewDataSet/Table[1]/TotalPages)"/>
  </xsl:variable>
  <xsl:variable name="currentpage">
    <xsl:value-of select="number(/results/exemel/NewDataSet/Table[1]/CurrentPage)"/>
  </xsl:variable>

  <xsl:variable name="nextnumber">
    <xsl:value-of select="number(/results/exemel/NewDataSet/Table[1]/CurrentPage)"/>
  </xsl:variable>
  <xsl:variable name="prevnumber">
    <xsl:value-of select="number(/results/exemel/NewDataSet/Table[1]/CurrentPage)"/>
  </xsl:variable>

  <xsl:variable name="nextpage">
    <xsl:value-of select="$nextnumber + 1"/>
  </xsl:variable>
  <xsl:variable name="prevpage">
    <xsl:value-of select="$prevnumber - 1"/>
  </xsl:variable>

  <xsl:variable name="repagination1">
    <xsl:value-of select="/results/exemel/NewDataSet/Table[1]/QueryParameters"/>
  </xsl:variable>
  <xsl:variable name="repagination2">
    <xsl:value-of select="substring-after($repagination1,'/')"/>
  </xsl:variable>
  <xsl:variable name="repagination3">
    <xsl:value-of select="substring-after($repagination2,'/')"/>
  </xsl:variable>
  <xsl:variable name="repagination4">
    <xsl:value-of select="substring-after($repagination3,'/')"/>
  </xsl:variable>
  <xsl:variable name="repagination5">
    <xsl:value-of select="substring-after($repagination4,'/')"/>
  </xsl:variable>
  <xsl:variable name="repagination6">
    <xsl:value-of select="substring-after($repagination5,'/')"/>
  </xsl:variable>


  <xsl:variable name="unit">
    <xsl:value-of select="substring-before($repagination1,'/')"/>
  </xsl:variable>
  <xsl:variable name="startdate">
    <xsl:value-of select="substring-before($repagination2,'/')"/>
  </xsl:variable>
  <xsl:variable name="enddate">
    <xsl:value-of select="substring-before($repagination3,'/')"/>
  </xsl:variable>
  <xsl:variable name="starttime">
    <xsl:value-of select="substring-before($repagination4,'/')"/>
  </xsl:variable>
  <xsl:variable name="endtime">
    <xsl:value-of select="substring-before($repagination5,'/')"/>
  </xsl:variable>
  <xsl:variable name="datetimestyle">
    <xsl:value-of select="substring-before($repagination6,'/')"/>
  </xsl:variable>
  <xsl:variable name="rowsperpage">
    <xsl:value-of select="substring-after($repagination6,'/')"/>
  </xsl:variable>

  <xsl:variable name="firstpage">
    <xsl:value-of select="number('1')"/>
  </xsl:variable>
  <xsl:variable name="lastpage">
    <xsl:value-of select="number($totalpages)"/>
  </xsl:variable>

  <xsl:variable name="subjectStringAdditionalText">
    <xsl:value-of select="$currentpage"/> of <xsl:value-of select="$totalpages"/>
  </xsl:variable>
  <xsl:output method="xml" />
  <xsl:preserve-space elements="*"/>

  <xsl:template match="/">
    <HTML>
      <xsl:if test="$totalpages &gt; number('1')">
        <div id="additional" style="display:none">
          <AdditionalText>
            <xsl:value-of select="$subjectStringAdditionalText"/>
          </AdditionalText>
        </div>
      </xsl:if>
      <xsl:if test="$totalpages &lt;= number('1')">
        <AdditionalText>
          <xsl:value-of select="''"/>
        </AdditionalText>
      </xsl:if>
      <HEAD>
        <TITLE>VisiNET Mobile - Unit History</TITLE>
        <LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
      </HEAD>
      <BODY>
        <TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
          <TBODY>
            <TR>
              <TD vAlign="top">
                <H4 align="center">Unit History</H4>
                <form id="queryform" name="queryform" action="UnitHistory.aspx?queryfile=UnitHistory.qry" method="post">
                  <P>
                    <div id="paginationTop" align="Right" style="display:none">
                      <b>

                        <a id="FirstTop" href="#" onclick="processArrowClick('first')" style="display:initial; font-size:3em; margin-right:20px;">&lt;&lt;</a>

                        <a id="PrevTop" href="#" onclick="processArrowClick('previous')" style="display:initial; font-size:3em; margin-left:40px; margin-right:50px;">&lt;</a>
                      </b>
                      <b>
                        <font size="5">Page    </font>
                        <input id="pagetogotop" type="text" value="0" size="1" style="text-align:center; font-size:20px"/>
                        <font size="5">   of   </font>
                        <font size="5">
                          <xsl:value-of select="$totalpages"/>
                        </font>

                      </b>
                      <input id="radioname" type="hidden" name="radioname" value="{$unit}"/>
                      <input id="startdate" type="hidden" name="startdate" value="{$startdate}"/>
                      <input id="enddate" type="hidden" name="enddate" value="{$enddate}"/>
                      <input id="starttime" type="hidden" name="startime" value="{$starttime}"/>
                      <input id="endtime" type="hidden" name="endtime" value="{$endtime}"/>
                      <input id="datetimestyle" type="hidden" name="datetimestyle" value="{$datetimestyle}"/>
                      <input id="rowsperpage" type="hidden" name="rowsperpage" value="{$rowsperpage}"/>


                      <button type="button" name="GoTo" style="text-align:center; font-size:25px; margin-left:20px; margin-right:20px;" onclick="mimicbottombutton()" onkeypress="mimicbottombutton()"> Go </button>

                      <b>
                        <a id="NextTop" href="#" onclick="processArrowClick('next')" style="display:initial; font-size:3em; margin-left:40px; margin-right:60px;">&gt;</a>


                        <a id="LastTop" href="#" onclick="processArrowClick('last')" style="display:initial; font-size:3em; margin-right:30px; margin-left:20px;">&gt;&gt;</a>
                      </b>
                    </div>
                    <br/>
                    <table id="innerTable" handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">

                      <tr style="font-weight:bold;color:white;">
                        <td>Date</td>
                        <td>Incident Number</td>
                        <td>Activity</td>
                        <td>Comment</td>
                        <td>Dispatcher</td>
                        <td>Terminal</td>
                        <td>User Name</td>
                        <td>Unit Location</td>
                      </tr>

                      <xsl:apply-templates select="/results/exemel/NewDataSet/Table"/>

                    </table>
                    <br/>
                    <br/>
                    <div id="paginationBottom" name="paginationBottom" align="right" style="display:none" >
                      <b>
                        <a id="FirstBottom" href="#" onclick="processArrowClick('first')" style="display:initial; font-size:3em; margin-right:20px;">&lt;&lt;</a>

                        <a id="PrevBottom" href="#" onclick="processArrowClick('previous')" style="display:initial; font-size:3em; margin-left:60px; margin-right:50px;">&lt;</a>
                      </b>
                      <b>
                        <font size="5">Page    </font>
                        <input id="pagenumber" type="text" name="pagenumber" value="0" size="1" style="text-align:center; font-size:20px"/>
                        <font size="5">   of   </font>
                        <font size="5">
                          <xsl:value-of select="$totalpages"/>
                        </font>
                      </b>
                      <b>
                        <button id="GoToQuery" type="button" name="GoToQuery" value="Go" style="text-align:center; font-size:25px; margin-left:20px; margin-right:20px;" onclick="validatepage()" onkeypress="validatepage()" >Go</button>
                        <input style="width:0px;" id="submitbutton" type="submit" name="submitbutton"/>

                        <a id="NextBottom" href="#" onclick="processArrowClick('next')" style="display:initial; font-size:3em; margin-left:40px; margin-right:60px;">&gt;</a>


                        <a id="LastBottom" href="#" onclick="processArrowClick('last')" style="display:initial; font-size:3em; margin-right:20px; margin-left:20px;" location="remote">&gt;&gt;</a>
                      </b>
                    </div>
                  </P>
                </form>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
      </BODY>


      <script type="text/javascript">
        var current = parse(<xsl:value-of select="/results/exemel/NewDataSet/Table[1]/CurrentPage"/>) || 0;
        var total = parse(<xsl:value-of select="/results/exemel/NewDataSet/Table[1]/TotalPages"/>) || 0;
        var prev = parse(<xsl:value-of select="$prevpage"/>) || 0;
        var next = parse(<xsl:value-of select="$nextpage"/>) || 0;
        var pageentered;

        function parse(param){
        return param;
        }
        function window.onload()
        {
        if(total === 0)
        {
        return;
        }
        else
        {
        displayArrows();
        }

        }

        function displayArrows()
        {

        if(total == 1)
        {
        return;
        }

        else if(current == 1)
        {
        document.getElementById("paginationTop").style.display = "inline-block";
        document.getElementById("paginationBottom").style.display = "inline-block";

        document.getElementById("pagenumber").value = current;
        document.getElementById("pagetogotop").value = current;

        document.getElementById("FirstTop").style.display = "none";
        document.getElementById("PrevTop").style.display = "none";

        document.getElementById("FirstBottom").style.display = "none";
        document.getElementById("PrevBottom").style.display = "none";
        }

        else if(current == total)
        {
        document.getElementById("paginationTop").style.display = "inline-block";
        document.getElementById("paginationBottom").style.display = "inline-block";

        document.getElementById("NextTop").style.display = "none";
        document.getElementById("LastTop").style.display = "none";

        document.getElementById("pagenumber").value = current;
        document.getElementById("pagetogotop").value = current;

        document.getElementById("NextBottom").style.display = "none";
        document.getElementById("LastBottom").style.display = "none";
        }
        else
        {
        document.getElementById("pagenumber").value = current;
        document.getElementById("pagetogotop").value = current;

        document.getElementById("paginationTop").style.display = "inline-block";
        document.getElementById("paginationBottom").style.display = "inline-block";
        }
        }

        <xsl:text disable-output-escaping="yes">
				function window.validatepage()
				{	
					var valid = false;
					document.getElementById("pagetogotop").value = "";
					pageentered = document.getElementById("pagenumber").value;
					
					if(pageentered > 0)
					{
						valid = true;
					}
					if(pageentered > total)
					{
						valid = false;						
					}
					if(valid)
					{
						document.getElementById("submitbutton").click();
					}
					else
					{
              document.getElementById("pagetogotop").value = pageentered;
						  document.getElementById("pagenumber").value = pageentered;
						  alert("Invalid page number");
					}
				}
			</xsl:text>

        function mimicbottombutton()
        {
        pageentered = document.getElementById("pagetogotop").value;
        document.getElementById("pagetogotop").value="";
        document.getElementById("pagenumber").value = pageentered;
        document.getElementById("GoToQuery").click();
        }

        <xsl:text disable-output-escaping="yes">
			function processArrowClick(arrowname)
			{
				document.getElementById("pagetogotop").value="";
				if(arrowname == "first")
				{
					document.getElementById("pagenumber").value = 1;
				}
				else if(arrowname == "previous")
				{
					document.getElementById("pagenumber").value = prev;
				}
				else if(arrowname == "next")
				{
					document.getElementById("pagenumber").value = next;
				}
				else
				{
					document.getElementById("pagenumber").value = total;
				}
				
				document.getElementById("submitbutton").click();
			}
			</xsl:text>
      </script>
    </HTML>

  </xsl:template>

  <xsl:template match="/results/exemel/NewDataSet/Table">

    <xsl:variable name="recordid">
      <xsl:value-of select="Master_Incident_ID"/>
    </xsl:variable>
    <tr style="background-color:window;color:windowtext;">
      <td>
        <xsl:value-of select="Date_Time"/>
      </td>
      <td>
        <a href="SingleIncidentQuery.aspx?ID={$recordid}&#38;queryfile=SingleIncident.qry" location="remote">
          <xsl:value-of select="IncidentNumber"/>
        </a>
      </td>
      <td>
        <xsl:value-of select="Activity"/>
      </td>
      <td>
        <xsl:value-of select="Comment"/>
      </td>
      <td>
        <xsl:value-of select="Dispatcher_Init"/>
      </td>
      <td>
        <xsl:value-of select="Terminal"/>
      </td>
      <td>
        <xsl:value-of select="PersonnelName"/>
      </td>
      <td>
        <xsl:value-of select="VehicleLocation"/>
      </td>
    </tr>
  </xsl:template>

</xsl:transform>