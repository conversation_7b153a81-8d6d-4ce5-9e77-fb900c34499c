<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Personnel Search Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="PersonnelSearch.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Personnel Search Results</H4>
						<P>
							<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
								<tr style="font-weight:bold;color:white;">
									<td>Name (ID)</td>
									<td>On Duty</td>
									<td>Unit,Vehicle, Incident, CAD Workstation</td>
									<td>Position</td>
									<td>Phone</td>
									<td>Capabilities Vehicle or Personnel</td>
									<td>Jurisdiction, Division</td>
								</tr>

	<xsl:apply-templates select="results/exemel"/>	
							
							</table>
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel">
	<xsl:variable name="unique-employees" 
		select="NewDataSet
			/Table[not(ID=preceding-sibling::Table[1]/ID)]
			/ID"
	/>
	<xsl:for-each select="$unique-employees">
		<TR style="color:windowtext;background-color:window;">
			<td>
				<table>
					<tr><td>
					<xsl:value-of select="//Table[ID=current()]/EmployeeName"/>(<xsl:value-of select="//Table[ID=current()]/EmployeeID"/>)
					</td></tr>
					<xsl:if test="//Table[ID=current()]/UnitForGivenIncident!=''">
						<tr><td>
						<b>From Unit: </b><xsl:value-of select="//Table[ID=current()]/UnitForGivenIncident"/>
						</td></tr>
					</xsl:if>
				</table>
			</td>
			<td><xsl:value-of select="//Table[ID=current()]/IsOnDuty"/></td>
			<xsl:variable name="v_rec"><xsl:value-of select="//Table[ID=current()]/CurrentUnitName"/></xsl:variable>
			<td>
				<table>
					<tr><td></td></tr>
					<xsl:if test="$v_rec!=''">
						<tr><td>
						<a href="UnitStatus.aspx?radioname={$v_rec}&#38;queryfile=UnitStatus.qry" location="remote"><xsl:value-of select="$v_rec"/></a> 
						</td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/Vehicle!=''">
						<tr><td>
						<b>Vehicle: </b><xsl:value-of select="//Table[ID=current()]/Vehicle"/>
						</td></tr>
					</xsl:if>
					<xsl:variable name="incidentid"><xsl:value-of select="//Table[ID=current()]/IncidentID"/></xsl:variable>
					<xsl:if test="$incidentid!=''">
						<tr><td>
							<B>Incident: </B>
					                <a href="SingleIncidentQuery.aspx?ID={$incidentid}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="//Table[ID=current()]/IncidentNumber"/></a>
						</td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/CadWorkStation!=''">
						<tr><td>
						<b>CAD: </b><xsl:value-of select="//Table[ID=current()]/CadWorkStation"/>
						</td></tr>
					</xsl:if>
				</table>
			</td>
			<td><xsl:value-of select="//Table[ID=current()]/EmployeeRank"/></td>
			<td>
				<table>
					<tr><td></td></tr>
					<tr><td><xsl:value-of select="//Table[ID=current()]/EmployeePhone"/></td></tr>

					<!--<xsl:for-each select="//Table[ID=current()]">
						<TR><td><xsl:value-of select="EmployeePhone"/></td></TR>
					</xsl:for-each>-->
				</table>
			</td>
			<td>
				<table>
					<tr><td></td></tr>
					<xsl:if test="//Table[ID=current()]/UnitCapabilities!=''">
						<tr><td><b>Vehicle: </b><xsl:value-of select="//Table[ID=current()]/UnitCapabilities"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/PersonnelCapabilities!=''">
						<tr><td><b>Personnel: </b><xsl:value-of select="//Table[ID=current()]/PersonnelCapabilities"/></td></tr>
					</xsl:if>
				</table>
			</td>
			<td>
				<table>
					<tr><td></td></tr>
					<xsl:if test="//Table[ID=current()]/Division!=''">
						<tr><td><b>Division: </b><xsl:value-of select="//Table[ID=current()]/Division"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/HomeDivision!='' and //Table[ID=current()]/HomeDivision!=//Table[ID=current()]/Division">
						<tr><td><b>Home Division: </b><xsl:value-of select="//Table[ID=current()]/HomeDivision"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/UnitJurisdiction!=''">
						<tr><td><b>Jurisdiction: </b><xsl:value-of select="//Table[ID=current()]/UnitJurisdiction"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/Sector!=''">
						<tr><td><b>Sector: </b><xsl:value-of select="//Table[ID=current()]/Sector"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/HomeSector!='' and //Table[ID=current()]/HomeSector!=//Table[ID=current()]/Sector">
						<tr><td><b>Home Sector: </b><xsl:value-of select="//Table[ID=current()]/HomeSector"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/EmpJurisdiction!='' and $v_rec=''">
						<tr><td><b>Employee Jurisdiction: </b><xsl:value-of select="//Table[ID=current()]/EmpJurisdiction"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/CurrentStation!=''">
						<tr><td><b>Station: </b><xsl:value-of select="//Table[ID=current()]/CurrentStation"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/HomeStation!='' and //Table[ID=current()]/HomeStation!=//Table[ID=current()]/CurrentStation">
						<tr><td><b>Home Station: </b><xsl:value-of select="//Table[ID=current()]/HomeStation"/></td></tr>
					</xsl:if>
				</table>
			</td>
		</TR>	
	</xsl:for-each>
</xsl:template> 


</xsl:transform>