<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
  <xsl:output method="xml" />

  <xsl:template match="/">

    <xsl:variable name="incidentid">
      <xsl:value-of select="/results/exemel/NewDataSet/Table/ID"/>
    </xsl:variable>
    <html>
      <head>
        <title>VisiNET Mobile - Other Linked Incidents Results </title>
        <link href="style.css" type="text/css" rel="stylesheet"></link>
      </head>
      <body>
        <form action="IncidentSearch.aspx?queryfile=">
          <table class="base" cellpading="10" align="center" border="0" ID="Table1">
            <tbody>
              <tr>
                <td vAlign="top">
                  <h4 align="center">
                    Linked Incidents
                  </h4>
                  <p>
                    <table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
                      <tr style="font-weight:bold;color:white;">
                        <td>Incident #</td>
                        <td>Problem Nature</td>
                        <td>Address</td>
                        <td>Response Date</td>
                        <td>Agency</td>
                      </tr>
                      <xsl:apply-templates select="results/exemel/NewDataSet/Table"/>
                    </table>
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
        </form>
      </body>
    </html>
  </xsl:template>

  <xsl:template match="/results/exemel/NewDataSet/Table">
    <xsl:variable name="incidentid" select="IncidentID"/>
    <xsl:variable name="incidentnumber" select="Incident_Number"/>
    <xsl:variable name="problem" select="Problem_Nature"/>
    <xsl:variable name="address" select="Address"/>
    <xsl:variable name="responsedate" select="Response_Date"/>
    <xsl:variable name="agencytype" select="Agency"/>

    <tr style="color:windowtext;background-color:window;">
      <td>
        <a href="SingleIncidentQuery.aspx?ID={$incidentid}&#38;queryfile=SingleIncident.qry" location="remote">
          <xsl:value-of select="$incidentnumber"/>
        </a>
      </td>
      <td>
        <xsl:value-of select="$problem"/>
      </td>
      <td>
        <xsl:value-of select="$address"/>
      </td>
      <td>
        <xsl:value-of select="$responsedate"/>
      </td>
      <td>
        <xsl:value-of select="$agencytype"/>
      </td>
    </tr>
  </xsl:template>

</xsl:transform>