<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
        <TITLE>Mobile Enterprise - Traffic Stop</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
		<LINK href="GeoValidate.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1" width="638">
			<TBODY>
				<TR>
					<TD vAlign="top" width="616">
						<H4 align="center">Traffic Stop</H4>
						<form action="Form.aspx?queryfile=trafficstop.qry" method="post" id="Form"
							name="Form">
							<table ID="Table2" width="599">
                                <tr>
									<td width="185" nowrap>
                                      <p align="left"><b>License Plate Information:</b></p>
                                    </td>
									<td align="right" width="400"><input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
                                    </td>
                                </tr>
                                <tr>
									<td width="185">License #:</td>
									<td width="400"><input type="text" name="licensenumber" id="licensenumber" maxlength="10"></td>
                                </tr>
                                <tr>
									<td width="185">State:</td>
									<td width="400">
										<XML id="statestyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="statevals" name="statevals">
											<XML id="statesource" src="state.xml"></XML>
										</SPAN>
									</td>
                                </tr>
								<tr>
									<td valign="top" width="185"><b>Location:</b></td>
								</tr>
								<tr>
									<td align="right" width="185">Current<input type="radio" class="bigradio" name="location" id="currentlocation" value="currentlocation" onclick="radioChange()" executefunction="ReverseGeoCode" parameters="Latitude=curlat&Longitude=curlong&Address=Address&City=City"></td>
									<td width="400"><span id="curlat" name="curlat" formvalue="true"></span></td>
								</tr>
								<tr>
									<td width="185"></td>
									<td width="400"><span id="curlong" name="curlong" formvalue="true"></span></td>
								</tr>
								<tr>
									<td align="right" width="185">Selected<input type="radio" class="bigradio" name="location" id="selectedlocation" value="selectedlocation" onclick="radioChange()" executefunction="ReverseGeoCode" parameters="Latitude=sellat&Longitude=sellong&Address=Address&City=City"></td>
									<td width="400">Lat.&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="sellat" name="sellat" fillbutton="selectbutton" size="9">&nbsp;&nbsp;<INPUT type="image" align="absMiddle" enablefill="true" value="Select from map" alt="Select from map"
											src="map.gif" id="selectbutton" name="selectbutton" simulateclickcontrolid="selectedlocation"></td>
								</tr>
								<tr>
									<td width="185"></td>
									<td width="400">Long.&nbsp;<input type="text" id="sellong" name="sellong" fillbutton="selectbutton" size="9"></td>
								</tr>
								<tr>
									<td align="right" width="185">Address<input type="radio" class="bigradio" name="location" id="addresslocation" value="addresslocation" onclick="radioChange()"></td>
									<td width="400"><input type="text" name="Address" id="Address"></td>
								</tr>
								<tr>
									<td width="185"></td>
									<td width="400">City:&nbsp;&nbsp;<input type="text" name="City" id="City" size="15"></td>
								</tr>
							</table>
							<!-- GeoValidation start -->
							<table width="100%">
								<tr>
									<td align="right" width="185">
										<label for="verifiedlocation" style="display:inline-block;vertical-align:middle;">
											Verified<br>Location
										</label>
										<input disabled type="radio" class="bigradio" name="location" id="verifiedlocation" value="verifiedlocation" onclick="radioChange()">
									</td>
                                    <td align="left">
                                        <input type="button" name="verifyLocationButton"
                                               id="verifyLocationButton" value="Verify Location" onkeydown="verifyLocationButtonKeyDown(event, false)"
                                               onclick="verifyLocationButtonClicked(false)" executefunction="VerifyLocation" parameters="">
                                        &nbsp;
                                        <input disabled type="button" name="verifyLocationButtonMore"
                                               id="verifyLocationButtonMore" value="More..." onkeydown="verifyLocationButtonKeyDown(event, true)"
                                               onclick="verifyLocationButtonClicked(true)" parameters="">
                                    </td>
								</tr>
								<tr>
                                    <td colspan="2">
                                        <div id="verifyLocationContainer">
                                            <ul id="verifiedLocationsList"></ul>
                                        </div>
                                        <label id="lblVerifiedLocationsListCount" name="lblVerifiedLocationsListCount">0 record/s found</label>
                                    </td>
								</tr>
							</table>
							<!-- GeoValidation end -->
							<table height="6" width="491">
								<tr>
									<td valign="top" height="2" width="69"><b>Comment:</b></td>
									<td width="408" height="2"><textarea name="Comment" id="Comment" rows="2" cols="35" onkeyup="CheckTextAreaLength(this.form.Comment,200);"></textarea></td>
								</tr>
							</table>
							<br>
							<!-- Below are fields hidden for a variety of reasons. -->
							<input type="hidden" name="CallTaking_Performed_By" id="CallTaking_Performed_By">
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<input type="hidden" name="ProblemNature" id="ProblemNature" value="TRAFFIC STOP">
							<input type="hidden" name="SelfAssign" id="SelfAssign" value="true">
							<input type="hidden" name="Lat" id="Lat" size="10">
							<input type="hidden" name="Long" id="Long" size="10">&nbsp;
							<input type="hidden" name="CHECKTYPE" id="CHECKTYPE" size="10">&nbsp;
							<input type="hidden" name="VerifyLocationResults" id="VerifyLocationResults" value="">
							<input type="hidden" name="SelectedAddressIndex" id="SelectedAddressIndex" value="-1">
                            <input type="hidden" name="VerifyMoreN" id="VerifyMoreN" value="0">
                            <input type="hidden" name="VerifyMaxResultsReturned" id="VerifyMaxResultsReturned" value="0">
                            <input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	
		<script src="clientutilities.js"></script>
		<script src="GeoValidate.js"></script>
		<script language="javascript">
		function window.onload()
		{
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.licensenumber.focus();
			PrepareValidation(Form);
			statevals.innerHTML = GenerateSelectBox("state", statesource, statestyle, false, false, false, 1, false, false);
		}
		function OnAfterFormFill()
		{
			if (!Form.currentlocation.checked &&
				!Form.selectedlocation.checked &&
				!Form.addresslocation.checked)
			{
				Form.currentlocation.click();
			}
			
			// after we call Show() and set this field's values, this function is always called by C# FormFill.PerformAfterFormFill
			// just need to add this button to the other forms which do not have it already (all but traffic stop)
			populateListOfAddressResults();
		}
		function window.validatepage()
		{
			if (Form.licensenumber.value == '')
			{		
				Form.state.selectedIndex = -1;
			}
			else
			{		
				//Append the licence number to the comment so that it will be saved in the incident
				//comment.
				Form.Comment.value += '\n License #: ' + Form.licensenumber.value;
			}


			if (Form.currentlocation.checked)
			{
				if (curlat != null)
				{
					if (curlat.innerText == null)
						Form.Lat.value = '';
					else
						Form.Lat.value = curlat.innerText;
						}
				else
					Form.Lat.value = '';

				if (curlong != null)
				{
					if (curlong.innerText == null)
						Form.Long.value = '';
					else
						Form.Long.value = curlong.innerText;
				}
				else
					Form.Long.value = '';

				// Form.Address.value = '';
			}
			else if (Form.selectedlocation.checked)
			{
				Form.Lat.value = Form.sellat.value;
				Form.Long.value = Form.sellong.value;
				// Form.Address.value = '';
			}
			else if (Form.addresslocation.checked)
			{
				Form.Lat.value = '';
				Form.Long.value = '';
			}

			if ((Form.selectedlocation.checked || Form.currentlocation.checked) && ((Form.Lat.value == 0) ||(Form.Long.value == 0)))
			{
				alert ("Latitude/Longitude cannot be zero");
				//Set focus
				if (Form.Lat.value == 0 )
				{
					Form.sellat.focus();
				}
				else
				{
					Form.sellong.focus();
				}
			}
			else if ((Form.addresslocation.checked) && (Form.Address.value.length <= 0))
			{
				alert("Please enter a location / address.");
				Form.Address.focus();
            }
            // GeoValidation start
			else if ((Form.verifiedlocation.checked) && (Form.SelectedAddressIndex.value < 0))
			{
				alert("Please select a location from the list.");
				Form.verifiedlocation.focus();
            }
            // GeoValidation end
			else
			{
				// if this is a texas plate search then use the "Complette and Enhanced" query type
				if (Form.state.value == "TX")
				{
					Form.CHECKTYPE.value = "REGX";
				}

				// clear this out - no need to send this data back to Mobile Server
				clearHiddenAddressResults();
				
				Form.Submit.click();
			}
			
		}


		</script>
	</body>
</HTML>
