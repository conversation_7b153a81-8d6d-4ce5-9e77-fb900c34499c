<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Division Unit Result</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="DivisionUnitResult.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Division Unit Result</H4>
	<xsl:apply-templates select="results/errormessage"/>
						<p>
							<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
								<tr style="font-weight:bold;color:white;">
									<td>Unit</td>
									<td>Division</td>
									<td>Station</td>
									<td>Status</td>
									<td>Location</td>
									<td>Destination</td>
								</tr>
	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
							</table>
	<BR></BR>
	Click on a location to Map It.
						</p>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">

	<xsl:variable name="BackColor"><xsl:value-of select="BackColor"/></xsl:variable>
	<xsl:variable name="ForeColor"><xsl:value-of select="ForeColor"/></xsl:variable>
		
	<tr style="color:#{$ForeColor};background-color:#{$BackColor};">
		<td><xsl:value-of select="UnitName"/></td>
		<td><xsl:value-of select="DivisionName"/></td>
		<td><xsl:value-of select="StationName"/></td>
		<td><xsl:value-of select="Status"/></td>
		<td>
			<xsl:element name="a">
		                <xsl:attribute name="href">#</xsl:attribute>
	                	<xsl:attribute name="id">MapIt</xsl:attribute>
		                <xsl:attribute name="executefunction">MapIt</xsl:attribute>
	        	        <xsl:attribute name="parameters">Latitude=<xsl:value-of select="CurrentLat"/>&amp;Longitude=<xsl:value-of select="CurrentLon"/></xsl:attribute>
				<xsl:value-of select="CurrentLocation"/>
	                </xsl:element>
		</td>
		<td>
			<xsl:element name="a">
		                <xsl:attribute name="href">#</xsl:attribute>
	                	<xsl:attribute name="id">MapIt</xsl:attribute>
		                <xsl:attribute name="executefunction">MapIt</xsl:attribute>
	        	        <xsl:attribute name="parameters">Latitude=<xsl:value-of select="DestinationLat"/>&amp;Longitude=<xsl:value-of select="DestinationLon"/></xsl:attribute>
				<xsl:value-of select="DestinationLocation"/>
	                </xsl:element>
		</td>
<!--DDD map it button
		<td>
			<xsl:if test="@DestinationLat!=0">
				<xsl:element name="input">
			                <xsl:attribute name="type">button</xsl:attribute>
		                	<xsl:attribute name="id">MapIt</xsl:attribute>
		        	        <xsl:attribute name="executefunction">MapIt</xsl:attribute>
	        		        <xsl:attribute name="parameters">Latitude=<xsl:value-of select="DestinationLat"/>&amp;Longitude=<xsl:value-of select="DestinationLon"/></xsl:attribute>
		        	        <xsl:attribute name="value">Map It</xsl:attribute>
		                </xsl:element>
			</xsl:if>
			<xsl:value-of select="DestinationLocation"/>
		</td>
-->
<!--DDD
		<td><xsl:value-of select="Address"/>
		    <xsl:text> </xsl:text><xsl:value-of select="City"/>
		    <xsl:text> </xsl:text><xsl:value-of select="State"/>
		    <xsl:text> </xsl:text><xsl:value-of select="Postal_Code"/></td>
-->
		
    </tr>

</xsl:template> 

<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
