<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - In Quarters</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">In Quarters</H4>
						<form action="InQuarters.aspx?queryfile=InQuarters.qry" method="post" id="Form"
							name="Form">
							<p>
								<table ID="Table2">
									<tr>
										<td>
											<XML id="stationstyle" src="genericselect.xsl"></XML>
											<SPAN type="selectlist" id="stationvals" name="stationvals">
												<XML id="stationsource" src="Station.xml"></XML>
											</SPAN>
										</td>
									</tr>
								</table>
								<br>
							</p>
							<input type="submit" name="Query" id="Query" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<script language="javascript">

	function window.onload()
	{
		// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
		stationvals.innerHTML = GenerateSelectBox("Station", stationsource, stationstyle, true, false, true, 6, false, false);
		Form.Station.focus();

	}
	</script>
</HTML>
