﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Clear All</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>
    <script language="javascript">

        $(document).ready(function () {

            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    var $form = $(this);
                    PromptIncidentTags().then(function () {
                        var values = $form.serialize();
                        SubmitQuery(values, $form.attr('action'));
                    });
                }
                return false;
            });

            $("#incidentTagsVals").hide();
        });

        function AfterFillForm() {

            GetIncidentNumber().then(function (result) {
                $("#incidentnumber").val(result);
                M.updateTextFields();
            });
            GetProblemNature().then(function (result) {
                $("#pnvals").val(result);
            });

            var problemnature = $("#pnvals").val();

            // < !--To allow users to select multiple dispositions: 1. Comment out SingleSelect(problemnature); 2. Uncomment MultiSelect(problemnature)-- >
           SingleSelect(problemnature);
           //MultiSelect(problemnature);
            if (!CefSharpEnabled)//xamarin only
            {
                $('.modal').modal();
                InsertIncidentTagsSelect();
            }

        }
        function SingleSelect(problemnature) {

            // By default form does not set problem nature so it uses DispositionCodes.xml
            // To use DispositionCodesByProblemNature.xml 1. Comment out section labeled HIDE PROBLEM NATURE and Uncomment section labeled SHOW PROBLEM NATURE
            // 2. Comment out section labeled Use DispositionCodes.xml and Uncomment section labeled Use DispositionCodesByProblemNature.xml

            //<!--Use DispositionCodes.xml-->
            //<!--START-->
            GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
                $("#dispositionvals").prepend(result);
                $("#dispositionvals option:first").attr('selected', 'selected');
                SetSelectBoxFromParameters($("#ResponseDisposition"));
                $('select').formSelect();
                $(".select-dropdown").focus();
            });
            //<!--END-->
            //<!--Use DispositionCodesByProblemNature.xml-->
            // Disposition Codes are limited by ProblemNature using the DispositionCodesByProblemNature.xml. If DispositionCodesByProblemNature.xml does not exist or Problem Nature is not pulled from incident then DispositionCodes.xml will be used.
            //<!-- START-->
            //if (problemnature != undefined && problemnature != "") {
            //        GenerateListBox("ResponseDisposition", 8, "DispositionCodesByProblemNature.xml", "dispositionbyproblemnaturelist.xsl", problemnature).then(function (result) {
            //            if (result != "") {
            //                $("#dispositionvals").prepend(result);
            //                $("#dispositionvals option:first").attr('selected', 'selected');
            //                $('select').formSelect();
            //                $(".select-dropdown").focus();
            //            }
            //            else {
            //                GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
            //                    $("#dispositionvals").prepend(result);
            //                    $("#dispositionvals option:first").attr('selected', 'selected');
            //                    $('select').formSelect();
            //                    $(".select-dropdown").focus();
            //                });
            //            }
            //        });
            //    }
            //    else {
            //        GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
            //            $("#dispositionvals").prepend(result);
            //            $("#dispositionvals option:first").attr('selected', 'selected');
            //            $('select').formSelect();
            //                $(".select-dropdown").focus();
            //        });
            //}
            //<!--END-->

        }
        function MultiSelect(problemnature) {

            // By default form does not set problem nature so it uses DispositionCodes.xml
            // To use DispositionCodesByProblemNature.xml 1. Comment out section labeled HIDE PROBLEM NATURE and Uncomment section labeled SHOW PROBLEM NATURE
            // 2. Comment out section labeled Use DispositionCodes.xml and Uncomment section labeled Use DispositionCodesByProblemNature.xml

            //<!--Use DispositionCodes.xml-->
            //<!--START-->
            GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
                $("#dispositionvals").prepend(result);
                $("#dispositionvals option:first").attr('selected', 'selected');
                $('select').formSelect();
                SetMultiSelectBoxFromParameters($("#ResponseDisposition"));
                $("#ResponseDisposition").prop("required", true);
                $("#ResponseDisposition").change(ValidateRequiredSelect);
                $(".select-dropdown").focus();

            });
            //<!--END-->
            //<!--Use DispositionCodesByProblemNature.xml-->
            // Disposition Codes are limited by ProblemNature using the DispositionCodesByProblemNature.xml. If DispositionCodesByProblemNature.xml does not exist or Problem Nature is not pulled from incident then DispositionCodes.xml will be used.
            //<!-- START-->
            //if (problemnature != undefined && problemnature != "") {
            //        GenerateSelectBox("ResponseDisposition", "DispositionCodesByProblemNature.xml", "dispositionbyproblemnatureselect.xsl", false, true, true, 8, true, true, problemnature).then(function (result) {
            //            if (result != "") {
            //                $("#dispositionvals").prepend(result);
            //                $("#dispositionvals option:first").attr('selected', 'selected');
            //                $('select').formSelect();
            //                $(".select-dropdown").focus();
            //            }
            //            else {
            //                GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
            //                    $("#dispositionvals").prepend(result);
            //                    $("#dispositionvals option:first").attr('selected', 'selected');
            //                    $('select').formSelect();
            //                    $(".select-dropdown").focus();
            //                });
            //            }
            //        });
            //    }
            //    else {
            //        GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
            //            $("#dispositionvals").prepend(result);
            //            $("#dispositionvals option:first").attr('selected', 'selected');
            //            $('select').formSelect();
            //            $(".select-dropdown").focus();
            //        });
            //}
            //<!--END-->
        }
    </script>
</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">CLEAR ALL</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="main" action="ClearAllUnitsQuery.aspx?queryfile=ClearAllUnits.qry" method="post" id="Form" name="Form">
        <div class="row" style="margin-top:20px">
            <div class="col s12">
                <div class="row">
                    <div class="input-field col s12 m4">
                        <input name="incidentnumber" id="incidentnumber" type="text" disabled style="color:black;">
                        <label for="incidentnumber">Incident Number</label>
                    </div>
                    <div class="input-field col s12 m4">
                        <!--HIDE PROBLEM NATURE-->
                        <!--Start-->
                        <input name="ProblemNature" id="ProblemNature" type="hidden">
                        <!--End-->
                        <!--SHOW PROBLEM NATURE-->
                        <!--Start-->
                        <!--<input name="pnvals" id="pnvals" type="text" disabled style="color:black;">
            <label for="pnvals">Problem Nature</label>-->
                        <!--End-->
                    </div>
                    <div class="input-field col s12 m4">
                        <input name="CurrentIncidentID" id="CurrentIncidentID" type="hidden">
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s12 m4" type="selectlist" id="dispositionvals" name="dispositionvals">
                        <label for="ResponseDisposition">Response Disposition</label>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s12 m5" id="incidentTagsVals" name="incidentTagsVals">
                        <select id="IncidentTags" name="IncidentTags" multiple></select>
                        <label for="IncidentTags">Tags</label>
                    </div>
                </div>
            </div>
        </div>
        <div id="modal" class="modal">
            <div class="header valign-wrapper modal-header">
                <h5>ADD TAG</h5>
            </div>
            <div class="modal-content">
                <p>add this tag</p>
            </div>
            <div class="modal-footer">
                <a id="addbtn" class="modal-close btn">ADD TAG</a>
                <a id="closebtn" class="modal-close btn-flat">DISMISS</a>
            </div>
        </div>
    </form>
</body>
</html>