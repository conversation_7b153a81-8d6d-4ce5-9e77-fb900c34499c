<!DOCTYPE html PUBLIC "-//W3C//DTD html 4.0 Transitional//EN">
<html>
	<HEAD>
		<TITLE>VisiNET Mobile - Vehicle Check</TITLE>
		<LINK href="normalstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<table class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<tr>
					<td vAlign="top">
						<H4 align="center">Vehicle Check</H4>
						<form action="VehicleCheck.aspx?queryfile=VehicleCheck.qry" method="post" id="Form" name="Form">
								<table>
                                    <tr>
                                        <td>
                                            <table>
<!--
                                            <tr>
                                                <td style="text-align:center" width="50%">
                                                    <input id="doSupplement" type="checkbox" name="doSupplement"/>
                                                    <label id="labelSupplement">Supplement</label>
                                                </td>
                                            
                                                <td style="text-align:center"  >
                                                    <input id="doSupplementByVin" type="checkbox" name="doSupplementByVin"/>
                                                    <label id="labelSupplementByVin">Supplement By Vin</label>
                                                </td>


                                                <td style="text-align:center"   >
                                                    <input id="doSupplementByLic" type="checkbox" name="doSupplementByLic"/>
                                                    <label id="labelSupplementByLic">Supplement By Lic</label>
                                                </td>
	
                                             
                                                <td  style="text-align:center" width="50%">
                                                    <input id="doRms" type="checkbox" name="doRms"/>
                                                    <label id="labelRms">RMS</label>
                                                </td>
                                           
                                                <td style="text-align:center" >
                                                    <input id="doRmsByLic" type="checkbox" name="doRmsByLic"/>
                                                    <label id="labelRmsLic">RMS By Lic</label>
                                                </td>

                                                <td style="text-align:center" >
                                                    <input id="doRmsByVin" type="checkbox" name="doRmsByVin"/>
                                                    <label id="labelRmsByVin">RMS By Vin</label>
                                                </td>
                                                    
                                                </tr>
 -->
                                            </table>
                                        </td>
                                    </tr>
                                     <tr>

										<td>
											<table>
												<tr>
													<td width="50%">LIC: <INPUT id="LIC" type="text" size="10" name="LIC"></td>
<!--													<td width="50%">LIS: 
                                                        <INPUT id="LIS" type="text" size="2" maxlength="2" name="LIS"> 
                                                    </td>-->
													<td width="50%">LIS:
                                                        <xml id="statestyle" src="genericselect.xsl"></xml>
                                                        <span type="selectlist" id="statevals" name="statevals">
                                                            <xml id="statesource" src="state.xml"></xml>
                                                        </span>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td>
											<table>
												<tr>
													<td width="50%">LIY: <input id="LIY" type="text" size="4" maxlength="4" name="LIY"/></td>
													<td width="50%">LIT: <input id="LIT" type="text" size="2" maxlength="2" name="LIT"/></td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td>
											<table>
											</table>
											<table id="Table2">
												<tr>
													<td width="40%">VIN:<input id="VIN" type="text" size="20" maxLength="20" name="VIN"/></td>
												</tr>
												<tr>
													<td width="20%">Vehicle Year:<input id="VehicleYear" type="text" maxLength="4" size="4" name="VehicleYear" numeric="true"/></td>
													<td width="40%">Make:
<!--<INPUT id="Make" type="text" maxLength="4" size="4" name="Make">-->
										<XML id="makestyle" src="genericselect.xsl"></XML>										
										<SPAN type="selectlist" id="makevals" name="makevals">
											<XML id="makesource" src="VehicleMake.xml"></XML>
										</SPAN>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td>
											<table>
												<tr>
													<td width="25%">NAME: Last</td>
													<td width="25%">First</td>
													<td width="25%">Middle Initial</td>
													<td width="25%">DOB <FONT size="1">(MMDDYYYY)</FONT></td>
												</tr>
												<tr>
													<td><input size="12" id="Last" type="text" name="Last"/></td>
													<td><input size="12" id="First" type="text" name="First"/></td>
													<td><input size="3" id="MiddleInitial" type="text" name="MiddleInitial"/></td>
													<td><input size="8" id="DOB" type="text" name="DOB" numeric="true"/></td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td>
											<table id="Table3">
												<tr>
													<td>Company Name</td>
												</tr>
												<tr>
													<td><input id="CompanyName" type="text" size="30" name="CompanyName"/></td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td>
											<table id="Table4">
												<tr>
													<td>Lojack: <input id="Lojack" type="text" size="15" name="Lojack"/></td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
                            <!--when click on this button, it call the validation.-->
                            <input type="button" name="Query" id="Button" value="Submit" onkeypress="validatepage()" onclick="validatepage()" />
                            <!--when validation is ok, it will call this button to submit the page.  This button is not visible because of it width is zero.-->
                            <input style="width: 0px;" tabindex="-1" type="submit" name="Submit" id="Submit" value="Submit" />
						</form>
					</td>
				</tr>
			</TBODY></table>
		<script type="text/javascript" src="clientutilities.js"></script>
		<script type="text/javascript" language="javascript">
		function window.onload()
		{
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.LIC.focus();
			PrepareValidation(Form);

			statevals.innerHTML = GenerateSelectBox("State", statesource, statestyle, false, false, false, 1, false, false);
			makevals.innerHTML = GenerateSelectBox("Make", makesource, makestyle, false, false, false, 1, false, false);
			Form.innerHTML = Form.innerHTML + GenerateVehicleSupplementHiddenFields();   //ensure that the supplement is correctly setup.
			Form.innerHTML = Form.innerHTML + GenerateVehicleRmsHiddenFields();  //ensure that the rms is correctly setup.
		}
		function window.validatepage() {

		    if (!HandleVehicleSupplementFieldsBeforeSubmit()) return;
		    if (!HandleVehicleRmsFieldsBeforeSubmit()) return;

		    Form.Submit.click();

		}
		</script>
	</body>
</html>
