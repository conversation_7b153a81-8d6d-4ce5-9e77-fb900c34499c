﻿function UpdateCrew() {

    //crew
    var currentCrew = $("#LoggedinCrew").val();
    if (currentCrew === "") {
        //no current crew just show one
    }
    else {
        var crewArr = currentCrew.split("/");
        var count = crewArr.length;
        if (count > 0) {
            for (var i = 0; i < count; i++) {
                if (i == 0) {
                    $(".crew-member").find("#crewmember").val(crewArr[i]);
                }
                else {
                    AddCrewMemberField(crewArr[i]);
                }
                if ($("#crewmemberPosition").length > 0) {
                    //generate positions drop down for logged in crew
                    var $crewMemberDiv = $(".crew-member");
                    $crewMemberDiv.each(function () {
                        $div = $(this);
                        SetUserPositions($div.find("#crewmember"), $div.find("#crewmemberPosition"));
                        $div.find("#crewmemberPosition").prop('disabled', true);
                    });
                }
            }
        }
    }
}

function AddCrewMemberField(crewmember) {
    var $crewMemberFields = $(".crew-member");
    var $clone = $crewMemberFields.last().clone();
    $crewMemberFields.find(".addCrewBtn").hide();
    $("#crewMembersDiv").append($clone);

    $clone.find('input.autocomplete').autocomplete({
        data: {
        },
        onAutocomplete: function () {
            this.$el[0].focus();
        }
    });
    $clone.find('input.autocomplete').on('input', function () {
        GetDataList($(this));
    });
    $clone.find("#crewmember").val(crewmember);
    var $crewpositiondiv = $clone.find(".crewPosition");
    if ($crewpositiondiv.length > 0) {
        $crewpositiondiv.empty();
        $crewpositiondiv.append('<select name="crewmemberPosition" class="crew-toggle-disabled" id="crewmemberPosition"><option value="-1"></option></select><label for="crewmemberPosition">Position</label>');

        $crewpositiondiv.find("#crewmemberPosition").formSelect();
        if (typeof (SetTabIndex) == typeof (Function)) {
            SetTabIndex();
        }

    }
    //max has been reached
    if ($(".crew-member").length >= Number($("#maxCrew").val())) {
        $clone.find(".addCrewBtn a").addClass("disabled");
        $clone.find(".addCrewBtn a").addClass("permanently-disabled");
    }

}

function DeleteCrewMemberField(ev) {
    var $crewMemberFields = $(".crew-member");
    if ($crewMemberFields.length > 1) {
        $(ev.currentTarget).closest(".crew-member").remove();
        $crewMemberFields = $(".crew-member");
        $crewMemberFields.find(".addCrewBtn").hide();
        $crewMemberFields.last().find(".addCrewBtn").show();
    }
    else {
        $(ev.currentTarget).closest(".crew-member").find("#crewmember").val("");
        $select = $(ev.currentTarget).closest(".crew-member").find("#crewmemberPosition");
        if ($select.length > 0) {
            $select.val("");
            $select.find('option').remove();
            $select.append('<option value="-1"></option>');
            $select.formSelect();
            if (typeof (SetTabIndex) == typeof (Function)) {
                SetTabIndex();
            }
            //$select.trigger('contentChanged');
        }
    }

    if ($(".crew-member").length < Number($("#maxCrew").val())) {
        $crewMemberFields.last().find(".addCrewBtn a").removeClass("disabled");
        $crewMemberFields.last().find(".addCrewBtn a").removeClass("permanently-disabled");
    }
}

function AddTemporaryRadioField() {
    var $temporaryRadioFields = $(".temporary-radio");
    var $clone = $temporaryRadioFields.last().clone();
    $clone.find("input[type=text]").val("");
    $temporaryRadioFields.find(".addTemporaryRadioBtn").hide();
    $("#temporaryRadiosDiv").append($clone);
    //max has been reached
    if ($(".temporary-radio").length >= Number($("#maxRadios").val())) {
        $clone.find(".addTemporaryRadioBtn a").addClass("disabled");
        $clone.find(".addTemporaryRadioBtn a").addClass("permanently-disabled");
    }
    RenameRadioFields();

    if (typeof (SetTabIndex) == typeof (Function)) {
        SetTabIndex();
    }
}

function DeleteTemporaryRadioField(ev) {
    DeleteTemporaryRadioFieldWithTarget($(ev.currentTarget));
}

function DeleteTemporaryRadioFieldWithTarget($currentTarget) {
    var $temporaryRadioFields = $(".temporary-radio");
    if ($temporaryRadioFields.length > 1) {
        $currentTarget.closest(".temporary-radio").remove();
        $temporaryRadioFields = $(".temporary-radio");
        $temporaryRadioFields.find(".addTemporaryRadioBtn").hide();
        $temporaryRadioFields.last().find(".addTemporaryRadioBtn").show();
    }
    else {
        $currentTarget.closest(".temporary-radio").find("input[type=text]").val("");
    }
    $temporaryRadioFields = $(".temporary-radio");
    if ($temporaryRadioFields.length < Number($("#maxRadios").val())) {
        $temporaryRadioFields.last().find(".addTemporaryRadioBtn a").removeClass("disabled");
        $temporaryRadioFields.last().find(".addTemporaryRadioBtn a").removeClass("permanently-disabled");
    }
    RenameRadioFields();

    if (typeof (SetTabIndex) == typeof (Function)) {
        SetTabIndex();
    }
}

function RenameRadioFields() {
    var $temporaryRadioFields = $(".temporary-radio");
    $temporaryRadioFields.each(function (index) {
        var $inputs = $(this).find("input[type=text]");
        $inputs.each(function () {
            var name = $(this).attr("name");
            name = name.slice(0, -1) + (index + 1);
            $(this).attr("name", name);
            $(this).attr("id", name);
        })
    });
}

function SetUserPositions($input, $select, event) {
    var user = "";

    if (event != undefined) {
        $input = $(event.currentTarget);
        user = $input.val();
        $select = $input.parent().parent().find("select");
    }

    if ($input != undefined) {
        user = $input.val();
    }

    if (user != "" && $select != undefined) {
        GetDataByFilter("NextGenForms/PersonnelAndPositions.xml", user, "Personnel", "Name", true, true).then(function (result) {
            $select.find('option').remove();
            $select.append('<option value="-1"></option>');
            var userPositions;
            if (result != "") {
                userPositions = JSON.parse(result);
            }
            if (userPositions != undefined && userPositions != null && userPositions.length == 1) {
                var positions = userPositions[0].Personnel.Positions.Position;
                if (Array.isArray(positions) && positions.length > 0) {
                    for (var i = 0; i < positions.length; i++) {
                        $select.append('<option value="' + positions[i]["#text"] + '">' + positions[i]["#text"] + '</option>');
                    }
                }
                else if (positions != undefined) {
                    $select.append('<option value="' + positions["#text"] + '">' + positions["#text"] + '</option>');
                }
            }

            $select.formSelect();

            if (typeof (SetTabIndex) == typeof (Function)) {
                SetTabIndex();
            }
        });
    }
}

function AddTemporaryPagerField(crewmember) {
    var $temporaryPagerFields = $(".temporary-pager");
    var $clone = $temporaryPagerFields.last().clone();
    $clone.find("input[type=text]").val("");
    $temporaryPagerFields.find(".addTemporaryPagerBtn").hide();
    $("#temporaryPagerDiv").append($clone);

    if ($(".temporary-pager").length >= Number($("#maxPins").val())) {
        $clone.find(".addTemporaryPagerBtn a").addClass("disabled");
        $clone.find(".addTemporaryPagerBtn a").addClass("permanently-disabled");
    }

    RenamePagerFields();

    if (typeof (SetTabIndex) == typeof (Function)) {
        SetTabIndex();
    }
}

function DeleteTemporaryPagerField(ev) {
    var $temporaryPagerFields = $(".temporary-pager");
    if ($temporaryPagerFields.length > 1) {
        ev.currentTarget.closest(".temporary-pager").remove();
        $temporaryPagerFields = $(".temporary-pager");
        $temporaryPagerFields.find(".addTemporaryPagerBtn").hide();
        $temporaryPagerFields.last().find(".addTemporaryPagerBtn").show();
    }
    else {
        ev.currentTarget.closest(".temporary-pager").find("input[type=text]").val("");
    }
    $temporaryRadioFields = $(".temporary-pager");
    if ($temporaryRadioFields.length < Number($("#maxPins").val())) {
        $temporaryRadioFields.last().find(".addTemporaryPagerBtn a").removeClass("disabled");
        $temporaryRadioFields.last().find(".addTemporaryPagerBtn a").removeClass("permanently-disabled");
    }

    RenamePagerFields();

    if (typeof (SetTabIndex) == typeof (Function)) {
        SetTabIndex();
    }
}

function RenamePagerFields() {
    var $temporaryPagerFields = $(".temporary-pager");
    $temporaryPagerFields.each(function (index) {
        var $input = $(this).find("input[type=text]");
        var name = $input.attr("name");
        name = name.slice(0, -1) + (index + 1);
        $input.attr("name", name);
        $input.attr("id", name);  
    });
}

//Sets the index of each input.  This is necessary because formSelect() function updates all the select's tabindex to -1.
function SetTabIndex() {
    var n = 1;
    $(":input:not(:hidden):not(:disabled)").each(function () {
        $(this).attr('tabindex', n++);
    });
}
