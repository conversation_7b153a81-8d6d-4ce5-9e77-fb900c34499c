<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - At Destination</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">At Destination</H4>
						<form action="AtDestinationQuery.aspx?queryfile=atdestination.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td>Current odometer:</td>
									<td><input style="WIDTH:150px" type="number" name="currentodometer" id="currentodometer" step="0.1" min="0" max="1000000" value=""></td>
<!--									<td><input style="WIDTH:150px" type="text" name="currentodometer" id="currentodometer"></td>
-->
									</tr>
							</table>
							<br>
		<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
		<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script language="javascript">

		function window.onload()
		{
			Form.currentodometer.focus();
		}

		function window.validatepage()
		{
			if(isNaN(Form.currentodometer.value))
			{
				alert ("Please enter a valid number");
				Form.currentodometer.focus();
			}
			else
			{
				Form.Submit.click();
				return;
			}
		}

	</script>
</HTML>
