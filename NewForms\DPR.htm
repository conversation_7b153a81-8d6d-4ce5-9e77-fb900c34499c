<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Boat Inquiry</TITLE>
		<LINK href="normalstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Boat Inquiry</H4>
						<form action="BoatInquiry.aspx?queryfile=BoatInquiry.qry" method="post" id="Form" name="Form">
							<P>
								<TABLE width="100%">
									<TR>
										<TD>
											<TABLE>
												<TR>
													<TD width="47%">Registration #:<input size="8" id="RegID" type="text" name="RegID" maxlength="8"></TD>
													<TD width="53%">State:
<!--<input size="2" id="State" value="CA" type="text" name="State" maxlength="2">-->
										<XML id="statestyle" src="genericselect.xsl"></XML>										
										<SPAN type="selectlist" id="statevals" name="statevals">
											<XML id="statesource" src="state.xml"></XML>
										</SPAN>
													</TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
											<TABLE>
												<TR>
													<TD width="100%">Hull #:<input size="12" id="HullNo" type="text" name="HullNo" maxlength="20"></TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
											<TABLE id="Table2">
												<TR>
													<TD width="20%">OAN #:</TD>
													<TD><INPUT id="OANNO" type="text" size="20" name="OANNO" maxlength="20"></TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
								</TABLE>
							</P>
							<input type="submit" name="Query" id="Query" value="Query">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
		<script src="clientutilities.js"></script>
		<SCRIPT language="javascript">
		function window.onload()
		{

			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.RegID.focus();
			PrepareValidation(Form);

			statevals.innerHTML = GenerateSelectBox("State", statesource, statestyle, false, false, false, 1, false, false);

		}

		</SCRIPT>
	</body>
</HTML>
