﻿.icon-check {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0Ij48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik05IDE2LjJMNC44IDEybC0xLjQgMS40TDkgMTkgMjEgN2wtMS40LTEuNEw5IDE2LjJ6Ii8+PC9zdmc+');
    background-repeat: no-repeat;
    fill: white;
}
.icon-search {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%235f6670'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
}
.icon-search-blue {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231066C7'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
}
.icon-close {
    background-repeat: no-repeat;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' height='24px' viewBox='0 0 24 24' width='24px' fill='%23000000'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E");
}

.icon-add {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3C?xml version='1.0' encoding='UTF-8'?%3E%3Csvg width='18px' height='18px' viewBox='0 0 18 18' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eicon / add%3C/title%3E%3Cdefs%3E%3Cpolygon id='path-1' points='14.25 9.75 9.75 9.75 9.75 14.25 8.25 14.25 8.25 9.75 3.75 9.75 3.75 8.25 8.25 8.25 8.25 3.75 9.75 3.75 9.75 8.25 14.25 8.25'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='icon-/-add' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpolygon id='Bounds' points='0 0 18 0 18 18 0 18'%3E%3C/polygon%3E%3Cmask id='mask-2' fill='white'%3E%3Cuse xlink:href='%23path-1'%3E%3C/use%3E%3C/mask%3E%3Cuse id='Icon' fill='currentColor' xlink:href='%23path-1'%3E%3C/use%3E%3C/g%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml;charset=utf8,%3C?xml version='1.0' encoding='UTF-8'?%3E%3Csvg width='18px' height='18px' viewBox='0 0 18 18' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eicon / add%3C/title%3E%3Cdefs%3E%3Cpolygon id='path-1' points='14.25 9.75 9.75 9.75 9.75 14.25 8.25 14.25 8.25 9.75 3.75 9.75 3.75 8.25 8.25 8.25 8.25 3.75 9.75 3.75 9.75 8.25 14.25 8.25'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='icon-/-add' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpolygon id='Bounds' points='0 0 18 0 18 18 0 18'%3E%3C/polygon%3E%3Cmask id='mask-2' fill='white'%3E%3Cuse xlink:href='%23path-1'%3E%3C/use%3E%3C/mask%3E%3Cuse id='Icon' fill='currentColor' xlink:href='%23path-1'%3E%3C/use%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-color: #192231;
}

.icon-delete {
    -webkit-mask-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+aWNvbiAvIGRlbGV0ZTwvdGl0bGU+CiAgICA8ZGVmcz4KICAgICAgICA8cGF0aCBkPSJNNC41LDE0LjI1IEM0LjUsMTUuMDc1IDUuMTc1LDE1Ljc1IDYsMTUuNzUgTDEyLDE1Ljc1IEMxMi44MjUsMTUuNzUgMTMuNSwxNS4wNzUgMTMuNSwxNC4yNSBMMTMuNSw1LjI1IEw0LjUsNS4yNSBMNC41LDE0LjI1IEw0LjUsMTQuMjUgWiBNMTQuMjUsMyBMMTEuNjI1LDMgTDEwLjg3NSwyLjI1IEw3LjEyNSwyLjI1IEw2LjM3NSwzIEwzLjc1LDMgTDMuNzUsNC41IEwxNC4yNSw0LjUgTDE0LjI1LDMgTDE0LjI1LDMgWiIgaWQ9InBhdGgtMSI+PC9wYXRoPgogICAgPC9kZWZzPgogICAgPGcgaWQ9Imljb24tLy1kZWxldGUiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGlkPSJCb3VuZHMiIHBvaW50cz0iMCAwIDE4IDAgMTggMTggMCAxOCI+PC9wb2x5Z29uPgogICAgICAgIDxtYXNrIGlkPSJtYXNrLTIiIGZpbGw9IndoaXRlIj4KICAgICAgICAgICAgPHVzZSB4bGluazpocmVmPSIjcGF0aC0xIj48L3VzZT4KICAgICAgICA8L21hc2s+CiAgICAgICAgPHVzZSBpZD0iSWNvbiIgZmlsbD0iIzE5MjIzMSIgeGxpbms6aHJlZj0iI3BhdGgtMSI+PC91c2U+CiAgICAgICAgPGcgaWQ9Ikdyb3VwIiBtYXNrPSJ1cmwoI21hc2stMikiIGZpbGw9IiMxOTIyMzEiPgogICAgICAgICAgICA8ZyBpZD0iY29sb3ItLy1uNjAwIj4KICAgICAgICAgICAgICAgIDxyZWN0IGlkPSJjb2xvciIgeD0iMCIgeT0iMCIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4Ij48L3JlY3Q+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
    mask-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+aWNvbiAvIGRlbGV0ZTwvdGl0bGU+CiAgICA8ZGVmcz4KICAgICAgICA8cGF0aCBkPSJNNC41LDE0LjI1IEM0LjUsMTUuMDc1IDUuMTc1LDE1Ljc1IDYsMTUuNzUgTDEyLDE1Ljc1IEMxMi44MjUsMTUuNzUgMTMuNSwxNS4wNzUgMTMuNSwxNC4yNSBMMTMuNSw1LjI1IEw0LjUsNS4yNSBMNC41LDE0LjI1IEw0LjUsMTQuMjUgWiBNMTQuMjUsMyBMMTEuNjI1LDMgTDEwLjg3NSwyLjI1IEw3LjEyNSwyLjI1IEw2LjM3NSwzIEwzLjc1LDMgTDMuNzUsNC41IEwxNC4yNSw0LjUgTDE0LjI1LDMgTDE0LjI1LDMgWiIgaWQ9InBhdGgtMSI+PC9wYXRoPgogICAgPC9kZWZzPgogICAgPGcgaWQ9Imljb24tLy1kZWxldGUiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGlkPSJCb3VuZHMiIHBvaW50cz0iMCAwIDE4IDAgMTggMTggMCAxOCI+PC9wb2x5Z29uPgogICAgICAgIDxtYXNrIGlkPSJtYXNrLTIiIGZpbGw9IndoaXRlIj4KICAgICAgICAgICAgPHVzZSB4bGluazpocmVmPSIjcGF0aC0xIj48L3VzZT4KICAgICAgICA8L21hc2s+CiAgICAgICAgPHVzZSBpZD0iSWNvbiIgZmlsbD0iIzE5MjIzMSIgeGxpbms6aHJlZj0iI3BhdGgtMSI+PC91c2U+CiAgICAgICAgPGcgaWQ9Ikdyb3VwIiBtYXNrPSJ1cmwoI21hc2stMikiIGZpbGw9IiMxOTIyMzEiPgogICAgICAgICAgICA8ZyBpZD0iY29sb3ItLy1uNjAwIj4KICAgICAgICAgICAgICAgIDxyZWN0IGlkPSJjb2xvciIgeD0iMCIgeT0iMCIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4Ij48L3JlY3Q+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
    background-repeat: no-repeat;
    background-color: #192231;
}

.icon-arrow_forward {
    background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='18px' height='18px' viewBox='0 0 18 18' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eicon / arrow-forward%3C/title%3E%3Cdefs%3E%3Cpolygon id='path-1' points='9 3 7.9425 4.0575 12.1275 8.25 3 8.25 3 9.75 12.1275 9.75 7.9425 13.9425 9 15 15 9'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='icon-/-arrow-forward' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpolygon id='Bounds' points='0 0 18 0 18 18 0 18'%3E%3C/polygon%3E%3Cmask id='mask-2' fill='white'%3E%3Cuse xlink:href='%23path-1'%3E%3C/use%3E%3C/mask%3E%3Cuse id='Icon' fill='%23000000' xlink:href='%23path-1'%3E%3C/use%3E%3Cg id='Group' mask='url(%23mask-2)' fill='%23192231'%3E%3Cg id='color-/-n600'%3E%3Crect id='color' x='0' y='0' width='18' height='18'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
}

.icon-clock {
    -webkit-mask-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='18px' height='18px' viewBox='0 0 18 18' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eicon / clock-outline%3C/title%3E%3Cdefs%3E%3Cpath d='M9.375,5.25 L8.25,5.25 L8.25,9.75 L11.8125,11.8875 L12.375,10.965 L9.375,9.1875 L9.375,5.25 L9.375,5.25 Z M9,2.25 C5.2725,2.25 2.25,5.2725 2.25,9 C2.25,12.7275 5.265,15.75 9,15.75 C12.7275,15.75 15.75,12.7275 15.75,9 C15.75,5.2725 12.7275,2.25 9,2.25 L9,2.25 Z M9,14.25 C6.0975,14.25 3.75,11.9025 3.75,9 C3.75,6.0975 6.0975,3.75 9,3.75 C11.9025,3.75 14.25,6.0975 14.25,9 C14.25,11.9025 11.9025,14.25 9,14.25 L9,14.25 Z' id='path-1'%3E%3C/path%3E%3C/defs%3E%3Cg id='icon-/-clock-outline' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpolygon id='Bounds' points='0 0 18 0 18 18 0 18'%3E%3C/polygon%3E%3Cmask id='mask-2' fill='white'%3E%3Cuse xlink:href='%23path-1'%3E%3C/use%3E%3C/mask%3E%3Cuse id='Icon' fill='%23192231' xlink:href='%23path-1'%3E%3C/use%3E%3Cg id='Group' mask='url(%23mask-2)' fill='%23192231'%3E%3Cg id='color-/-n600'%3E%3Crect id='color' x='0' y='0' width='18' height='18'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-color: #192231;
}

.icon-16 {
    width: 16px;
    height: 16px;
    background-size: contain;
    margin: 8px 0px 0px 2px;
}

.icon-18 {
    width: 18px;
    height: 18px;
    display:inline-block;
}

.icon-20 {
    width: 20px;
    height: 20px;
    background-size: contain;
    position: absolute;
    margin: 4px 0px 0px 2px;
}

.icon-24 {
    width: 24px;
    height: 24px;
    background-size: contain;
    position: absolute;
    margin: 4px 0px 0px 2px;
}
