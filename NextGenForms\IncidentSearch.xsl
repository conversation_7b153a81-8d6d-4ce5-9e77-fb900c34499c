<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>Mobile Enterprise - Incident Search Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="IncidentSearch.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Search Results</H4>
	<xsl:apply-templates select="results/errormessage"/>
							<p>
								<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
									<tr style="font-weight:bold;color:white;">
										<td>Date</td>
										<td>Incident Number</td>
										<td>Problem/Priority</td>
										<td>Address</td>
										<td>Disposition</td>
										<td>Options</td>
									</tr>
	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
								</table>
							</p>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
    <xsl:variable name="recordid"><xsl:value-of select="ID"/></xsl:variable>
    <xsl:variable name="IncidentNumber"><xsl:value-of select="Incident_Number"/></xsl:variable>
    <xsl:variable name="Problem"><xsl:value-of select="Problem"/></xsl:variable>
    <xsl:variable name="Address"><xsl:value-of select="Address"/></xsl:variable>
		
	<tr style="color:windowtext;background-color:window;">
		<td><xsl:value-of select="Response_Date"/></td>
		<td>
			<a href="SingleIncidentQuery.aspx?ID={$recordid}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="Incident_Number"/></a>
		</td>
		<td>
			<table>
				<tr><td><xsl:value-of select="Problem"/></td></tr>
				<xsl:if test="Priority!=''">
					<tr><td>Priority: <xsl:value-of select="Priority"/></td></tr>
				</xsl:if>
			</table>
		</td>
		<td>
			<table>
				<xsl:if test="LocationName!=''">
					<tr><td><xsl:value-of select="LocationName"/></td></tr>
				</xsl:if>
				<xsl:if test="Address!=''">
					<tr><td>
					<xsl:element name="a">
			                	<xsl:attribute name="href">#</xsl:attribute>
	                			<xsl:attribute name="id">MapIt</xsl:attribute>
		        		        <xsl:attribute name="executefunction">MapIt</xsl:attribute>
	        			        <xsl:attribute name="parameters">Latitude=<xsl:value-of select="Latitude"/>&amp;Longitude=<xsl:value-of select="Longitude"/></xsl:attribute>
						<xsl:value-of select="Address"/>
			                </xsl:element>
					</td></tr>
				</xsl:if>
				<xsl:if test="Address=''"><!--at least one row has to be in the table-->
					<tr><td></td></tr>
				</xsl:if>
				<xsl:if test="Building!=''">
					<tr><td>Bldg:&#32;<xsl:value-of select="Building"/></td></tr>
				</xsl:if>
				<xsl:if test="Apartment!=''">
					<tr><td>Apt:&#32;<xsl:value-of select="Apartment"/></td></tr>
				</xsl:if>
				<xsl:if test="City!=''">
					<tr><td><xsl:value-of select="City"/></td></tr>
				</xsl:if>
			</table>
		</td>
		<td><xsl:value-of select="Disposition"/></td>
		<td>
			<table>
				<tr><td valign="top" height="40">
				<!--DDD<input type="button" value="Add Incident Comment..." id="AddIncidentComment" executefunction="OpenForm" parameters="FormName=addincidentcomment&#38;IncidentID={$recordid}&#38;IncidentNumber={$IncidentNumber}&#38;Problem={$Problem}&#38;Address={$Address}"/>-->
				<a href="$" id="AddIncidentComment" executefunction="OpenForm" parameters="FormName=addincidentcomment&#38;IncidentID={$recordid}&#38;IncidentNumber={$IncidentNumber}&#38;Problem={$Problem}&#38;Address={$Address}">Add Incident Comment</a>
				<!--DDD<a href="AddIncidentComment.htm?IncidentID={$recordid}" location="local" formfill="true">Add Incident Comment</a>-->
				</td></tr>
				<tr><td valign="top" height="40">
				<a href="PersonnelSearch.aspx?IncludeOnDuty=true&#38;IncludeOffDuty=true&#38;IncludeFromUnits=true&#38;IncludeFromUnits=true&#38;IncidentID={$recordid}&#38;queryfile=PersonnelSearch.qry" location="remote">Get Personnel&#39;s Current Status</a>
				</td></tr>
				<xsl:if test="AllowRequestCaseNumber='true'">
					<tr><td>
					<a href="RequestCaseNumber.aspx?IncidentID={$recordid}&#38;queryfile=RequestCaseNumber.qry" location="remote">Request Case Number</a>
					</td></tr>
				</xsl:if>
			</table>
		</td>		
    </tr>
</xsl:template> 

<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
