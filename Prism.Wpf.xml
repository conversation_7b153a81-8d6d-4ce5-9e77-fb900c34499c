<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Prism.Wpf</name>
    </assembly>
    <members>
        <member name="T:Prism.Bootstrapper">
            <summary>
            Base class that provides a basic bootstrapping sequence and hooks
            that specific implementations can override
            </summary>
            <remarks>
            This class must be overridden to provide application specific configuration.
            </remarks>
        </member>
        <member name="P:Prism.Bootstrapper.Logger">
            <summary>
            Gets the <see cref="T:Prism.Logging.ILoggerFacade"/> for the application.
            </summary>
            <value>A <see cref="T:Prism.Logging.ILoggerFacade"/> instance.</value>
        </member>
        <member name="P:Prism.Bootstrapper.ModuleCatalog">
            <summary>
            Gets the default <see cref="T:Prism.Modularity.IModuleCatalog"/> for the application.
            </summary>
            <value>The default <see cref="T:Prism.Modularity.IModuleCatalog"/> instance.</value>
        </member>
        <member name="P:Prism.Bootstrapper.Shell">
            <summary>
            Gets the shell user interface
            </summary>
            <value>The shell user interface.</value>
        </member>
        <member name="M:Prism.Bootstrapper.CreateLogger">
            <summary>
            Create the <see cref="T:Prism.Logging.ILoggerFacade" /> used by the bootstrapper.
            </summary>
            <remarks>
            The base implementation returns a new TextLogger.
            </remarks>
        </member>
        <member name="M:Prism.Bootstrapper.Run">
            <summary>
            Runs the bootstrapper process.
            </summary>
        </member>
        <member name="M:Prism.Bootstrapper.CreateModuleCatalog">
            <summary>
            Creates the <see cref="T:Prism.Modularity.IModuleCatalog"/> used by Prism.
            </summary>
             <remarks>
            The base implementation returns a new ModuleCatalog.
            </remarks>
        </member>
        <member name="M:Prism.Bootstrapper.ConfigureModuleCatalog">
            <summary>
            Configures the <see cref="T:Prism.Modularity.IModuleCatalog"/> used by Prism.
            </summary>
        </member>
        <member name="M:Prism.Bootstrapper.ConfigureViewModelLocator">
            <summary>
            Configures the <see cref="T:Prism.Mvvm.ViewModelLocator"/> used by Prism.
            </summary>
        </member>
        <member name="M:Prism.Bootstrapper.RegisterFrameworkExceptionTypes">
            <summary>
            Registers the <see cref="T:System.Type"/>s of the Exceptions that are not considered 
            root exceptions by the <see cref="T:System.ExceptionExtensions"/>.
            </summary>
        </member>
        <member name="M:Prism.Bootstrapper.InitializeModules">
            <summary>
            Initializes the modules. May be overwritten in a derived class to use a custom Modules Catalog
            </summary>
        </member>
        <member name="M:Prism.Bootstrapper.ConfigureRegionAdapterMappings">
            <summary>
            Configures the default region adapter mappings to use in the application, in order
            to adapt UI controls defined in XAML to use a region and register it automatically.
            May be overwritten in a derived class to add specific mappings required by the application.
            </summary>
            <returns>The <see cref="T:Prism.Regions.RegionAdapterMappings"/> instance containing all the mappings.</returns>
        </member>
        <member name="M:Prism.Bootstrapper.ConfigureDefaultRegionBehaviors">
            <summary>
            Configures the <see cref="T:Prism.Regions.IRegionBehaviorFactory"/>. 
            This will be the list of default behaviors that will be added to a region. 
            </summary>
        </member>
        <member name="M:Prism.Bootstrapper.CreateShell">
            <summary>
            Creates the shell or main window of the application.
            </summary>
            <returns>The shell of the application.</returns>
            <remarks>
            If the returned instance is a <see cref="T:System.Windows.DependencyObject"/>, the
            <see cref="T:Prism.Bootstrapper"/> will attach the default <see cref="T:Prism.Regions.IRegionManager"/> of
            the application in its <see cref="F:Prism.Regions.RegionManager.RegionManagerProperty"/> attached property
            in order to be able to add regions by using the <see cref="F:Prism.Regions.RegionManager.RegionNameProperty"/>
            attached property from XAML.
            </remarks>
        </member>
        <member name="M:Prism.Bootstrapper.InitializeShell">
            <summary>
            Initializes the shell.
            </summary>
        </member>
        <member name="M:Prism.Bootstrapper.Run(System.Boolean)">
            <summary>
            Run the bootstrapper process.
            </summary>
            <param name="runWithDefaultConfiguration">If <see langword="true"/>, registers default 
            Prism Library services in the container. This is the default behavior.</param>
        </member>
        <member name="M:Prism.Bootstrapper.ConfigureServiceLocator">
            <summary>
            Configures the LocatorProvider for the <see cref="T:Microsoft.Practices.ServiceLocation.ServiceLocator" />.
            </summary>
        </member>
        <member name="T:Prism.Common.ListDictionary`2">
            <summary>
            A dictionary of lists.
            </summary>
            <typeparam name="TKey">The key to use for lists.</typeparam>
            <typeparam name="TValue">The type of the value held by lists.</typeparam>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.Add(`0)">
            <summary>
            If a list does not already exist, it will be created automatically.
            </summary>
            <param name="key">The key of the list that will hold the value.</param>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.Add(`0,`1)">
            <summary>
            Adds a value to a list with the given key. If a list does not already exist,
            it will be created automatically.
            </summary>
            <param name="key">The key of the list that will hold the value.</param>
            <param name="value">The value to add to the list under the given key.</param>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.Clear">
            <summary>
            Removes all entries in the dictionary.
            </summary>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.ContainsValue(`1)">
            <summary>
            Determines whether the dictionary contains the specified value.
            </summary>
            <param name="value">The value to locate.</param>
            <returns>true if the dictionary contains the value in any list; otherwise, false.</returns>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.ContainsKey(`0)">
            <summary>
            Determines whether the dictionary contains the given key.
            </summary>
            <param name="key">The key to locate.</param>
            <returns>true if the dictionary contains the given key; otherwise, false.</returns>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.FindAllValuesByKey(System.Predicate{`0})">
            <summary>
            Retrieves the all the elements from the list which have a key that matches the condition
            defined by the specified predicate.
            </summary>
            <param name="keyFilter">The filter with the condition to use to filter lists by their key.</param>
            <returns>The elements that have a key that matches the condition defined by the specified predicate.</returns>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.FindAllValues(System.Predicate{`1})">
            <summary>
            Retrieves all the elements that match the condition defined by the specified predicate.
            </summary>
            <param name="valueFilter">The filter with the condition to use to filter values.</param>
            <returns>The elements that match the condition defined by the specified predicate.</returns>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.Remove(`0)">
            <summary>
            Removes a list by key.
            </summary>
            <param name="key">The key of the list to remove.</param>
            <returns><see langword="true" /> if the element was removed.</returns>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.Remove(`0,`1)">
            <summary>
            Removes a value from the list with the given key.
            </summary>
            <param name="key">The key of the list where the value exists.</param>
            <param name="value">The value to remove.</param>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.Remove(`1)">
            <summary>
            Removes a value from all lists where it may be found.
            </summary>
            <param name="value">The value to remove.</param>
        </member>
        <member name="P:Prism.Common.ListDictionary`2.Values">
            <summary>
            Gets a shallow copy of all values in all lists.
            </summary>
            <value>List of values.</value>
        </member>
        <member name="P:Prism.Common.ListDictionary`2.Keys">
            <summary>
            Gets the list of keys in the dictionary.
            </summary>
            <value>Collection of keys.</value>
        </member>
        <member name="P:Prism.Common.ListDictionary`2.Item(`0)">
            <summary>
            Gets or sets the list associated with the given key. The
            access always succeeds, eventually returning an empty list.
            </summary>
            <param name="key">The key of the list to access.</param>
            <returns>The list associated with the key.</returns>
        </member>
        <member name="P:Prism.Common.ListDictionary`2.Count">
            <summary>
            Gets the number of lists in the dictionary.
            </summary>
            <value>Value indicating the values count.</value>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.System#Collections#Generic#IDictionary{TKey,System#Collections#Generic#IList{TValue}}#Add(`0,System.Collections.Generic.IList{`1})">
            <summary>
            See <see cref="M:System.Collections.Generic.IDictionary`2.Add(`0,`1)"/> for more information.
            </summary>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.System#Collections#Generic#IDictionary{TKey,System#Collections#Generic#IList{TValue}}#TryGetValue(`0,System.Collections.Generic.IList{`1}@)">
            <summary>
            See <see cref="M:System.Collections.Generic.IDictionary`2.TryGetValue(`0,`1@)"/> for more information.
            </summary>
        </member>
        <member name="P:Prism.Common.ListDictionary`2.System#Collections#Generic#IDictionary{TKey,System#Collections#Generic#IList{TValue}}#Values">
            <summary>
            See <see cref="P:System.Collections.Generic.IDictionary`2.Values"/> for more information.
            </summary>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,System#Collections#Generic#IList{TValue}}}#Add(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            See <see cref="M:System.Collections.Generic.ICollection`1.Add(`0)"/> for more information.
            </summary>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,System#Collections#Generic#IList{TValue}}}#Contains(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            See <see cref="M:System.Collections.Generic.ICollection`1.Contains(`0)"/> for more information.
            </summary>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,System#Collections#Generic#IList{TValue}}}#CopyTo(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}}[],System.Int32)">
            <summary>
            See <see cref="M:System.Collections.Generic.ICollection`1.CopyTo(`0[],System.Int32)"/> for more information.
            </summary>
        </member>
        <member name="P:Prism.Common.ListDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,System#Collections#Generic#IList{TValue}}}#IsReadOnly">
            <summary>
            See <see cref="P:System.Collections.Generic.ICollection`1.IsReadOnly"/> for more information.
            </summary>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{TKey,System#Collections#Generic#IList{TValue}}}#Remove(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            See <see cref="M:System.Collections.Generic.ICollection`1.Remove(`0)"/> for more information.
            </summary>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{TKey,System#Collections#Generic#IList{TValue}}}#GetEnumerator">
            <summary>
            See <see cref="M:System.Collections.Generic.IEnumerable`1.GetEnumerator"/> for more information.
            </summary>
        </member>
        <member name="M:Prism.Common.ListDictionary`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            See <see cref="M:System.Collections.IEnumerable.GetEnumerator"/> for more information.
            </summary>
        </member>
        <member name="T:Prism.Common.ObservableObject`1">
            <summary>
            Class that wraps an object, so that other classes can notify for Change events. Typically, this class is set as 
            a Dependency Property on DependencyObjects, and allows other classes to observe any changes in the Value. 
            </summary>
            <remarks>
            This class is required, because in Silverlight, it's not possible to receive Change notifications for Dependency properties that you do not own. 
            </remarks>
            <typeparam name="T">The type of the property that's wrapped in the Observable object</typeparam>
        </member>
        <member name="F:Prism.Common.ObservableObject`1.ValueProperty">
            <summary>
            Identifies the Value property of the ObservableObject
            </summary>
        </member>
        <member name="E:Prism.Common.ObservableObject`1.PropertyChanged">
            <summary>
            Event that gets invoked when the Value property changes. 
            </summary>
        </member>
        <member name="P:Prism.Common.ObservableObject`1.Value">
            <summary>
            The value that's wrapped inside the ObservableObject.
            </summary>
        </member>
        <member name="T:Prism.Common.UriParsingHelper">
            <summary>
            Helper class for parsing <see cref="T:System.Uri"/> instances.
            </summary>
        </member>
        <member name="M:Prism.Common.UriParsingHelper.GetQuery(System.Uri)">
            <summary>
            Gets the query part of <paramref name="uri"/>.
            </summary>
            <param name="uri">The Uri.</param>
        </member>
        <member name="M:Prism.Common.UriParsingHelper.GetAbsolutePath(System.Uri)">
            <summary>
            Gets the AbsolutePath part of <paramref name="uri"/>.
            </summary>
            <param name="uri">The Uri.</param>
        </member>
        <member name="M:Prism.Common.UriParsingHelper.ParseQuery(System.Uri)">
            <summary>
            Parses the query of <paramref name="uri"/> into a dictionary.
            </summary>
            <param name="uri">The URI.</param>
        </member>
        <member name="T:Prism.Interactivity.CommandBehaviorBase`1">
            <summary>
            Base behavior to handle connecting a <see cref="T:System.Windows.Controls.Control"/> to a Command.
            </summary>
            <typeparam name="T">The target object must derive from Control</typeparam>
            <remarks>
            CommandBehaviorBase can be used to provide new behaviors for commands.
            </remarks>
        </member>
        <member name="M:Prism.Interactivity.CommandBehaviorBase`1.#ctor(`0)">
            <summary>
            Constructor specifying the target object.
            </summary>
            <param name="targetObject">The target object the behavior is attached to.</param>
        </member>
        <member name="P:Prism.Interactivity.CommandBehaviorBase`1.Command">
            <summary>
            Corresponding command to be execute and monitored for <see cref="E:System.Windows.Input.ICommand.CanExecuteChanged"/>
            </summary>
        </member>
        <member name="P:Prism.Interactivity.CommandBehaviorBase`1.CommandParameter">
            <summary>
            The parameter to supply the command during execution
            </summary>
        </member>
        <member name="P:Prism.Interactivity.CommandBehaviorBase`1.TargetObject">
            <summary>
            Object to which this behavior is attached.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.CommandBehaviorBase`1.UpdateEnabledState">
            <summary>
            Updates the target object's IsEnabled property based on the commands ability to execute.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.CommandBehaviorBase`1.ExecuteCommand(System.Object)">
            <summary>
            Executes the command, if it's set, providing the <see cref="P:Prism.Interactivity.CommandBehaviorBase`1.CommandParameter"/>
            </summary>
        </member>
        <member name="T:Prism.Interactivity.DefaultPopupWindows.DefaultConfirmationWindow">
            <summary>
            Interaction logic for ConfirmationChildWindow.xaml
            </summary>
            <summary>
            DefaultConfirmationWindow
            </summary>
        </member>
        <member name="M:Prism.Interactivity.DefaultPopupWindows.DefaultConfirmationWindow.#ctor">
            <summary>
            Creates a new instance of ConfirmationChildWindow.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.DefaultPopupWindows.DefaultConfirmationWindow.Confirmation">
            <summary>
            Sets or gets the <see cref="T:Prism.Interactivity.InteractionRequest.IConfirmation"/> shown by this window./>
            </summary>
        </member>
        <member name="M:Prism.Interactivity.DefaultPopupWindows.DefaultConfirmationWindow.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:Prism.Interactivity.DefaultPopupWindows.DefaultNotificationWindow">
            <summary>
            Interaction logic for NotificationChildWindow.xaml
            </summary>
            <summary>
            DefaultNotificationWindow
            </summary>
        </member>
        <member name="M:Prism.Interactivity.DefaultPopupWindows.DefaultNotificationWindow.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Prism.Interactivity.DefaultPopupWindows.DefaultNotificationWindow"/>
            </summary>
        </member>
        <member name="P:Prism.Interactivity.DefaultPopupWindows.DefaultNotificationWindow.Notification">
            <summary>
            Sets or gets the <see cref="T:Prism.Interactivity.InteractionRequest.INotification"/> shown by this window./>
            </summary>
        </member>
        <member name="M:Prism.Interactivity.DefaultPopupWindows.DefaultNotificationWindow.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:Prism.Interactivity.DefaultPopupWindows.DefaultWindow">
            <summary>
            Interaction logic for DefaultWindow.xaml
            </summary>
            <summary>
            DefaultWindow
            </summary>
        </member>
        <member name="M:Prism.Interactivity.DefaultPopupWindows.DefaultWindow.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.Confirmation">
            <summary>
            Basic implementation of <see cref="T:Prism.Interactivity.InteractionRequest.IConfirmation"/>.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.Confirmation.Confirmed">
            <summary>
            Gets or sets a value indicating that the confirmation is confirmed.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.IConfirmation">
            <summary>
            Represents an interaction request used for confirmations.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.IConfirmation.Confirmed">
            <summary>
            Gets or sets a value indicating that the confirmation is confirmed.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.IInteractionRequest">
            <summary>
            Represents a request from user interaction.
            </summary>
            <remarks>
            View models can expose interaction request objects through properties and raise them when user interaction
            is required so views associated with the view models can materialize the user interaction using an appropriate
            mechanism.
            </remarks>
        </member>
        <member name="E:Prism.Interactivity.InteractionRequest.IInteractionRequest.Raised">
            <summary>
            Fired when the interaction is needed.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.IInteractionRequestAware">
            <summary>
            Interface used by the <see cref="T:Prism.Interactivity.PopupWindowAction"/>.
            If the DataContext object of a view that is shown with this action implements this interface
            it will be populated with the <see cref="T:Prism.Interactivity.InteractionRequest.INotification"/> data of the interaction request 
            as well as an <see cref="T:System.Action"/> to finish the request upon invocation.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.IInteractionRequestAware.Notification">
            <summary>
            The <see cref="T:Prism.Interactivity.InteractionRequest.INotification"/> passed when the interaction request was raised.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.IInteractionRequestAware.FinishInteraction">
            <summary>
            An <see cref="T:System.Action"/> that can be invoked to finish the interaction.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.INotification">
            <summary>
            Represents an interaction request used for notifications.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.INotification.Title">
            <summary>
            Gets or sets the title to use for the notification.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.INotification.Content">
            <summary>
            Gets or sets the content of the notification.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.InteractionRequest`1">
            <summary>
            Implementation of the <see cref="T:Prism.Interactivity.InteractionRequest.IInteractionRequest"/> interface.
            </summary>
        </member>
        <member name="E:Prism.Interactivity.InteractionRequest.InteractionRequest`1.Raised">
            <summary>
            Fired when interaction is needed.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.InteractionRequest.InteractionRequest`1.Raise(`0)">
            <summary>
            Fires the Raised event.
            </summary>
            <param name="context">The context for the interaction request.</param>
        </member>
        <member name="M:Prism.Interactivity.InteractionRequest.InteractionRequest`1.Raise(`0,System.Action{`0})">
            <summary>
            Fires the Raised event.
            </summary>
            <param name="context">The context for the interaction request.</param>
            <param name="callback">The callback to execute when the interaction is completed.</param>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.InteractionRequestedEventArgs">
            <summary>
            Event args for the <see cref="E:Prism.Interactivity.InteractionRequest.IInteractionRequest.Raised"/> event.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.InteractionRequest.InteractionRequestedEventArgs.#ctor(Prism.Interactivity.InteractionRequest.INotification,System.Action)">
            <summary>
            Constructs a new instance of <see cref="T:Prism.Interactivity.InteractionRequest.InteractionRequestedEventArgs"/>
            </summary>
            <param name="context"></param>
            <param name="callback"></param>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.InteractionRequestedEventArgs.Context">
            <summary>
            Gets the context for a requested interaction.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.InteractionRequestedEventArgs.Callback">
            <summary>
            Gets the callback to execute when an interaction is completed.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.InteractionRequestTrigger">
            <summary>
            Custom event trigger for using with <see cref="T:Prism.Interactivity.InteractionRequest.IInteractionRequest"/> objects.
            </summary>
            <remarks>
            The standard <see cref="T:System.Windows.Interactivity.EventTrigger"/> class can be used instead, as long as the 'Raised' event 
            name is specified.
            </remarks>
        </member>
        <member name="M:Prism.Interactivity.InteractionRequest.InteractionRequestTrigger.GetEventName">
            <summary>
            Specifies the name of the Event this EventTriggerBase is listening for.
            </summary>
            <returns>This implementation always returns the Raised event name for ease of connection with <see cref="T:Prism.Interactivity.InteractionRequest.IInteractionRequest"/>.</returns>
        </member>
        <member name="T:Prism.Interactivity.InteractionRequest.Notification">
            <summary>
            Basic implementation of <see cref="T:Prism.Interactivity.InteractionRequest.INotification"/>.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.Notification.Title">
            <summary>
            Gets or sets the title to use for the notification.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InteractionRequest.Notification.Content">
            <summary>
            Gets or sets the content of the notification.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InvokeCommandAction">
            <summary>
            Trigger action that executes a command when invoked. 
            It also maintains the Enabled state of the target control based on the CanExecute method of the command.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.InvokeCommandAction.AutoEnableProperty">
            <summary>
            Dependency property identifying if the associated element should automaticlaly be enabled or disabled based on the result of the Command's CanExecute
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InvokeCommandAction.AutoEnable">
            <summary>
            Gets or sets whther or not the associated element will automatically be enabled or disabled based on the result of the commands CanExecute
            </summary>
        </member>
        <member name="F:Prism.Interactivity.InvokeCommandAction.CommandProperty">
            <summary>
            Dependency property identifying the command to execute when invoked.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InvokeCommandAction.Command">
            <summary>
            Gets or sets the command to execute when invoked.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.InvokeCommandAction.CommandParameterProperty">
            <summary>
            Dependency property identifying the command parameter to supply on command execution.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InvokeCommandAction.CommandParameter">
            <summary>
            Gets or sets the command parameter to supply on command execution.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.InvokeCommandAction.TriggerParameterPathProperty">
            <summary>
            Dependency property identifying the TriggerParameterPath to be parsed to identify the child property of the trigger parameter to be used as the command parameter.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.InvokeCommandAction.TriggerParameterPath">
            <summary>
            Gets or sets the TriggerParameterPath value.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.InvokeCommandAction.InvokeAction(System.Object)">
            <summary>
            Public wrapper of the Invoke method.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.InvokeCommandAction.Invoke(System.Object)">
            <summary>
            Executes the command
            </summary>
            <param name="parameter">This parameter is passed to the command; the CommandParameter specified in the CommandParameterProperty is used for command invocation if not null.</param>
        </member>
        <member name="M:Prism.Interactivity.InvokeCommandAction.OnDetaching">
            <summary>
            Sets the Command and CommandParameter properties to null.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.InvokeCommandAction.OnAttached">
            <summary>
            This method is called after the behavior is attached.
            It updates the command behavior's Command and CommandParameter properties if necessary.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.InvokeCommandAction.ExecutableCommandBehavior">
            <summary>
            A CommandBehavior that exposes a public ExecuteCommand method. It provides the functionality to invoke commands and update Enabled state of the target control.
            It is not possible to make the <see cref="T:Prism.Interactivity.InvokeCommandAction"/> inherit from <see cref="T:Prism.Interactivity.CommandBehaviorBase`1"/>, since the <see cref="T:Prism.Interactivity.InvokeCommandAction"/>
            must already inherit from <see cref="T:System.Windows.Interactivity.TriggerAction`1"/>, so we chose to follow the aggregation approach.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.InvokeCommandAction.ExecutableCommandBehavior.#ctor(System.Windows.UIElement)">
            <summary>
            Constructor specifying the target object.
            </summary>
            <param name="target">The target object the behavior is attached to.</param>
        </member>
        <member name="M:Prism.Interactivity.InvokeCommandAction.ExecutableCommandBehavior.ExecuteCommand(System.Object)">
            <summary>
            Executes the command, if it's set.
            </summary>
        </member>
        <member name="T:Prism.Interactivity.PopupWindowAction">
            <summary>
            Shows a popup window in response to an <see cref="N:Prism.Interactivity.InteractionRequest"/> being raised.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.PopupWindowAction.WindowContentProperty">
            <summary>
            The content of the child window to display as part of the popup.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.PopupWindowAction.WindowContentTypeProperty">
            <summary>
            The type of content of the child window to display as part of the popup.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.PopupWindowAction.IsModalProperty">
            <summary>
            Determines if the content should be shown in a modal window or not.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.PopupWindowAction.CenterOverAssociatedObjectProperty">
            <summary>
            Determines if the content should be initially shown centered over the view that raised the interaction request or not.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.PopupWindowAction.WindowStartupLocationProperty">
            <summary>
            If set, applies this WindowStartupLocation to the child window.
            </summary>
        </member>
        <member name="F:Prism.Interactivity.PopupWindowAction.WindowStyleProperty">
            <summary>
            If set, applies this Style to the child window.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.PopupWindowAction.WindowContent">
            <summary>
            Gets or sets the content of the window.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.PopupWindowAction.WindowContentType">
            <summary>
            Gets or sets the type of content of the window.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.PopupWindowAction.IsModal">
            <summary>
            Gets or sets if the window will be modal or not.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.PopupWindowAction.CenterOverAssociatedObject">
            <summary>
            Gets or sets if the window will be initially shown centered over the view that raised the interaction request or not.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.PopupWindowAction.WindowStartupLocation">
            <summary>
            Gets or sets the startup location of the Window.
            </summary>
        </member>
        <member name="P:Prism.Interactivity.PopupWindowAction.WindowStyle">
            <summary>
            Gets or sets the Style of the Window.
            </summary>
        </member>
        <member name="M:Prism.Interactivity.PopupWindowAction.Invoke(System.Object)">
            <summary>
            Displays the child window and collects results for <see cref="T:Prism.Interactivity.InteractionRequest.IInteractionRequest"/>.
            </summary>
            <param name="parameter">The parameter to the action. If the action does not require a parameter, the parameter may be set to a null reference.</param>
        </member>
        <member name="M:Prism.Interactivity.PopupWindowAction.GetWindow(Prism.Interactivity.InteractionRequest.INotification)">
            <summary>
            Returns the window to display as part of the trigger action.
            </summary>
            <param name="notification">The notification to be set as a DataContext in the window.</param>
            <returns></returns>
        </member>
        <member name="M:Prism.Interactivity.PopupWindowAction.PrepareContentForWindow(Prism.Interactivity.InteractionRequest.INotification,System.Windows.Window)">
            <summary>
            Checks if the WindowContent or its DataContext implements <see cref="T:Prism.Interactivity.InteractionRequest.IInteractionRequestAware"/>.
            If so, it sets the corresponding values.
            </summary>
            <param name="notification">The notification to be set as a DataContext in the HostWindow.</param>
            <param name="wrapperWindow">The HostWindow</param>
        </member>
        <member name="M:Prism.Interactivity.PopupWindowAction.CreateWindow">
            <summary>
            Creates a Window that is used when providing custom Window Content
            </summary>
            <returns>The Window</returns>
        </member>
        <member name="M:Prism.Interactivity.PopupWindowAction.CreateDefaultWindow(Prism.Interactivity.InteractionRequest.INotification)">
            <summary>
            When no WindowContent is sent this method is used to create a default basic window to show
            the corresponding <see cref="T:Prism.Interactivity.InteractionRequest.INotification"/> or <see cref="T:Prism.Interactivity.InteractionRequest.IConfirmation"/>.
            </summary>
            <param name="notification">The INotification or IConfirmation parameter to show.</param>
            <returns></returns>
        </member>
        <member name="T:Prism.Logging.TextLogger">
            <summary>
            Implementation of <see cref="T:Prism.Logging.ILoggerFacade"/> that logs into a <see cref="T:System.IO.TextWriter"/>.
            </summary>
        </member>
        <member name="M:Prism.Logging.TextLogger.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Logging.TextLogger"/> that writes to
            the console output.
            </summary>
        </member>
        <member name="M:Prism.Logging.TextLogger.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Logging.TextLogger"/>.
            </summary>
            <param name="writer">The writer to use for writing log entries.</param>
        </member>
        <member name="M:Prism.Logging.TextLogger.Log(System.String,Prism.Logging.Category,Prism.Logging.Priority)">
            <summary>
            Write a new log entry with the specified category and priority.
            </summary>
            <param name="message">Message body to log.</param>
            <param name="category">Category of the entry.</param>
            <param name="priority">The priority of the entry.</param>
        </member>
        <member name="M:Prism.Logging.TextLogger.Dispose(System.Boolean)">
            <summary>
            Disposes the associated <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="disposing">When <see langword="true"/>, disposes the associated <see cref="T:System.IO.TextWriter"/>.</param>
        </member>
        <member name="M:Prism.Logging.TextLogger.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
             <remarks>Calls <see cref="M:Prism.Logging.TextLogger.Dispose(System.Boolean)"/></remarks>.
            <filterpriority>2</filterpriority>
        </member>
        <member name="T:Prism.Logging.TraceLogger">
            <summary>
            Implementation of <see cref="T:Prism.Logging.ILoggerFacade"/> that logs to .NET <see cref="T:System.Diagnostics.Trace"/> class.
            </summary>
        </member>
        <member name="M:Prism.Logging.TraceLogger.Log(System.String,Prism.Logging.Category,Prism.Logging.Priority)">
            <summary>
            Write a new log entry with the specified category and priority.
            </summary>
            <param name="message">Message body to log.</param>
            <param name="category">Category of the entry.</param>
            <param name="priority">The priority of the entry.</param>
        </member>
        <member name="T:Prism.Modularity.AssemblyResolver">
            <summary>
            Handles AppDomain's AssemblyResolve event to be able to load assemblies dynamically in 
            the LoadFrom context, but be able to reference the type from assemblies loaded in the Load context.
            </summary>
        </member>
        <member name="M:Prism.Modularity.AssemblyResolver.LoadAssemblyFrom(System.String)">
            <summary>
            Registers the specified assembly and resolves the types in it when the AppDomain requests for it.
            </summary>
            <param name="assemblyFilePath">The path to the assemly to load in the LoadFrom context.</param>
            <remarks>This method does not load the assembly immediately, but lazily until someone requests a <see cref="T:System.Type"/>
            declared in the assembly.</remarks>
        </member>
        <member name="M:Prism.Modularity.AssemblyResolver.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
            <remarks>Calls <see cref="M:Prism.Modularity.AssemblyResolver.Dispose(System.Boolean)"/></remarks>.
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Prism.Modularity.AssemblyResolver.Dispose(System.Boolean)">
            <summary>
            Disposes the associated <see cref="T:Prism.Modularity.AssemblyResolver"/>.
            </summary>
            <param name="disposing">When <see langword="true"/>, it is being called from the Dispose method.</param>
        </member>
        <member name="T:Prism.Modularity.ConfigurationModuleCatalog">
            <summary>
            A catalog built from a configuration file.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ConfigurationModuleCatalog.#ctor">
            <summary>
            Builds an instance of ConfigurationModuleCatalog with a <see cref="T:Prism.Modularity.ConfigurationStore"/> as the default store.
            </summary>
        </member>
        <member name="P:Prism.Modularity.ConfigurationModuleCatalog.Store">
            <summary>
            Gets or sets the store where the configuration is kept.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ConfigurationModuleCatalog.InnerLoad">
            <summary>
            Loads the catalog from the configuration.
            </summary>
        </member>
        <member name="T:Prism.Modularity.ConfigurationStore">
            <summary>
            Defines a store for the module metadata.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ConfigurationStore.RetrieveModuleConfigurationSection">
            <summary>
            Gets the module configuration data.
            </summary>
            <returns>A <see cref="T:Prism.Modularity.ModulesConfigurationSection"/> instance.</returns>
        </member>
        <member name="T:Prism.Modularity.CyclicDependencyFoundException">
            <summary>
            Represents the exception that is thrown when there is a circular dependency
            between modules during the module loading process.
            </summary>
        </member>
        <member name="M:Prism.Modularity.CyclicDependencyFoundException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.CyclicDependencyFoundException"/> class.
            </summary>
        </member>
        <member name="M:Prism.Modularity.CyclicDependencyFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.CyclicDependencyFoundException"/> class
            with the specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Prism.Modularity.CyclicDependencyFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.CyclicDependencyFoundException"/> class
            with the specified error message and inner exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
        </member>
        <member name="M:Prism.Modularity.CyclicDependencyFoundException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes the exception with a particular module, error message and inner exception that happened.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, 
            or a <see langword="null"/> reference if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Modularity.CyclicDependencyFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.CyclicDependencyFoundException"/> class
            with the serialization data.
            </summary>
            <param name="info">Holds the serialized object data about the exception being thrown.</param>
            <param name="context">Contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Modularity.DirectoryModuleCatalog">
             <summary>
             Represets a catalog created from a directory on disk.
             </summary>
             <remarks>
             The directory catalog will scan the contents of a directory, locating classes that implement
             <see cref="T:Prism.Modularity.IModule"/> and add them to the catalog based on contents in their associated <see cref="T:Prism.Modularity.ModuleAttribute"/>.
             Assemblies are loaded into a new application domain with ReflectionOnlyLoad.  The application domain is destroyed
             once the assemblies have been discovered.
            
             The diretory catalog does not continue to monitor the directory after it has created the initialze catalog.
             </remarks>
        </member>
        <member name="P:Prism.Modularity.DirectoryModuleCatalog.ModulePath">
            <summary>
            Directory containing modules to search for.
            </summary>
        </member>
        <member name="M:Prism.Modularity.DirectoryModuleCatalog.InnerLoad">
            <summary>
            Drives the main logic of building the child domain and searching for the assemblies.
            </summary>
        </member>
        <member name="M:Prism.Modularity.DirectoryModuleCatalog.BuildChildDomain(System.AppDomain)">
            <summary>
            Creates a new child domain and copies the evidence from a parent domain.
            </summary>
            <param name="parentDomain">The parent domain.</param>
            <returns>The new child domain.</returns>
            <remarks>
            Grabs the <paramref name="parentDomain"/> evidence and uses it to construct the new
            <see cref="T:System.AppDomain"/> because in a ClickOnce execution environment, creating an
            <see cref="T:System.AppDomain"/> will by default pick up the partial trust environment of
            the AppLaunch.exe, which was the root executable. The AppLaunch.exe does a
            create domain and applies the evidence from the ClickOnce manifests to
            create the domain that the application is actually executing in. This will
            need to be Full Trust for Prism applications.
            </remarks>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="parentDomain"/> is null.</exception>
        </member>
        <member name="T:Prism.Modularity.DuplicateModuleException">
            <summary>
            Exception thrown when a module is declared twice in the same catalog.
            </summary>
        </member>
        <member name="M:Prism.Modularity.DuplicateModuleException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.DuplicateModuleException"/> class.
            </summary>
        </member>
        <member name="M:Prism.Modularity.DuplicateModuleException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.DuplicateModuleException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Prism.Modularity.DuplicateModuleException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.DuplicateModuleException"/> class.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Prism.Modularity.DuplicateModuleException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.DuplicateModuleException" /> class with a specified error message.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Prism.Modularity.DuplicateModuleException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.DuplicateModuleException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Modularity.DuplicateModuleException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.DuplicateModuleException"/> class
            with the serialization data.
            </summary>
            <param name="info">Holds the serialized object data about the exception being thrown.</param>
            <param name="context">Contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Modularity.FileModuleTypeLoader">
            <summary>
            Loads modules from an arbitrary location on the filesystem. This typeloader is only called if
            <see cref="T:Prism.Modularity.ModuleInfo"/> classes have a Ref parameter that starts with "file://".
            This class is only used on the Desktop version of the Prism Library.
            </summary>
        </member>
        <member name="M:Prism.Modularity.FileModuleTypeLoader.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.FileModuleTypeLoader"/> class.
            </summary>
        </member>
        <member name="M:Prism.Modularity.FileModuleTypeLoader.#ctor(Prism.Modularity.IAssemblyResolver)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.FileModuleTypeLoader"/> class.
            </summary>
            <param name="assemblyResolver">The assembly resolver.</param>
        </member>
        <member name="E:Prism.Modularity.FileModuleTypeLoader.ModuleDownloadProgressChanged">
            <summary>
            Raised repeatedly to provide progress as modules are loaded in the background.
            </summary>
        </member>
        <member name="E:Prism.Modularity.FileModuleTypeLoader.LoadModuleCompleted">
            <summary>
            Raised when a module is loaded or fails to load.
            </summary>
        </member>
        <member name="M:Prism.Modularity.FileModuleTypeLoader.CanLoadModuleType(Prism.Modularity.ModuleInfo)">
            <summary>
            Evaluates the <see cref="P:Prism.Modularity.ModuleInfo.Ref"/> property to see if the current typeloader will be able to retrieve the <paramref name="moduleInfo"/>.
            Returns true if the <see cref="P:Prism.Modularity.ModuleInfo.Ref"/> property starts with "file://", because this indicates that the file
            is a local file.
            </summary>
            <param name="moduleInfo">Module that should have it's type loaded.</param>
            <returns>
            	<see langword="true"/> if the current typeloader is able to retrieve the module, otherwise <see langword="false"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="moduleInfo"/> is null.</exception>
        </member>
        <member name="M:Prism.Modularity.FileModuleTypeLoader.LoadModuleType(Prism.Modularity.ModuleInfo)">
            <summary>
            Retrieves the <paramref name="moduleInfo"/>.
            </summary>
            <param name="moduleInfo">Module that should have it's type loaded.</param>
        </member>
        <member name="M:Prism.Modularity.FileModuleTypeLoader.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
            <remarks>Calls <see cref="M:Prism.Modularity.FileModuleTypeLoader.Dispose(System.Boolean)"/></remarks>.
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Prism.Modularity.FileModuleTypeLoader.Dispose(System.Boolean)">
            <summary>
            Disposes the associated <see cref="T:Prism.Modularity.AssemblyResolver"/>.
            </summary>
            <param name="disposing">When <see langword="true"/>, it is being called from the Dispose method.</param>
        </member>
        <member name="T:Prism.Modularity.IAssemblyResolver">
            <summary>
            Interface for classes that are responsible for resolving and loading assembly files. 
            </summary>
        </member>
        <member name="M:Prism.Modularity.IAssemblyResolver.LoadAssemblyFrom(System.String)">
            <summary>
            Load an assembly when it's required by the application. 
            </summary>
            <param name="assemblyFilePath"></param>
        </member>
        <member name="T:Prism.Modularity.IConfigurationStore">
            <summary>
            Defines a store for the module metadata.
            </summary>
        </member>
        <member name="M:Prism.Modularity.IConfigurationStore.RetrieveModuleConfigurationSection">
            <summary>
            Gets the module configuration data.
            </summary>
            <returns>A <see cref="T:Prism.Modularity.ModulesConfigurationSection"/> instance.</returns>
        </member>
        <member name="T:Prism.Modularity.IModule">
            <summary>
            Defines the contract for the modules deployed in the application.
            </summary>
        </member>
        <member name="M:Prism.Modularity.IModule.Initialize">
            <summary>
            Notifies the module that it has be initialized.
            </summary>
        </member>
        <member name="T:Prism.Modularity.IModuleCatalog">
            <summary>
            This is the expected catalog definition for the ModuleManager. 
            The ModuleCatalog holds information about the modules that can be used by the 
            application. Each module is described in a ModuleInfo class, that records the 
            name, type and location of the module. 
            </summary>
        </member>
        <member name="P:Prism.Modularity.IModuleCatalog.Modules">
            <summary>
            Gets all the <see cref="T:Prism.Modularity.ModuleInfo"/> classes that are in the <see cref="T:Prism.Modularity.ModuleCatalog"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.IModuleCatalog.GetDependentModules(Prism.Modularity.ModuleInfo)">
            <summary>
            Return the list of <see cref="T:Prism.Modularity.ModuleInfo"/>s that <paramref name="moduleInfo"/> depends on.
            </summary>
            <param name="moduleInfo">The <see cref="T:Prism.Modularity.ModuleInfo"/> to get the </param>
            <returns>An enumeration of <see cref="T:Prism.Modularity.ModuleInfo"/> that <paramref name="moduleInfo"/> depends on.</returns>
        </member>
        <member name="M:Prism.Modularity.IModuleCatalog.CompleteListWithDependencies(System.Collections.Generic.IEnumerable{Prism.Modularity.ModuleInfo})">
            <summary>
            Returns the collection of <see cref="T:Prism.Modularity.ModuleInfo"/>s that contain both the <see cref="T:Prism.Modularity.ModuleInfo"/>s in 
            <paramref name="modules"/>, but also all the modules they depend on. 
            </summary>
            <param name="modules">The modules to get the dependencies for.</param>
            <returns>
            A collection of <see cref="T:Prism.Modularity.ModuleInfo"/> that contains both all <see cref="T:Prism.Modularity.ModuleInfo"/>s in <paramref name="modules"/>
            and also all the <see cref="T:Prism.Modularity.ModuleInfo"/> they depend on.
            </returns>
        </member>
        <member name="M:Prism.Modularity.IModuleCatalog.Initialize">
            <summary>
            Initializes the catalog, which may load and validate the modules.
            </summary>
        </member>
        <member name="M:Prism.Modularity.IModuleCatalog.AddModule(Prism.Modularity.ModuleInfo)">
            <summary>
            Adds a <see cref="T:Prism.Modularity.ModuleInfo"/> to the <see cref="T:Prism.Modularity.ModuleCatalog"/>.
            </summary>
            <param name="moduleInfo">The <see cref="T:Prism.Modularity.ModuleInfo"/> to add.</param>
            <returns>The <see cref="T:Prism.Modularity.ModuleCatalog"/> for easily adding multiple modules.</returns>
        </member>
        <member name="T:Prism.Modularity.IModuleCatalogItem">
            <summary>
            Marker interface that allows both <see cref="T:Prism.Modularity.ModuleInfoGroup"/>s and <see cref="T:Prism.Modularity.ModuleInfo"/>s to be
            added to the <see cref="T:Prism.Modularity.IModuleCatalog"/> from code and XAML. 
            </summary>
        </member>
        <member name="T:Prism.Modularity.IModuleInitializer">
            <summary>
            Declares a service which initializes the modules into the application.
            </summary>
        </member>
        <member name="M:Prism.Modularity.IModuleInitializer.Initialize(Prism.Modularity.ModuleInfo)">
            <summary>
            Initializes the specified module.
            </summary>
            <param name="moduleInfo">The module to initialize</param>
        </member>
        <member name="T:Prism.Modularity.IModuleManager">
            <summary>
            Defines the interface for the service that will retrieve and initialize the application's modules.
            </summary>
        </member>
        <member name="M:Prism.Modularity.IModuleManager.Run">
            <summary>
            Initializes the modules marked as <see cref="F:Prism.Modularity.InitializationMode.WhenAvailable"/> on the <see cref="T:Prism.Modularity.ModuleCatalog"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.IModuleManager.LoadModule(System.String)">
            <summary>
            Loads and initializes the module on the <see cref="T:Prism.Modularity.ModuleCatalog"/> with the name <paramref name="moduleName"/>.
            </summary>
            <param name="moduleName">Name of the module requested for initialization.</param>
        </member>
        <member name="E:Prism.Modularity.IModuleManager.ModuleDownloadProgressChanged">
            <summary>
            Raised repeatedly to provide progress as modules are downloaded.
            </summary>
        </member>
        <member name="E:Prism.Modularity.IModuleManager.LoadModuleCompleted">
            <summary>
            Raised when a module is loaded or fails to load.
            </summary>
        </member>
        <member name="T:Prism.Modularity.IModuleTypeLoader">
            <summary>
            Defines the interface for moduleTypeLoaders
            </summary>
        </member>
        <member name="M:Prism.Modularity.IModuleTypeLoader.CanLoadModuleType(Prism.Modularity.ModuleInfo)">
            <summary>
            Evaluates the <see cref="P:Prism.Modularity.ModuleInfo.Ref"/> property to see if the current typeloader will be able to retrieve the <paramref name="moduleInfo"/>.
            </summary>
            <param name="moduleInfo">Module that should have it's type loaded.</param>
            <returns><see langword="true"/> if the current typeloader is able to retrieve the module, otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Prism.Modularity.IModuleTypeLoader.LoadModuleType(Prism.Modularity.ModuleInfo)">
            <summary>
            Retrieves the <paramref name="moduleInfo"/>.
            </summary>
            <param name="moduleInfo">Module that should have it's type loaded.</param>
        </member>
        <member name="E:Prism.Modularity.IModuleTypeLoader.ModuleDownloadProgressChanged">
            <summary>
            Raised repeatedly to provide progress as modules are downloaded in the background.
            </summary>
        </member>
        <member name="E:Prism.Modularity.IModuleTypeLoader.LoadModuleCompleted">
            <summary>
            Raised when a module is loaded or fails to load.
            </summary>
            <remarks>
            This event is raised once per ModuleInfo instance requested in <see cref="M:Prism.Modularity.IModuleTypeLoader.LoadModuleType(Prism.Modularity.ModuleInfo)"/>.
            </remarks>
        </member>
        <member name="T:Prism.Modularity.InitializationMode">
            <summary>
            Specifies on which stage the Module group will be initialized.
            </summary>
        </member>
        <member name="F:Prism.Modularity.InitializationMode.WhenAvailable">
            <summary>
            The module will be initialized when it is available on application start-up.
            </summary>
        </member>
        <member name="F:Prism.Modularity.InitializationMode.OnDemand">
            <summary>
            The module will be initialized when requested, and not automatically on application start-up.
            </summary>
        </member>
        <member name="T:Prism.Modularity.LoadModuleCompletedEventArgs">
            <summary>
            Provides completion information after a module is loaded, or fails to load.
            </summary>
        </member>
        <member name="M:Prism.Modularity.LoadModuleCompletedEventArgs.#ctor(Prism.Modularity.ModuleInfo,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.LoadModuleCompletedEventArgs"/> class.
            </summary>
            <param name="moduleInfo">The module info.</param>
            <param name="error">Any error that occurred during the call.</param>
        </member>
        <member name="P:Prism.Modularity.LoadModuleCompletedEventArgs.ModuleInfo">
            <summary>
            Gets the module info.
            </summary>
            <value>The module info.</value>
        </member>
        <member name="P:Prism.Modularity.LoadModuleCompletedEventArgs.Error">
            <summary>
            Gets any error that occurred
            </summary>
            <value>The exception if an error occurred; otherwise null.</value>
        </member>
        <member name="P:Prism.Modularity.LoadModuleCompletedEventArgs.IsErrorHandled">
            <summary>
            Gets or sets a value indicating whether the error has been handled by the event subscriber.
            </summary>
            <value><c>true</c>if the error is handled; otherwise, <c>false</c>.</value>
            <remarks>
            If there is an error on this event and no event subscriber sets this to true, an exception will be thrown by the event publisher.
            </remarks>
        </member>
        <member name="T:Prism.Modularity.ModularityException">
            <summary>
            Base class for exceptions that are thrown because of a problem with modules. 
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModularityException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModularityException"/> class.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModularityException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModularityException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Prism.Modularity.ModularityException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModularityException"/> class.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Prism.Modularity.ModularityException.#ctor(System.String,System.String)">
            <summary>
            Initializes the exception with a particular module and error message.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Prism.Modularity.ModularityException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes the exception with a particular module, error message and inner exception that happened.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, 
            or a <see langword="null"/> reference if no inner exception is specified.</param>
        </member>
        <member name="P:Prism.Modularity.ModularityException.ModuleName">
            <summary>
            Gets or sets the name of the module that this exception refers to.
            </summary>
            <value>The name of the module.</value>
        </member>
        <member name="M:Prism.Modularity.ModularityException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="M:Prism.Modularity.ModularityException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with information about the exception.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Modularity.ModuleAttribute">
            <summary>
            Indicates that the class should be considered a named module using the
            provided module name.
            </summary>
        </member>
        <member name="P:Prism.Modularity.ModuleAttribute.ModuleName">
            <summary>
            Gets or sets the name of the module.
            </summary>
            <value>The name of the module.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleAttribute.OnDemand">
            <summary>
            Gets or sets the value indicating whether the module should be loaded OnDemand.
            </summary>
            When <see langword="false"/> (default value), it indicates the module should be loaded as soon as it's dependencies are satisfied.
            Otherwise you should explicitily load this module via the <see cref="T:Prism.Modularity.ModuleManager"/>.
        </member>
        <member name="T:Prism.Modularity.ModuleCatalog">
             <summary>
             The <see cref="T:Prism.Modularity.ModuleCatalog"/> holds information about the modules that can be used by the
             application. Each module is described in a <see cref="T:Prism.Modularity.ModuleInfo"/> class, that records the
             name, type and location of the module.
            
             It also verifies that the <see cref="T:Prism.Modularity.ModuleCatalog"/> is internally valid. That means that
             it does not have:
             <list>
                 <item>Circular dependencies</item>
                 <item>Missing dependencies</item>
                 <item>
                     Invalid dependencies, such as a Module that's loaded at startup that depends on a module
                     that might need to be retrieved.
                 </item>
             </list>
             The <see cref="T:Prism.Modularity.ModuleCatalog"/> also serves as a baseclass for more specialized Catalogs .
             </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleCatalog"/> class.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.#ctor(System.Collections.Generic.IEnumerable{Prism.Modularity.ModuleInfo})">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleCatalog"/> class while providing an
            initial list of <see cref="T:Prism.Modularity.ModuleInfo"/>s.
            </summary>
            <param name="modules">The initial list of modules.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleCatalog.Items">
            <summary>
            Gets the items in the <see cref="T:Prism.Modularity.ModuleCatalog"/>. This property is mainly used to add <see cref="T:Prism.Modularity.ModuleInfoGroup"/>s or
            <see cref="T:Prism.Modularity.ModuleInfo"/>s through XAML.
            </summary>
            <value>The items in the catalog.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleCatalog.Modules">
            <summary>
            Gets all the <see cref="T:Prism.Modularity.ModuleInfo"/> classes that are in the <see cref="T:Prism.Modularity.ModuleCatalog"/>, regardless
            if they are within a <see cref="T:Prism.Modularity.ModuleInfoGroup"/> or not.
            </summary>
            <value>The modules.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleCatalog.Groups">
            <summary>
            Gets the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>s that have been added to the <see cref="T:Prism.Modularity.ModuleCatalog"/>.
            </summary>
            <value>The groups.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleCatalog.Validated">
            <summary>
            Gets or sets a value that remembers whether the <see cref="T:Prism.Modularity.ModuleCatalog"/> has been validated already.
            </summary>
        </member>
        <member name="P:Prism.Modularity.ModuleCatalog.GrouplessModules">
            <summary>
            Returns the list of <see cref="T:Prism.Modularity.ModuleInfo"/>s that are not contained within any <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </summary>
            <value>The groupless modules.</value>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.CreateFromXaml(System.IO.Stream)">
            <summary>
            Creates a <see cref="T:Prism.Modularity.ModuleCatalog"/> from XAML.
            </summary>
            <param name="xamlStream"><see cref="T:System.IO.Stream"/> that contains the XAML declaration of the catalog.</param>
            <returns>An instance of <see cref="T:Prism.Modularity.ModuleCatalog"/> built from the XAML.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.CreateFromXaml(System.Uri)">
            <summary>
            Creates a <see cref="T:Prism.Modularity.ModuleCatalog"/> from a XAML included as an Application Resource.
            </summary>
            <param name="builderResourceUri">Relative <see cref="T:System.Uri"/> that identifies the XAML included as an Application Resource.</param>
            <returns>An instance of <see cref="T:Prism.Modularity.ModuleCatalog"/> build from the XAML.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.Load">
            <summary>
            Loads the catalog if necessary.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.GetDependentModules(Prism.Modularity.ModuleInfo)">
            <summary>
            Return the list of <see cref="T:Prism.Modularity.ModuleInfo"/>s that <paramref name="moduleInfo"/> depends on.
            </summary>
            <remarks>
            If  the <see cref="T:Prism.Modularity.ModuleCatalog"/> was not yet validated, this method will call <see cref="M:Prism.Modularity.ModuleCatalog.Validate"/>.
            </remarks>
            <param name="moduleInfo">The <see cref="T:Prism.Modularity.ModuleInfo"/> to get the </param>
            <returns>An enumeration of <see cref="T:Prism.Modularity.ModuleInfo"/> that <paramref name="moduleInfo"/> depends on.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.CompleteListWithDependencies(System.Collections.Generic.IEnumerable{Prism.Modularity.ModuleInfo})">
            <summary>
            Returns a list of <see cref="T:Prism.Modularity.ModuleInfo"/>s that contain both the <see cref="T:Prism.Modularity.ModuleInfo"/>s in
            <paramref name="modules"/>, but also all the modules they depend on.
            </summary>
            <param name="modules">The modules to get the dependencies for.</param>
            <returns>
            A list of <see cref="T:Prism.Modularity.ModuleInfo"/> that contains both all <see cref="T:Prism.Modularity.ModuleInfo"/>s in <paramref name="modules"/>
            but also all the <see cref="T:Prism.Modularity.ModuleInfo"/> they depend on.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.Validate">
            <summary>
            Validates the <see cref="T:Prism.Modularity.ModuleCatalog"/>.
            </summary>
            <exception cref="T:Prism.Modularity.ModularityException">When validation of the <see cref="T:Prism.Modularity.ModuleCatalog"/> fails.</exception>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.AddModule(Prism.Modularity.ModuleInfo)">
            <summary>
            Adds a <see cref="T:Prism.Modularity.ModuleInfo"/> to the <see cref="T:Prism.Modularity.ModuleCatalog"/>.
            </summary>
            <param name="moduleInfo">The <see cref="T:Prism.Modularity.ModuleInfo"/> to add.</param>
            <returns>The <see cref="T:Prism.Modularity.ModuleCatalog"/> for easily adding multiple modules.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.AddModule(System.Type,System.String[])">
            <summary>
            Adds a groupless <see cref="T:Prism.Modularity.ModuleInfo"/> to the catalog.
            </summary>
            <param name="moduleType"><see cref="T:System.Type"/> of the module to be added.</param>
            <param name="dependsOn">Collection of module names (<see cref="P:Prism.Modularity.ModuleInfo.ModuleName"/>) of the modules on which the module to be added logically depends on.</param>
            <returns>The same <see cref="T:Prism.Modularity.ModuleCatalog"/> instance with the added module.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.AddModule(System.Type,Prism.Modularity.InitializationMode,System.String[])">
            <summary>
            Adds a groupless <see cref="T:Prism.Modularity.ModuleInfo"/> to the catalog.
            </summary>
            <param name="moduleType"><see cref="T:System.Type"/> of the module to be added.</param>
            <param name="initializationMode">Stage on which the module to be added will be initialized.</param>
            <param name="dependsOn">Collection of module names (<see cref="P:Prism.Modularity.ModuleInfo.ModuleName"/>) of the modules on which the module to be added logically depends on.</param>
            <returns>The same <see cref="T:Prism.Modularity.ModuleCatalog"/> instance with the added module.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.AddModule(System.String,System.String,System.String[])">
            <summary>
            Adds a groupless <see cref="T:Prism.Modularity.ModuleInfo"/> to the catalog.
            </summary>
            <param name="moduleName">Name of the module to be added.</param>
            <param name="moduleType"><see cref="T:System.Type"/> of the module to be added.</param>
            <param name="dependsOn">Collection of module names (<see cref="P:Prism.Modularity.ModuleInfo.ModuleName"/>) of the modules on which the module to be added logically depends on.</param>
            <returns>The same <see cref="T:Prism.Modularity.ModuleCatalog"/> instance with the added module.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.AddModule(System.String,System.String,Prism.Modularity.InitializationMode,System.String[])">
            <summary>
            Adds a groupless <see cref="T:Prism.Modularity.ModuleInfo"/> to the catalog.
            </summary>
            <param name="moduleName">Name of the module to be added.</param>
            <param name="moduleType"><see cref="T:System.Type"/> of the module to be added.</param>
            <param name="initializationMode">Stage on which the module to be added will be initialized.</param>
            <param name="dependsOn">Collection of module names (<see cref="P:Prism.Modularity.ModuleInfo.ModuleName"/>) of the modules on which the module to be added logically depends on.</param>
            <returns>The same <see cref="T:Prism.Modularity.ModuleCatalog"/> instance with the added module.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.AddModule(System.String,System.String,System.String,Prism.Modularity.InitializationMode,System.String[])">
            <summary>
            Adds a groupless <see cref="T:Prism.Modularity.ModuleInfo"/> to the catalog.
            </summary>
            <param name="moduleName">Name of the module to be added.</param>
            <param name="moduleType"><see cref="T:System.Type"/> of the module to be added.</param>
            <param name="refValue">Reference to the location of the module to be added assembly.</param>
            <param name="initializationMode">Stage on which the module to be added will be initialized.</param>
            <param name="dependsOn">Collection of module names (<see cref="P:Prism.Modularity.ModuleInfo.ModuleName"/>) of the modules on which the module to be added logically depends on.</param>
            <returns>The same <see cref="T:Prism.Modularity.ModuleCatalog"/> instance with the added module.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.Initialize">
            <summary>
            Initializes the catalog, which may load and validate the modules.
            </summary>
            <exception cref="T:Prism.Modularity.ModularityException">When validation of the <see cref="T:Prism.Modularity.ModuleCatalog"/> fails, because this method calls <see cref="M:Prism.Modularity.ModuleCatalog.Validate"/>.</exception>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.AddGroup(Prism.Modularity.InitializationMode,System.String,Prism.Modularity.ModuleInfo[])">
            <summary>
            Creates and adds a <see cref="T:Prism.Modularity.ModuleInfoGroup"/> to the catalog.
            </summary>
            <param name="initializationMode">Stage on which the module group to be added will be initialized.</param>
            <param name="refValue">Reference to the location of the module group to be added.</param>
            <param name="moduleInfos">Collection of <see cref="T:Prism.Modularity.ModuleInfo"/> included in the group.</param>
            <returns><see cref="T:Prism.Modularity.ModuleCatalog"/> with the added module group.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.SolveDependencies(System.Collections.Generic.IEnumerable{Prism.Modularity.ModuleInfo})">
            <summary>
            Checks for cyclic dependencies, by calling the dependencysolver.
            </summary>
            <param name="modules">the.</param>
            <returns></returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.ValidateDependencies(System.Collections.Generic.IEnumerable{Prism.Modularity.ModuleInfo})">
            <summary>
            Ensures that all the dependencies within <paramref name="modules"/> refer to <see cref="T:Prism.Modularity.ModuleInfo"/>s
            within that list.
            </summary>
            <param name="modules">The modules to validate modules for.</param>
            <exception cref="T:Prism.Modularity.ModularityException">
            Throws if a <see cref="T:Prism.Modularity.ModuleInfo"/> in <paramref name="modules"/> depends on a module that's
            not in <paramref name="modules"/>.
            </exception>
            <exception cref="T:System.ArgumentNullException">Throws if <paramref name="modules"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.InnerLoad">
            <summary>
            Does the actual work of loading the catalog.  The base implementation does nothing.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.Sort(System.Collections.Generic.IEnumerable{Prism.Modularity.ModuleInfo})">
            <summary>
            Sorts a list of <see cref="T:Prism.Modularity.ModuleInfo"/>s. This method is called by <see cref="M:Prism.Modularity.ModuleCatalog.CompleteListWithDependencies(System.Collections.Generic.IEnumerable{Prism.Modularity.ModuleInfo})"/>
            to return a sorted list.
            </summary>
            <param name="modules">The <see cref="T:Prism.Modularity.ModuleInfo"/>s to sort.</param>
            <returns>Sorted list of <see cref="T:Prism.Modularity.ModuleInfo"/>s</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.ValidateUniqueModules">
            <summary>
            Makes sure all modules have an Unique name.
            </summary>
            <exception cref="T:Prism.Modularity.DuplicateModuleException">
            Thrown if the names of one or more modules are not unique.
            </exception>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.ValidateDependencyGraph">
            <summary>
            Ensures that there are no cyclic dependencies.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.ValidateCrossGroupDependencies">
            <summary>
            Ensures that there are no dependencies between modules on different groups.
            </summary>
            <remarks>
            A groupless module can only depend on other groupless modules.
            A module within a group can depend on other modules within the same group and/or on groupless modules.
            </remarks>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.ValidateDependenciesInitializationMode">
            <summary>
            Ensures that there are no modules marked to be loaded <see cref="F:Prism.Modularity.InitializationMode.WhenAvailable"/>
            depending on modules loaded <see cref="F:Prism.Modularity.InitializationMode.OnDemand"/>
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.GetDependentModulesInner(Prism.Modularity.ModuleInfo)">
            <summary>
            Returns the <see cref="T:Prism.Modularity.ModuleInfo"/> on which the received module dependens on.
            </summary>
            <param name="moduleInfo">Module whose dependant modules are requested.</param>
            <returns>Collection of <see cref="T:Prism.Modularity.ModuleInfo"/> dependants of <paramref name="moduleInfo"/>.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleCatalog.EnsureCatalogValidated">
            <summary>
            Ensures that the catalog is validated.
            </summary>
        </member>
        <member name="T:Prism.Modularity.ModuleConfigurationElement">
            <summary>
            A configuration element to declare module metadata.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElement.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleConfigurationElement"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElement.#ctor(System.String,System.String,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleConfigurationElement"/>.
            </summary>
            <param name="assemblyFile">The assembly file where the module is located.</param>
            <param name="moduleType">The type of the module.</param>
            <param name="moduleName">The name of the module.</param>
            <param name="startupLoaded">This attribute specifies whether the module is loaded at startup.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElement.AssemblyFile">
            <summary>
            Gets or sets the assembly file.
            </summary>
            <value>The assembly file.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElement.ModuleType">
            <summary>
            Gets or sets the module type.
            </summary>
            <value>The module's type.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElement.ModuleName">
            <summary>
            Gets or sets the module name.
            </summary>
            <value>The module's name.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElement.StartupLoaded">
            <summary>
            Gets or sets a value indicating whether the module should be loaded at startup.
            </summary>
            <value>A value indicating whether the module should be loaded at startup.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElement.Dependencies">
            <summary>
            Gets or sets the modules this module depends on.
            </summary>
            <value>The names of the modules that this depends on.</value>
        </member>
        <member name="T:Prism.Modularity.ModuleConfigurationElementCollection">
            <summary>
            A collection of <see cref="T:Prism.Modularity.ModuleConfigurationElement"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElementCollection.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleConfigurationElementCollection"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElementCollection.#ctor(Prism.Modularity.ModuleConfigurationElement[])">
            <summary>
            Initializes a new <see cref="T:Prism.Modularity.ModuleConfigurationElementCollection"/>.
            </summary>
            <param name="modules">The initial set of <see cref="T:Prism.Modularity.ModuleConfigurationElement"/>.</param>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="modules"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElementCollection.ThrowOnDuplicate">
            <summary>
            Gets a value indicating whether an exception should be raised if a duplicate element is found.
            This property will always return true.
            </summary>
            <value>A <see cref="T:System.Boolean"/> value.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElementCollection.CollectionType">
            <summary>
            Gets the type of the <see cref="T:System.Configuration.ConfigurationElementCollection" />.
            </summary>
            <value>
            The <see cref="T:System.Configuration.ConfigurationElementCollectionType" /> of this collection.
            </value>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElementCollection.ElementName">
            <summary>
            Gets the name used to identify this collection of elements in the configuration file when overridden in a derived class.
            </summary>
            <value>
            The name of the collection; otherwise, an empty string.
            </value>
        </member>
        <member name="P:Prism.Modularity.ModuleConfigurationElementCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Prism.Modularity.ModuleConfigurationElement"/> located at the specified index in the collection.
            </summary>
            <param name="index">The index of the element in the collection.</param>
            <returns>A <see cref="T:Prism.Modularity.ModuleConfigurationElement"/>.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElementCollection.Add(Prism.Modularity.ModuleConfigurationElement)">
            <summary>
            Adds a <see cref="T:Prism.Modularity.ModuleConfigurationElement"/> to the collection.
            </summary>
            <param name="module">A <see cref="T:Prism.Modularity.ModuleConfigurationElement"/> instance.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElementCollection.Contains(System.String)">
            <summary>
            Tests if the collection contains the configuration for the specified module name.
            </summary>
            <param name="moduleName">The name of the module to search the configuration for.</param>
            <returns><see langword="true"/> if a configuration for the module is present; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElementCollection.FindAll(System.Predicate{Prism.Modularity.ModuleConfigurationElement})">
            <summary>
            Searches the collection for all the <see cref="T:Prism.Modularity.ModuleConfigurationElement"/> that match the specified predicate.
            </summary>
            <param name="match">A <see cref="T:System.Predicate`1"/> that implements the match test.</param>
            <returns>A <see cref="T:System.Collections.Generic.List`1"/> with the successful matches.</returns>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="match"/> is null.</exception>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElementCollection.CreateNewElement">
            <summary>
            Creates a new <see cref="T:Prism.Modularity.ModuleConfigurationElement"/>.
            </summary>
            <returns>A <see cref="T:Prism.Modularity.ModuleConfigurationElement"/>.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleConfigurationElementCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement" /> to return the key for. </param>
            <returns>
            An <see cref="T:System.Object" /> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement" />.
            </returns>
        </member>
        <member name="T:Prism.Modularity.ModuleDependencyAttribute">
            <summary>
            Specifies that the current module has a dependency on another module. This attribute should be used on classes that implement <see cref="T:Prism.Modularity.IModule"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencyAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleDependencyAttribute"/>.
            </summary>
            <param name="moduleName">The name of the module that this module is dependant upon.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleDependencyAttribute.ModuleName">
            <summary>
            Gets the name of the module that this module is dependant upon.
            </summary>
            <value>The name of the module that this module is dependant upon.</value>
        </member>
        <member name="T:Prism.Modularity.ModuleDependencyCollection">
            <summary>
            A collection of <see cref="T:Prism.Modularity.ModuleDependencyConfigurationElement"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencyCollection.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleDependencyCollection"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencyCollection.#ctor(Prism.Modularity.ModuleDependencyConfigurationElement[])">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleDependencyCollection"/>.
            </summary>
            <param name="dependencies">An array of <see cref="T:Prism.Modularity.ModuleDependencyConfigurationElement"/> with initial list of dependencies.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleDependencyCollection.CollectionType">
            <summary>
            Gets the type of the <see cref="T:System.Configuration.ConfigurationElementCollection" />.
            </summary>
            <value>
            The <see cref="T:System.Configuration.ConfigurationElementCollectionType" /> of this collection.
            </value>
        </member>
        <member name="P:Prism.Modularity.ModuleDependencyCollection.ElementName">
            <summary>
            Gets the name used to identify this collection of elements in the configuration file when overridden in a derived class.
            </summary>
            <value>
            The name of the collection; otherwise, an empty string.
            </value>
        </member>
        <member name="P:Prism.Modularity.ModuleDependencyCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Prism.Modularity.ModuleDependencyConfigurationElement"/> located at the specified index in the collection.
            </summary>
            <param name="index">The index of the element in the collection.</param>
            <returns>A <see cref="T:Prism.Modularity.ModuleDependencyConfigurationElement"/>.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencyCollection.CreateNewElement">
            <summary>
            Creates a new <see cref="T:Prism.Modularity.ModuleDependencyConfigurationElement"/>.
            </summary>
            <returns>A <see cref="T:Prism.Modularity.ModuleDependencyConfigurationElement"/>.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencyCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement" /> to return the key for. </param>
            <returns>
            An <see cref="T:System.Object" /> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement" />.
            </returns>
        </member>
        <member name="T:Prism.Modularity.ModuleDependencyConfigurationElement">
            <summary>
            A <see cref="T:System.Configuration.ConfigurationElement"/> for module dependencies.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencyConfigurationElement.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleDependencyConfigurationElement"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencyConfigurationElement.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleDependencyConfigurationElement"/>.
            </summary>
            <param name="moduleName">A module name.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleDependencyConfigurationElement.ModuleName">
            <summary>
            Gets or sets the name of a module antoher module depends on.
            </summary>
            <value>The name of a module antoher module depends on.</value>
        </member>
        <member name="T:Prism.Modularity.ModuleDependencySolver">
            <summary>
            Used by <see cref="T:Prism.Modularity.ModuleInitializer"/> to get the load sequence
            for the modules to load according to their dependencies.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencySolver.AddModule(System.String)">
            <summary>
            Adds a module to the solver.
            </summary>
            <param name="name">The name that uniquely identifies the module.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencySolver.AddDependency(System.String,System.String)">
            <summary>
            Adds a module dependency between the modules specified by dependingModule and
            dependentModule.
            </summary>
            <param name="dependingModule">The name of the module with the dependency.</param>
            <param name="dependentModule">The name of the module dependingModule
            depends on.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleDependencySolver.Solve">
            <summary>
            Calculates an ordered vector according to the defined dependencies.
            Non-dependant modules appears at the beginning of the resulting array.
            </summary>
            <returns>The resulting ordered list of modules.</returns>
            <exception cref="T:Prism.Modularity.CyclicDependencyFoundException">This exception is thrown
            when a cycle is found in the defined depedency graph.</exception>
        </member>
        <member name="P:Prism.Modularity.ModuleDependencySolver.ModuleCount">
            <summary>
            Gets the number of modules added to the solver.
            </summary>
            <value>The number of modules.</value>
        </member>
        <member name="T:Prism.Modularity.ModuleDownloadProgressChangedEventArgs">
            <summary>
            Provides progress information as a module downloads.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleDownloadProgressChangedEventArgs.#ctor(Prism.Modularity.ModuleInfo,System.Int64,System.Int64)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleDownloadProgressChangedEventArgs"/> class.
            </summary>
            <param name="moduleInfo">The module info.</param>
            <param name="bytesReceived">The bytes received.</param>
            <param name="totalBytesToReceive">The total bytes to receive.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleDownloadProgressChangedEventArgs.ModuleInfo">
            <summary>
            Getsthe module info.
            </summary>
            <value>The module info.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleDownloadProgressChangedEventArgs.BytesReceived">
            <summary>
            Gets the bytes received.
            </summary>
            <value>The bytes received.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleDownloadProgressChangedEventArgs.TotalBytesToReceive">
            <summary>
            Gets the total bytes to receive.
            </summary>
            <value>The total bytes to receive.</value>
        </member>
        <member name="T:Prism.Modularity.ModuleInfo">
            <summary>
            Defines the metadata that describes a module.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleInfo.#ctor">
            <summary>
            Initializes a new empty instance of <see cref="T:Prism.Modularity.ModuleInfo"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleInfo.#ctor(System.String,System.String,System.String[])">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleInfo"/>.
            </summary>
            <param name="name">The module's name.</param>
            <param name="type">The module <see cref="T:System.Type"/>'s AssemblyQualifiedName.</param>
            <param name="dependsOn">The modules this instance depends on.</param>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="dependsOn"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Prism.Modularity.ModuleInfo.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleInfo"/>.
            </summary>
            <param name="name">The module's name.</param>
            <param name="type">The module's type.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleInfo.ModuleName">
            <summary>
            Gets or sets the name of the module.
            </summary>
            <value>The name of the module.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleInfo.ModuleType">
            <summary>
            Gets or sets the module <see cref="T:System.Type"/>'s AssemblyQualifiedName.
            </summary>
            <value>The type of the module.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleInfo.DependsOn">
            <summary>
            Gets or sets the list of modules that this module depends upon.
            </summary>
            <value>The list of modules that this module depends upon.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleInfo.InitializationMode">
            <summary>
            Specifies on which stage the Module will be initialized.
            </summary>
        </member>
        <member name="P:Prism.Modularity.ModuleInfo.Ref">
            <summary>
            Reference to the location of the module assembly.
            <example>The following are examples of valid <see cref="P:Prism.Modularity.ModuleInfo.Ref"/> values:
            file://c:/MyProject/Modules/MyModule.dll for a loose DLL in WPF.
            </example>
            </summary>
        </member>
        <member name="P:Prism.Modularity.ModuleInfo.State">
            <summary>
            Gets or sets the state of the <see cref="T:Prism.Modularity.ModuleInfo"/> with regards to the module loading and initialization process.
            </summary>
        </member>
        <member name="T:Prism.Modularity.ModuleInfoGroup">
             <summary>
             Represents a group of <see cref="T:Prism.Modularity.ModuleInfo"/> instances that are usually deployed together. <see cref="T:Prism.Modularity.ModuleInfoGroup"/>s
             are also used by the <see cref="T:Prism.Modularity.ModuleCatalog"/> to prevent common deployment problems such as having a module that's required
             at startup that depends on modules that will only be downloaded on demand.
            
             The group also forwards <see cref="P:Prism.Modularity.ModuleInfoGroup.Ref"/> and <see cref="P:Prism.Modularity.ModuleInfoGroup.InitializationMode"/> values to the <see cref="T:Prism.Modularity.ModuleInfo"/>s that it
             contains.
             </summary>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.InitializationMode">
            <summary>
            Gets or sets the <see cref="P:Prism.Modularity.ModuleInfo.InitializationMode"/> for the whole group. Any <see cref="T:Prism.Modularity.ModuleInfo"/> classes that are
            added after setting this value will also get this <see cref="P:Prism.Modularity.ModuleInfoGroup.InitializationMode"/>.
            </summary>
            <see cref="P:Prism.Modularity.ModuleInfo.InitializationMode"/>
            <value>The initialization mode.</value>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.Ref">
             <summary>
             Gets or sets the <see cref="P:Prism.Modularity.ModuleInfo.Ref"/> value for the whole group. Any <see cref="T:Prism.Modularity.ModuleInfo"/> classes that are
             added after setting this value will also get this <see cref="P:Prism.Modularity.ModuleInfoGroup.Ref"/>.
            
             The ref value will also be used by the <see cref="T:Prism.Modularity.IModuleManager"/> to determine which  <see cref="T:Prism.Modularity.IModuleTypeLoader"/> to use.
             For example, using an "file://" prefix with a valid URL will cause the FileModuleTypeLoader to be used
             (Only available in the desktop version of CAL).
             </summary>
             <see cref="P:Prism.Modularity.ModuleInfo.Ref"/>
             <value>The ref value that will be used.</value>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.Add(Prism.Modularity.ModuleInfo)">
            <summary>
            Adds an <see cref="T:Prism.Modularity.ModuleInfo"/> moduleInfo to the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </summary>
            <param name="item">The <see cref="T:Prism.Modularity.ModuleInfo"/> to the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.ForwardValues(Prism.Modularity.ModuleInfo)">
            <summary>
            Forwards <see cref="P:Prism.Modularity.ModuleInfoGroup.InitializationMode"/> and <see cref="P:Prism.Modularity.ModuleInfoGroup.Ref"/> properties from this <see cref="T:Prism.Modularity.ModuleInfoGroup"/>
            to <paramref name="moduleInfo"/>.
            </summary>
            <param name="moduleInfo">The module info to forward values to.</param>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="moduleInfo"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.Clear">
            <summary>
            Removes all <see cref="T:Prism.Modularity.ModuleInfo"/>s from the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.Contains(Prism.Modularity.ModuleInfo)">
            <summary>
            Determines whether the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> contains a specific value.
            </summary>
            <param name="item">The object to locate in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.</param>
            <returns>
            true if <paramref name="item"/> is found in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>; otherwise, false.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.CopyTo(Prism.Modularity.ModuleInfo[],System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from <see cref="T:Prism.Modularity.ModuleInfoGroup"/>. The <see cref="T:System.Array"/> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array"/> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="array"/> is null.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="arrayIndex"/> is less than 0.
            </exception>
            <exception cref="T:System.ArgumentException">
            	<paramref name="array"/> is multidimensional.
            -or-
            <paramref name="arrayIndex"/> is equal to or greater than the length of <paramref name="array"/>.
            -or-
            The number of elements in the source <see cref="T:Prism.Modularity.ModuleInfoGroup"/> is greater than the available space from <paramref name="arrayIndex"/> to the end of the destination <paramref name="array"/>.
            </exception>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </summary>
            <value></value>
            <returns>
            The number of elements contained in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </returns>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> is read-only.
            </summary>
            <value></value>
            <returns>false, because the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> is not Read-Only.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.Remove(Prism.Modularity.ModuleInfo)">
            <summary>
            Removes the first occurrence of a specific object from the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </summary>
            <param name="item">The object to remove from the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.</param>
            <returns>
            true if <paramref name="item"/> was successfully removed from the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>; otherwise, false. This method also returns false if <paramref name="item"/> is not found in the original <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.System#Collections#IList#Add(System.Object)">
            <summary>
            Adds an item to the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </summary>
            <param name="value">
            The <see cref="T:System.Object"/> to add to the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            Must be of type <see cref="T:Prism.Modularity.ModuleInfo"/>
            </param>
            <returns>
            The position into which the new element was inserted.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.System#Collections#IList#Contains(System.Object)">
            <summary>
            Determines whether the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> contains a specific value.
            </summary>
            <param name="value">
            The <see cref="T:System.Object"/> to locate in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            Must be of type <see cref="T:Prism.Modularity.ModuleInfo"/>
            </param>
            <returns>
            true if the <see cref="T:System.Object"/> is found in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>; otherwise, false.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.IndexOf(System.Object)">
            <summary>
            Determines the index of a specific item in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </summary>
            <param name="value">
            The <see cref="T:System.Object"/> to locate in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            Must be of type <see cref="T:Prism.Modularity.ModuleInfo"/>
            </param>
            <returns>
            The index of <paramref name="value"/> if found in the list; otherwise, -1.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.Insert(System.Int32,System.Object)">
            <summary>
            Inserts an item to the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="value"/> should be inserted.</param>
            <param name="value">
            The <see cref="T:System.Object"/> to insert into the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            Must be of type <see cref="T:Prism.Modularity.ModuleInfo"/>
            </param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is not a valid index in the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </exception>
            <exception cref="T:System.ArgumentNullException">
            If <paramref name="value"/> is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            If <paramref name="value"/> is not of type <see cref="T:Prism.Modularity.ModuleInfo"/>
            </exception>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.IsFixedSize">
            <summary>
            Gets a value indicating whether the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> has a fixed size.
            </summary>
            <returns>false, because the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> does not have a fixed length.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.System#Collections#IList#Remove(System.Object)">
            <summary>
            Removes the first occurrence of a specific object from the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            </summary>
            <param name="value">
            The <see cref="T:System.Object"/> to remove from the <see cref="T:Prism.Modularity.ModuleInfoGroup"/>.
            Must be of type <see cref="T:Prism.Modularity.ModuleInfo"/>
            </param>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.RemoveAt(System.Int32)">
            <summary>
            Removes the <see cref="T:System.Collections.Generic.IList`1"/> item at the specified index.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1"/>.
            </exception>
            <exception cref="T:System.NotSupportedException">
            The <see cref="T:System.Collections.Generic.IList`1"/> is read-only.
            </exception>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.System#Collections#IList#Item(System.Int32)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> at the specified index.
            </summary>
            <value></value>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.ICollection"/> to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection"/>. The <see cref="T:System.Array"/> must have zero-based indexing.</param>
            <param name="index">The zero-based index in <paramref name="array"/> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="array"/> is null.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is less than zero.
            </exception>
            <exception cref="T:System.ArgumentException">
            	<paramref name="array"/> is multidimensional.
            -or-
            <paramref name="index"/> is equal to or greater than the length of <paramref name="array"/>.
            -or-
            The number of elements in the source <see cref="T:System.Collections.ICollection"/> is greater than the available space from <paramref name="index"/> to the end of the destination <paramref name="array"/>.
            </exception>
            <exception cref="T:System.ArgumentException">
            The type of the source <see cref="T:System.Collections.ICollection"/> cannot be cast automatically to the type of the destination <paramref name="array"/>.
            </exception>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.IsSynchronized">
            <summary>
            Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection"/> is synchronized (thread safe).
            </summary>
            <value></value>
            <returns>true if access to the <see cref="T:System.Collections.ICollection"/> is synchronized (thread safe); otherwise, false.
            </returns>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.SyncRoot">
            <summary>
            Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
            <returns>
            An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.IndexOf(Prism.Modularity.ModuleInfo)">
            <summary>
            Determines the index of a specific item in the <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.IList`1"/>.</param>
            <returns>
            The index of <paramref name="item"/> if found in the list; otherwise, -1.
            </returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroup.Insert(System.Int32,Prism.Modularity.ModuleInfo)">
            <summary>
            Inserts an item to the <see cref="T:System.Collections.Generic.IList`1"/> at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="item"/> should be inserted.</param>
            <param name="item">The object to insert into the <see cref="T:System.Collections.Generic.IList`1"/>.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1"/>.
            </exception>
        </member>
        <member name="P:Prism.Modularity.ModuleInfoGroup.Item(System.Int32)">
            <summary>
            Gets or sets the <see cref="T:Prism.Modularity.ModuleInfo"/> at the specified index.
            </summary>
            <value>The <see cref="T:Prism.Modularity.ModuleInfo"/> at the specified index </value>
        </member>
        <member name="T:Prism.Modularity.ModuleInfoGroupExtensions">
            <summary>
            Defines extension methods for the <see cref="T:Prism.Modularity.ModuleInfoGroup"/> class.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroupExtensions.AddModule(Prism.Modularity.ModuleInfoGroup,System.String,System.Type,System.String[])">
            <summary>
            Adds a new module that is statically referenced to the specified module info group.
            </summary>
            <param name="moduleInfoGroup">The group where to add the module info in.</param>
            <param name="moduleName">The name for the module.</param>
            <param name="moduleType">The type for the module. This type should be a descendant of <see cref="T:Prism.Modularity.IModule"/>.</param>
            <param name="dependsOn">The names for the modules that this module depends on.</param>
            <returns>Returns the instance of the passed in module info group, to provide a fluid interface.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInfoGroupExtensions.AddModule(Prism.Modularity.ModuleInfoGroup,System.Type,System.String[])">
            <summary>
            Adds a new module that is statically referenced to the specified module info group.
            </summary>
            <param name="moduleInfoGroup">The group where to add the module info in.</param>
            <param name="moduleType">The type for the module. This type should be a descendant of <see cref="T:Prism.Modularity.IModule"/>.</param>
            <param name="dependsOn">The names for the modules that this module depends on.</param>
            <returns>Returns the instance of the passed in module info group, to provide a fluid interface.</returns>
            <remarks>The name of the module will be the type name.</remarks>
        </member>
        <member name="T:Prism.Modularity.ModuleInitializeException">
            <summary>
            Exception thrown by <see cref="T:Prism.Modularity.IModuleInitializer"/> implementations whenever 
            a module fails to load.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializeException.#ctor">
            <summary>
            Initializes a new instance.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleInitializeException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleInitializeException"/> class.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializeException.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes the exception with a particular module and error message.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="moduleAssembly">The assembly where the module is located.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializeException.#ctor(System.String,System.String,System.String,System.Exception)">
            <summary>
            Initializes the exception with a particular module, error message and inner exception 
            that happened.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="moduleAssembly">The assembly where the module is located.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, 
            or a <see langword="null"/> reference if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializeException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes the exception with a particular module, error message and inner exception that happened.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, 
            or a <see langword="null"/> reference if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializeException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Modularity.ModuleInitializer">
            <summary>
            Implements the <see cref="T:Prism.Modularity.IModuleInitializer"/> interface. Handles loading of a module based on a type.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializer.#ctor(Microsoft.Practices.ServiceLocation.IServiceLocator,Prism.Logging.ILoggerFacade)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Modularity.ModuleInitializer"/>.
            </summary>
            <param name="serviceLocator">The container that will be used to resolve the modules by specifying its type.</param>
            <param name="loggerFacade">The logger to use.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializer.Initialize(Prism.Modularity.ModuleInfo)">
            <summary>
            Initializes the specified module.
            </summary>
            <param name="moduleInfo">The module to initialize</param>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializer.HandleModuleInitializationError(Prism.Modularity.ModuleInfo,System.String,System.Exception)">
            <summary>
            Handles any exception occurred in the module Initialization process,
            logs the error using the <see cref="T:Prism.Logging.ILoggerFacade"/> and throws a <see cref="T:Prism.Modularity.ModuleInitializeException"/>.
            This method can be overridden to provide a different behavior.
            </summary>
            <param name="moduleInfo">The module metadata where the error happenened.</param>
            <param name="assemblyName">The assembly name.</param>
            <param name="exception">The exception thrown that is the cause of the current error.</param>
            <exception cref="T:Prism.Modularity.ModuleInitializeException"></exception>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializer.CreateModule(Prism.Modularity.ModuleInfo)">
            <summary>
            Uses the container to resolve a new <see cref="T:Prism.Modularity.IModule"/> by specifying its <see cref="T:System.Type"/>.
            </summary>
            <param name="moduleInfo">The module to create.</param>
            <returns>A new instance of the module specified by <paramref name="moduleInfo"/>.</returns>
        </member>
        <member name="M:Prism.Modularity.ModuleInitializer.CreateModule(System.String)">
            <summary>
            Uses the container to resolve a new <see cref="T:Prism.Modularity.IModule"/> by specifying its <see cref="T:System.Type"/>.
            </summary>
            <param name="typeName">The type name to resolve. This type must implement <see cref="T:Prism.Modularity.IModule"/>.</param>
            <returns>A new instance of <paramref name="typeName"/>.</returns>
        </member>
        <member name="T:Prism.Modularity.ModuleManager">
            <summary>
            Component responsible for coordinating the modules' type loading and module initialization process.
            </summary>
            <summary>
            Component responsible for coordinating the modules' type loading and module initialization process. 
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleManager.#ctor(Prism.Modularity.IModuleInitializer,Prism.Modularity.IModuleCatalog,Prism.Logging.ILoggerFacade)">
            <summary>
            Initializes an instance of the <see cref="T:Prism.Modularity.ModuleManager"/> class.
            </summary>
            <param name="moduleInitializer">Service used for initialization of modules.</param>
            <param name="moduleCatalog">Catalog that enumerates the modules to be loaded and initialized.</param>
            <param name="loggerFacade">Logger used during the load and initialization of modules.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleManager.ModuleCatalog">
            <summary>
            The module catalog specified in the constructor.
            </summary>
        </member>
        <member name="E:Prism.Modularity.ModuleManager.ModuleDownloadProgressChanged">
            <summary>
            Raised repeatedly to provide progress as modules are loaded in the background.
            </summary>
        </member>
        <member name="E:Prism.Modularity.ModuleManager.LoadModuleCompleted">
            <summary>
            Raised when a module is loaded or fails to load.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleManager.Run">
            <summary>
            Initializes the modules marked as <see cref="F:Prism.Modularity.InitializationMode.WhenAvailable"/> on the <see cref="P:Prism.Modularity.ModuleManager.ModuleCatalog"/>.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleManager.LoadModule(System.String)">
            <summary>
            Loads and initializes the module on the <see cref="P:Prism.Modularity.ModuleManager.ModuleCatalog"/> with the name <paramref name="moduleName"/>.
            </summary>
            <param name="moduleName">Name of the module requested for initialization.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleManager.ModuleNeedsRetrieval(Prism.Modularity.ModuleInfo)">
            <summary>
            Checks if the module needs to be retrieved before it's initialized.
            </summary>
            <param name="moduleInfo">Module that is being checked if needs retrieval.</param>
            <returns></returns>
        </member>
        <member name="M:Prism.Modularity.ModuleManager.LoadModulesThatAreReadyForLoad">
            <summary>
            Loads the modules that are not intialized and have their dependencies loaded.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleManager.HandleModuleTypeLoadingError(Prism.Modularity.ModuleInfo,System.Exception)">
            <summary>
            Handles any exception occurred in the module typeloading process,
            logs the error using the <see cref="T:Prism.Logging.ILoggerFacade"/> and throws a <see cref="T:Prism.Modularity.ModuleTypeLoadingException"/>.
            This method can be overridden to provide a different behavior.
            </summary>
            <param name="moduleInfo">The module metadata where the error happenened.</param>
            <param name="exception">The exception thrown that is the cause of the current error.</param>
            <exception cref="T:Prism.Modularity.ModuleTypeLoadingException"></exception>
        </member>
        <member name="M:Prism.Modularity.ModuleManager.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
            <remarks>Calls <see cref="M:Prism.Modularity.ModuleManager.Dispose(System.Boolean)"/></remarks>.
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Prism.Modularity.ModuleManager.Dispose(System.Boolean)">
            <summary>
            Disposes the associated <see cref="T:Prism.Modularity.IModuleTypeLoader"/>s.
            </summary>
            <param name="disposing">When <see langword="true"/>, it is being called from the Dispose method.</param>
        </member>
        <member name="P:Prism.Modularity.ModuleManager.ModuleTypeLoaders">
            <summary>
            Returns the list of registered <see cref="T:Prism.Modularity.IModuleTypeLoader"/> instances that will be 
            used to load the types of modules. 
            </summary>
            <value>The module type loaders.</value>
        </member>
        <member name="T:Prism.Modularity.ModuleNotFoundException">
            <summary>
            Exception thrown when a requested <see cref="F:Prism.Modularity.InitializationMode.OnDemand"/> <see cref="T:Prism.Modularity.IModule"/> was not found.
            </summary>
            <summary>
            Exception thrown when a requested <see cref="T:Prism.Modularity.ModuleInfo"/> is not found.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleNotFoundException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleNotFoundException" /> class.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleNotFoundException" /> class with a specified error message.
            </summary>
            <param name="message">
            The message that describes the error. 
            </param>
        </member>
        <member name="M:Prism.Modularity.ModuleNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleNotFoundException" /> class with a specified error message.
            </summary>
            <param name="message">
            The message that describes the error. 
            </param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:Prism.Modularity.ModuleNotFoundException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleNotFoundException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleNotFoundException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleNotFoundException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleNotFoundException"/> class
            with the serialization data.
            </summary>
            <param name="info">Holds the serialized object data about the exception being thrown.</param>
            <param name="context">Contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Modularity.ModulesConfigurationSection">
            <summary>
            A <see cref="T:System.Configuration.ConfigurationSection"/> for module configuration.
            </summary>
        </member>
        <member name="P:Prism.Modularity.ModulesConfigurationSection.Modules">
            <summary>
            Gets or sets the collection of modules configuration.
            </summary>
            <value>A <see cref="T:Prism.Modularity.ModuleConfigurationElementCollection"/> of <see cref="T:Prism.Modularity.ModuleConfigurationElement"/>.</value>
        </member>
        <member name="T:Prism.Modularity.ModuleState">
            <summary>
            Defines the states a <see cref="T:Prism.Modularity.ModuleInfo"/> can be in, with regards to the module loading and initialization process. 
            </summary>
        </member>
        <member name="F:Prism.Modularity.ModuleState.NotStarted">
            <summary>
            Initial state for <see cref="T:Prism.Modularity.ModuleInfo"/>s. The <see cref="T:Prism.Modularity.ModuleInfo"/> is defined, 
            but it has not been loaded, retrieved or initialized yet. 
            </summary>
        </member>
        <member name="F:Prism.Modularity.ModuleState.LoadingTypes">
            <summary>
            The assembly that contains the type of the module is currently being loaded by an instance of a
            <see cref="T:Prism.Modularity.IModuleTypeLoader"/>. 
            </summary>
        </member>
        <member name="F:Prism.Modularity.ModuleState.ReadyForInitialization">
            <summary>
            The assembly that holds the Module is present. This means the type of the <see cref="T:Prism.Modularity.IModule"/> can be instantiated and initialized. 
            </summary>
        </member>
        <member name="F:Prism.Modularity.ModuleState.Initializing">
            <summary>
            The module is currently Initializing, by the <see cref="T:Prism.Modularity.IModuleInitializer"/>
            </summary>
        </member>
        <member name="F:Prism.Modularity.ModuleState.Initialized">
            <summary>
            The module is initialized and ready to be used. 
            </summary>
        </member>
        <member name="T:Prism.Modularity.ModuleTypeLoaderNotFoundException">
            <summary>
            Exception that's thrown when there is no <see cref="T:Prism.Modularity.IModuleTypeLoader"/> registered in 
            <see cref="P:Prism.Modularity.ModuleManager.ModuleTypeLoaders"/> that can handle this particular type of module. 
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoaderNotFoundException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleTypeLoaderNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoaderNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleTypeLoaderNotFoundException" /> class with a specified error message.
            </summary>
            <param name="message">
            The message that describes the error. 
            </param>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoaderNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Modularity.ModuleTypeLoaderNotFoundException" /> class with a specified error message.
            </summary>
            <param name="message">
            The message that describes the error. 
            </param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoaderNotFoundException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes the exception with a particular module, error message and inner exception that happened.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, 
            or a <see langword="null"/> reference if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoaderNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Modularity.ModuleTypeLoadingException">
            <summary>
            Exception thrown by <see cref="T:Prism.Modularity.IModuleManager"/> implementations whenever 
            a module fails to retrieve.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoadingException.#ctor">
            <summary>
            Initializes a new instance.
            </summary>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoadingException.#ctor(System.String)">
            <summary>
            Initializes a new instance with a specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoadingException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance with a specified error message 
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="exception">The exception that is the cause of the current exception, 
            or a <see langword="null"/> reference if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoadingException.#ctor(System.String,System.String)">
            <summary>
            Initializes the exception with a particular module and error message.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoadingException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes the exception with a particular module, error message and inner exception that happened.
            </summary>
            <param name="moduleName">The name of the module.</param>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, 
            or a <see langword="null"/> reference if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Modularity.ModuleTypeLoadingException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Mvvm.ViewModelLocator">
            <summary>
            This class defines the attached property and related change handler that calls the ViewModelLocator in Prism.Mvvm.
            </summary>
        </member>
        <member name="F:Prism.Mvvm.ViewModelLocator.AutoWireViewModelProperty">
            <summary>
            The AutoWireViewModel attached property.
            </summary>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocator.Bind(System.Object,System.Object)">
            <summary>
            Sets the DataContext of a View
            </summary>
            <param name="view">The View to set the DataContext on</param>
            <param name="viewModel">The object to use as the DataContext for the View</param>
        </member>
        <member name="T:Prism.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.AdapterInvalidTypeException">
            <summary>
              Looks up a localized string similar to The object must be of type &apos;{0}&apos; in order to use the current region adapter..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.CannotChangeRegionNameException">
            <summary>
              Looks up a localized string similar to Cannot change the region name once is set. The current region name is &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.CannotCreateNavigationTarget">
            <summary>
              Looks up a localized string similar to Cannot create navigation target &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.CanOnlyAddTypesThatInheritIFromRegionBehavior">
            <summary>
              Looks up a localized string similar to Type &apos;{0}&apos; does not implement from IRegionBehavior..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ConfigurationStoreCannotBeNull">
            <summary>
              Looks up a localized string similar to The ConfigurationStore cannot contain a null value. .
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ContentControlHasContentException">
            <summary>
              Looks up a localized string similar to ContentControl&apos;s Content property is not empty. 
               This control is being associated with a region, but the control is already bound to something else. 
               If you did not explicitly set the control&apos;s Content property, 
               this exception may be caused by a change in the value of the inherited RegionManager attached property..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.CyclicDependencyFound">
            <summary>
              Looks up a localized string similar to At least one cyclic dependency has been found in the module catalog. Cycles in the module dependencies must be avoided..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DeactiveNotPossibleException">
            <summary>
              Looks up a localized string similar to Deactivation is not possible in this type of region..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DefaultTextLoggerPattern">
            <summary>
              Looks up a localized string similar to {1}: {2}. Priority: {3}. Timestamp:{0:u}..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DelegateCommandDelegatesCannotBeNull">
            <summary>
              Looks up a localized string similar to Neither the executeMethod nor the canExecuteMethod delegates can be null..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DelegateCommandInvalidGenericPayloadType">
            <summary>
              Looks up a localized string similar to T for DelegateCommand&lt;T&gt; is not an object nor Nullable..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DependencyForUnknownModule">
            <summary>
              Looks up a localized string similar to Cannot add dependency for unknown module {0}.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DependencyOnMissingModule">
            <summary>
              Looks up a localized string similar to A module declared a dependency on another module which is not declared to be loaded. Missing module(s): {0}.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DirectoryNotFound">
            <summary>
              Looks up a localized string similar to Directory {0} was not found..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DuplicatedModule">
            <summary>
              Looks up a localized string similar to A duplicated module with name {0} has been found by the loader..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DuplicatedModuleGroup">
            <summary>
              Looks up a localized string similar to A duplicated module group with name {0} has been found by the loader..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.FailedToGetType">
            <summary>
              Looks up a localized string similar to Unable to retrieve the module type {0} from the loaded assemblies.  You may need to specify a more fully-qualified type name..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.FailedToLoadModule">
            <summary>
              Looks up a localized string similar to An exception occurred while initializing module &apos;{0}&apos;. 
               - The exception message was: {2}
               - The Assembly that the module was trying to be loaded from was:{1}
               Check the InnerException property of the exception for more information. If the exception occurred while creating an object in a DI container, you can exception.GetRootException() to help locate the root cause of the problem. 
             .
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.FailedToLoadModuleNoAssemblyInfo">
            <summary>
              Looks up a localized string similar to An exception occurred while initializing module &apos;{0}&apos;. 
               - The exception message was: {1}
               Check the InnerException property of the exception for more information. If the exception occurred 
               while creating an object in a DI container, you can exception.GetRootException() to help locate the 
               root cause of the problem. .
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.FailedToRetrieveModule">
             <summary>
               Looks up a localized string similar to Failed to load type for module {0}. 
            Error was: {1}..
             </summary>
        </member>
        <member name="P:Prism.Properties.Resources.HostControlCannotBeNull">
            <summary>
              Looks up a localized string similar to HostControl cannot have null value when behavior attaches. .
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.HostControlCannotBeSetAfterAttach">
            <summary>
              Looks up a localized string similar to The HostControl property cannot be set after Attach method has been called..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.HostControlMustBeATabControl">
            <summary>
              Looks up a localized string similar to HostControl type must be a TabControl..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.IEnumeratorObsolete">
            <summary>
              Looks up a localized string similar to The IModuleEnumerator interface is no longer used and has been replaced by ModuleCatalog..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.InvalidArgumentAssemblyUri">
            <summary>
              Looks up a localized string similar to The argument must be a valid absolute Uri to an assembly file..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.InvalidDelegateRerefenceTypeException">
            <summary>
              Looks up a localized string similar to The Target of the IDelegateReference should be of type {0}..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ItemsControlHasItemsSourceException">
            <summary>
              Looks up a localized string similar to ItemsControl&apos;s ItemsSource property is not empty. 
               This control is being associated with a region, but the control is already bound to something else. 
               If you did not explicitly set the control&apos;s ItemSource property, 
               this exception may be caused by a change in the value of the inherited RegionManager attached property..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.MappingExistsException">
            <summary>
              Looks up a localized string similar to Mapping with the given type is already registered: {0}..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ModuleDependenciesNotMetInGroup">
            <summary>
              Looks up a localized string similar to Module {0} depends on other modules that don&apos;t belong to the same group..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ModuleNotFound">
            <summary>
              Looks up a localized string similar to Module {0} was not found in the catalog..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ModulePathCannotBeNullOrEmpty">
            <summary>
              Looks up a localized string similar to The ModulePath cannot contain a null value or be empty.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ModuleTypeNotFound">
            <summary>
              Looks up a localized string similar to Failed to load type &apos;{0}&apos; from assembly &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.NavigationInProgress">
            <summary>
              Looks up a localized string similar to Navigation is already in progress on region with name &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.NavigationServiceHasNoRegion">
            <summary>
              Looks up a localized string similar to Navigation cannot proceed until a region is set for the RegionNavigationService..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.NoRegionAdapterException">
            <summary>
              Looks up a localized string similar to The IRegionAdapter for the type {0} is not registered in the region adapter mappings. You can register an IRegionAdapter for this control by overriding the ConfigureRegionAdapterMappings method in the bootstrapper..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.NoRetrieverCanRetrieveModule">
            <summary>
              Looks up a localized string similar to There is currently no moduleTypeLoader in the ModuleManager that can retrieve the specified module..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.OnViewRegisteredException">
            <summary>
              Looks up a localized string similar to An exception has occurred while trying to add a view to region &apos;{0}&apos;. 
               - The most likely causing exception was was: &apos;{1}&apos;.
               But also check the InnerExceptions for more detail or call .GetRootException(). .
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.PropertySupport_ExpressionNotProperty_Exception">
            <summary>
              Looks up a localized string similar to The member access expression does not access a property..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.PropertySupport_NotMemberAccessExpression_Exception">
            <summary>
              Looks up a localized string similar to The expression is not a member access expression..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.PropertySupport_StaticExpression_Exception">
            <summary>
              Looks up a localized string similar to The referenced property is a static property..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionBehaviorAttachCannotBeCallWithNullRegion">
            <summary>
              Looks up a localized string similar to The Attach method cannot be called when Region property is null..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionBehaviorRegionCannotBeSetAfterAttach">
            <summary>
              Looks up a localized string similar to The Region property cannot be set after Attach method has been called..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionCreationException">
            <summary>
              Looks up a localized string similar to An exception occurred while creating a region with name &apos;{0}&apos;. The exception was: {1}. .
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionManagerWithDifferentNameException">
            <summary>
              Looks up a localized string similar to The region being added already has a name of &apos;{0}&apos; and cannot be added to the region manager with a different name (&apos;{1}&apos;)..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionNameCannotBeEmptyException">
            <summary>
              Looks up a localized string similar to The region name cannot be null or empty..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionNameExistsException">
            <summary>
              Looks up a localized string similar to Region with the given name is already registered: {0}.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionNotFound">
            <summary>
              Looks up a localized string similar to This RegionManager does not contain a Region with the name &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionNotInRegionManagerException">
            <summary>
              Looks up a localized string similar to The region manager does not contain the {0} region..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionViewExistsException">
            <summary>
              Looks up a localized string similar to View already exists in region..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.RegionViewNameExistsException">
            <summary>
              Looks up a localized string similar to View with name &apos;{0}&apos; already exists in the region..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.StartupModuleDependsOnAnOnDemandModule">
            <summary>
              Looks up a localized string similar to Module {0} is marked for automatic initialization when the application starts, but it depends on modules that are marked as OnDemand initialization. To fix this error, mark the dependency modules for InitializationMode=WhenAvailable, or remove this validation by extending the ModuleCatalog class..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.StringCannotBeNullOrEmpty">
            <summary>
              Looks up a localized string similar to The provided String argument {0} must not be null or empty..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.StringCannotBeNullOrEmpty1">
            <summary>
              Looks up a localized string similar to The provided String argument {0} must not be null or empty..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.TypeWithKeyNotRegistered">
            <summary>
              Looks up a localized string similar to No BehaviorType with key &apos;{0}&apos; was registered..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.UpdateRegionException">
            <summary>
              Looks up a localized string similar to An exception occurred while trying to create region objects. 
               - The most likely causing exception was: &apos;{0}&apos;.
               But also check the InnerExceptions for more detail or call .GetRootException(). .
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ValueMustBeOfTypeModuleInfo">
            <summary>
              Looks up a localized string similar to The value must be of type ModuleInfo..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ValueNotFound">
            <summary>
              Looks up a localized string similar to {0} not found..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ViewNotInRegionException">
            <summary>
              Looks up a localized string similar to The region does not contain the specified view..
            </summary>
        </member>
        <member name="T:Prism.Regions.AllActiveRegion">
            <summary>
            Region that keeps all the views in it as active. Deactivation of views is not allowed.
            </summary>
        </member>
        <member name="P:Prism.Regions.AllActiveRegion.ActiveViews">
            <summary>
            Gets a readonly view of the collection of all the active views in the region. These are all the added views.
            </summary>
            <value>An <see cref="T:Prism.Regions.IViewsCollection"/> of all the active views.</value>
        </member>
        <member name="M:Prism.Regions.AllActiveRegion.Deactivate(System.Object)">
            <summary>
            Deactive is not valid in this Region. This method will always throw <see cref="T:System.InvalidOperationException"/>.
            </summary>
            <param name="view">The view to deactivate.</param>
            <exception cref="T:System.InvalidOperationException">Every time this method is called.</exception>
        </member>
        <member name="T:Prism.Regions.Behaviors.AutoPopulateRegionBehavior">
            <summary>
            Populates the target region with the views registered to it in the <see cref="T:Prism.Regions.IRegionViewRegistry"/>.
            </summary>
        </member>
        <member name="F:Prism.Regions.Behaviors.AutoPopulateRegionBehavior.BehaviorKey">
            <summary>
            The key of this behavior.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.AutoPopulateRegionBehavior.#ctor(Prism.Regions.IRegionViewRegistry)">
            <summary>
            Creates a new instance of the AutoPopulateRegionBehavior
            associated with the <see cref="T:Prism.Regions.IRegionViewRegistry"/> received.
            </summary>
            <param name="regionViewRegistry"><see cref="T:Prism.Regions.IRegionViewRegistry"/> that the behavior will monitor for views to populate the region.</param>
        </member>
        <member name="M:Prism.Regions.Behaviors.AutoPopulateRegionBehavior.OnAttach">
            <summary>
            Attaches the AutoPopulateRegionBehavior to the Region.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.AutoPopulateRegionBehavior.CreateViewsToAutoPopulate">
            <summary>
            Returns a collection of views that will be added to the
            View collection.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Prism.Regions.Behaviors.AutoPopulateRegionBehavior.AddViewIntoRegion(System.Object)">
            <summary>
            Adds a view into the views collection of this region.
            </summary>
            <param name="viewToAdd"></param>
        </member>
        <member name="M:Prism.Regions.Behaviors.AutoPopulateRegionBehavior.OnViewRegistered(System.Object,Prism.Regions.ViewRegisteredEventArgs)">
            <summary>
            Handler of the event that fires when a new viewtype is registered to the registry.
            </summary>
            <remarks>Although this is a public method to support Weak Delegates in Silverlight, it should not be called by the user.</remarks>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Prism.Regions.Behaviors.BindRegionContextToDependencyObjectBehavior">
            <summary>
            Defines a behavior that forwards the <see cref="F:Prism.Regions.RegionManager.RegionContextProperty"/> 
            to the views in the region.
            </summary>
        </member>
        <member name="F:Prism.Regions.Behaviors.BindRegionContextToDependencyObjectBehavior.BehaviorKey">
            <summary>
            The key of this behavior.
            </summary>
        </member>
        <member name="P:Prism.Regions.Behaviors.BindRegionContextToDependencyObjectBehavior.Region">
            <summary>
            Behavior's attached region.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.BindRegionContextToDependencyObjectBehavior.Attach">
            <summary>
            Attaches the behavior to the specified region.
            </summary>
        </member>
        <member name="T:Prism.Regions.Behaviors.ClearChildViewsRegionBehavior">
            <summary>
            Behavior that removes the RegionManager attached property of all the views in a region once the RegionManager property of a region becomes null.
            This is useful when removing views with nested regions, to ensure these nested regions get removed from the RegionManager as well.
            <remarks>
            This behavior does not apply by default.
            In order to activate it, the ClearChildViews attached property must be set to True in the view containing the affected child regions.
            </remarks>
            </summary>
        </member>
        <member name="F:Prism.Regions.Behaviors.ClearChildViewsRegionBehavior.BehaviorKey">
            <summary>
            The behavior key.
            </summary>
        </member>
        <member name="F:Prism.Regions.Behaviors.ClearChildViewsRegionBehavior.ClearChildViewsProperty">
            <summary>
            This attached property can be defined on a view to indicate that regions defined in it must be removed from the region manager when the parent view gets removed from a region.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.ClearChildViewsRegionBehavior.GetClearChildViews(System.Windows.DependencyObject)">
            <summary>
            Gets the ClearChildViews attached property from a DependencyObject.
            </summary>
            <param name="target">The object from which to get the value.</param>
            <returns>The value of the ClearChildViews attached property in the target specified.</returns>
        </member>
        <member name="M:Prism.Regions.Behaviors.ClearChildViewsRegionBehavior.SetClearChildViews(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets the ClearChildViews attached property in a DependencyObject.
            </summary>
            <param name="target">The object in which to set the value.</param>
            <param name="value">The value of to set in the target object's ClearChildViews attached property.</param>
        </member>
        <member name="M:Prism.Regions.Behaviors.ClearChildViewsRegionBehavior.OnAttach">
            <summary>
            Subscribes to the <see cref="T:Prism.Regions.Region"/>'s PropertyChanged method to monitor its RegionManager property.
            </summary>
        </member>
        <member name="T:Prism.Regions.Behaviors.DelayedRegionCreationBehavior">
            <summary>
            Behavior that creates a new <see cref="T:Prism.Regions.IRegion"/>, when the control that will host the <see cref="T:Prism.Regions.IRegion"/> (see <see cref="P:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.TargetElement"/>)
            is added to the VisualTree. This behavior will use the <see cref="T:Prism.Regions.RegionAdapterMappings"/> class to find the right type of adapter to create
            the region. After the region is created, this behavior will detach.
            </summary>
            <remarks>
            Attached property value inheritance is not available in Silverlight, so the current approach walks up the visual tree when requesting a region from a region manager.
            The <see cref="T:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior"/> is now responsible for walking up the Tree.
            </remarks>
        </member>
        <member name="M:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.#ctor(Prism.Regions.RegionAdapterMappings)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.Behaviors.DelayedRegionCreationBehavior"/> class.
            </summary>
            <param name="regionAdapterMappings">
            The region adapter mappings, that are used to find the correct adapter for
            a given controltype. The controltype is determined by the <see name="TargetElement"/> value.
            </param>
        </member>
        <member name="P:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.RegionManagerAccessor">
            <summary>
            Sets a class that interfaces between the <see cref="T:Prism.Regions.RegionManager"/> 's static properties/events and this behavior,
            so this behavior can be tested in isolation.
            </summary>
            <value>The region manager accessor.</value>
        </member>
        <member name="P:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.TargetElement">
            <summary>
            The element that will host the Region.
            </summary>
            <value>The target element.</value>
        </member>
        <member name="M:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.Attach">
            <summary>
            Start monitoring the <see cref="T:Prism.Regions.RegionManager"/> and the <see cref="P:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.TargetElement"/> to detect when the <see cref="P:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.TargetElement"/> becomes
            part of the Visual Tree. When that happens, the Region will be created and the behavior will <see cref="M:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.Detach"/>.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.Detach">
            <summary>
            Stop monitoring the <see cref="T:Prism.Regions.RegionManager"/> and the  <see cref="P:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.TargetElement"/>, so that this behavior can be garbage collected.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.OnUpdatingRegions(System.Object,System.EventArgs)">
            <summary>
            Called when the <see cref="T:Prism.Regions.RegionManager"/> is updating it's <see cref="P:Prism.Regions.RegionManager.Regions"/> collection.
            </summary>
            <remarks>
            This method has to be public, because it has to be callable using weak references in silverlight and other partial trust environments.
            </remarks>
            <param name="sender">The <see cref="T:Prism.Regions.RegionManager"/>. </param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Prism.Regions.Behaviors.DelayedRegionCreationBehavior.CreateRegion(System.Windows.DependencyObject,System.String)">
            <summary>
            Method that will create the region, by calling the right <see cref="T:Prism.Regions.IRegionAdapter"/>.
            </summary>
            <param name="targetElement">The target element that will host the <see cref="T:Prism.Regions.IRegion"/>.</param>
            <param name="regionName">Name of the region.</param>
            <returns>The created <see cref="T:Prism.Regions.IRegion"/></returns>
        </member>
        <member name="T:Prism.Regions.Behaviors.IHostAwareRegionBehavior">
            <summary>
            Defines a <see cref="T:Prism.Regions.IRegionBehavior"/> that not allows extensible behaviors on regions which also interact
            with the target element that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            </summary>
        </member>
        <member name="P:Prism.Regions.Behaviors.IHostAwareRegionBehavior.HostControl">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DependencyObject"/> that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            </summary>
            <value>A <see cref="T:System.Windows.DependencyObject"/> that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            This is usually a <see cref="T:System.Windows.FrameworkElement"/> that is part of the tree.</value>
        </member>
        <member name="T:Prism.Regions.Behaviors.RegionActiveAwareBehavior">
            <summary>
            Behavior that monitors a <see cref="T:Prism.Regions.IRegion"/> object and 
            changes the value for the <see cref="P:Prism.IActiveAware.IsActive"/> property when
            an object that implements <see cref="T:Prism.IActiveAware"/> gets added or removed 
            from the collection.
            </summary>
            <remarks>
            This class can also sync the active state for any scoped regions directly on the view based on the <see cref="T:Prism.Regions.SyncActiveStateAttribute"/>.
            If you use the <see cref="M:Prism.Regions.Region.Add(System.Object,System.String,System.Boolean)" /> method with the createRegionManagerScope option, the scoped manager will be attached to the view.
            </remarks>
        </member>
        <member name="F:Prism.Regions.Behaviors.RegionActiveAwareBehavior.BehaviorKey">
            <summary>
            Name that identifies the <see cref="T:Prism.Regions.Behaviors.RegionActiveAwareBehavior"/> behavior in a collection of <see cref="T:Prism.Regions.IRegionBehavior"/>.
            </summary>
        </member>
        <member name="P:Prism.Regions.Behaviors.RegionActiveAwareBehavior.Region">
            <summary>
            The region that this behavior is extending
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionActiveAwareBehavior.Attach">
            <summary>
            Attaches the behavior to the specified region
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionActiveAwareBehavior.Detach">
            <summary>
            Detaches the behavior from the <see cref="T:System.Collections.Specialized.INotifyCollectionChanged"/>.
            </summary>
        </member>
        <member name="T:Prism.Regions.Behaviors.RegionCreationException">
            <summary>
            Represents errors that occured during region creation.
            </summary>
            <summary>
            
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionCreationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.Behaviors.RegionCreationException"/>
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionCreationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.Behaviors.RegionCreationException"/> class with a specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionCreationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.Behaviors.RegionCreationException"/> class with a specified error message and a reference 
            to the inner exception that is the cause of this exception. 
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="inner">The exception that is the cause of the current exception, or a null reference 
            (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionCreationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.Behaviors.RegionCreationException"/> class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior">
            <summary>
            Subscribes to a static event from the <see cref="T:Prism.Regions.RegionManager"/> in order to register the target <see cref="T:Prism.Regions.IRegion"/>
            in a <see cref="T:Prism.Regions.IRegionManager"/> when one is available on the host control by walking up the tree and finding
            a control whose <see cref="F:Prism.Regions.RegionManager.RegionManagerProperty"/> property is not <see langword="null"/>.
            </summary>
        </member>
        <member name="F:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior.BehaviorKey">
            <summary>
            The key of this behavior.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior"/>.
            </summary>
        </member>
        <member name="P:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior.RegionManagerAccessor">
            <summary>
            Provides an abstraction on top of the RegionManager static members.
            </summary>
        </member>
        <member name="P:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior.HostControl">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DependencyObject"/> that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            </summary>
            <value>A <see cref="T:System.Windows.DependencyObject"/> that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            This is usually a <see cref="T:System.Windows.FrameworkElement"/> that is part of the tree.</value>
            <exception cref="T:System.InvalidOperationException">When this member is set after the <see cref="M:Prism.Regions.IRegionBehavior.Attach"/> method has being called.</exception>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior.OnAttach">
            <summary>
            When the <see cref="T:Prism.Regions.IRegion"/> has a name assigned, the behavior will start monitoring the ancestor controls in the element tree
            to look for an <see cref="T:Prism.Regions.IRegionManager"/> where to register the region in.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionManagerRegistrationBehavior.OnUpdatingRegions(System.Object,System.EventArgs)">
            <summary>
            This event handler gets called when a RegionManager is requering the instances of a region to be registered if they are not already.
            <remarks>Although this is a public method to support Weak Delegates in Silverlight, it should not be called by the user.</remarks>
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The arguments.</param>
        </member>
        <member name="T:Prism.Regions.Behaviors.RegionMemberLifetimeBehavior">
            <summary>
            The RegionMemberLifetimeBehavior determines if items should be removed from the <see cref="T:Prism.Regions.IRegion"/>
            when they are deactivated.
            </summary>
            <remarks>
            The <see cref="T:Prism.Regions.Behaviors.RegionMemberLifetimeBehavior"/> monitors the <see cref="P:Prism.Regions.IRegion.ActiveViews"/>
            collection to discover items that transition into a deactivated state.  
            <p/>
            The behavior checks the removed items for either the <see cref="T:Prism.Regions.IRegionMemberLifetime"/>
            or the <see cref="T:Prism.Regions.RegionMemberLifetimeAttribute"/> (in that order) to determine if it should be kept 
            alive on removal.
            <p/>
            If the item in the collection is a <see cref="T:System.Windows.FrameworkElement"/>, it will
            also check it's DataContext for <see cref="T:Prism.Regions.IRegionMemberLifetime"/> or the <see cref="T:Prism.Regions.RegionMemberLifetimeAttribute"/>.
            <p/>
            The order of checks are:
            <list type="number">
                <item>Region Item's IRegionMemberLifetime.KeepAlive value.</item>
                <item>Region Item's DataContext's IRegionMemberLifetime.KeepAlive value.</item>
                <item>Region Item's RegionMemberLifetimeAttribute.KeepAlive value.</item>
                <item>Region Item's DataContext's RegionMemberLifetimeAttribute.KeepAlive value.</item>
            </list>
            </remarks>
        </member>
        <member name="F:Prism.Regions.Behaviors.RegionMemberLifetimeBehavior.BehaviorKey">
            <summary>
            The key for this behavior.
            </summary>
        </member>
        <member name="M:Prism.Regions.Behaviors.RegionMemberLifetimeBehavior.OnAttach">
            <summary>
            Override this method to perform the logic after the behavior has been attached.
            </summary>
        </member>
        <member name="T:Prism.Regions.Behaviors.SelectorItemsSourceSyncBehavior">
            <summary>
            Defines the attached behavior that keeps the items of the <see cref="T:System.Windows.Controls.Primitives.Selector"/> host control in synchronization with the <see cref="T:Prism.Regions.IRegion"/>.
            
            This behavior also makes sure that, if you activate a view in a region, the SelectedItem is set. If you set the SelectedItem or SelectedItems (ListBox)
            then this behavior will also call Activate on the selected items. 
            <remarks>
            When calling Activate on a view, you can only select a single active view at a time. By setting the SelectedItems property of a listbox, you can set
            multiple views to active. 
            </remarks>
            </summary>
        </member>
        <member name="F:Prism.Regions.Behaviors.SelectorItemsSourceSyncBehavior.BehaviorKey">
            <summary>
            Name that identifies the SelectorItemsSourceSyncBehavior behavior in a collection of RegionsBehaviors. 
            </summary>
        </member>
        <member name="P:Prism.Regions.Behaviors.SelectorItemsSourceSyncBehavior.HostControl">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DependencyObject"/> that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            </summary>
            <value>
            A <see cref="T:System.Windows.DependencyObject"/> that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            </value>
            <remarks>For this behavior, the host control must always be a <see cref="T:System.Windows.Controls.Primitives.Selector"/> or an inherited class.</remarks>
        </member>
        <member name="M:Prism.Regions.Behaviors.SelectorItemsSourceSyncBehavior.OnAttach">
            <summary>
            Starts to monitor the <see cref="T:Prism.Regions.IRegion"/> to keep it in synch with the items of the <see cref="P:Prism.Regions.Behaviors.SelectorItemsSourceSyncBehavior.HostControl"/>.
            </summary>
        </member>
        <member name="T:Prism.Regions.Behaviors.SyncRegionContextWithHostBehavior">
            <summary>
            Behavior that synchronizes the <see cref="P:Prism.Regions.IRegion.Context"/> property of a <see cref="T:Prism.Regions.IRegion"/> with 
            the control that hosts the Region. It does this by setting the <see cref="F:Prism.Regions.RegionManager.RegionContextProperty"/> 
            Dependency Property on the host control.
            
            This behavior allows the usage of two way databinding of the RegionContext from XAML. 
            </summary>
        </member>
        <member name="F:Prism.Regions.Behaviors.SyncRegionContextWithHostBehavior.BehaviorKey">
            <summary>
            Name that identifies the SyncRegionContextWithHostBehavior behavior in a collection of RegionsBehaviors. 
            </summary>
        </member>
        <member name="P:Prism.Regions.Behaviors.SyncRegionContextWithHostBehavior.HostControl">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DependencyObject"/> that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            </summary>
            <value>
            A <see cref="T:System.Windows.DependencyObject"/> that the <see cref="T:Prism.Regions.IRegion"/> is attached to.
            This is usually a <see cref="T:System.Windows.FrameworkElement"/> that is part of the tree.
            </value>
        </member>
        <member name="M:Prism.Regions.Behaviors.SyncRegionContextWithHostBehavior.OnAttach">
            <summary>
            Override this method to perform the logic after the behavior has been attached.
            </summary>
        </member>
        <member name="T:Prism.Regions.ContentControlRegionAdapter">
            <summary>
            Adapter that creates a new <see cref="T:Prism.Regions.SingleActiveRegion"/> and monitors its
            active view to set it on the adapted <see cref="T:System.Windows.Controls.ContentControl"/>.
            </summary>
        </member>
        <member name="M:Prism.Regions.ContentControlRegionAdapter.#ctor(Prism.Regions.IRegionBehaviorFactory)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.ContentControlRegionAdapter"/>.
            </summary>
            <param name="regionBehaviorFactory">The factory used to create the region behaviors to attach to the created regions.</param>
        </member>
        <member name="M:Prism.Regions.ContentControlRegionAdapter.Adapt(Prism.Regions.IRegion,System.Windows.Controls.ContentControl)">
            <summary>
            Adapts a <see cref="T:System.Windows.Controls.ContentControl"/> to an <see cref="T:Prism.Regions.IRegion"/>.
            </summary>
            <param name="region">The new region being used.</param>
            <param name="regionTarget">The object to adapt.</param>
        </member>
        <member name="M:Prism.Regions.ContentControlRegionAdapter.CreateRegion">
            <summary>
            Creates a new instance of <see cref="T:Prism.Regions.SingleActiveRegion"/>.
            </summary>
            <returns>A new instance of <see cref="T:Prism.Regions.SingleActiveRegion"/>.</returns>
        </member>
        <member name="E:Prism.Regions.DefaultRegionManagerAccessor.UpdatingRegions">
            <summary>
            Notification used by attached behaviors to update the region managers appropriatelly if needed to.
            </summary>
            <remarks>This event uses weak references to the event handler to prevent this static event of keeping the
            target element longer than expected.</remarks>
        </member>
        <member name="M:Prism.Regions.DefaultRegionManagerAccessor.GetRegionName(System.Windows.DependencyObject)">
            <summary>
            Gets the value for the RegionName attached property.
            </summary>
            <param name="element">The object to adapt. This is typically a container (i.e a control).</param>
            <returns>The name of the region that should be created when
            the RegionManager is also set in this element.</returns>
        </member>
        <member name="M:Prism.Regions.DefaultRegionManagerAccessor.GetRegionManager(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the RegionName attached property.
            </summary>
            <param name="element">The target element.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionManager"/> attached to the <paramref name="element"/> element.</returns>
        </member>
        <member name="T:Prism.Regions.IConfirmNavigationRequest">
            <summary>
            Provides a way for objects involved in navigation to determine if a navigation request should continue.
            </summary>
        </member>
        <member name="M:Prism.Regions.IConfirmNavigationRequest.ConfirmNavigationRequest(Prism.Regions.NavigationContext,System.Action{System.Boolean})">
            <summary>
            Determines whether this instance accepts being navigated away from.
            </summary>
            <param name="navigationContext">The navigation context.</param>
            <param name="continuationCallback">The callback to indicate when navigation can proceed.</param>
            <remarks>
            Implementors of this method do not need to invoke the callback before this method is completed,
            but they must ensure the callback is eventually invoked.
            </remarks>
        </member>
        <member name="T:Prism.Regions.INavigateAsync">
            <summary>
            Provides methods to perform navigation.
            </summary>
            <remarks>
            Convenience overloads for the methods in this interface can be found as extension methods on the 
            <see cref="T:Prism.Regions.NavigationAsyncExtensions"/> class.
            </remarks>
        </member>
        <member name="M:Prism.Regions.INavigateAsync.RequestNavigate(System.Uri,System.Action{Prism.Regions.NavigationResult})">
            <summary>
            Initiates navigation to the target specified by the <see cref="T:System.Uri"/>.
            </summary>
            <param name="target">The navigation target</param>
            <param name="navigationCallback">The callback executed when the navigation request is completed.</param>
            <remarks>
            Convenience overloads for this method can be found as extension methods on the 
            <see cref="T:Prism.Regions.NavigationAsyncExtensions"/> class.
            </remarks>
        </member>
        <member name="M:Prism.Regions.INavigateAsync.RequestNavigate(System.Uri,System.Action{Prism.Regions.NavigationResult},Prism.Regions.NavigationParameters)">
            <summary>
            Initiates navigation to the target specified by the <see cref="T:System.Uri"/>.
            </summary>
            <param name="target">The navigation target</param>
            <param name="navigationCallback">The callback executed when the navigation request is completed.</param>
            <param name="navigationParameters">The navigation parameters specific to the navigation request.</param>
            <remarks>
            Convenience overloads for this method can be found as extension methods on the 
            <see cref="T:Prism.Regions.NavigationAsyncExtensions"/> class.
            </remarks>
        </member>
        <member name="T:Prism.Regions.INavigationAware">
            <summary>
            Provides a way for objects involved in navigation to be notified of navigation activities.
            </summary>
        </member>
        <member name="M:Prism.Regions.INavigationAware.OnNavigatedTo(Prism.Regions.NavigationContext)">
            <summary>
            Called when the implementer has been navigated to.
            </summary>
            <param name="navigationContext">The navigation context.</param>
        </member>
        <member name="M:Prism.Regions.INavigationAware.IsNavigationTarget(Prism.Regions.NavigationContext)">
            <summary>
            Called to determine if this instance can handle the navigation request.
            </summary>
            <param name="navigationContext">The navigation context.</param>
            <returns>
            <see langword="true"/> if this instance accepts the navigation request; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Prism.Regions.INavigationAware.OnNavigatedFrom(Prism.Regions.NavigationContext)">
            <summary>
            Called when the implementer is being navigated away from.
            </summary>
            <param name="navigationContext">The navigation context.</param>
        </member>
        <member name="T:Prism.Regions.IRegion">
            <summary>
            Defines a model that can be used to compose views.
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegion.Views">
            <summary>
            Gets a readonly view of the collection of views in the region.
            </summary>
            <value>An <see cref="T:Prism.Regions.IViewsCollection"/> of all the added views.</value>
        </member>
        <member name="P:Prism.Regions.IRegion.ActiveViews">
            <summary>
            Gets a readonly view of the collection of all the active views in the region.
            </summary>
            <value>An <see cref="T:Prism.Regions.IViewsCollection"/> of all the active views.</value>
        </member>
        <member name="P:Prism.Regions.IRegion.Context">
            <summary>
            Gets or sets a context for the region. This value can be used by the user to share context with the views.
            </summary>
            <value>The context value to be shared.</value>
        </member>
        <member name="P:Prism.Regions.IRegion.Name">
            <summary>
            Gets the name of the region that uniequely identifies the region within a <see cref="T:Prism.Regions.IRegionManager"/>.
            </summary>
            <value>The name of the region.</value>
        </member>
        <member name="P:Prism.Regions.IRegion.SortComparison">
            <summary>
            Gets or sets the comparison used to sort the views.
            </summary>
            <value>The comparison to use.</value>
        </member>
        <member name="M:Prism.Regions.IRegion.Add(System.Object)">
            <overloads>Adds a new view to the region.</overloads>
             <summary>
             Adds a new view to the region.
             </summary>
             <param name="view">The view to add.</param>
             <returns>The <see cref="T:Prism.Regions.IRegionManager"/> that is set on the view if it is a <see cref="T:System.Windows.DependencyObject"/>. It will be the current region manager when using this overload.</returns>
        </member>
        <member name="M:Prism.Regions.IRegion.Add(System.Object,System.String)">
            <summary>
            Adds a new view to the region.
            </summary>
            <param name="view">The view to add.</param>
            <param name="viewName">The name of the view. This can be used to retrieve it later by calling <see cref="M:Prism.Regions.IRegion.GetView(System.String)"/>.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionManager"/> that is set on the view if it is a <see cref="T:System.Windows.DependencyObject"/>. It will be the current region manager when using this overload.</returns>
        </member>
        <member name="M:Prism.Regions.IRegion.Add(System.Object,System.String,System.Boolean)">
            <summary>
            Adds a new view to the region.
            </summary>
            <param name="view">The view to add.</param>
            <param name="viewName">The name of the view. This can be used to retrieve it later by calling <see cref="M:Prism.Regions.IRegion.GetView(System.String)"/>.</param>
            <param name="createRegionManagerScope">When <see langword="true"/>, the added view will receive a new instance of <see cref="T:Prism.Regions.IRegionManager"/>, otherwise it will use the current region manager for this region.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionManager"/> that is set on the view if it is a <see cref="T:System.Windows.DependencyObject"/>.</returns>
        </member>
        <member name="M:Prism.Regions.IRegion.Remove(System.Object)">
            <summary>
            Removes the specified view from the region.
            </summary>
            <param name="view">The view to remove.</param>
        </member>
        <member name="M:Prism.Regions.IRegion.RemoveAll">
            <summary>
            Removes all views from the region.
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegion.Activate(System.Object)">
            <summary>
            Marks the specified view as active. 
            </summary>
            <param name="view">The view to activate.</param>
        </member>
        <member name="M:Prism.Regions.IRegion.Deactivate(System.Object)">
            <summary>
            Marks the specified view as inactive. 
            </summary>
            <param name="view">The view to deactivate.</param>
        </member>
        <member name="M:Prism.Regions.IRegion.GetView(System.String)">
            <summary>
            Returns the view instance that was added to the region using a specific name.
            </summary>
            <param name="viewName">The name used when adding the view to the region.</param>
            <returns>Returns the named view or <see langword="null"/> if the view with <paramref name="viewName"/> does not exist in the current region.</returns>
        </member>
        <member name="P:Prism.Regions.IRegion.RegionManager">
            <summary>
            Gets or sets the <see cref="T:Prism.Regions.IRegionManager"/> that will be passed to the views when adding them to the region, unless the view is added by specifying createRegionManagerScope as <see langword="true" />.
            </summary>
            <value>The <see cref="T:Prism.Regions.IRegionManager"/> where this <see cref="T:Prism.Regions.IRegion"/> is registered.</value>
            <remarks>This is usually used by implementations of <see cref="T:Prism.Regions.IRegionManager"/> and should not be
            used by the developer explicitely.</remarks>
        </member>
        <member name="P:Prism.Regions.IRegion.Behaviors">
            <summary>
            Gets the collection of <see cref="T:Prism.Regions.IRegionBehavior"/>s that can extend the behavior of regions. 
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegion.NavigationService">
            <summary>
            Gets or sets the navigation service.
            </summary>
            <value>The navigation service.</value>
        </member>
        <member name="T:Prism.Regions.IRegionAdapter">
            <summary>
            Defines an interfaces to adapt an object and bind it to a new <see cref="T:Prism.Regions.IRegion"/>.
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegionAdapter.Initialize(System.Object,System.String)">
            <summary>
            Adapts an object and binds it to a new <see cref="T:Prism.Regions.IRegion"/>.
            </summary>
            <param name="regionTarget">The object to adapt.</param>
            <param name="regionName">The name of the region to be created.</param>
            <returns>The new instance of <see cref="T:Prism.Regions.IRegion"/> that the <paramref name="regionTarget"/> is bound to.</returns>
        </member>
        <member name="T:Prism.Regions.IRegionBehavior">
            <summary>
            Interface for allowing extensible behavior on regions.
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegionBehavior.Region">
            <summary>
            The region that this behavior is extending.
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegionBehavior.Attach">
            <summary>
            Attaches the behavior to the specified region.
            </summary>
        </member>
        <member name="T:Prism.Regions.IRegionBehaviorCollection">
            <summary>
            Defines the interface for a collection of <see cref="T:Prism.Regions.IRegionBehavior"/> classes on a Region.
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegionBehaviorCollection.Add(System.String,Prism.Regions.IRegionBehavior)">
            <summary>
            Adds a <see cref="T:Prism.Regions.IRegionBehavior"/> to the collection, using the specified key as an indexer. 
            </summary>
            <param name="key">
            The key that specifies the type of <see cref="T:Prism.Regions.IRegionBehavior"/> that's added. 
            </param>
            <param name="regionBehavior">The <see cref="T:Prism.Regions.IRegionBehavior"/> to add.</param>
        </member>
        <member name="M:Prism.Regions.IRegionBehaviorCollection.ContainsKey(System.String)">
            <summary>
            Checks if a <see cref="T:Prism.Regions.IRegionBehavior"/> with the specified key is already present. 
            </summary>
            <param name="key">The key to use to find a particular <see cref="T:Prism.Regions.IRegionBehavior"/>.</param>
            <returns></returns>
        </member>
        <member name="P:Prism.Regions.IRegionBehaviorCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Prism.Regions.IRegionBehavior"/> with the specified key.
            </summary>
            <value>The registered <see cref="T:Prism.Regions.IRegionBehavior"/></value>
        </member>
        <member name="T:Prism.Regions.IRegionBehaviorFactory">
            <summary>
            Interface for RegionBehaviorFactories. This factory allows the registration of the default set of RegionBehaviors, that will
            be added to the <see cref="T:Prism.Regions.IRegionBehaviorCollection"/>s of all <see cref="T:Prism.Regions.IRegion"/>s, unless overridden on a 'per-region' basis. 
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegionBehaviorFactory.AddIfMissing(System.String,System.Type)">
            <summary>
            Adds a particular type of RegionBehavior if it was not already registered. the <paramref name="behaviorKey"/> string is used to check if the behavior is already present
            </summary>
            <param name="behaviorKey">The behavior key that's used to find if a certain behavior is already added.</param>
            <param name="behaviorType">Type of the behavior to add. .</param>
        </member>
        <member name="M:Prism.Regions.IRegionBehaviorFactory.ContainsKey(System.String)">
            <summary>
            Determines whether a behavior with the specified key already exists
            </summary>
            <param name="behaviorKey">The behavior key.</param>
            <returns>
            <see langword="true"/> if a behavior with the specified key is present; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Prism.Regions.IRegionBehaviorFactory.CreateFromKey(System.String)">
            <summary>
            Creates an instance of the Behaviortype that's registered using the specified key.
            </summary>
            <param name="key">The key that's used to register a behavior type.</param>
            <returns>The created behavior. </returns>
        </member>
        <member name="T:Prism.Regions.IRegionCollection">
            <summary>
            Defines a collection of <see cref="T:Prism.Regions.IRegion"/> uniquely identified by their Name.
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegionCollection.Item(System.String)">
            <summary>
            Gets the IRegion with the name received as index.
            </summary>
            <param name="regionName">Name of the region to be retrieved.</param>
            <returns>The <see cref="T:Prism.Regions.IRegion"/> identified with the requested name.</returns>
        </member>
        <member name="M:Prism.Regions.IRegionCollection.Add(Prism.Regions.IRegion)">
            <summary>
            Adds a <see cref="T:Prism.Regions.IRegion"/> to the collection.
            </summary>
            <param name="region">Region to be added to the collection.</param>
        </member>
        <member name="M:Prism.Regions.IRegionCollection.Remove(System.String)">
            <summary>
            Removes a <see cref="T:Prism.Regions.IRegion"/> from the collection.
            </summary>
            <param name="regionName">Name of the region to be removed.</param>
            <returns><see langword="true"/> if the region was removed from the collection, otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Prism.Regions.IRegionCollection.ContainsRegionWithName(System.String)">
            <summary>
            Checks if the collection contains a <see cref="T:Prism.Regions.IRegion"/> with the name received as parameter.
            </summary>
            <param name="regionName">The name of the region to look for.</param>
            <returns><see langword="true"/> if the region is contained in the collection, otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Prism.Regions.IRegionCollection.Add(System.String,Prism.Regions.IRegion)">
            <summary>
            Adds a region to the regionmanager with the name received as argument.
            </summary>
            <param name="regionName">The name to be given to the region.</param>
            <param name="region">The region to be added to the regionmanager.</param>        
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="region"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">Thrown if <paramref name="regionName"/> and <paramref name="region"/>'s name do not match and the <paramref name="region"/> <see cref="P:Prism.Regions.IRegion.Name"/> is not <see langword="null"/>.</exception>
        </member>
        <member name="T:Prism.Regions.IRegionManager">
            <summary>
            Defines an interface to manage a set of <see cref="T:Prism.Regions.IRegion">regions</see> and to attach regions to objects (typically controls).
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegionManager.Regions">
            <summary>
            Gets a collection of <see cref="T:Prism.Regions.IRegion"/> that identify each region by name. You can use this collection to add or remove regions to the current region manager.
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegionManager.CreateRegionManager">
            <summary>
            Creates a new region manager.
            </summary>
            <returns>A new region manager that can be used as a different scope from the current region manager.</returns>
        </member>
        <member name="M:Prism.Regions.IRegionManager.AddToRegion(System.String,System.Object)">
            <summary>
                Add a view to the Views collection of a Region. Note that the region must already exist in this regionmanager. 
            </summary>
            <param name="regionName">The name of the region to add a view to</param>
            <param name="view">The view to add to the views collection</param>
            <returns>The RegionManager, to easily add several views. </returns>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RegisterViewWithRegion(System.String,System.Type)">
            <summary>
            Associate a view with a region, by registering a type. When the region get's displayed
            this type will be resolved using the ServiceLocator into a concrete instance. The instance
            will be added to the Views collection of the region
            </summary>
            <param name="regionName">The name of the region to associate the view with.</param>
            <param name="viewType">The type of the view to register with the </param>
            <returns>The regionmanager, for adding several views easily</returns>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RegisterViewWithRegion(System.String,System.Func{System.Object})">
            <summary>
            Associate a view with a region, using a delegate to resolve a concrete instance of the view. 
            When the region get's displayed, this delegate will be called and the result will be added to the
            views collection of the region. 
            </summary>
            <param name="regionName">The name of the region to associate the view with.</param>
            <param name="getContentDelegate">The delegate used to resolve a concrete instance of the view.</param>
            <returns>The regionmanager, for adding several views easily</returns>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RequestNavigate(System.String,System.Uri,System.Action{Prism.Regions.NavigationResult})">
            <summary>
            Navigates the specified region manager.
            </summary>
            <param name="regionName">The name of the region to call Navigate on.</param>
            <param name="source">The URI of the content to display.</param>
            <param name="navigationCallback">The navigation callback.</param>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RequestNavigate(System.String,System.Uri)">
            <summary>
            Navigates the specified region manager.
            </summary>
            <param name="regionName">The name of the region to call Navigate on.</param>
            <param name="source">The URI of the content to display.</param>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RequestNavigate(System.String,System.String,System.Action{Prism.Regions.NavigationResult})">
            <summary>
            Navigates the specified region manager.
            </summary>
            <param name="regionName">The name of the region to call Navigate on.</param>
            <param name="source">The URI of the content to display.</param>
            <param name="navigationCallback">The navigation callback.</param>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RequestNavigate(System.String,System.String)">
            <summary>
            Navigates the specified region manager.
            </summary>
            <param name="regionName">The name of the region to call Navigate on.</param>
            <param name="source">The URI of the content to display.</param>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RequestNavigate(System.String,System.Uri,System.Action{Prism.Regions.NavigationResult},Prism.Regions.NavigationParameters)">
            <summary>
            This method allows an IRegionManager to locate a specified region and navigate in it to the specified target Uri, passing a navigation callback and an instance of NavigationParameters, which holds a collection of object parameters.
            </summary>
            <param name="regionName">The name of the region where the navigation will occur.</param>
            <param name="target">A Uri that represents the target where the region will navigate.</param>
            <param name="navigationCallback">The navigation callback that will be executed after the navigation is completed.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RequestNavigate(System.String,System.String,System.Action{Prism.Regions.NavigationResult},Prism.Regions.NavigationParameters)">
            <summary>
            This method allows an IRegionManager to locate a specified region and navigate in it to the specified target string, passing a navigation callback and an instance of NavigationParameters, which holds a collection of object parameters.
            </summary>
            <param name="regionName">The name of the region where the navigation will occur.</param>
            <param name="target">A string that represents the target where the region will navigate.</param>
            <param name="navigationCallback">The navigation callback that will be executed after the navigation is completed.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RequestNavigate(System.String,System.Uri,Prism.Regions.NavigationParameters)">
            <summary>
            This method allows an IRegionManager to locate a specified region and navigate in it to the specified target Uri, passing an instance of NavigationParameters, which holds a collection of object parameters.
            </summary>
            <param name="regionName">The name of the region where the navigation will occur.</param>
            <param name="target">A Uri that represents the target where the region will navigate.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.IRegionManager.RequestNavigate(System.String,System.String,Prism.Regions.NavigationParameters)">
            <summary>
            This method allows an IRegionManager to locate a specified region and navigate in it to the specified target string, passing an instance of NavigationParameters, which holds a collection of object parameters.
            </summary>
            <param name="regionName">The name of the region where the navigation will occur.</param>
            <param name="target">A string that represents the target where the region will navigate.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="T:Prism.Regions.IRegionManagerAccessor">
            <summary>
            Provides an abstraction on top of the RegionManager static members.
            </summary>
        </member>
        <member name="E:Prism.Regions.IRegionManagerAccessor.UpdatingRegions">
            <summary>
            Notification used by attached behaviors to update the region managers appropriatelly if needed to.
            </summary>
            <remarks>This event uses weak references to the event handler to prevent this static event of keeping the
            target element longer than expected.</remarks>
        </member>
        <member name="M:Prism.Regions.IRegionManagerAccessor.GetRegionName(System.Windows.DependencyObject)">
            <summary>
            Gets the value for the RegionName attached property.
            </summary>
            <param name="element">The object to adapt. This is typically a container (i.e a control).</param>
            <returns>The name of the region that should be created when 
            the RegionManager is also set in this element.</returns>
        </member>
        <member name="M:Prism.Regions.IRegionManagerAccessor.GetRegionManager(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the RegionName attached property.
            </summary>
            <param name="element">The target element.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionManager"/> attached to the <paramref name="element"/> element.</returns>
        </member>
        <member name="T:Prism.Regions.IRegionMemberLifetime">
            <summary>
            When implemented, allows an instance placed in a <see cref="T:Prism.Regions.IRegion"/>
            that uses a <see cref="T:Prism.Regions.Behaviors.RegionMemberLifetimeBehavior"/> to indicate
            it should be removed when it transitions from an activated to deactived state.
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegionMemberLifetime.KeepAlive">
            <summary>
            Gets a value indicating whether this instance should be kept-alive upon deactivation.
            </summary>
        </member>
        <member name="T:Prism.Regions.IRegionNavigationContentLoader">
            <summary>
            Identifies the view in a region that is the target of a navigation request.
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegionNavigationContentLoader.LoadContent(Prism.Regions.IRegion,Prism.Regions.NavigationContext)">
            <summary>
            Gets the content to which the navigation request represented by <paramref name="navigationContext"/> applies.
            </summary>
            <remarks>
            If none of the items in the region match the target of the navigation request, a new item
            will be created and added to the region.
            </remarks>
            <param name="region">The region.</param>
            <param name="navigationContext">The context representing the navigation request.</param>
            <returns>The item to be the target of the navigation request.</returns>
            <exception cref="T:System.InvalidOperationException">when a new item cannot be created for the navigation request.</exception>
        </member>
        <member name="T:Prism.Regions.IRegionNavigationJournal">
            <summary>
            Provides journaling of current, back, and forward navigation within regions.
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegionNavigationJournal.CanGoBack">
            <summary>
            Gets a value that indicates whether there is at least one entry in the back navigation history.
            </summary>
            <value>
            <c>true</c> if the journal can go back; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Prism.Regions.IRegionNavigationJournal.CanGoForward">
            <summary>
            Gets a value that indicates whether there is at least one entry in the forward navigation history.
            </summary>
            <value>
            	<c>true</c> if this instance can go forward; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Prism.Regions.IRegionNavigationJournal.CurrentEntry">
            <summary>
            Gets the current navigation entry of the content that is currently displayed.
            </summary>
            <value>The current entry.</value>
        </member>
        <member name="P:Prism.Regions.IRegionNavigationJournal.NavigationTarget">
            <summary>
            Gets or sets the target that implements INavigateAsync.
            </summary>
            <value>The INavigate implementation.</value>
            <remarks>
            This is set by the owner of this journal.
            </remarks>
        </member>
        <member name="M:Prism.Regions.IRegionNavigationJournal.GoBack">
            <summary>
            Navigates to the most recent entry in the back navigation history, or does nothing if no entry exists in back navigation.
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegionNavigationJournal.GoForward">
            <summary>
            Navigates to the most recent entry in the forward navigation history, or does nothing if no entry exists in forward navigation.
            </summary>
        </member>
        <member name="M:Prism.Regions.IRegionNavigationJournal.RecordNavigation(Prism.Regions.IRegionNavigationJournalEntry)">
            <summary>
            Records the navigation to the entry..
            </summary>
            <param name="entry">The entry to record.</param>
        </member>
        <member name="M:Prism.Regions.IRegionNavigationJournal.Clear">
            <summary>
            Clears the journal of current, back, and forward navigation histories.
            </summary>
        </member>
        <member name="T:Prism.Regions.IRegionNavigationJournalEntry">
            <summary>
            An entry in an IRegionNavigationJournal representing the URI navigated to.
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegionNavigationJournalEntry.Uri">
            <summary>
            Gets or sets the URI.
            </summary>
            <value>The URI.</value>
        </member>
        <member name="P:Prism.Regions.IRegionNavigationJournalEntry.Parameters">
            <summary>
            Gets or sets the NavigationParameters instance.
            </summary>
        </member>
        <member name="T:Prism.Regions.IRegionNavigationService">
            <summary>
            Provides navigation for regions.
            </summary>
        </member>
        <member name="P:Prism.Regions.IRegionNavigationService.Region">
            <summary>
            Gets or sets the region owning this service.
            </summary>
            <value>A Region.</value>
        </member>
        <member name="P:Prism.Regions.IRegionNavigationService.Journal">
            <summary>
            Gets the journal.
            </summary>
            <value>The journal.</value>
        </member>
        <member name="E:Prism.Regions.IRegionNavigationService.Navigating">
            <summary>
            Raised when the region is about to be navigated to content.
            </summary>
        </member>
        <member name="E:Prism.Regions.IRegionNavigationService.Navigated">
            <summary>
            Raised when the region is navigated to content.
            </summary>
        </member>
        <member name="E:Prism.Regions.IRegionNavigationService.NavigationFailed">
            <summary>
            Raised when a navigation request fails.
            </summary>
        </member>
        <member name="T:Prism.Regions.IRegionViewRegistry">
            <summary>
            Defines the interface for the registry of region's content.
            </summary>
        </member>
        <member name="E:Prism.Regions.IRegionViewRegistry.ContentRegistered">
            <summary>
            Event triggered when a content is registered to a region name.
            </summary>
            <remarks>
            This event uses weak references to the event handler to prevent this service (typically a singleton) of keeping the
            target element longer than expected.
            </remarks>
        </member>
        <member name="M:Prism.Regions.IRegionViewRegistry.GetContents(System.String)">
            <summary>
            Returns the contents associated with a region name.
            </summary>
            <param name="regionName">Region name for which contents are requested.</param>
            <returns>Collection of contents associated with the <paramref name="regionName"/>.</returns>
        </member>
        <member name="M:Prism.Regions.IRegionViewRegistry.RegisterViewWithRegion(System.String,System.Type)">
            <summary>
            Registers a content type with a region name.
            </summary>
            <param name="regionName">Region name to which the <paramref name="viewType"/> will be registered.</param>
            <param name="viewType">Content type to be registered for the <paramref name="regionName"/>.</param>
        </member>
        <member name="M:Prism.Regions.IRegionViewRegistry.RegisterViewWithRegion(System.String,System.Func{System.Object})">
            <summary>
            Registers a delegate that can be used to retrieve the content associated with a region name. 
            </summary>
            <param name="regionName">Region name to which the <paramref name="getContentDelegate"/> will be registered.</param>
            <param name="getContentDelegate">Delegate used to retrieve the content associated with the <paramref name="regionName"/>.</param>
        </member>
        <member name="T:Prism.Regions.ItemMetadata">
            <summary>
            Defines a class that wraps an item and adds metadata for it.
            </summary>
        </member>
        <member name="F:Prism.Regions.ItemMetadata.NameProperty">
            <summary>
            The name of the wrapped item.
            </summary>
        </member>
        <member name="F:Prism.Regions.ItemMetadata.IsActiveProperty">
            <summary>
            Value indicating whether the wrapped item is considered active.
            </summary>
        </member>
        <member name="M:Prism.Regions.ItemMetadata.#ctor(System.Object)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.ItemMetadata"/>.
            </summary>
            <param name="item">The item to wrap.</param>
        </member>
        <member name="P:Prism.Regions.ItemMetadata.Item">
            <summary>
            Gets the wrapped item.
            </summary>
            <value>The wrapped item.</value>
        </member>
        <member name="P:Prism.Regions.ItemMetadata.Name">
            <summary>
            Gets or sets a name for the wrapped item.
            </summary>
            <value>The name of the wrapped item.</value>
        </member>
        <member name="P:Prism.Regions.ItemMetadata.IsActive">
            <summary>
            Gets or sets a value indicating whether the wrapped item is considered active.
            </summary>
            <value><see langword="true" /> if the item should be considered active; otherwise <see langword="false" />.</value>
        </member>
        <member name="E:Prism.Regions.ItemMetadata.MetadataChanged">
            <summary>
            Occurs when metadata on the item changes.
            </summary>
        </member>
        <member name="M:Prism.Regions.ItemMetadata.InvokeMetadataChanged">
            <summary>
            Explicitly invokes <see cref="E:Prism.Regions.ItemMetadata.MetadataChanged"/> to notify listeners.
            </summary>
        </member>
        <member name="T:Prism.Regions.ItemsControlRegionAdapter">
            <summary>
            Adapter that creates a new <see cref="T:Prism.Regions.AllActiveRegion"/> and binds all
            the views to the adapted <see cref="T:System.Windows.Controls.ItemsControl"/>.
            </summary>
        </member>
        <member name="M:Prism.Regions.ItemsControlRegionAdapter.#ctor(Prism.Regions.IRegionBehaviorFactory)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.ItemsControlRegionAdapter"/>.
            </summary>
            <param name="regionBehaviorFactory">The factory used to create the region behaviors to attach to the created regions.</param>
        </member>
        <member name="M:Prism.Regions.ItemsControlRegionAdapter.Adapt(Prism.Regions.IRegion,System.Windows.Controls.ItemsControl)">
            <summary>
            Adapts an <see cref="T:System.Windows.Controls.ItemsControl"/> to an <see cref="T:Prism.Regions.IRegion"/>.
            </summary>
            <param name="region">The new region being used.</param>
            <param name="regionTarget">The object to adapt.</param>
        </member>
        <member name="M:Prism.Regions.ItemsControlRegionAdapter.CreateRegion">
            <summary>
            Creates a new instance of <see cref="T:Prism.Regions.AllActiveRegion"/>.
            </summary>
            <returns>A new instance of <see cref="T:Prism.Regions.AllActiveRegion"/>.</returns>
        </member>
        <member name="T:Prism.Regions.IViewsCollection">
            <summary>
            Defines a view of a collection.
            </summary>
        </member>
        <member name="M:Prism.Regions.IViewsCollection.Contains(System.Object)">
            <summary>
            Determines whether the collection contains a specific value.
            </summary>
            <param name="value">The object to locate in the collection.</param>
            <returns><see langword="true" /> if <paramref name="value"/> is found in the collection; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="T:Prism.Regions.NavigationAsyncExtensions">
            <summary>
            Provides additional methods to the <see cref="T:Prism.Regions.INavigateAsync"/> interface.
            </summary>
        </member>
        <member name="M:Prism.Regions.NavigationAsyncExtensions.RequestNavigate(Prism.Regions.INavigateAsync,System.String)">
            <summary>
            Initiates navigation to the target specified by the <paramref name="target"/>.
            </summary>
            <param name="navigation">The navigation object.</param>
            <param name="target">The navigation target</param>
        </member>
        <member name="M:Prism.Regions.NavigationAsyncExtensions.RequestNavigate(Prism.Regions.INavigateAsync,System.String,System.Action{Prism.Regions.NavigationResult})">
            <summary>
            Initiates navigation to the target specified by the <paramref name="target"/>.
            </summary>
            <param name="navigation">The navigation object.</param>
            <param name="target">The navigation target</param>
            <param name="navigationCallback">The callback executed when the navigation request is completed.</param>
        </member>
        <member name="M:Prism.Regions.NavigationAsyncExtensions.RequestNavigate(Prism.Regions.INavigateAsync,System.Uri)">
            <summary>
            Initiates navigation to the target specified by the <see cref="T:System.Uri"/>.
            </summary>
            <param name="navigation">The navigation object.</param>
            <param name="target">The navigation target</param>
        </member>
        <member name="M:Prism.Regions.NavigationAsyncExtensions.RequestNavigate(Prism.Regions.INavigateAsync,System.String,System.Action{Prism.Regions.NavigationResult},Prism.Regions.NavigationParameters)">
            <summary>
            Initiates navigation to the target specified by the <paramref name="target"/>.
            </summary>
            <param name="navigation">The navigation object.</param>
            <param name="target">The navigation target</param>
            <param name="navigationCallback">The callback executed when the navigation request is completed.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.NavigationAsyncExtensions.RequestNavigate(Prism.Regions.INavigateAsync,System.Uri,Prism.Regions.NavigationParameters)">
            <summary>
            Initiates navigation to the target specified by the <paramref name="target"/>.
            </summary>
            <param name="navigation">The navigation object.</param>
            <param name="target">A Uri that represents the target where the region will navigate.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.NavigationAsyncExtensions.RequestNavigate(Prism.Regions.INavigateAsync,System.String,Prism.Regions.NavigationParameters)">
            <summary>
            Initiates navigation to the target specified by the <paramref name="target"/>.
            </summary>
            <param name="navigation">The navigation object.</param>
            <param name="target">A string that represents the target where the region will navigate.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="T:Prism.Regions.NavigationContext">
            <summary>
            Encapsulates information about a navigation request.
            </summary>
        </member>
        <member name="M:Prism.Regions.NavigationContext.#ctor(Prism.Regions.IRegionNavigationService,System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.NavigationContext"/> class for a region name and a 
            <see cref="P:Prism.Regions.NavigationContext.Uri"/>.
            </summary>
            <param name="navigationService">The navigation service.</param>
            <param name="uri">The Uri.</param>
        </member>
        <member name="M:Prism.Regions.NavigationContext.#ctor(Prism.Regions.IRegionNavigationService,System.Uri,Prism.Regions.NavigationParameters)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.NavigationContext"/> class for a region name and a 
            <see cref="P:Prism.Regions.NavigationContext.Uri"/>.
            </summary>
            <param name="navigationService">The navigation service.</param>
            <param name="navigationParameters">The navigation parameters.</param>
            <param name="uri">The Uri.</param>
        </member>
        <member name="P:Prism.Regions.NavigationContext.NavigationService">
            <summary>
            Gets the region navigation service.
            </summary>
            <value>The navigation service.</value>
        </member>
        <member name="P:Prism.Regions.NavigationContext.Uri">
            <summary>
            Gets the navigation URI.
            </summary>
            <value>The navigation URI.</value>
        </member>
        <member name="P:Prism.Regions.NavigationContext.Parameters">
            <summary>
            Gets the <see cref="T:Prism.Regions.NavigationParameters"/> extracted from the URI and the object parameters passed in navigation.
            </summary>
            <value>The URI query.</value>
        </member>
        <member name="T:Prism.Regions.NavigationParameters">
            <summary>
            Represents Navigation parameters.
            </summary>
            <remarks>
            This class can be used to to pass object parameters during Navigation. 
            </remarks>
        </member>
        <member name="M:Prism.Regions.NavigationParameters.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.NavigationParameters"/> class.
            </summary>
        </member>
        <member name="M:Prism.Regions.NavigationParameters.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.NavigationParameters"/> class with a query string.
            </summary>
            <param name="query">The query string.</param>
        </member>
        <member name="P:Prism.Regions.NavigationParameters.Item(System.String)">
            <summary>
            Gets the <see cref="T:System.String"/> with the specified key.
            </summary>
            <returns>The value for the specified key, or <see langword="null"/> if the query does not contain such a key.</returns>
        </member>
        <member name="M:Prism.Regions.NavigationParameters.GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Prism.Regions.NavigationParameters.Add(System.String,System.Object)">
            <summary>
            Adds the specified key and value.
            </summary>
            <param name="key">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Prism.Regions.NavigationParameters.ToString">
            <summary>
            Converts the list of key value pairs to a query string.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Prism.Regions.NavigationResult">
            <summary>
            Represents the result of navigating to a URI.
            </summary>
        </member>
        <member name="M:Prism.Regions.NavigationResult.#ctor(Prism.Regions.NavigationContext,System.Nullable{System.Boolean})">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.NavigationResult"/> class.
            </summary>
            <param name="context">The context.</param>
            <param name="result">The result.</param>
        </member>
        <member name="M:Prism.Regions.NavigationResult.#ctor(Prism.Regions.NavigationContext,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.NavigationResult"/> class.
            </summary>
            <param name="context">The context.</param>
            <param name="error">The error.</param>
        </member>
        <member name="P:Prism.Regions.NavigationResult.Result">
            <summary>
            Gets the result.
            </summary>
            <value>The result.</value>
        </member>
        <member name="P:Prism.Regions.NavigationResult.Error">
            <summary>
            Gets an exception that occurred while navigating.
            </summary>
            <value>The exception.</value>
        </member>
        <member name="P:Prism.Regions.NavigationResult.Context">
            <summary>
            Gets the navigation context.
            </summary>
            <value>The navigation context.</value>
        </member>
        <member name="T:Prism.Regions.Region">
            <summary>
            Implementation of <see cref="T:Prism.Regions.IRegion"/> that allows multiple active views.
            </summary>
        </member>
        <member name="M:Prism.Regions.Region.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.Region"/>.
            </summary>
        </member>
        <member name="E:Prism.Regions.Region.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Prism.Regions.Region.Behaviors">
            <summary>
            Gets the collection of <see cref="T:Prism.Regions.IRegionBehavior"/>s that can extend the behavior of regions.
            </summary>
        </member>
        <member name="P:Prism.Regions.Region.Context">
            <summary>
            Gets or sets a context for the region. This value can be used by the user to share context with the views.
            </summary>
            <value>The context value to be shared.</value>
        </member>
        <member name="P:Prism.Regions.Region.Name">
            <summary>
            Gets the name of the region that uniequely identifies the region within a <see cref="T:Prism.Regions.IRegionManager"/>.
            </summary>
            <value>The name of the region.</value>
        </member>
        <member name="P:Prism.Regions.Region.Views">
            <summary>
            Gets a readonly view of the collection of views in the region.
            </summary>
            <value>An <see cref="T:Prism.Regions.IViewsCollection"/> of all the added views.</value>
        </member>
        <member name="P:Prism.Regions.Region.ActiveViews">
            <summary>
            Gets a readonly view of the collection of all the active views in the region.
            </summary>
            <value>An <see cref="T:Prism.Regions.IViewsCollection"/> of all the active views.</value>
        </member>
        <member name="P:Prism.Regions.Region.SortComparison">
            <summary>
            Gets or sets the comparison used to sort the views.
            </summary>
            <value>The comparison to use.</value>
        </member>
        <member name="P:Prism.Regions.Region.RegionManager">
            <summary>
            Gets or sets the <see cref="T:Prism.Regions.IRegionManager"/> that will be passed to the views when adding them to the region, unless the view is added by specifying createRegionManagerScope as <see langword="true" />.
            </summary>
            <value>The <see cref="T:Prism.Regions.IRegionManager"/> where this <see cref="T:Prism.Regions.IRegion"/> is registered.</value>
            <remarks>This is usually used by implementations of <see cref="T:Prism.Regions.IRegionManager"/> and should not be
            used by the developer explicitely.</remarks>
        </member>
        <member name="P:Prism.Regions.Region.NavigationService">
            <summary>
            Gets the navigation service.
            </summary>
            <value>The navigation service.</value>
        </member>
        <member name="P:Prism.Regions.Region.ItemMetadataCollection">
            <summary>
            Gets the collection with all the views along with their metadata.
            </summary>
            <value>An <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/> of <see cref="T:Prism.Regions.ItemMetadata"/> with all the added views.</value>
        </member>
        <member name="M:Prism.Regions.Region.Add(System.Object)">
            <overloads>Adds a new view to the region.</overloads>
            <summary>
            Adds a new view to the region.
            </summary>
            <param name="view">The view to add.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionManager"/> that is set on the view if it is a <see cref="T:System.Windows.DependencyObject"/>. It will be the current region manager when using this overload.</returns>
        </member>
        <member name="M:Prism.Regions.Region.Add(System.Object,System.String)">
            <summary>
            Adds a new view to the region.
            </summary>
            <param name="view">The view to add.</param>
            <param name="viewName">The name of the view. This can be used to retrieve it later by calling <see cref="M:Prism.Regions.IRegion.GetView(System.String)"/>.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionManager"/> that is set on the view if it is a <see cref="T:System.Windows.DependencyObject"/>. It will be the current region manager when using this overload.</returns>
        </member>
        <member name="M:Prism.Regions.Region.Add(System.Object,System.String,System.Boolean)">
            <summary>
            Adds a new view to the region.
            </summary>
            <param name="view">The view to add.</param>
            <param name="viewName">The name of the view. This can be used to retrieve it later by calling <see cref="M:Prism.Regions.IRegion.GetView(System.String)"/>.</param>
            <param name="createRegionManagerScope">When <see langword="true"/>, the added view will receive a new instance of <see cref="T:Prism.Regions.IRegionManager"/>, otherwise it will use the current region manager for this region.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionManager"/> that is set on the view if it is a <see cref="T:System.Windows.DependencyObject"/>.</returns>
        </member>
        <member name="M:Prism.Regions.Region.Remove(System.Object)">
            <summary>
            Removes the specified view from the region.
            </summary>
            <param name="view">The view to remove.</param>
        </member>
        <member name="M:Prism.Regions.Region.RemoveAll">
            <summary>
            Removes all views from the region.
            </summary>
        </member>
        <member name="M:Prism.Regions.Region.Activate(System.Object)">
            <summary>
            Marks the specified view as active.
            </summary>
            <param name="view">The view to activate.</param>
        </member>
        <member name="M:Prism.Regions.Region.Deactivate(System.Object)">
            <summary>
            Marks the specified view as inactive.
            </summary>
            <param name="view">The view to deactivate.</param>
        </member>
        <member name="M:Prism.Regions.Region.GetView(System.String)">
            <summary>
            Returns the view instance that was added to the region using a specific name.
            </summary>
            <param name="viewName">The name used when adding the view to the region.</param>
            <returns>Returns the named view or <see langword="null"/> if the view with <paramref name="viewName"/> does not exist in the current region.</returns>
        </member>
        <member name="M:Prism.Regions.Region.RequestNavigate(System.Uri,System.Action{Prism.Regions.NavigationResult})">
            <summary>
            Initiates navigation to the specified target.
            </summary>
            <param name="target">The target.</param>
            <param name="navigationCallback">A callback to execute when the navigation request is completed.</param>
        </member>
        <member name="M:Prism.Regions.Region.RequestNavigate(System.Uri,System.Action{Prism.Regions.NavigationResult},Prism.Regions.NavigationParameters)">
            <summary>
            Initiates navigation to the specified target.
            </summary>
            <param name="target">The target.</param>
            <param name="navigationCallback">A callback to execute when the navigation request is completed.</param>
            <param name="navigationParameters">The navigation parameters specific to the navigation request.</param>
        </member>
        <member name="M:Prism.Regions.Region.DefaultSortComparison(System.Object,System.Object)">
            <summary>
            The default sort algorithm.
            </summary>
            <param name="x">The first view to compare.</param>
            <param name="y">The second view to compare.</param>
            <returns></returns>
        </member>
        <member name="T:Prism.Regions.RegionAdapterBase`1">
            <summary>
            Base class to facilitate the creation of <see cref="T:Prism.Regions.IRegionAdapter"/> implementations.
            </summary>
            <typeparam name="T">Type of object to adapt.</typeparam>
        </member>
        <member name="M:Prism.Regions.RegionAdapterBase`1.#ctor(Prism.Regions.IRegionBehaviorFactory)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.RegionAdapterBase`1"/>.
            </summary>
            <param name="regionBehaviorFactory">The factory used to create the region behaviors to attach to the created regions.</param>
        </member>
        <member name="P:Prism.Regions.RegionAdapterBase`1.RegionBehaviorFactory">
            <summary>
            Gets or sets the factory used to create the region behaviors to attach to the created regions.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionAdapterBase`1.Initialize(`0,System.String)">
            <summary>
            Adapts an object and binds it to a new <see cref="T:Prism.Regions.IRegion"/>.
            </summary>
            <param name="regionTarget">The object to adapt.</param>
            <param name="regionName">The name of the region to be created.</param>
            <returns>The new instance of <see cref="T:Prism.Regions.IRegion"/> that the <paramref name="regionTarget"/> is bound to.</returns>
        </member>
        <member name="M:Prism.Regions.RegionAdapterBase`1.Prism#Regions#IRegionAdapter#Initialize(System.Object,System.String)">
            <summary>
            Adapts an object and binds it to a new <see cref="T:Prism.Regions.IRegion"/>.
            </summary>
            <param name="regionTarget">The object to adapt.</param>
            <param name="regionName">The name of the region to be created.</param>
            <returns>The new instance of <see cref="T:Prism.Regions.IRegion"/> that the <paramref name="regionTarget"/> is bound to.</returns>
            <remarks>This methods performs validation to check that <paramref name="regionTarget"/>
            is of type <typeparamref name="T"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">When <paramref name="regionTarget"/> is <see langword="null" />.</exception>
            <exception cref="T:System.InvalidOperationException">When <paramref name="regionTarget"/> is not of type <typeparamref name="T"/>.</exception>
        </member>
        <member name="M:Prism.Regions.RegionAdapterBase`1.AttachDefaultBehaviors(Prism.Regions.IRegion,`0)">
            <summary>
            This method adds the default behaviors by using the <see cref="T:Prism.Regions.IRegionBehaviorFactory"/> object.
            </summary>
            <param name="region">The region being used.</param>
            <param name="regionTarget">The object to adapt.</param>
        </member>
        <member name="M:Prism.Regions.RegionAdapterBase`1.AttachBehaviors(Prism.Regions.IRegion,`0)">
            <summary>
            Template method to attach new behaviors.
            </summary>
            <param name="region">The region being used.</param>
            <param name="regionTarget">The object to adapt.</param>
        </member>
        <member name="M:Prism.Regions.RegionAdapterBase`1.Adapt(Prism.Regions.IRegion,`0)">
            <summary>
            Template method to adapt the object to an <see cref="T:Prism.Regions.IRegion"/>.
            </summary>
            <param name="region">The new region being used.</param>
            <param name="regionTarget">The object to adapt.</param>
        </member>
        <member name="M:Prism.Regions.RegionAdapterBase`1.CreateRegion">
            <summary>
            Template method to create a new instance of <see cref="T:Prism.Regions.IRegion"/>
            that will be used to adapt the object.
            </summary>
            <returns>A new instance of <see cref="T:Prism.Regions.IRegion"/>.</returns>
        </member>
        <member name="T:Prism.Regions.RegionAdapterMappings">
            <summary>
            This class maps <see cref="T:System.Type"/> with <see cref="T:Prism.Regions.IRegionAdapter"/>.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionAdapterMappings.RegisterMapping(System.Type,Prism.Regions.IRegionAdapter)">
            <summary>
            Registers the mapping between a type and an adapter.
            </summary>
            <param name="controlType">The type of the control.</param>
            <param name="adapter">The adapter to use with the <paramref name="controlType"/> type.</param>
            <exception cref="T:System.ArgumentNullException">When any of <paramref name="controlType"/> or <paramref name="adapter"/> are <see langword="null" />.</exception>
            <exception cref="T:System.InvalidOperationException">If a mapping for <paramref name="controlType"/> already exists.</exception>
        </member>
        <member name="M:Prism.Regions.RegionAdapterMappings.GetMapping(System.Type)">
            <summary>
            Returns the adapter associated with the type provided.
            </summary>
            <param name="controlType">The type to obtain the <see cref="T:Prism.Regions.IRegionAdapter"/> mapped.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionAdapter"/> mapped to the <paramref name="controlType"/>.</returns>
            <remarks>This class will look for a registered type for <paramref name="controlType"/> and if there is not any,
            it will look for a registered type for any of its ancestors in the class hierarchy.
            If there is no registered type for <paramref name="controlType"/> or any of its ancestors,
            an exception will be thrown.</remarks>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">When there is no registered type for <paramref name="controlType"/> or any of its ancestors.</exception>
        </member>
        <member name="T:Prism.Regions.RegionBehavior">
            <summary>
            Provides a base class for region's behaviors.
            </summary>
        </member>
        <member name="P:Prism.Regions.RegionBehavior.Region">
            <summary>
            Behavior's attached region.
            </summary>
        </member>
        <member name="P:Prism.Regions.RegionBehavior.IsAttached">
            <summary>
            Returns <see langword="true"/> if the behavior is attached to a region, <see langword="false"/> otherwise.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionBehavior.Attach">
            <summary>
            Attaches the behavior to the region.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionBehavior.OnAttach">
            <summary>
            Override this method to perform the logic after the behavior has been attached.
            </summary>
        </member>
        <member name="T:Prism.Regions.RegionBehaviorCollection">
            <summary>
            A collection of <see cref="T:Prism.Regions.IRegionBehavior"/> instances, that are stored and retrieved by Key.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorCollection.#ctor(Prism.Regions.IRegion)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.RegionBehaviorCollection"/> class and associates it with a region.
            </summary>
            <param name="region">The region to associate the behavior collection with.</param>
        </member>
        <member name="P:Prism.Regions.RegionBehaviorCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Prism.Regions.IRegionBehavior"/> with the specified key.
            </summary>
            <value>The RegionBehavior that's registered with the key.</value>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorCollection.Add(System.String,Prism.Regions.IRegionBehavior)">
            <summary>
            Adds a <see cref="T:Prism.Regions.IRegionBehavior"/> to the collection, using the specified key as an indexer.
            </summary>
            <param name="key">The key that specifies the type of <see cref="T:Prism.Regions.IRegionBehavior"/> that's added.</param>
            <param name="regionBehavior">The <see cref="T:Prism.Regions.IRegionBehavior"/> to add.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown is the <paramref name="key"/> parameter is Null,
            or if the <paramref name="regionBehavior"/> parameter is Null.
            </exception>
            <exception cref="T:System.ArgumentException">Thrown if a behavior with the specified Key parameter already exists.</exception>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorCollection.ContainsKey(System.String)">
            <summary>
            Checks if a <see cref="T:Prism.Regions.IRegionBehavior"/> with the specified key is already present.
            </summary>
            <param name="key">The key to use to find a particular <see cref="T:Prism.Regions.IRegionBehavior"/>.</param>
            <returns></returns>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="T:Prism.Regions.RegionBehaviorFactory">
            <summary>
            Defines a factory that allows the registration of the default set of <see cref="T:Prism.Regions.IRegionBehavior"/>, that will
            be added to the <see cref="T:Prism.Regions.IRegionBehaviorCollection"/> of all <see cref="T:Prism.Regions.IRegion"/>s, unless overridden on a 'per-region' basis.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorFactory.#ctor(Microsoft.Practices.ServiceLocation.IServiceLocator)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.RegionBehaviorFactory"/>.
            </summary>
            <param name="serviceLocator"><see cref="T:Microsoft.Practices.ServiceLocation.IServiceLocator"/> used to create the instance of the behavior from its <see cref="T:System.Type"/>.</param>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorFactory.AddIfMissing(System.String,System.Type)">
            <summary>
            Adds a particular type of RegionBehavior if it was not already registered. The <paramref name="behaviorKey"/> string is used to check if the behavior is already present
            </summary>
            <param name="behaviorKey">The behavior key that's used to find if a certain behavior is already added.</param>
            <param name="behaviorType">Type of the behavior to add.</param>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorFactory.CreateFromKey(System.String)">
            <summary>
            Creates an instance of the behavior <see cref="T:System.Type"/> that is registered using the specified key.
            </summary>
            <param name="key">The key that is used to register a behavior type.</param>
            <returns>A new instance of the behavior. </returns>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorFactory.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.
            </returns>
            <filterpriority>1</filterpriority>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorFactory.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Prism.Regions.RegionBehaviorFactory.ContainsKey(System.String)">
            <summary>
            Determines whether a behavior with the specified key already exists.
            </summary>
            <param name="behaviorKey">The behavior key.</param>
            <returns>
            <see langword="true"/> if a behavior with the specified key is present; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="T:Prism.Regions.RegionContext">
             <summary>
             Class that holds methods to Set and Get the RegionContext from a DependencyObject.
            
             RegionContext allows sharing of contextual information between the view that's hosting a <see cref="T:Prism.Regions.IRegion"/>
             and any views that are inside the Region.
             </summary>
        </member>
        <member name="M:Prism.Regions.RegionContext.GetObservableContext(System.Windows.DependencyObject)">
             <summary>
             Returns an <see cref="T:Prism.Common.ObservableObject`1"/> wrapper around the RegionContext value. The RegionContext
             will be set on any views (dependency objects) that are inside the <see cref="P:Prism.Regions.IRegion.Views"/> collection by
             the <see cref="T:Prism.Regions.Behaviors.BindRegionContextToDependencyObjectBehavior"/> Behavior.
             The RegionContext will also be set to the control that hosts the Region, by the <see cref="T:Prism.Regions.Behaviors.SyncRegionContextWithHostBehavior"/> Behavior.
            
             If the <see cref="T:Prism.Common.ObservableObject`1"/> wrapper does not already exist, an empty one will be created. This way, an observer can
             notify when the value is set for the first time.
             </summary>
             <param name="view">Any view that hold the RegionContext value. </param>
             <returns>Wrapper around the Regioncontext value. </returns>
        </member>
        <member name="T:Prism.Regions.RegionManager">
            <summary>
            This class is responsible for maintaining a collection of regions and attaching regions to controls.
            </summary>
            <remarks>
            This class supplies the attached properties that can be used for simple region creation from XAML.
            </remarks>
        </member>
        <member name="F:Prism.Regions.RegionManager.RegionNameProperty">
            <summary>
            Identifies the RegionName attached property.
            </summary>
            <remarks>
            When a control has both the <see cref="F:Prism.Regions.RegionManager.RegionNameProperty"/> and
            <see cref="F:Prism.Regions.RegionManager.RegionManagerProperty"/> attached properties set to
            a value different than <see langword="null" /> and there is a
            <see cref="T:Prism.Regions.IRegionAdapter"/> mapping registered for the control, it
            will create and adapt a new region for that control, and register it
            in the <see cref="T:Prism.Regions.IRegionManager"/> with the specified region name.
            </remarks>
        </member>
        <member name="M:Prism.Regions.RegionManager.SetRegionName(System.Windows.DependencyObject,System.String)">
            <summary>
            Sets the <see cref="F:Prism.Regions.RegionManager.RegionNameProperty"/> attached property.
            </summary>
            <param name="regionTarget">The object to adapt. This is typically a container (i.e a control).</param>
            <param name="regionName">The name of the region to register.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.GetRegionName(System.Windows.DependencyObject)">
            <summary>
            Gets the value for the <see cref="F:Prism.Regions.RegionManager.RegionNameProperty"/> attached property.
            </summary>
            <param name="regionTarget">The object to adapt. This is typically a container (i.e a control).</param>
            <returns>The name of the region that should be created when
            <see cref="F:Prism.Regions.RegionManager.RegionManagerProperty"/> is also set in this element.</returns>
        </member>
        <member name="M:Prism.Regions.RegionManager.GetObservableRegion(System.Windows.DependencyObject)">
             <summary>
             Returns an <see cref="T:Prism.Common.ObservableObject`1"/> wrapper that can hold an <see cref="T:Prism.Regions.IRegion"/>. Using this wrapper
             you can detect when an <see cref="T:Prism.Regions.IRegion"/> has been created by the <see cref="T:Prism.Regions.RegionAdapterBase`1"/>.
            
             If the <see cref="T:Prism.Common.ObservableObject`1"/> wrapper does not yet exist, a new wrapper will be created. When the region
             gets created and assigned to the wrapper, you can use the <see cref="E:Prism.Common.ObservableObject`1.PropertyChanged"/> event
             to get notified of that change.
             </summary>
             <param name="view">The view that will host the region. </param>
             <returns>Wrapper that can hold an <see cref="T:Prism.Regions.IRegion"/> value and can notify when the <see cref="T:Prism.Regions.IRegion"/> value changes. </returns>
        </member>
        <member name="F:Prism.Regions.RegionManager.RegionManagerProperty">
            <summary>
            Identifies the RegionManager attached property.
            </summary>
            <remarks>
            When a control has both the <see cref="F:Prism.Regions.RegionManager.RegionNameProperty"/> and
            <see cref="F:Prism.Regions.RegionManager.RegionManagerProperty"/> attached properties set to
            a value different than <see langword="null" /> and there is a
            <see cref="T:Prism.Regions.IRegionAdapter"/> mapping registered for the control, it
            will create and adapt a new region for that control, and register it
            in the <see cref="T:Prism.Regions.IRegionManager"/> with the specified region name.
            </remarks>
        </member>
        <member name="M:Prism.Regions.RegionManager.GetRegionManager(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the <see cref="F:Prism.Regions.RegionManager.RegionNameProperty"/> attached property.
            </summary>
            <param name="target">The target element.</param>
            <returns>The <see cref="T:Prism.Regions.IRegionManager"/> attached to the <paramref name="target"/> element.</returns>
        </member>
        <member name="M:Prism.Regions.RegionManager.SetRegionManager(System.Windows.DependencyObject,Prism.Regions.IRegionManager)">
            <summary>
            Sets the <see cref="F:Prism.Regions.RegionManager.RegionManagerProperty"/> attached property.
            </summary>
            <param name="target">The target element.</param>
            <param name="value">The value.</param>
        </member>
        <member name="F:Prism.Regions.RegionManager.RegionContextProperty">
            <summary>
            Identifies the RegionContext attached property.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionManager.GetRegionContext(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the <see cref="F:Prism.Regions.RegionManager.RegionContextProperty"/> attached property.
            </summary>
            <param name="target">The target element.</param>
            <returns>The region context to pass to the contained views.</returns>
        </member>
        <member name="M:Prism.Regions.RegionManager.SetRegionContext(System.Windows.DependencyObject,System.Object)">
            <summary>
            Sets the <see cref="F:Prism.Regions.RegionManager.RegionContextProperty"/> attached property.
            </summary>
            <param name="target">The target element.</param>
            <param name="value">The value.</param>
        </member>
        <member name="E:Prism.Regions.RegionManager.UpdatingRegions">
            <summary>
            Notification used by attached behaviors to update the region managers appropriatelly if needed to.
            </summary>
            <remarks>This event uses weak references to the event handler to prevent this static event of keeping the
            target element longer than expected.</remarks>
        </member>
        <member name="M:Prism.Regions.RegionManager.UpdateRegions">
            <summary>
            Notifies attached behaviors to update the region managers appropriatelly if needed to.
            </summary>
            <remarks>
            This method is normally called internally, and there is usually no need to call this from user code.
            </remarks>
        </member>
        <member name="M:Prism.Regions.RegionManager.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.RegionManager"/>.
            </summary>
        </member>
        <member name="P:Prism.Regions.RegionManager.Regions">
            <summary>
            Gets a collection of <see cref="T:Prism.Regions.IRegion"/> that identify each region by name. You can use this collection to add or remove regions to the current region manager.
            </summary>
            <value>A <see cref="T:Prism.Regions.IRegionCollection"/> with all the registered regions.</value>
        </member>
        <member name="M:Prism.Regions.RegionManager.CreateRegionManager">
            <summary>
            Creates a new region manager.
            </summary>
            <returns>A new region manager that can be used as a different scope from the current region manager.</returns>
        </member>
        <member name="M:Prism.Regions.RegionManager.AddToRegion(System.String,System.Object)">
            <summary>
                Add a view to the Views collection of a Region. Note that the region must already exist in this regionmanager.
            </summary>
            <param name="regionName">The name of the region to add a view to</param>
            <param name="view">The view to add to the views collection</param>
            <returns>The RegionManager, to easily add several views. </returns>
        </member>
        <member name="M:Prism.Regions.RegionManager.RegisterViewWithRegion(System.String,System.Type)">
            <summary>
            Associate a view with a region, by registering a type. When the region get's displayed
            this type will be resolved using the ServiceLocator into a concrete instance. The instance
            will be added to the Views collection of the region
            </summary>
            <param name="regionName">The name of the region to associate the view with.</param>
            <param name="viewType">The type of the view to register with the </param>
            <returns>The regionmanager, for adding several views easily</returns>
        </member>
        <member name="M:Prism.Regions.RegionManager.RegisterViewWithRegion(System.String,System.Func{System.Object})">
            <summary>
            Associate a view with a region, using a delegate to resolve a concrete instance of the view.
            When the region get's displayed, this delegate will be called and the result will be added to the
            views collection of the region.
            </summary>
            <param name="regionName">The name of the region to associate the view with.</param>
            <param name="getContentDelegate">The delegate used to resolve a concrete instance of the view.</param>
            <returns>The regionmanager, for adding several views easily</returns>
        </member>
        <member name="M:Prism.Regions.RegionManager.RequestNavigate(System.String,System.Uri,System.Action{Prism.Regions.NavigationResult})">
            <summary>
            Navigates the specified region manager.
            </summary>
            <param name="regionName">The name of the region to call Navigate on.</param>
            <param name="source">The URI of the content to display.</param>
            <param name="navigationCallback">The navigation callback.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.RequestNavigate(System.String,System.Uri)">
            <summary>
            Navigates the specified region manager.
            </summary>
            <param name="regionName">The name of the region to call Navigate on.</param>
            <param name="source">The URI of the content to display.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.RequestNavigate(System.String,System.String,System.Action{Prism.Regions.NavigationResult})">
            <summary>
            Navigates the specified region manager.
            </summary>
            <param name="regionName">The name of the region to call Navigate on.</param>
            <param name="source">The URI of the content to display.</param>
            <param name="navigationCallback">The navigation callback.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.RequestNavigate(System.String,System.String)">
            <summary>
            Navigates the specified region manager.
            </summary>
            <param name="regionName">The name of the region to call Navigate on.</param>
            <param name="source">The URI of the content to display.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.RequestNavigate(System.String,System.Uri,System.Action{Prism.Regions.NavigationResult},Prism.Regions.NavigationParameters)">
            <summary>
            This method allows an IRegionManager to locate a specified region and navigate in it to the specified target Uri, passing a navigation callback and an instance of NavigationParameters, which holds a collection of object parameters.
            </summary>
            <param name="regionName">The name of the region where the navigation will occur.</param>
            <param name="target">A Uri that represents the target where the region will navigate.</param>
            <param name="navigationCallback">The navigation callback that will be executed after the navigation is completed.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.RequestNavigate(System.String,System.String,System.Action{Prism.Regions.NavigationResult},Prism.Regions.NavigationParameters)">
            <summary>
            This method allows an IRegionManager to locate a specified region and navigate in it to the specified target string, passing a navigation callback and an instance of NavigationParameters, which holds a collection of object parameters.
            </summary>
            <param name="regionName">The name of the region where the navigation will occur.</param>
            <param name="target">A string that represents the target where the region will navigate.</param>
            <param name="navigationCallback">The navigation callback that will be executed after the navigation is completed.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.RequestNavigate(System.String,System.Uri,Prism.Regions.NavigationParameters)">
            <summary>
            This method allows an IRegionManager to locate a specified region and navigate in it to the specified target Uri, passing an instance of NavigationParameters, which holds a collection of object parameters.
            </summary>
            <param name="regionName">The name of the region where the navigation will occur.</param>
            <param name="target">A Uri that represents the target where the region will navigate.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.RequestNavigate(System.String,System.String,Prism.Regions.NavigationParameters)">
            <summary>
            This method allows an IRegionManager to locate a specified region and navigate in it to the specified target string, passing an instance of NavigationParameters, which holds a collection of object parameters.
            </summary>
            <param name="regionName">The name of the region where the navigation will occur.</param>
            <param name="target">A string that represents the target where the region will navigate.</param>
            <param name="navigationParameters">An instance of NavigationParameters, which holds a collection of object parameters.</param>
        </member>
        <member name="M:Prism.Regions.RegionManager.RegionCollection.Add(System.String,Prism.Regions.IRegion)">
            <summary>
            Adds a region to the regionmanager with the name received as argument.
            </summary>
            <param name="regionName">The name to be given to the region.</param>
            <param name="region">The region to be added to the regionmanager.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="region"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">Thrown if <paramref name="regionName"/> and <paramref name="region"/>'s name do not match and the <paramref name="region"/> <see cref="P:Prism.Regions.IRegion.Name"/> is not <see langword="null"/>.</exception>
        </member>
        <member name="T:Prism.Regions.RegionMemberLifetimeAttribute">
            <summary>
            When <see cref="T:Prism.Regions.RegionMemberLifetimeAttribute"/> is applied to class provides data
            the <see cref="T:Prism.Regions.Behaviors.RegionMemberLifetimeBehavior"/> can use to determine if the instance should
            be removed when it is deactivated.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionMemberLifetimeAttribute.#ctor">
            <summary>
            Instantiates an instance of <see cref="T:Prism.Regions.RegionMemberLifetimeAttribute"/>
            </summary>
        </member>
        <member name="P:Prism.Regions.RegionMemberLifetimeAttribute.KeepAlive">
            <summary>
             Determines if the region member should be kept-alive
             when deactivated.
            </summary>
        </member>
        <member name="T:Prism.Regions.RegionNavigationContentLoader">
            <summary>
            Implementation of <see cref="T:Prism.Regions.IRegionNavigationContentLoader"/> that relies on a <see cref="T:Microsoft.Practices.ServiceLocation.IServiceLocator"/>
            to create new views when necessary.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionNavigationContentLoader.#ctor(Microsoft.Practices.ServiceLocation.IServiceLocator)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.RegionNavigationContentLoader"/> class with a service locator.
            </summary>
            <param name="serviceLocator">The service locator.</param>
        </member>
        <member name="M:Prism.Regions.RegionNavigationContentLoader.LoadContent(Prism.Regions.IRegion,Prism.Regions.NavigationContext)">
            <summary>
            Gets the view to which the navigation request represented by <paramref name="navigationContext"/> applies.
            </summary>
            <param name="region">The region.</param>
            <param name="navigationContext">The context representing the navigation request.</param>
            <returns>
            The view to be the target of the navigation request.
            </returns>
            <remarks>
            If none of the views in the region can be the target of the navigation request, a new view
            is created and added to the region.
            </remarks>
            <exception cref="T:System.ArgumentException">when a new view cannot be created for the navigation request.</exception>
        </member>
        <member name="M:Prism.Regions.RegionNavigationContentLoader.CreateNewRegionItem(System.String)">
            <summary>
            Provides a new item for the region based on the supplied candidate target contract name.
            </summary>
            <param name="candidateTargetContract">The target contract to build.</param>
            <returns>An instance of an item to put into the <see cref="T:Prism.Regions.IRegion"/>.</returns>
        </member>
        <member name="M:Prism.Regions.RegionNavigationContentLoader.GetContractFromNavigationContext(Prism.Regions.NavigationContext)">
            <summary>
            Returns the candidate TargetContract based on the <see cref="T:Prism.Regions.NavigationContext"/>.
            </summary>
            <param name="navigationContext">The navigation contract.</param>
            <returns>The candidate contract to seek within the <see cref="T:Prism.Regions.IRegion"/> and to use, if not found, when resolving from the container.</returns>
        </member>
        <member name="M:Prism.Regions.RegionNavigationContentLoader.GetCandidatesFromRegion(Prism.Regions.IRegion,System.String)">
            <summary>
            Returns the set of candidates that may satisfiy this navigation request.
            </summary>
            <param name="region">The region containing items that may satisfy the navigation request.</param>
            <param name="candidateNavigationContract">The candidate navigation target as determined by <see cref="M:Prism.Regions.RegionNavigationContentLoader.GetContractFromNavigationContext(Prism.Regions.NavigationContext)"/></param>
            <returns>An enumerable of candidate objects from the <see cref="T:Prism.Regions.IRegion"/></returns>
        </member>
        <member name="T:Prism.Regions.RegionNavigationEventArgs">
            <summary>
            EventArgs used with the Navigated event.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionNavigationEventArgs.#ctor(Prism.Regions.NavigationContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.RegionNavigationEventArgs"/> class.
            </summary>
            <param name="navigationContext">The navigation context.</param>
        </member>
        <member name="P:Prism.Regions.RegionNavigationEventArgs.NavigationContext">
            <summary>
            Gets the navigation context.
            </summary>
            <value>The navigation context.</value>
        </member>
        <member name="P:Prism.Regions.RegionNavigationEventArgs.Uri">
            <summary>
            Gets the navigation URI
            </summary>
            <value>The URI.</value>
            <remarks>
            This is a convenience accessor around NavigationContext.Uri.
            </remarks>
        </member>
        <member name="T:Prism.Regions.RegionNavigationFailedEventArgs">
            <summary>
            EventArgs used with the NavigationFailed event.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionNavigationFailedEventArgs.#ctor(Prism.Regions.NavigationContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.RegionNavigationEventArgs"/> class.
            </summary>
            <param name="navigationContext">The navigation context.</param>
        </member>
        <member name="M:Prism.Regions.RegionNavigationFailedEventArgs.#ctor(Prism.Regions.NavigationContext,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.RegionNavigationFailedEventArgs"/> class.
            </summary>
            <param name="navigationContext">The navigation context.</param>
            <param name="error">The error.</param>
        </member>
        <member name="P:Prism.Regions.RegionNavigationFailedEventArgs.NavigationContext">
            <summary>
            Gets the navigation context.
            </summary>
            <value>The navigation context.</value>
        </member>
        <member name="P:Prism.Regions.RegionNavigationFailedEventArgs.Error">
            <summary>
            Gets the error.
            </summary>
            <value>The <see cref="T:System.Exception"/>, or <see langword="null"/> if the failure was not caused by an exception.</value>
        </member>
        <member name="P:Prism.Regions.RegionNavigationFailedEventArgs.Uri">
            <summary>
            Gets the navigation URI
            </summary>
            <value>The URI.</value>
            <remarks>
            This is a convenience accessor around NavigationContext.Uri.
            </remarks>
        </member>
        <member name="T:Prism.Regions.RegionNavigationJournal">
            <summary>
            Provides journaling of current, back, and forward navigation within regions.    
            </summary>
        </member>
        <member name="P:Prism.Regions.RegionNavigationJournal.NavigationTarget">
            <summary>
            Gets or sets the target that implements INavigate.
            </summary>
            <value>The INavigate implementation.</value>
            <remarks>
            This is set by the owner of this journal.
            </remarks>
        </member>
        <member name="P:Prism.Regions.RegionNavigationJournal.CurrentEntry">
            <summary>
            Gets the current navigation entry of the content that is currently displayed.
            </summary>
            <value>The current entry.</value>
        </member>
        <member name="P:Prism.Regions.RegionNavigationJournal.CanGoBack">
            <summary>
            Gets a value that indicates whether there is at least one entry in the back navigation history.
            </summary>
            <value><c>true</c> if the journal can go back; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Prism.Regions.RegionNavigationJournal.CanGoForward">
            <summary>
            Gets a value that indicates whether there is at least one entry in the forward navigation history.
            </summary>
            <value>
            	<c>true</c> if this instance can go forward; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Prism.Regions.RegionNavigationJournal.GoBack">
            <summary>
            Navigates to the most recent entry in the back navigation history, or does nothing if no entry exists in back navigation.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionNavigationJournal.GoForward">
            <summary>
            Navigates to the most recent entry in the forward navigation history, or does nothing if no entry exists in forward navigation.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionNavigationJournal.RecordNavigation(Prism.Regions.IRegionNavigationJournalEntry)">
            <summary>
            Records the navigation to the entry..
            </summary>
            <param name="entry">The entry to record.</param>
        </member>
        <member name="M:Prism.Regions.RegionNavigationJournal.Clear">
            <summary>
            Clears the journal of current, back, and forward navigation histories.
            </summary>
        </member>
        <member name="T:Prism.Regions.RegionNavigationJournalEntry">
            <summary>
            An entry in an IRegionNavigationJournal representing the URI navigated to.
            </summary>
        </member>
        <member name="P:Prism.Regions.RegionNavigationJournalEntry.Uri">
            <summary>
            Gets or sets the URI.
            </summary>
            <value>The URI.</value>
        </member>
        <member name="P:Prism.Regions.RegionNavigationJournalEntry.Parameters">
            <summary>
            Gets or sets the NavigationParameters instance.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionNavigationJournalEntry.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="T:Prism.Regions.RegionNavigationService">
            <summary>
            Provides navigation for regions.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionNavigationService.#ctor(Microsoft.Practices.ServiceLocation.IServiceLocator,Prism.Regions.IRegionNavigationContentLoader,Prism.Regions.IRegionNavigationJournal)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.RegionNavigationService"/> class.
            </summary>
            <param name="serviceLocator">The service locator.</param>
            <param name="regionNavigationContentLoader">The navigation target handler.</param>
            <param name="journal">The journal.</param>
        </member>
        <member name="P:Prism.Regions.RegionNavigationService.Region">
            <summary>
            Gets or sets the region.
            </summary>
            <value>The region.</value>
        </member>
        <member name="P:Prism.Regions.RegionNavigationService.Journal">
            <summary>
            Gets the journal.
            </summary>
            <value>The journal.</value>
        </member>
        <member name="E:Prism.Regions.RegionNavigationService.Navigating">
            <summary>
            Raised when the region is about to be navigated to content.
            </summary>
        </member>
        <member name="E:Prism.Regions.RegionNavigationService.Navigated">
            <summary>
            Raised when the region is navigated to content.
            </summary>
        </member>
        <member name="E:Prism.Regions.RegionNavigationService.NavigationFailed">
            <summary>
            Raised when a navigation request fails.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionNavigationService.RequestNavigate(System.Uri,System.Action{Prism.Regions.NavigationResult})">
            <summary>
            Initiates navigation to the specified target.
            </summary>
            <param name="target">The target.</param>
            <param name="navigationCallback">A callback to execute when the navigation request is completed.</param>
        </member>
        <member name="M:Prism.Regions.RegionNavigationService.RequestNavigate(System.Uri,System.Action{Prism.Regions.NavigationResult},Prism.Regions.NavigationParameters)">
            <summary>
            Initiates navigation to the specified target.
            </summary>
            <param name="target">The target.</param>
            <param name="navigationCallback">A callback to execute when the navigation request is completed.</param>
            <param name="navigationParameters">The navigation parameters specific to the navigation request.</param>
        </member>
        <member name="T:Prism.Regions.RegionViewRegistry">
            <summary>
            Defines a registry for the content of the regions used on View Discovery composition.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionViewRegistry.#ctor(Microsoft.Practices.ServiceLocation.IServiceLocator)">
            <summary>
            Creates a new instance of the <see cref="T:Prism.Regions.RegionViewRegistry"/> class.
            </summary>
            <param name="locator"><see cref="T:Microsoft.Practices.ServiceLocation.IServiceLocator"/> used to create the instance of the views from its <see cref="T:System.Type"/>.</param>
        </member>
        <member name="E:Prism.Regions.RegionViewRegistry.ContentRegistered">
            <summary>
            Occurs whenever a new view is registered.
            </summary>
        </member>
        <member name="M:Prism.Regions.RegionViewRegistry.GetContents(System.String)">
            <summary>
            Returns the contents registered for a region.
            </summary>
            <param name="regionName">Name of the region which content is being requested.</param>
            <returns>Collection of contents registered for the region.</returns>
        </member>
        <member name="M:Prism.Regions.RegionViewRegistry.RegisterViewWithRegion(System.String,System.Type)">
            <summary>
            Registers a content type with a region name.
            </summary>
            <param name="regionName">Region name to which the <paramref name="viewType"/> will be registered.</param>
            <param name="viewType">Content type to be registered for the <paramref name="regionName"/>.</param>
        </member>
        <member name="M:Prism.Regions.RegionViewRegistry.RegisterViewWithRegion(System.String,System.Func{System.Object})">
            <summary>
            Registers a delegate that can be used to retrieve the content associated with a region name. 
            </summary>
            <param name="regionName">Region name to which the <paramref name="getContentDelegate"/> will be registered.</param>
            <param name="getContentDelegate">Delegate used to retrieve the content associated with the <paramref name="regionName"/>.</param>
        </member>
        <member name="M:Prism.Regions.RegionViewRegistry.CreateInstance(System.Type)">
            <summary>
            Creates an instance of a registered view <see cref="T:System.Type"/>. 
            </summary>
            <param name="type">Type of the registered view.</param>
            <returns>Instance of the registered view.</returns>
        </member>
        <member name="T:Prism.Regions.SelectorRegionAdapter">
            <summary>
            Adapter that creates a new <see cref="T:Prism.Regions.Region"/> and binds all
            the views to the adapted <see cref="T:System.Windows.Controls.Primitives.Selector"/>.
            It also keeps the <see cref="P:Prism.Regions.IRegion.ActiveViews"/> and the selected items
            of the <see cref="T:System.Windows.Controls.Primitives.Selector"/> in sync.
            </summary>
        </member>
        <member name="M:Prism.Regions.SelectorRegionAdapter.#ctor(Prism.Regions.IRegionBehaviorFactory)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Regions.SelectorRegionAdapter"/>.
            </summary>
            <param name="regionBehaviorFactory">The factory used to create the region behaviors to attach to the created regions.</param>
        </member>
        <member name="M:Prism.Regions.SelectorRegionAdapter.Adapt(Prism.Regions.IRegion,System.Windows.Controls.Primitives.Selector)">
            <summary>
            Adapts an <see cref="T:System.Windows.Controls.Primitives.Selector"/> to an <see cref="T:Prism.Regions.IRegion"/>.
            </summary>
            <param name="region">The new region being used.</param>
            <param name="regionTarget">The object to adapt.</param>
        </member>
        <member name="M:Prism.Regions.SelectorRegionAdapter.AttachBehaviors(Prism.Regions.IRegion,System.Windows.Controls.Primitives.Selector)">
            <summary>
            Attach new behaviors.
            </summary>
            <param name="region">The region being used.</param>
            <param name="regionTarget">The object to adapt.</param>
            <remarks>
            This class attaches the base behaviors and also listens for changes in the
            activity of the region or the control selection and keeps the in sync.
            </remarks>
        </member>
        <member name="M:Prism.Regions.SelectorRegionAdapter.CreateRegion">
            <summary>
            Creates a new instance of <see cref="T:Prism.Regions.Region"/>.
            </summary>
            <returns>A new instance of <see cref="T:Prism.Regions.Region"/>.</returns>
        </member>
        <member name="T:Prism.Regions.SingleActiveRegion">
            <summary>
            Region that allows a maximum of one active view at a time.
            </summary>
        </member>
        <member name="M:Prism.Regions.SingleActiveRegion.Activate(System.Object)">
            <summary>
            Marks the specified view as active.
            </summary>
            <param name="view">The view to activate.</param>
            <remarks>If there is an active view before calling this method,
            that view will be deactivated automatically.</remarks>
        </member>
        <member name="T:Prism.Regions.SyncActiveStateAttribute">
            <summary>
            Defines that a view is synchronized with its parent view's Active state.
            </summary>
        </member>
        <member name="T:Prism.Regions.UpdateRegionsException">
            <summary>
            Represents errors that occured during the regions' update.
            </summary>
            <summary>
            Represents errors that occured during the regions' update.
            </summary>
        </member>
        <member name="M:Prism.Regions.UpdateRegionsException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.UpdateRegionsException"/>
            </summary>
        </member>
        <member name="M:Prism.Regions.UpdateRegionsException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.UpdateRegionsException"/> class with a specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Prism.Regions.UpdateRegionsException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.UpdateRegionsException"/> class with a specified error message and a reference 
            to the inner exception that is the cause of this exception. 
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="inner">The exception that is the cause of the current exception, or a null reference 
            (Nothing in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:Prism.Regions.UpdateRegionsException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.UpdateRegionsException"/> class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Regions.ViewRegisteredEventArgs">
            <summary>
            Argument class used by the <see cref="E:Prism.Regions.IRegionViewRegistry.ContentRegistered"/> event when a new content is registered.
            </summary>
        </member>
        <member name="M:Prism.Regions.ViewRegisteredEventArgs.#ctor(System.String,System.Func{System.Object})">
            <summary>
            Initializes the ViewRegisteredEventArgs class.
            </summary>
            <param name="regionName">The region name to which the content was registered.</param>
            <param name="getViewDelegate">The content which was registered.</param>
        </member>
        <member name="P:Prism.Regions.ViewRegisteredEventArgs.RegionName">
            <summary>
            Gets the region name to which the content was registered.
            </summary>
        </member>
        <member name="P:Prism.Regions.ViewRegisteredEventArgs.GetView">
            <summary>
            Gets the content which was registered.
            </summary>
        </member>
        <member name="T:Prism.Regions.ViewRegistrationException">
            <summary>
            Exception that's thrown when something goes wrong while Registering a View with a region name in the <see cref="T:Prism.Regions.RegionViewRegistry"/> class. 
            </summary>
            <summary>
            Exception that's thrown when something goes wrong while Registering a View with a region name in the <see cref="T:Prism.Regions.RegionViewRegistry"/> class. 
            </summary>
        </member>
        <member name="M:Prism.Regions.ViewRegistrationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.ViewRegistrationException"/> class.
            </summary>
        </member>
        <member name="M:Prism.Regions.ViewRegistrationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.ViewRegistrationException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Prism.Regions.ViewRegistrationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.ViewRegistrationException"/> class.
            </summary>
            <param name="message">The exception message.</param>
            <param name="inner">The inner exception.</param>
        </member>
        <member name="M:Prism.Regions.ViewRegistrationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.ViewRegistrationException"/> class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized 
            object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Prism.Regions.ViewsCollection">
            <summary>
            Implementation of <see cref="T:Prism.Regions.IViewsCollection"/> that takes an <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/> of <see cref="T:Prism.Regions.ItemMetadata"/>
            and filters it to display an <see cref="T:System.Collections.Specialized.INotifyCollectionChanged"/> collection of
            <see cref="T:System.Object"/> elements (the items which the <see cref="T:Prism.Regions.ItemMetadata"/> wraps).
            </summary>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.#ctor(System.Collections.ObjectModel.ObservableCollection{Prism.Regions.ItemMetadata},System.Predicate{Prism.Regions.ItemMetadata})">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.ViewsCollection"/> class.
            </summary>
            <param name="list">The list to wrap and filter.</param>
            <param name="filter">A predicate to filter the <paramref name="list"/> collection.</param>
        </member>
        <member name="E:Prism.Regions.ViewsCollection.CollectionChanged">
            <summary>
            Occurs when the collection changes.
            </summary>
        </member>
        <member name="P:Prism.Regions.ViewsCollection.SortComparison">
            <summary>
            Gets or sets the comparison used to sort the views.
            </summary>
            <value>The comparison to use.</value>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.Contains(System.Object)">
            <summary>
            Determines whether the collection contains a specific value.
            </summary>
            <param name="value">The object to locate in the collection.</param>
            <returns><see langword="true" /> if <paramref name="value"/> is found in the collection; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Used to invoked the <see cref="E:Prism.Regions.ViewsCollection.CollectionChanged"/> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.ResetAllMonitors">
            <summary>
            Removes all monitoring of underlying MetadataItems and re-adds them.
            </summary>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.MonitorAllMetadataItems">
            <summary>
            Adds all underlying MetadataItems to the list from the subjectCollection
            </summary>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.RemoveAllMetadataMonitors">
            <summary>
            Removes all monitored items from our monitoring list.
            </summary>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.AddMetadataMonitor(Prism.Regions.ItemMetadata,System.Boolean)">
            <summary>
            Adds handler to monitor the MetadatItem and adds it to our monitoring list.
            </summary>
            <param name="itemMetadata"></param>
            <param name="isInList"></param>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.RemoveMetadataMonitor(Prism.Regions.ItemMetadata)">
            <summary>
            Unhooks from the MetadataItem change event and removes from our monitoring list.
            </summary>
            <param name="itemMetadata"></param>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.OnItemMetadataChanged(System.Object,System.EventArgs)">
            <summary>
            Invoked when any of the underlying ItemMetadata items we're monitoring changes.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:Prism.Regions.ViewsCollection.SourceCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            The event handler due to changes in the underlying collection.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Prism.Regions.ViewSortHintAttribute">
            <summary>
            Provides a hint from a view to a region on how to sort the view.
            </summary>
        </member>
        <member name="M:Prism.Regions.ViewSortHintAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Regions.ViewSortHintAttribute"/> class.
            </summary>
            <param name="hint">The hint to use for sorting.</param>
        </member>
        <member name="P:Prism.Regions.ViewSortHintAttribute.Hint">
            <summary>
            Gets  the hint.
            </summary>
            <value>The hint to use for sorting.</value>
        </member>
        <member name="T:System.Collections.ObjectModel.CollectionExtensions">
            <summary>
            Class that provides extension methods to Collection
            </summary>
        </member>
        <member name="M:System.Collections.ObjectModel.CollectionExtensions.AddRange``1(System.Collections.ObjectModel.Collection{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a range of items to a collection.
            </summary>
            <typeparam name="T">Type of objects within the collection.</typeparam>
            <param name="collection">The collection to add items to.</param>
            <param name="items">The items to add to the collection.</param>
            <returns>The collection.</returns>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="collection"/> or <paramref name="items"/> is <see langword="null"/>.</exception>
        </member>
        <member name="T:System.ExceptionExtensions">
            <summary>
            Class that provides extension methods for the Exception class. These extension methods provide
            a mechanism for developers to get more easily to the root cause of an exception, especially in combination with 
            DI-containers such as Unity. 
            </summary>
        </member>
        <member name="M:System.ExceptionExtensions.RegisterFrameworkExceptionType(System.Type)">
            <summary>
            Register the type of an Exception that is thrown by the framework. The <see cref="M:System.ExceptionExtensions.GetRootException(System.Exception)"/> method uses
            this list of Exception types to find out if something has gone wrong.  
            </summary>
            <param name="frameworkExceptionType">The type of exception to register.</param>
        </member>
        <member name="M:System.ExceptionExtensions.IsFrameworkExceptionRegistered(System.Type)">
            <summary>
            Determines whether the exception type is already registered using the <see cref="M:System.ExceptionExtensions.RegisterFrameworkExceptionType(System.Type)"/> 
            method
            </summary>
            <param name="frameworkExceptionType">The type of framework exception to find.</param>
            <returns>
            	<c>true</c> if the exception type is already registered; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.ExceptionExtensions.GetRootException(System.Exception)">
            <summary>
            Looks at all the inner exceptions of the <paramref name="exception"/> parameter to find the 
            most likely root cause of the exception. This works by skipping all registered exception types.
            </summary>
            <remarks>
            This method is not 100% accurate and should only be used to point a developer into the most likely direction.
            It should not be used to replace the Inner Exception stack of an exception, because this might hide required exception
            information. 
            </remarks>
            <param name="exception">The exception that will provide the list of inner exeptions to examine.</param>
            <returns>
            The exception that most likely caused the exception to occur. If it can't find the root exception, it will return the 
            <paramref name="exception"/> value itself.
            </returns>
        </member>
        <member name="T:Microsoft.Practices.ServiceLocation.ServiceLocatorExtensions">
            <summary>
            Defines extension methods for the <see cref="T:Microsoft.Practices.ServiceLocation.ServiceLocator"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.ServiceLocation.ServiceLocatorExtensions.TryResolve(Microsoft.Practices.ServiceLocation.IServiceLocator,System.Type)">
            <summary>
            Attempts to resolve specified type from the underlying <see cref="T:Microsoft.Practices.ServiceLocation.IServiceLocator"/>.
            </summary>
            <remarks>
            This will return null on any <see cref="T:Microsoft.Practices.ServiceLocation.ActivationException"/>.</remarks>
            <param name="locator">Locator to use in resolving.</param>
            <param name="type">Type to resolve.</param>
            <returns>T or null</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="locator"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.ServiceLocation.ServiceLocatorExtensions.TryResolve``1(Microsoft.Practices.ServiceLocation.IServiceLocator)">
            <summary>
            Attempts to resolve specified type from the underlying <see cref="T:Microsoft.Practices.ServiceLocation.IServiceLocator"/>.
            </summary>
            <remarks>
            This will return null on any <see cref="T:Microsoft.Practices.ServiceLocation.ActivationException"/>.</remarks>
            <typeparam name="T">Type to resolve.</typeparam>
            <param name="locator">Locator to use in resolving.</param>
            <returns>T or null</returns>
        </member>
    </members>
</doc>
