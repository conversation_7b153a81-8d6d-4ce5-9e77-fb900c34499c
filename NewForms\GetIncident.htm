<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
        <TITLE>Mobile Enterprise - Get Incident</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Get Incident</H4>
						<form action="GetIncident.aspx?queryfile=getincident.qry" method="post" id="GetIncident" name="GetIncident">
							<table ID="Table2">
								<tr>
									<td>Incident Number:</td>
									<td>
										<input type="text" name="incidentnumber" id="incidentnumber">
										<input type="hidden" name="pagenumber" id="pagenumber" value="1" size="1">
									</td>
								</tr>
								<tr>
									<td>&nbsp;</td>
								</tr>
							</table>
							<input type="button" name="Query" id="Query" value="Query" onkeypress="processButton()" onclick="processButton()"/>
							<input style="width:0px" tabindex=-1 type="submit" name="Submit" id="Submit" value="Submit"/>
							<br>
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<SCRIPT language="javascript">
		function window.onload()
		{
			GetIncident.incidentnumber.focus();					
		}
		
		function processButton()
		{
			GetIncident.pagenumber.value = 1;
			GetIncident.Submit.click();
		}
	</SCRIPT>
</HTML>
