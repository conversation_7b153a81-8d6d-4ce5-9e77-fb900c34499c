<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Prism</name>
    </assembly>
    <members>
        <member name="T:Prism.Commands.CompositeCommand">
            <summary>
            The CompositeCommand composes one or more ICommands.
            </summary>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Commands.CompositeCommand"/>.
            </summary>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Commands.CompositeCommand"/>.
            </summary>
            <param name="monitorCommandActivity">Indicates when the command activity is going to be monitored.</param>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.RegisterCommand(System.Windows.Input.ICommand)">
            <summary>
            Adds a command to the collection and signs up for the <see cref="E:System.Windows.Input.ICommand.CanExecuteChanged"/> event of it.
            </summary>
             <remarks>
            If this command is set to monitor command activity, and <paramref name="command"/> 
            implements the <see cref="T:Prism.IActiveAware"/> interface, this method will subscribe to its
            <see cref="E:Prism.IActiveAware.IsActiveChanged"/> event.
            </remarks>
            <param name="command">The command to register.</param>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.UnregisterCommand(System.Windows.Input.ICommand)">
            <summary>
            Removes a command from the collection and removes itself from the <see cref="E:System.Windows.Input.ICommand.CanExecuteChanged"/> event of it.
            </summary>
            <param name="command">The command to unregister.</param>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.CanExecute(System.Object)">
            <summary>
            Forwards <see cref="M:System.Windows.Input.ICommand.CanExecute(System.Object)"/> to the registered commands and returns
            <see langword="true" /> if all of the commands return <see langword="true" />.
            </summary>
            <param name="parameter">Data used by the command.
            If the command does not require data to be passed, this object can be set to <see langword="null" />.
            </param>
            <returns><see langword="true" /> if all of the commands return <see langword="true" />; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="E:Prism.Commands.CompositeCommand.CanExecuteChanged">
            <summary>
            Occurs when any of the registered commands raise <see cref="E:System.Windows.Input.ICommand.CanExecuteChanged"/>.
            </summary>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.Execute(System.Object)">
            <summary>
            Forwards <see cref="M:System.Windows.Input.ICommand.Execute(System.Object)"/> to the registered commands.
            </summary>
            <param name="parameter">Data used by the command.
            If the command does not require data to be passed, this object can be set to <see langword="null" />.
            </param>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.ShouldExecute(System.Windows.Input.ICommand)">
            <summary>
            Evaluates if a command should execute.
            </summary>
            <param name="command">The command to evaluate.</param>
            <returns>A <see cref="T:System.Boolean"/> value indicating whether the command should be used 
            when evaluating <see cref="M:Prism.Commands.CompositeCommand.CanExecute(System.Object)"/> and <see cref="M:Prism.Commands.CompositeCommand.Execute(System.Object)"/>.</returns>
            <remarks>
            If this command is set to monitor command activity, and <paramref name="command"/>
            implements the <see cref="T:Prism.IActiveAware"/> interface, 
            this method will return <see langword="false" /> if the command's <see cref="P:Prism.IActiveAware.IsActive"/> 
            property is <see langword="false" />; otherwise it always returns <see langword="true" />.</remarks>
        </member>
        <member name="P:Prism.Commands.CompositeCommand.RegisteredCommands">
            <summary>
            Gets the list of all the registered commands.
            </summary>
            <value>A list of registered commands.</value>
            <remarks>This returns a copy of the commands subscribed to the CompositeCommand.</remarks>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.OnCanExecuteChanged">
            <summary>
            Raises <see cref="E:System.Windows.Input.ICommand.CanExecuteChanged"/> on the UI thread so every 
            command invoker can requery <see cref="M:System.Windows.Input.ICommand.CanExecute(System.Object)"/> to check if the
            <see cref="T:Prism.Commands.CompositeCommand"/> can execute.
            </summary>
        </member>
        <member name="M:Prism.Commands.CompositeCommand.Command_IsActiveChanged(System.Object,System.EventArgs)">
            <summary>
            Handler for IsActiveChanged events of registered commands.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">EventArgs to pass to the event.</param>
        </member>
        <member name="T:Prism.Commands.DelegateCommand">
            <summary>
            An <see cref="!:ICommand"/> whose delegates do not take any parameters for <see cref="M:Prism.Commands.DelegateCommand.Execute"/> and <see cref="M:Prism.Commands.DelegateCommand.CanExecute"/>.
            </summary>
            <see cref="T:Prism.Commands.DelegateCommandBase"/>
            <see cref="T:Prism.Commands.DelegateCommand`1"/>
        </member>
        <member name="M:Prism.Commands.DelegateCommand.#ctor(System.Action)">
            <summary>
            Creates a new instance of <see cref="T:Prism.Commands.DelegateCommand"/> with the <see cref="T:System.Action"/> to invoke on execution.
            </summary>
            <param name="executeMethod">The <see cref="T:System.Action"/> to invoke when <see cref="!:ICommand.Execute"/> is called.</param>
        </member>
        <member name="M:Prism.Commands.DelegateCommand.#ctor(System.Action,System.Func{System.Boolean})">
            <summary>
            Creates a new instance of <see cref="T:Prism.Commands.DelegateCommand"/> with the <see cref="T:System.Action"/> to invoke on execution
            and a <see langword="Func" /> to query for determining if the command can execute.
            </summary>
            <param name="executeMethod">The <see cref="T:System.Action"/> to invoke when <see cref="!:ICommand.Execute"/> is called.</param>
            <param name="canExecuteMethod">The <see cref="T:System.Func`1"/> to invoke when <see cref="!:ICommand.CanExecute"/> is called</param>
        </member>
        <member name="M:Prism.Commands.DelegateCommand.Execute">
            <summary>
             Executes the command.
            </summary>
        </member>
        <member name="M:Prism.Commands.DelegateCommand.CanExecute">
            <summary>
            Determines if the command can be executed.
            </summary>
            <returns>Returns <see langword="true"/> if the command can execute,otherwise returns <see langword="false"/>.</returns>
        </member>
        <member name="M:Prism.Commands.DelegateCommand.ObservesProperty``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Observes a property that implements INotifyPropertyChanged, and automatically calls DelegateCommandBase.RaiseCanExecuteChanged on property changed notifications.
            </summary>
            <typeparam name="T">The object type containing the property specified in the expression.</typeparam>
            <param name="propertyExpression">The property expression. Example: ObservesProperty(() => PropertyName).</param>
            <returns>The current instance of DelegateCommand</returns>
        </member>
        <member name="M:Prism.Commands.DelegateCommand.ObservesCanExecute(System.Linq.Expressions.Expression{System.Func{System.Boolean}})">
            <summary>
            Observes a property that is used to determine if this command can execute, and if it implements INotifyPropertyChanged it will automatically call DelegateCommandBase.RaiseCanExecuteChanged on property changed notifications.
            </summary>
            <param name="canExecuteExpression">The property expression. Example: ObservesCanExecute(() => PropertyName).</param>
            <returns>The current instance of DelegateCommand</returns>
        </member>
        <member name="T:Prism.Commands.DelegateCommandBase">
            <summary>
            An <see cref="T:System.Windows.Input.ICommand"/> whose delegates can be attached for <see cref="M:Prism.Commands.DelegateCommandBase.Execute(System.Object)"/> and <see cref="M:Prism.Commands.DelegateCommandBase.CanExecute(System.Object)"/>.
            </summary>
        </member>
        <member name="M:Prism.Commands.DelegateCommandBase.#ctor">
            <summary>
            Creates a new instance of a <see cref="T:Prism.Commands.DelegateCommandBase"/>, specifying both the execute action and the can execute function.
            </summary>
            <param name="executeMethod">The <see cref="T:System.Action"/> to execute when <see cref="M:System.Windows.Input.ICommand.Execute(System.Object)"/> is invoked.</param>
            <param name="canExecuteMethod">The <see cref="T:System.Func`2"/> to invoked when <see cref="M:System.Windows.Input.ICommand.CanExecute(System.Object)"/> is invoked.</param>
        </member>
        <member name="E:Prism.Commands.DelegateCommandBase.CanExecuteChanged">
            <summary>
            Occurs when changes occur that affect whether or not the command should execute.
            </summary>
        </member>
        <member name="M:Prism.Commands.DelegateCommandBase.OnCanExecuteChanged">
            <summary>
            Raises <see cref="E:System.Windows.Input.ICommand.CanExecuteChanged"/> so every 
            command invoker can requery <see cref="M:System.Windows.Input.ICommand.CanExecute(System.Object)"/>.
            </summary>
        </member>
        <member name="M:Prism.Commands.DelegateCommandBase.RaiseCanExecuteChanged">
            <summary>
            Raises <see cref="E:Prism.Commands.DelegateCommandBase.CanExecuteChanged"/> so every command invoker
            can requery to check if the command can execute.
            <remarks>Note that this will trigger the execution of <see cref="!:DelegateCommandBase.InvokeCanExecute"/> once for each invoker.</remarks>
            </summary>
        </member>
        <member name="M:Prism.Commands.DelegateCommandBase.ObservesPropertyInternal``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Observes a property that implements INotifyPropertyChanged, and automatically calls DelegateCommandBase.RaiseCanExecuteChanged on property changed notifications.
            </summary>
            <typeparam name="T">The object type containing the property specified in the expression.</typeparam>
            <param name="propertyExpression">The property expression. Example: ObservesProperty(() => PropertyName).</param>
        </member>
        <member name="P:Prism.Commands.DelegateCommandBase.IsActive">
            <summary>
            Gets or sets a value indicating whether the object is active.
            </summary>
            <value><see langword="true" /> if the object is active; otherwise <see langword="false" />.</value>
        </member>
        <member name="E:Prism.Commands.DelegateCommandBase.IsActiveChanged">
            <summary>
            Fired if the <see cref="P:Prism.Commands.DelegateCommandBase.IsActive"/> property changes.
            </summary>
        </member>
        <member name="M:Prism.Commands.DelegateCommandBase.OnIsActiveChanged">
            <summary>
            This raises the <see cref="E:Prism.Commands.DelegateCommandBase.IsActiveChanged"/> event.
            </summary>
        </member>
        <member name="T:Prism.Commands.DelegateCommand`1">
            <summary>
            An <see cref="!:ICommand"/> whose delegates can be attached for <see cref="M:Prism.Commands.DelegateCommand`1.Execute(`0)"/> and <see cref="M:Prism.Commands.DelegateCommand`1.CanExecute(`0)"/>.
            </summary>
            <typeparam name="T">Parameter type.</typeparam>
            <remarks>
            The constructor deliberately prevents the use of value types.
            Because ICommand takes an object, having a value type for T would cause unexpected behavior when CanExecute(null) is called during XAML initialization for command bindings.
            Using default(T) was considered and rejected as a solution because the implementor would not be able to distinguish between a valid and defaulted values.
            <para/>
            Instead, callers should support a value type by using a nullable value type and checking the HasValue property before using the Value property.
            <example>
                <code>
            public MyClass()
            {
                this.submitCommand = new DelegateCommand&lt;int?&gt;(this.Submit, this.CanSubmit);
            }
            
            private bool CanSubmit(int? customerId)
            {
                return (customerId.HasValue &amp;&amp; customers.Contains(customerId.Value));
            }
                </code>
            </example>
            </remarks>
        </member>
        <member name="M:Prism.Commands.DelegateCommand`1.#ctor(System.Action{`0})">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Commands.DelegateCommand`1"/>.
            </summary>
            <param name="executeMethod">Delegate to execute when Execute is called on the command. This can be null to just hook up a CanExecute delegate.</param>
            <remarks><see cref="M:Prism.Commands.DelegateCommand`1.CanExecute(`0)"/> will always return true.</remarks>
        </member>
        <member name="M:Prism.Commands.DelegateCommand`1.#ctor(System.Action{`0},System.Func{`0,System.Boolean})">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Commands.DelegateCommand`1"/>.
            </summary>
            <param name="executeMethod">Delegate to execute when Execute is called on the command. This can be null to just hook up a CanExecute delegate.</param>
            <param name="canExecuteMethod">Delegate to execute when CanExecute is called on the command. This can be null.</param>
            <exception cref="T:System.ArgumentNullException">When both <paramref name="executeMethod"/> and <paramref name="canExecuteMethod"/> ar <see langword="null" />.</exception>
        </member>
        <member name="M:Prism.Commands.DelegateCommand`1.Execute(`0)">
            <summary>
            Executes the command and invokes the <see cref="T:System.Action`1"/> provided during construction.
            </summary>
            <param name="parameter">Data used by the command.</param>
        </member>
        <member name="M:Prism.Commands.DelegateCommand`1.CanExecute(`0)">
            <summary>
            Determines if the command can execute by invoked the <see cref="T:System.Func`2"/> provided during construction.
            </summary>
            <param name="parameter">Data used by the command to determine if it can execute.</param>
            <returns>
            <see langword="true" /> if this command can be executed; otherwise, <see langword="false" />.
            </returns>
        </member>
        <member name="M:Prism.Commands.DelegateCommand`1.ObservesProperty``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Observes a property that implements INotifyPropertyChanged, and automatically calls DelegateCommandBase.RaiseCanExecuteChanged on property changed notifications.
            </summary>
            <typeparam name="TType">The type of the return value of the method that this delegate encapulates</typeparam>
            <param name="propertyExpression">The property expression. Example: ObservesProperty(() => PropertyName).</param>
            <returns>The current instance of DelegateCommand</returns>
        </member>
        <member name="M:Prism.Commands.DelegateCommand`1.ObservesCanExecute(System.Linq.Expressions.Expression{System.Func{System.Boolean}})">
            <summary>
            Observes a property that is used to determine if this command can execute, and if it implements INotifyPropertyChanged it will automatically call DelegateCommandBase.RaiseCanExecuteChanged on property changed notifications.
            </summary>
            <param name="canExecuteExpression">The property expression. Example: ObservesCanExecute(() => PropertyName).</param>
            <returns>The current instance of DelegateCommand</returns>
        </member>
        <member name="T:Prism.Commands.PropertyObserver">
            <summary>
            Provide a way to observe property changes of INotifyPropertyChanged objects and invokes a 
            custom action when the PropertyChanged event is fired.
            </summary>
        </member>
        <member name="M:Prism.Commands.PropertyObserver.Observes``1(System.Linq.Expressions.Expression{System.Func{``0}},System.Action)">
            <summary>
            Observes a property that implements INotifyPropertyChanged, and automatically calls a custom action on 
            property changed notifications. The given expression must be in this form: "() => Prop.NestedProp.PropToObserve".
            </summary>
            <param name="propertyExpression">Expression representing property to be observed. Ex.: "() => Prop.NestedProp.PropToObserve".</param>
            <param name="action">Action to be invoked when PropertyChanged event occours.</param>
        </member>
        <member name="T:Prism.Commands.PropertyObserverNode">
            <summary>
            Represents each node of nested properties expression and takes care of 
            subscribing/unsubscribing INotifyPropertyChanged.PropertyChanged listeners on it.
            </summary>
        </member>
        <member name="T:Prism.Events.BackgroundEventSubscription">
            <summary>
            Extends <see cref="T:Prism.Events.EventSubscription"/> to invoke the <see cref="P:Prism.Events.EventSubscription.Action"/> delegate in a background thread.
            </summary>
        </member>
        <member name="M:Prism.Events.BackgroundEventSubscription.#ctor(Prism.Events.IDelegateReference)">
            <summary>
            Creates a new instance of <see cref="T:Prism.Events.BackgroundEventSubscription"/>.
            </summary>
            <param name="actionReference">A reference to a delegate of type <see cref="T:System.Action"/>.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="actionReference"/> or <see paramref="filterReference"/> are <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">When the target of <paramref name="actionReference"/> is not of type <see cref="T:System.Action"/>.</exception>
        </member>
        <member name="M:Prism.Events.BackgroundEventSubscription.InvokeAction(System.Action)">
            <summary>
            Invokes the specified <see cref="T:System.Action"/> in an asynchronous thread by using a <see cref="T:System.Threading.Tasks.Task"/>.
            </summary>
            <param name="action">The action to execute.</param>
        </member>
        <member name="T:Prism.Events.BackgroundEventSubscription`1">
            <summary>
            Extends <see cref="T:Prism.Events.EventSubscription`1"/> to invoke the <see cref="P:Prism.Events.EventSubscription`1.Action"/> delegate in a background thread.
            </summary>
            <typeparam name="TPayload">The type to use for the generic <see cref="T:System.Action`1"/> and <see cref="T:System.Predicate`1"/> types.</typeparam>
        </member>
        <member name="M:Prism.Events.BackgroundEventSubscription`1.#ctor(Prism.Events.IDelegateReference,Prism.Events.IDelegateReference)">
            <summary>
            Creates a new instance of <see cref="T:Prism.Events.BackgroundEventSubscription`1"/>.
            </summary>
            <param name="actionReference">A reference to a delegate of type <see cref="T:System.Action`1"/>.</param>
            <param name="filterReference">A reference to a delegate of type <see cref="T:System.Predicate`1"/>.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="actionReference"/> or <see paramref="filterReference"/> are <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">When the target of <paramref name="actionReference"/> is not of type <see cref="T:System.Action`1"/>,
            or the target of <paramref name="filterReference"/> is not of type <see cref="T:System.Predicate`1"/>.</exception>
        </member>
        <member name="M:Prism.Events.BackgroundEventSubscription`1.InvokeAction(System.Action{`0},`0)">
            <summary>
            Invokes the specified <see cref="T:System.Action`1"/> in an asynchronous thread by using a <see cref="!:ThreadPool"/>.
            </summary>
            <param name="action">The action to execute.</param>
            <param name="argument">The payload to pass <paramref name="action"/> while invoking it.</param>
        </member>
        <member name="T:Prism.Events.DataEventArgs`1">
            <summary>
            Generic arguments class to pass to event handlers that need to receive data.
            </summary>
            <typeparam name="TData">The type of data to pass.</typeparam>
        </member>
        <member name="M:Prism.Events.DataEventArgs`1.#ctor(`0)">
            <summary>
            Initializes the DataEventArgs class.
            </summary>
            <param name="value">Information related to the event.</param>
        </member>
        <member name="P:Prism.Events.DataEventArgs`1.Value">
            <summary>
            Gets the information related to the event.
            </summary>
            <value>Information related to the event.</value>
        </member>
        <member name="T:Prism.Events.DelegateReference">
            <summary>
            Represents a reference to a <see cref="T:System.Delegate"/> that may contain a
            <see cref="T:System.WeakReference"/> to the target. This class is used
            internally by the Prism Library.
            </summary>
        </member>
        <member name="M:Prism.Events.DelegateReference.#ctor(System.Delegate,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Events.DelegateReference"/>.
            </summary>
            <param name="delegate">The original <see cref="T:System.Delegate"/> to create a reference for.</param>
            <param name="keepReferenceAlive">If <see langword="false" /> the class will create a weak reference to the delegate, allowing it to be garbage collected. Otherwise it will keep a strong reference to the target.</param>
            <exception cref="T:System.ArgumentNullException">If the passed <paramref name="delegate"/> is not assignable to <see cref="T:System.Delegate"/>.</exception>
        </member>
        <member name="P:Prism.Events.DelegateReference.Target">
            <summary>
            Gets the <see cref="T:System.Delegate" /> (the target) referenced by the current <see cref="T:Prism.Events.DelegateReference"/> object.
            </summary>
            <value><see langword="null"/> if the object referenced by the current <see cref="T:Prism.Events.DelegateReference"/> object has been garbage collected; otherwise, a reference to the <see cref="T:System.Delegate"/> referenced by the current <see cref="T:Prism.Events.DelegateReference"/> object.</value>
        </member>
        <member name="T:Prism.Events.DispatcherEventSubscription">
            <summary>
             Extends <see cref="T:Prism.Events.EventSubscription"/> to invoke the <see cref="P:Prism.Events.EventSubscription.Action"/> delegate
             in a specific <see cref="T:System.Threading.SynchronizationContext"/>.
            </summary>
        </member>
        <member name="M:Prism.Events.DispatcherEventSubscription.#ctor(Prism.Events.IDelegateReference,System.Threading.SynchronizationContext)">
            <summary>
             Creates a new instance of <see cref="T:Prism.Events.BackgroundEventSubscription"/>.
            </summary>
            <param name="actionReference">A reference to a delegate of type <see cref="T:System.Action`1"/>.</param>
            <param name="context">The synchronization context to use for UI thread dispatching.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="actionReference"/> or <see paramref="filterReference"/> are <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">When the target of <paramref name="actionReference"/> is not of type <see cref="T:System.Action`1"/>.</exception>
        </member>
        <member name="M:Prism.Events.DispatcherEventSubscription.InvokeAction(System.Action)">
            <summary>
            Invokes the specified <see cref="T:System.Action`1"/> asynchronously in the specified <see cref="T:System.Threading.SynchronizationContext"/>.
            </summary>
            <param name="action">The action to execute.</param>
        </member>
        <member name="T:Prism.Events.DispatcherEventSubscription`1">
            <summary>
             Extends <see cref="T:Prism.Events.EventSubscription`1"/> to invoke the <see cref="P:Prism.Events.EventSubscription`1.Action"/> delegate
             in a specific <see cref="T:System.Threading.SynchronizationContext"/>.
            </summary>
             <typeparam name="TPayload">The type to use for the generic <see cref="T:System.Action`1"/> and <see cref="T:System.Predicate`1"/> types.</typeparam>
        </member>
        <member name="M:Prism.Events.DispatcherEventSubscription`1.#ctor(Prism.Events.IDelegateReference,Prism.Events.IDelegateReference,System.Threading.SynchronizationContext)">
            <summary>
             Creates a new instance of <see cref="T:Prism.Events.BackgroundEventSubscription`1"/>.
            </summary>
            <param name="actionReference">A reference to a delegate of type <see cref="T:System.Action`1"/>.</param>
            <param name="filterReference">A reference to a delegate of type <see cref="T:System.Predicate`1"/>.</param>
            <param name="context">The synchronization context to use for UI thread dispatching.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="actionReference"/> or <see paramref="filterReference"/> are <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">When the target of <paramref name="actionReference"/> is not of type <see cref="T:System.Action`1"/>,
            or the target of <paramref name="filterReference"/> is not of type <see cref="T:System.Predicate`1"/>.</exception>
        </member>
        <member name="M:Prism.Events.DispatcherEventSubscription`1.InvokeAction(System.Action{`0},`0)">
            <summary>
            Invokes the specified <see cref="T:System.Action`1"/> asynchronously in the specified <see cref="T:System.Threading.SynchronizationContext"/>.
            </summary>
            <param name="action">The action to execute.</param>
            <param name="argument">The payload to pass <paramref name="action"/> while invoking it.</param>
        </member>
        <member name="T:Prism.Events.EventAggregator">
            <summary>
            Implements <see cref="T:Prism.Events.IEventAggregator"/>.
            </summary>
        </member>
        <member name="M:Prism.Events.EventAggregator.GetEvent``1">
            <summary>
            Gets the single instance of the event managed by this EventAggregator. Multiple calls to this method with the same <typeparamref name="TEventType"/> returns the same event instance.
            </summary>
            <typeparam name="TEventType">The type of event to get. This must inherit from <see cref="T:Prism.Events.EventBase"/>.</typeparam>
            <returns>A singleton instance of an event object of type <typeparamref name="TEventType"/>.</returns>
        </member>
        <member name="T:Prism.Events.EventBase">
            <summary>
             Defines a base class to publish and subscribe to events.
            </summary>
        </member>
        <member name="P:Prism.Events.EventBase.SynchronizationContext">
            <summary>
            Allows the SynchronizationContext to be set by the EventAggregator for UI Thread Dispatching
            </summary>
        </member>
        <member name="P:Prism.Events.EventBase.Subscriptions">
            <summary>
            Gets the list of current subscriptions.
            </summary>
            <value>The current subscribers.</value>
        </member>
        <member name="M:Prism.Events.EventBase.InternalSubscribe(Prism.Events.IEventSubscription)">
            <summary>
            Adds the specified <see cref="T:Prism.Events.IEventSubscription"/> to the subscribers' collection.
            </summary>
            <param name="eventSubscription">The subscriber.</param>
            <returns>The <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies every subscriber.</returns>
            <remarks>
            Adds the subscription to the internal list and assigns it a new <see cref="T:Prism.Events.SubscriptionToken"/>.
            </remarks>
        </member>
        <member name="M:Prism.Events.EventBase.InternalPublish(System.Object[])">
            <summary>
            Calls all the execution strategies exposed by the list of <see cref="T:Prism.Events.IEventSubscription"/>.
            </summary>
            <param name="arguments">The arguments that will be passed to the listeners.</param>
            <remarks>Before executing the strategies, this class will prune all the subscribers from the
            list that return a <see langword="null" /> <see cref="T:System.Action`1"/> when calling the
            <see cref="M:Prism.Events.IEventSubscription.GetExecutionStrategy"/> method.</remarks>
        </member>
        <member name="M:Prism.Events.EventBase.Unsubscribe(Prism.Events.SubscriptionToken)">
            <summary>
            Removes the subscriber matching the <see cref="T:Prism.Events.SubscriptionToken"/>.
            </summary>
            <param name="token">The <see cref="T:Prism.Events.SubscriptionToken"/> returned by <see cref="T:Prism.Events.EventBase"/> while subscribing to the event.</param>
        </member>
        <member name="M:Prism.Events.EventBase.Contains(Prism.Events.SubscriptionToken)">
            <summary>
            Returns <see langword="true"/> if there is a subscriber matching <see cref="T:Prism.Events.SubscriptionToken"/>.
            </summary>
            <param name="token">The <see cref="T:Prism.Events.SubscriptionToken"/> returned by <see cref="T:Prism.Events.EventBase"/> while subscribing to the event.</param>
            <returns><see langword="true"/> if there is a <see cref="T:Prism.Events.SubscriptionToken"/> that matches; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="T:Prism.Events.EventSubscription">
            <summary>
            Provides a way to retrieve a <see cref="T:System.Delegate"/> to execute an action depending
            on the value of a second filter predicate that returns true if the action should execute.
            </summary>
        </member>
        <member name="M:Prism.Events.EventSubscription.#ctor(Prism.Events.IDelegateReference)">
            <summary>
             Creates a new instance of <see cref="T:Prism.Events.EventSubscription"/>.
            </summary>
            <param name="actionReference">A reference to a delegate of type <see cref="T:System.Action"/>.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="actionReference"/> or <see paramref="filterReference"/> are <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">When the target of <paramref name="actionReference"/> is not of type <see cref="T:System.Action"/>.</exception>
        </member>
        <member name="P:Prism.Events.EventSubscription.Action">
            <summary>
            Gets the target <see cref="T:System.Action"/> that is referenced by the <see cref="T:Prism.Events.IDelegateReference"/>.
            </summary>
            <value>An <see cref="T:System.Action"/> or <see langword="null" /> if the referenced target is not alive.</value>
        </member>
        <member name="P:Prism.Events.EventSubscription.SubscriptionToken">
            <summary>
            Gets or sets a <see cref="P:Prism.Events.EventSubscription.SubscriptionToken"/> that identifies this <see cref="T:Prism.Events.IEventSubscription"/>.
            </summary>
            <value>A token that identifies this <see cref="T:Prism.Events.IEventSubscription"/>.</value>
        </member>
        <member name="M:Prism.Events.EventSubscription.GetExecutionStrategy">
            <summary>
            Gets the execution strategy to publish this event.
            </summary>
            <returns>An <see cref="T:System.Action"/> with the execution strategy, or <see langword="null" /> if the <see cref="T:Prism.Events.IEventSubscription"/> is no longer valid.</returns>
            <remarks>
            If <see cref="P:Prism.Events.EventSubscription.Action"/>is no longer valid because it was
            garbage collected, this method will return <see langword="null" />.
            Otherwise it will return a delegate that evaluates the <see cref="!:Filter"/> and if it
            returns <see langword="true" /> will then call <see cref="M:Prism.Events.EventSubscription.InvokeAction(System.Action)"/>. The returned
            delegate holds a hard reference to the <see cref="P:Prism.Events.EventSubscription.Action"/> target
            <see cref="T:System.Delegate">delegates</see>. As long as the returned delegate is not garbage collected,
            the <see cref="P:Prism.Events.EventSubscription.Action"/> references delegates won't get collected either.
            </remarks>
        </member>
        <member name="M:Prism.Events.EventSubscription.InvokeAction(System.Action)">
            <summary>
            Invokes the specified <see cref="T:System.Action`1"/> synchronously when not overridden.
            </summary>
            <param name="action">The action to execute.</param>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="action"/> is null.</exception>
        </member>
        <member name="T:Prism.Events.EventSubscription`1">
            <summary>
            Provides a way to retrieve a <see cref="T:System.Delegate"/> to execute an action depending
            on the value of a second filter predicate that returns true if the action should execute.
            </summary>
            <typeparam name="TPayload">The type to use for the generic <see cref="T:System.Action`1"/> and <see cref="T:System.Predicate`1"/> types.</typeparam>
        </member>
        <member name="M:Prism.Events.EventSubscription`1.#ctor(Prism.Events.IDelegateReference,Prism.Events.IDelegateReference)">
            <summary>
             Creates a new instance of <see cref="T:Prism.Events.EventSubscription`1"/>.
            </summary>
            <param name="actionReference">A reference to a delegate of type <see cref="T:System.Action`1"/>.</param>
            <param name="filterReference">A reference to a delegate of type <see cref="T:System.Predicate`1"/>.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="actionReference"/> or <see paramref="filterReference"/> are <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException">When the target of <paramref name="actionReference"/> is not of type <see cref="T:System.Action`1"/>,
            or the target of <paramref name="filterReference"/> is not of type <see cref="T:System.Predicate`1"/>.</exception>
        </member>
        <member name="P:Prism.Events.EventSubscription`1.Action">
            <summary>
            Gets the target <see cref="T:System.Action`1"/> that is referenced by the <see cref="T:Prism.Events.IDelegateReference"/>.
            </summary>
            <value>An <see cref="T:System.Action`1"/> or <see langword="null" /> if the referenced target is not alive.</value>
        </member>
        <member name="P:Prism.Events.EventSubscription`1.Filter">
            <summary>
            Gets the target <see cref="T:System.Predicate`1"/> that is referenced by the <see cref="T:Prism.Events.IDelegateReference"/>.
            </summary>
            <value>An <see cref="T:System.Predicate`1"/> or <see langword="null" /> if the referenced target is not alive.</value>
        </member>
        <member name="P:Prism.Events.EventSubscription`1.SubscriptionToken">
            <summary>
            Gets or sets a <see cref="P:Prism.Events.EventSubscription`1.SubscriptionToken"/> that identifies this <see cref="T:Prism.Events.IEventSubscription"/>.
            </summary>
            <value>A token that identifies this <see cref="T:Prism.Events.IEventSubscription"/>.</value>
        </member>
        <member name="M:Prism.Events.EventSubscription`1.GetExecutionStrategy">
            <summary>
            Gets the execution strategy to publish this event.
            </summary>
            <returns>An <see cref="T:System.Action`1"/> with the execution strategy, or <see langword="null" /> if the <see cref="T:Prism.Events.IEventSubscription"/> is no longer valid.</returns>
            <remarks>
            If <see cref="P:Prism.Events.EventSubscription`1.Action"/> or <see cref="P:Prism.Events.EventSubscription`1.Filter"/> are no longer valid because they were
            garbage collected, this method will return <see langword="null" />.
            Otherwise it will return a delegate that evaluates the <see cref="P:Prism.Events.EventSubscription`1.Filter"/> and if it
            returns <see langword="true" /> will then call <see cref="M:Prism.Events.EventSubscription`1.InvokeAction(System.Action{`0},`0)"/>. The returned
            delegate holds hard references to the <see cref="P:Prism.Events.EventSubscription`1.Action"/> and <see cref="P:Prism.Events.EventSubscription`1.Filter"/> target
            <see cref="T:System.Delegate">delegates</see>. As long as the returned delegate is not garbage collected,
            the <see cref="P:Prism.Events.EventSubscription`1.Action"/> and <see cref="P:Prism.Events.EventSubscription`1.Filter"/> references delegates won't get collected either.
            </remarks>
        </member>
        <member name="M:Prism.Events.EventSubscription`1.InvokeAction(System.Action{`0},`0)">
            <summary>
            Invokes the specified <see cref="T:System.Action`1"/> synchronously when not overridden.
            </summary>
            <param name="action">The action to execute.</param>
            <param name="argument">The payload to pass <paramref name="action"/> while invoking it.</param>
            <exception cref="T:System.ArgumentNullException">An <see cref="T:System.ArgumentNullException"/> is thrown if <paramref name="action"/> is null.</exception>
        </member>
        <member name="T:Prism.Events.IDelegateReference">
            <summary>
            Represents a reference to a <see cref="T:System.Delegate"/>.
            </summary>
        </member>
        <member name="P:Prism.Events.IDelegateReference.Target">
            <summary>
            Gets the referenced <see cref="T:System.Delegate" /> object.
            </summary>
            <value>A <see cref="T:System.Delegate"/> instance if the target is valid; otherwise <see langword="null"/>.</value>
        </member>
        <member name="T:Prism.Events.IEventAggregator">
            <summary>
            Defines an interface to get instances of an event type.
            </summary>
        </member>
        <member name="M:Prism.Events.IEventAggregator.GetEvent``1">
            <summary>
            Gets an instance of an event type.
            </summary>
            <typeparam name="TEventType">The type of event to get.</typeparam>
            <returns>An instance of an event object of type <typeparamref name="TEventType"/>.</returns>
        </member>
        <member name="T:Prism.Events.IEventSubscription">
            <summary>
             Defines a contract for an event subscription to be used by <see cref="T:Prism.Events.EventBase"/>.
            </summary>
        </member>
        <member name="P:Prism.Events.IEventSubscription.SubscriptionToken">
            <summary>
            Gets or sets a <see cref="P:Prism.Events.IEventSubscription.SubscriptionToken"/> that identifies this <see cref="T:Prism.Events.IEventSubscription"/>.
            </summary>
            <value>A token that identifies this <see cref="T:Prism.Events.IEventSubscription"/>.</value>
        </member>
        <member name="M:Prism.Events.IEventSubscription.GetExecutionStrategy">
            <summary>
            Gets the execution strategy to publish this event.
            </summary>
            <returns>An <see cref="T:System.Action`1"/> with the execution strategy, or <see langword="null" /> if the <see cref="T:Prism.Events.IEventSubscription"/> is no longer valid.</returns>
        </member>
        <member name="T:Prism.Events.PubSubEvent">
            <summary>
            Defines a class that manages publication and subscription to events.
            </summary>
        </member>
        <member name="M:Prism.Events.PubSubEvent.Subscribe(System.Action)">
            <summary>
            Subscribes a delegate to an event that will be published on the <see cref="F:Prism.Events.ThreadOption.PublisherThread"/>.
            <see cref="T:Prism.Events.PubSubEvent"/> will maintain a <see cref="T:System.WeakReference"/> to the target of the supplied <paramref name="action"/> delegate.
            </summary>
            <param name="action">The delegate that gets executed when the event is published.</param>
            <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
            <remarks>
            The PubSubEvent collection is thread-safe.
            </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent.Subscribe(System.Action,Prism.Events.ThreadOption)">
            <summary>
            Subscribes a delegate to an event.
            PubSubEvent will maintain a <see cref="T:System.WeakReference"/> to the Target of the supplied <paramref name="action"/> delegate.
            </summary>
            <param name="action">The delegate that gets executed when the event is raised.</param>
            <param name="threadOption">Specifies on which thread to receive the delegate callback.</param>
            <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
            <remarks>
            The PubSubEvent collection is thread-safe.
            </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent.Subscribe(System.Action,System.Boolean)">
            <summary>
            Subscribes a delegate to an event that will be published on the <see cref="F:Prism.Events.ThreadOption.PublisherThread"/>.
            </summary>
            <param name="action">The delegate that gets executed when the event is published.</param>
            <param name="keepSubscriberReferenceAlive">When <see langword="true"/>, the <see cref="T:Prism.Events.PubSubEvent"/> keeps a reference to the subscriber so it does not get garbage collected.</param>
            <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
            <remarks>
            If <paramref name="keepSubscriberReferenceAlive"/> is set to <see langword="false" />, <see cref="T:Prism.Events.PubSubEvent"/> will maintain a <see cref="T:System.WeakReference"/> to the Target of the supplied <paramref name="action"/> delegate.
            If not using a WeakReference (<paramref name="keepSubscriberReferenceAlive"/> is <see langword="true" />), the user must explicitly call Unsubscribe for the event when disposing the subscriber in order to avoid memory leaks or unexpected behavior.
            <para/>
            The PubSubEvent collection is thread-safe.
            </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent.Subscribe(System.Action,Prism.Events.ThreadOption,System.Boolean)">
            <summary>
            Subscribes a delegate to an event.
            </summary>
            <param name="action">The delegate that gets executed when the event is published.</param>
            <param name="threadOption">Specifies on which thread to receive the delegate callback.</param>
            <param name="keepSubscriberReferenceAlive">When <see langword="true"/>, the <see cref="T:Prism.Events.PubSubEvent"/> keeps a reference to the subscriber so it does not get garbage collected.</param>
            <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
            <remarks>
            If <paramref name="keepSubscriberReferenceAlive"/> is set to <see langword="false" />, <see cref="T:Prism.Events.PubSubEvent"/> will maintain a <see cref="T:System.WeakReference"/> to the Target of the supplied <paramref name="action"/> delegate.
            If not using a WeakReference (<paramref name="keepSubscriberReferenceAlive"/> is <see langword="true" />), the user must explicitly call Unsubscribe for the event when disposing the subscriber in order to avoid memory leaks or unexpected behavior.
            <para/>
            The PubSubEvent collection is thread-safe.
            </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent.Publish">
            <summary>
            Publishes the <see cref="T:Prism.Events.PubSubEvent"/>.
            </summary>
        </member>
        <member name="M:Prism.Events.PubSubEvent.Unsubscribe(System.Action)">
            <summary>
            Removes the first subscriber matching <see cref="T:System.Action"/> from the subscribers' list.
            </summary>
            <param name="subscriber">The <see cref="T:System.Action"/> used when subscribing to the event.</param>
        </member>
        <member name="M:Prism.Events.PubSubEvent.Contains(System.Action)">
            <summary>
            Returns <see langword="true"/> if there is a subscriber matching <see cref="T:System.Action"/>.
            </summary>
            <param name="subscriber">The <see cref="T:System.Action"/> used when subscribing to the event.</param>
            <returns><see langword="true"/> if there is an <see cref="T:System.Action"/> that matches; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="T:Prism.Events.PubSubEvent`1">
            <summary>
            Defines a class that manages publication and subscription to events.
            </summary>
            <typeparam name="TPayload">The type of message that will be passed to the subscribers.</typeparam>
        </member>
        <member name="M:Prism.Events.PubSubEvent`1.Subscribe(System.Action{`0})">
            <summary>
            Subscribes a delegate to an event that will be published on the <see cref="F:Prism.Events.ThreadOption.PublisherThread"/>.
            <see cref="T:Prism.Events.PubSubEvent`1"/> will maintain a <see cref="T:System.WeakReference"/> to the target of the supplied <paramref name="action"/> delegate.
            </summary>
            <param name="action">The delegate that gets executed when the event is published.</param>
            <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
            <remarks>
            The PubSubEvent collection is thread-safe.
            </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent`1.Subscribe(System.Action{`0},Prism.Events.ThreadOption)">
            <summary>
            Subscribes a delegate to an event.
            PubSubEvent will maintain a <see cref="T:System.WeakReference"/> to the Target of the supplied <paramref name="action"/> delegate.
            </summary>
            <param name="action">The delegate that gets executed when the event is raised.</param>
            <param name="threadOption">Specifies on which thread to receive the delegate callback.</param>
            <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
            <remarks>
            The PubSubEvent collection is thread-safe.
            </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent`1.Subscribe(System.Action{`0},System.Boolean)">
            <summary>
            Subscribes a delegate to an event that will be published on the <see cref="F:Prism.Events.ThreadOption.PublisherThread"/>.
            </summary>
            <param name="action">The delegate that gets executed when the event is published.</param>
            <param name="keepSubscriberReferenceAlive">When <see langword="true"/>, the <see cref="T:Prism.Events.PubSubEvent`1"/> keeps a reference to the subscriber so it does not get garbage collected.</param>
            <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
            <remarks>
            If <paramref name="keepSubscriberReferenceAlive"/> is set to <see langword="false" />, <see cref="T:Prism.Events.PubSubEvent`1"/> will maintain a <see cref="T:System.WeakReference"/> to the Target of the supplied <paramref name="action"/> delegate.
            If not using a WeakReference (<paramref name="keepSubscriberReferenceAlive"/> is <see langword="true" />), the user must explicitly call Unsubscribe for the event when disposing the subscriber in order to avoid memory leaks or unexpected behavior.
            <para/>
            The PubSubEvent collection is thread-safe.
            </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent`1.Subscribe(System.Action{`0},Prism.Events.ThreadOption,System.Boolean)">
            <summary>
            Subscribes a delegate to an event.
            </summary>
            <param name="action">The delegate that gets executed when the event is published.</param>
            <param name="threadOption">Specifies on which thread to receive the delegate callback.</param>
            <param name="keepSubscriberReferenceAlive">When <see langword="true"/>, the <see cref="T:Prism.Events.PubSubEvent`1"/> keeps a reference to the subscriber so it does not get garbage collected.</param>
            <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
            <remarks>
            If <paramref name="keepSubscriberReferenceAlive"/> is set to <see langword="false" />, <see cref="T:Prism.Events.PubSubEvent`1"/> will maintain a <see cref="T:System.WeakReference"/> to the Target of the supplied <paramref name="action"/> delegate.
            If not using a WeakReference (<paramref name="keepSubscriberReferenceAlive"/> is <see langword="true" />), the user must explicitly call Unsubscribe for the event when disposing the subscriber in order to avoid memory leaks or unexpected behavior.
            <para/>
            The PubSubEvent collection is thread-safe.
            </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent`1.Subscribe(System.Action{`0},Prism.Events.ThreadOption,System.Boolean,System.Predicate{`0})">
             <summary>
             Subscribes a delegate to an event.
             </summary>
             <param name="action">The delegate that gets executed when the event is published.</param>
             <param name="threadOption">Specifies on which thread to receive the delegate callback.</param>
             <param name="keepSubscriberReferenceAlive">When <see langword="true"/>, the <see cref="T:Prism.Events.PubSubEvent`1"/> keeps a reference to the subscriber so it does not get garbage collected.</param>
             <param name="filter">Filter to evaluate if the subscriber should receive the event.</param>
             <returns>A <see cref="T:Prism.Events.SubscriptionToken"/> that uniquely identifies the added subscription.</returns>
             <remarks>
             If <paramref name="keepSubscriberReferenceAlive"/> is set to <see langword="false" />, <see cref="T:Prism.Events.PubSubEvent`1"/> will maintain a <see cref="T:System.WeakReference"/> to the Target of the supplied <paramref name="action"/> delegate.
             If not using a WeakReference (<paramref name="keepSubscriberReferenceAlive"/> is <see langword="true" />), the user must explicitly call Unsubscribe for the event when disposing the subscriber in order to avoid memory leaks or unexpected behavior.
            
             The PubSubEvent collection is thread-safe.
             </remarks>
        </member>
        <member name="M:Prism.Events.PubSubEvent`1.Publish(`0)">
            <summary>
            Publishes the <see cref="T:Prism.Events.PubSubEvent`1"/>.
            </summary>
            <param name="payload">Message to pass to the subscribers.</param>
        </member>
        <member name="M:Prism.Events.PubSubEvent`1.Unsubscribe(System.Action{`0})">
            <summary>
            Removes the first subscriber matching <see cref="T:System.Action`1"/> from the subscribers' list.
            </summary>
            <param name="subscriber">The <see cref="T:System.Action`1"/> used when subscribing to the event.</param>
        </member>
        <member name="M:Prism.Events.PubSubEvent`1.Contains(System.Action{`0})">
            <summary>
            Returns <see langword="true"/> if there is a subscriber matching <see cref="T:System.Action`1"/>.
            </summary>
            <param name="subscriber">The <see cref="T:System.Action`1"/> used when subscribing to the event.</param>
            <returns><see langword="true"/> if there is an <see cref="T:System.Action`1"/> that matches; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="T:Prism.Events.SubscriptionToken">
            <summary>
            Subscription token returned from <see cref="T:Prism.Events.EventBase"/> on subscribe.
            </summary>
        </member>
        <member name="M:Prism.Events.SubscriptionToken.#ctor(System.Action{Prism.Events.SubscriptionToken})">
            <summary>
            Initializes a new instance of <see cref="T:Prism.Events.SubscriptionToken"/>.
            </summary>
        </member>
        <member name="M:Prism.Events.SubscriptionToken.Equals(Prism.Events.SubscriptionToken)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <returns>
            <see langword="true"/> if the current object is equal to the <paramref name="other" /> parameter; otherwise, <see langword="false"/>.
            </returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:Prism.Events.SubscriptionToken.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />.
            </summary>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />; otherwise, false.
            </returns>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />. </param>
            <exception cref="T:System.NullReferenceException">The <paramref name="obj" /> parameter is null.</exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Prism.Events.SubscriptionToken.GetHashCode">
            <summary>
            Serves as a hash function for a particular type. 
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object" />.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Prism.Events.SubscriptionToken.Dispose">
            <summary>
            Disposes the SubscriptionToken, removing the subscription from the corresponding <see cref="T:Prism.Events.EventBase"/>.
            </summary>
        </member>
        <member name="T:Prism.Events.ThreadOption">
            <summary>
            Specifies on which thread a <see cref="T:Prism.Events.PubSubEvent`1"/> subscriber will be called.
            </summary>
        </member>
        <member name="F:Prism.Events.ThreadOption.PublisherThread">
            <summary>
            The call is done on the same thread on which the <see cref="T:Prism.Events.PubSubEvent`1"/> was published.
            </summary>
        </member>
        <member name="F:Prism.Events.ThreadOption.UIThread">
            <summary>
            The call is done on the UI thread.
            </summary>
        </member>
        <member name="F:Prism.Events.ThreadOption.BackgroundThread">
            <summary>
            The call is done asynchronously on a background thread.
            </summary>
        </member>
        <member name="T:Prism.IActiveAware">
            <summary>
            Interface that defines if the object instance is active
            and notifies when the activity changes.
            </summary>
        </member>
        <member name="P:Prism.IActiveAware.IsActive">
            <summary>
            Gets or sets a value indicating whether the object is active.
            </summary>
            <value><see langword="true" /> if the object is active; otherwise <see langword="false" />.</value>
        </member>
        <member name="E:Prism.IActiveAware.IsActiveChanged">
            <summary>
            Notifies that the value for <see cref="P:Prism.IActiveAware.IsActive"/> property has changed.
            </summary>
        </member>
        <member name="P:Prism.Ioc.IContainerExtension`1.Instance">
            <summary>
            The instance of the wrapped container
            </summary>
        </member>
        <member name="P:Prism.Ioc.IContainerExtension.SupportsModules">
            <summary>
            Determines if the container can be used with modules.
            </summary>
            <remarks>Only containers that are mutable can support modules.</remarks>
        </member>
        <member name="M:Prism.Ioc.IContainerExtension.FinalizeExtension">
            <summary>
            Used to perform any final steps for configuring the extension that may be required by the container.
            </summary>
        </member>
        <member name="M:Prism.Ioc.IContainerExtension.ResolveViewModelForView(System.Object,System.Type)">
            <summary>
            Used as the ViewModel resolver for ViewModelLocationProvider.SetDefaultViewModelFactory
            </summary>
            <param name="view">The view instance</param>
            <param name="viewModelType">The ViewModel type to create</param>
            <returns></returns>
        </member>
        <member name="T:Prism.Logging.Category">
            <summary>
            Defines values for the categories used by <see cref="T:Prism.Logging.ILoggerFacade"/>.
            </summary>
        </member>
        <member name="F:Prism.Logging.Category.Debug">
            <summary>
            Debug category.
            </summary>
        </member>
        <member name="F:Prism.Logging.Category.Exception">
            <summary>
            Exception category.
            </summary>
        </member>
        <member name="F:Prism.Logging.Category.Info">
            <summary>
            Informational category.
            </summary>
        </member>
        <member name="F:Prism.Logging.Category.Warn">
            <summary>
            Warning category.
            </summary>
        </member>
        <member name="T:Prism.Logging.DebugLogger">
            <summary>
            Implementation of <see cref="T:Prism.Logging.ILoggerFacade"/> that logs into a message into the Debug.Listeners collection.
            </summary>
        </member>
        <member name="M:Prism.Logging.DebugLogger.Log(System.String,Prism.Logging.Category,Prism.Logging.Priority)">
            <summary>
            Write a new log entry with the specified category and priority.
            </summary>
            <param name="message">Message body to log.</param>
            <param name="category">Category of the entry.</param>
            <param name="priority">The priority of the entry.</param>
        </member>
        <member name="T:Prism.Logging.EmptyLogger">
            <summary>
            Implementation of <see cref="T:Prism.Logging.ILoggerFacade"/> that does nothing. This
            implementation is useful when the application does not need logging
            but there are infrastructure pieces that assume there is a logger.
            </summary>
        </member>
        <member name="M:Prism.Logging.EmptyLogger.Log(System.String,Prism.Logging.Category,Prism.Logging.Priority)">
            <summary>
            This method does nothing.
            </summary>
            <param name="message">Message body to log.</param>
            <param name="category">Category of the entry.</param>
            <param name="priority">The priority of the entry.</param>
        </member>
        <member name="T:Prism.Logging.ILoggerFacade">
            <summary>
            Defines a simple logger façade to be used by the Prism Library.
            </summary>
        </member>
        <member name="M:Prism.Logging.ILoggerFacade.Log(System.String,Prism.Logging.Category,Prism.Logging.Priority)">
            <summary>
            Write a new log entry with the specified category and priority.
            </summary>
            <param name="message">Message body to log.</param>
            <param name="category">Category of the entry.</param>
            <param name="priority">The priority of the entry.</param>
        </member>
        <member name="T:Prism.Logging.Priority">
            <summary>
            Defines values for the priorities used by <see cref="T:Prism.Logging.ILoggerFacade"/>.
            </summary>
        </member>
        <member name="F:Prism.Logging.Priority.None">
            <summary>
            No priority specified.
            </summary>
        </member>
        <member name="F:Prism.Logging.Priority.High">
            <summary>
            High priority entry.
            </summary>
        </member>
        <member name="F:Prism.Logging.Priority.Medium">
            <summary>
            Medium priority entry.
            </summary>
        </member>
        <member name="F:Prism.Logging.Priority.Low">
            <summary>
            Low priority entry.
            </summary>
        </member>
        <member name="T:Prism.Mvvm.BindableBase">
            <summary>
            Implementation of <see cref="T:System.ComponentModel.INotifyPropertyChanged"/> to simplify models.
            </summary>
        </member>
        <member name="E:Prism.Mvvm.BindableBase.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="M:Prism.Mvvm.BindableBase.SetProperty``1(``0@,``0,System.String)">
            <summary>
            Checks if a property already matches a desired value. Sets the property and
            notifies listeners only when necessary.
            </summary>
            <typeparam name="T">Type of the property.</typeparam>
            <param name="storage">Reference to a property with both getter and setter.</param>
            <param name="value">Desired value for the property.</param>
            <param name="propertyName">Name of the property used to notify listeners. This
            value is optional and can be provided automatically when invoked from compilers that
            support CallerMemberName.</param>
            <returns>True if the value was changed, false if the existing value matched the
            desired value.</returns>
        </member>
        <member name="M:Prism.Mvvm.BindableBase.SetProperty``1(``0@,``0,System.Action,System.String)">
            <summary>
            Checks if a property already matches a desired value. Sets the property and
            notifies listeners only when necessary.
            </summary>
            <typeparam name="T">Type of the property.</typeparam>
            <param name="storage">Reference to a property with both getter and setter.</param>
            <param name="value">Desired value for the property.</param>
            <param name="propertyName">Name of the property used to notify listeners. This
            value is optional and can be provided automatically when invoked from compilers that
            support CallerMemberName.</param>
            <param name="onChanged">Action that is called after the property value has been changed.</param>
            <returns>True if the value was changed, false if the existing value matched the
            desired value.</returns>
        </member>
        <member name="M:Prism.Mvvm.BindableBase.RaisePropertyChanged(System.String)">
            <summary>
            Raises this object's PropertyChanged event.
            </summary>
            <param name="propertyName">Name of the property used to notify listeners. This
            value is optional and can be provided automatically when invoked from compilers
            that support <see cref="T:System.Runtime.CompilerServices.CallerMemberNameAttribute"/>.</param>
        </member>
        <member name="M:Prism.Mvvm.BindableBase.OnPropertyChanged(System.String)">
            <summary>
            Notifies listeners that a property value has changed.
            </summary>
            <param name="propertyName">Name of the property used to notify listeners. This
            value is optional and can be provided automatically when invoked from compilers
            that support <see cref="T:System.Runtime.CompilerServices.CallerMemberNameAttribute"/>.</param>
        </member>
        <member name="M:Prism.Mvvm.BindableBase.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Raises this object's PropertyChanged event.
            </summary>
            <param name="args">The PropertyChangedEventArgs</param>
        </member>
        <member name="M:Prism.Mvvm.BindableBase.OnPropertyChanged``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Raises this object's PropertyChanged event.
            </summary>
            <typeparam name="T">The type of the property that has a new value</typeparam>
            <param name="propertyExpression">A Lambda expression representing the property that has a new value.</param>
        </member>
        <member name="T:Prism.Mvvm.ErrorsContainer`1">
            <summary>
            Manages validation errors for an object, notifying when the error state changes.
            </summary>
            <typeparam name="T">The type of the error object.</typeparam>
        </member>
        <member name="M:Prism.Mvvm.ErrorsContainer`1.#ctor(System.Action{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Prism.Mvvm.ErrorsContainer`1"/> class.
            </summary>
            <param name="raiseErrorsChanged">The action that invoked if when errors are added for an object./>
            event.</param>
        </member>
        <member name="P:Prism.Mvvm.ErrorsContainer`1.HasErrors">
            <summary>
            Gets a value indicating whether the object has validation errors. 
            </summary>
        </member>
        <member name="M:Prism.Mvvm.ErrorsContainer`1.GetErrors(System.String)">
            <summary>
            Gets the validation errors for a specified property.
            </summary>
            <param name="propertyName">The name of the property.</param>
            <returns>The validation errors of type <typeparamref name="T"/> for the property.</returns>
        </member>
        <member name="M:Prism.Mvvm.ErrorsContainer`1.ClearErrors">
            <summary>
            Clears all errors.
            </summary>
        </member>
        <member name="M:Prism.Mvvm.ErrorsContainer`1.ClearErrors``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Clears the errors for the property indicated by the property expression.
            </summary>
            <typeparam name="TProperty">The property type.</typeparam>
            <param name="propertyExpression">The expression indicating a property.</param>
            <example>
                container.ClearErrors(()=>SomeProperty);
            </example>
        </member>
        <member name="M:Prism.Mvvm.ErrorsContainer`1.ClearErrors(System.String)">
            <summary>
            Clears the errors for a property.
            </summary>
            <param name="propertyName">The name of th property for which to clear errors.</param>
            <example>
                container.ClearErrors("SomeProperty");
            </example>
        </member>
        <member name="M:Prism.Mvvm.ErrorsContainer`1.SetErrors``1(System.Linq.Expressions.Expression{System.Func{``0}},System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Sets the validation errors for the specified property.
            </summary>
            <typeparam name="TProperty">The property type for which to set errors.</typeparam>
            <param name="propertyExpression">The <see cref="T:System.Linq.Expressions.Expression"/> indicating the property.</param>
            <param name="propertyErrors">The list of errors to set for the property.</param>
        </member>
        <member name="M:Prism.Mvvm.ErrorsContainer`1.SetErrors(System.String,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Sets the validation errors for the specified property.
            </summary>
            <remarks>
            If a change is detected then the errors changed event is raised.
            </remarks>
            <param name="propertyName">The name of the property.</param>
            <param name="newValidationResults">The new validation errors.</param>
        </member>
        <member name="T:Prism.Mvvm.PropertySupport">
            <summary>
             Provides support for extracting property information based on a property expression.
            </summary>
        </member>
        <member name="M:Prism.Mvvm.PropertySupport.ExtractPropertyName``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Extracts the property name from a property expression.
            </summary>
            <typeparam name="T">The object type containing the property specified in the expression.</typeparam>
            <param name="propertyExpression">The property expression (e.g. p => p.PropertyName)</param>
            <returns>The name of the property.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if the <paramref name="propertyExpression"/> is null.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the expression is:<br/>
                Not a <see cref="T:System.Linq.Expressions.MemberExpression"/><br/>
                The <see cref="T:System.Linq.Expressions.MemberExpression"/> does not represent a property.<br/>
                Or, the property is static.
            </exception>
        </member>
        <member name="M:Prism.Mvvm.PropertySupport.ExtractPropertyNameFromLambda(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Extracts the property name from a LambdaExpression.
            </summary>
            <param name="expression">The LambdaExpression</param>
            <returns>The name of the property.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if the <paramref name="expression"/> is null.</exception>
            <exception cref="T:System.ArgumentException">Thrown when the expression is:<br/>
                The <see cref="T:System.Linq.Expressions.MemberExpression"/> does not represent a property.<br/>
                Or, the property is static.
            </exception>
        </member>
        <member name="T:Prism.Mvvm.ViewModelLocationProvider">
            <summary>
            The ViewModelLocationProvider class locates the view model for the view that has the AutoWireViewModelChanged attached property set to true.
            The view model will be located and injected into the view's DataContext. To locate the view, two strategies are used: First the ViewModelLocationProvider
            will look to see if there is a view model factory registered for that view, if not it will try to infer the view model using a convention based approach.
            This class also provide methods for registering the view model factories,
            and also to override the default view model factory and the default view type to view model type resolver.
            </summary>
        </member>
        <member name="F:Prism.Mvvm.ViewModelLocationProvider._factories">
            <summary>
            A dictionary that contains all the registered factories for the views.
            </summary>
        </member>
        <member name="F:Prism.Mvvm.ViewModelLocationProvider._typeFactories">
            <summary>
            A dictionary that contains all the registered ViewModel types for the views.
            </summary>
        </member>
        <member name="F:Prism.Mvvm.ViewModelLocationProvider._defaultViewModelFactory">
            <summary>
            The default view model factory which provides the ViewModel type as a parameter.
            </summary>
        </member>
        <member name="F:Prism.Mvvm.ViewModelLocationProvider._defaultViewModelFactoryWithViewParameter">
            <summary>
            ViewModelfactory that provides the View instance and ViewModel type as parameters.
            </summary>
        </member>
        <member name="F:Prism.Mvvm.ViewModelLocationProvider._defaultViewTypeToViewModelTypeResolver">
            <summary>
            Default view type to view model type resolver, assumes the view model is in same assembly as the view type, but in the "ViewModels" namespace.
            </summary>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.SetDefaultViewModelFactory(System.Func{System.Type,System.Object})">
            <summary>
            Sets the default view model factory.
            </summary>
            <param name="viewModelFactory">The view model factory which provides the ViewModel type as a parameter.</param>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.SetDefaultViewModelFactory(System.Func{System.Object,System.Type,System.Object})">
            <summary>
            Sets the default view model factory.
            </summary>
            <param name="viewModelFactory">The view model factory that provides the View instance and ViewModel type as parameters.</param>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.SetDefaultViewTypeToViewModelTypeResolver(System.Func{System.Type,System.Type})">
            <summary>
            Sets the default view type to view model type resolver.
            </summary>
            <param name="viewTypeToViewModelTypeResolver">The view type to view model type resolver.</param>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.AutoWireViewModelChanged(System.Object,System.Action{System.Object,System.Object})">
            <summary>
            Automatically looks up the viewmodel that corresponds to the current view, using two strategies:
            It first looks to see if there is a mapping registered for that view, if not it will fallback to the convention based approach.
            </summary>
            <param name="view">The dependency object, typically a view.</param>
            <param name="setDataContextCallback">The call back to use to create the binding between the View and ViewModel</param>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.GetViewModelForView(System.Object)">
            <summary>
            Gets the view model for the specified view.
            </summary>
            <param name="view">The view that the view model wants.</param>
            <returns>The ViewModel that corresponds to the view passed as a parameter.</returns>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.GetViewModelTypeForView(System.Type)">
            <summary>
            Gets the ViewModel type for the specified view.
            </summary>
            <param name="view">The View that the ViewModel wants.</param>
            <returns>The ViewModel type that corresponds to the View.</returns>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.Register``1(System.Func{System.Object})">
            <summary>
            Registers the ViewModel factory for the specified view type.
            </summary>
            <typeparam name="T">The View</typeparam>
            <param name="factory">The ViewModel factory.</param>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.Register(System.String,System.Func{System.Object})">
            <summary>
            Registers the ViewModel factory for the specified view type name.
            </summary>
            <param name="viewTypeName">The name of the view type.</param>
            <param name="factory">The ViewModel factory.</param>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.Register``2">
            <summary>
            Registers a ViewModel type for the specified view type.
            </summary>
            <typeparam name="T">The View</typeparam>
            <typeparam name="VM">The ViewModel</typeparam>
        </member>
        <member name="M:Prism.Mvvm.ViewModelLocationProvider.Register(System.String,System.Type)">
            <summary>
            Registers a ViewModel type for the specified view.
            </summary>
            <param name="viewTypeName">The View type name</param>
            <param name="viewModelType">The ViewModel type</param>
        </member>
        <member name="T:Prism.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.CannotRegisterCompositeCommandInItself">
            <summary>
              Looks up a localized string similar to Cannot register a CompositeCommand in itself..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.CannotRegisterSameCommandTwice">
            <summary>
              Looks up a localized string similar to Cannot register the same command twice in the same CompositeCommand..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DefaultDebugLoggerPattern">
            <summary>
              Looks up a localized string similar to {1}: {2}. Priority: {3}. Timestamp:{0:u}..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DelegateCommandDelegatesCannotBeNull">
            <summary>
              Looks up a localized string similar to Neither the executeMethod nor the canExecuteMethod delegates can be null..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.DelegateCommandInvalidGenericPayloadType">
            <summary>
              Looks up a localized string similar to T for DelegateCommand&lt;T&gt; is not an object nor Nullable..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.EventAggregatorNotConstructedOnUIThread">
            <summary>
              Looks up a localized string similar to To use the UIThread option for subscribing, the EventAggregator must be constructed on the UI thread..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.InvalidDelegateRerefenceTypeException">
            <summary>
              Looks up a localized string similar to Invalid Delegate Reference Type Exception.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.InvalidPropertyNameException">
            <summary>
              Looks up a localized string similar to The entity does not contain a property with that name.
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.PropertySupport_ExpressionNotProperty_Exception">
            <summary>
              Looks up a localized string similar to The member access expression does not access a property..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.PropertySupport_NotMemberAccessExpression_Exception">
            <summary>
              Looks up a localized string similar to The expression is not a member access expression..
            </summary>
        </member>
        <member name="P:Prism.Properties.Resources.PropertySupport_StaticExpression_Exception">
            <summary>
              Looks up a localized string similar to The referenced property is a static property..
            </summary>
        </member>
    </members>
</doc>
