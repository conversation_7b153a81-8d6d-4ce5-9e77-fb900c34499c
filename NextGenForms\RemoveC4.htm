<!DOCTYPE html>
<html lang="en-US">
<head>
	<TITLE>VisiNET Mobile - Remove Code 4</TITLE>
	<meta charset="utf-8" />
	<!-- Compiled and minified CSS -->
	<link rel="stylesheet" type="text/css" href="materialize.min.css" />

	<link rel="stylesheet" type="text/css" href="MobileStyle.css" />

	<script src="jquery.min.js"></script>
	<!-- Compiled and minified JavaScript -->
	<script src="materialize.min.js"></script>

	<script src="clientutilities.js"></script>

	<script language="javascript">
		$(document).ready(function () {
		
			//handle form submition
			$("#Form").submit(function () {
				if ($(this)[0].checkValidity() == true) {
					var values = $(this).serialize();
					SubmitQuery(values, $(this).attr('action'));
				}
				return false;
			});
			
		});

        function AfterFillForm() {
			if ($("#incidentid").val() == '') {
				$("#message").show();
				DisableResetSubmit();
                //disable submit button. 
            }
        }
	</script>

</head>
<body class="FlexBody">
	<div class="header">
		<div class="row">
			<div class="s12">
				<div class="valign-wrapper">
					<h5 style="margin-left: 20px;">REMOVE CODE 4</h5>
				</div>
			</div>
		</div>
	</div>
	<form class="Flex-Form" action="RemoveCodeFourQuery.aspx?queryfile=removecodefour.qry" method="post" id="Form" name="Form">
		<div class="Flex-Form-MainContent">
			<div class="col s8">
				<div class="row" style="margin-top:20px">
					<div class="input-field col s12 m5">
						<label id="message" class="autoSubmitErrorMessage">This action requires you to be assigned to an incident. Your request was not submitted.</label>
					</div>
				</div>
				
			</div>
		</div>
		<input type="hidden" name="incidentid" id="incidentid">
	</form>

</body>
</html>







<!--<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Remove Code 4</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Remove Code 4</H4>
						<form action="RemoveCodeFourQuery.aspx?queryfile=removecodefour.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td><div id=Text name=Text>Incident Number:</div></td>
									<td><span id="incidentnumber" name="incidentnumber" formvalue="true"></span></td>
									<td><input type="hidden" name="incidentid" id="incidentid"></td>
								</tr>
							</table>
							<br>
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<div id=SubmitButton name=SubmitButton>
								<input type="submit" name="Query" id="Query" value="Submit">
							</div>
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<SCRIPT language="javascript">
		function window.onload()
		{
			Form.Query.focus();
		}


		function OnAfterFormFill()
		{
			if (Form.incidentid.value == '')
			{
				Text.innerText = 'This command requires you to be assigned to an incident. Your request was not submitted.';
				SubmitButton.innerText = '';
			}
		}

	</SCRIPT>
</HTML>-->
