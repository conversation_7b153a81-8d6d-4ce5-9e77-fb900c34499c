﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">	
    <title></title>
</head>
<body>
    <input type="hidden" name="PictometryLoadURL" id="PictometryLoadURL" />
    <input type="hidden" name="PictometryJavascriptURL" id="PictometryJavascriptURL" />
    <input type="hidden" name="PictometrySecretKey" id="PictometrySecretKey" />
    <input type="hidden" name="PictometryAPIKey" id="PictometryAPIKey" />
    <input type="hidden" name="PictometryHostURL" id="PictometryHostURL" />
    <input type="hidden" name="MapCenterLocationLat" id="MapCenterLocationLat" />
    <input type="hidden" name="MapCenterLocationLong" id="MapCenterLocationLong" />
	<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="onAfterFormFill()" />

    <script type="text/javascript">
        function onAfterFormFill()
        {
            var javascriptURL = document.getElementById('PictometryJavascriptURL').value;
            var loadURL = document.getElementById('PictometryLoadURL').value;
            var apiKey = document.getElementById('PictometryAPIKey').value;
            var secretKey = document.getElementById('PictometrySecretKey').value;
            var hostURL = document.getElementById('PictometryHostURL').value;
            var mapCenterLocationLat = document.getElementById('MapCenterLocationLat').value;
            var mapCenterLocationLong = document.getElementById('MapCenterLocationLong').value;
            // note: for now we hard code the zoom level to 19, which is the 45 degrees view.
            // in the future we might expose this to either mobile client configuration or calculate it on the fly based on the mobile map extent
            var pictometryZoomLevel = 19;
            window.location.href = hostURL +
                "?ApiKey=" + apiKey +
                "&SecretKey=" + secretKey +
                "&JavascriptUrl=" + javascriptURL +
                "&LoadUrl=" + loadURL +
                "&MapCenterLocationLat=" + mapCenterLocationLat +
                "&MapCenterLocationLong=" + mapCenterLocationLong +
                "&PictometryZoomLevel=" + pictometryZoomLevel;
        }
    </script>
</body>
</html>