<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <title>VisiNET Mobile - Person Check</title>
    <link href="normalstyle.css" type="text/css" rel="stylesheet">
</head>
<body>
    <table class="base" cellpadding="10" align="center" border="0" id="Table1">
        <tbody>
            <tr>
                <td valign="top">
                    <h4 align="center">
                        Person Check</h4>
                    <form action="PersonCheck.aspx?queryfile=PersonCheck.qry" method="post" id="Form"
                    name="Form">
                    <p>
                        <table>
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td width="28%"><b>Checks Requested:</b>
                                            </td>
                                            <td width="15%"><center>Wants</center>
                                            </td>
                                            <td width="17%"><center>License</center>
                                            </td>
                                            <td width="18%"><center>Registration</center>
                                            </td>
                                            <td width="25%"><center>Image</center></td>
											<!--<td width="25%"><center>Criminal History</center></td>-->
                                        </tr>
                                        <tr>
                                            <td>
                                            </td>
                                            <td><center><input id="doWants" type="checkbox" checked name="doWants" /></center>
                                            </td>
                                            <td><center><input id="doLicense" type="checkbox" checked name="doLicense" /></center>
                                            </td>
                                            <td><center><input id="doRegistration" type="checkbox" name="doRegistration" /></center>
                                            </td>
                                            <td><center><input id="doImage" type="checkbox" name="doImage" checked /></center>
											 </td>
											<!--<td><center><input id="doCriminalHistory" type="checkbox" name="doCriminalHistory" /></center></td>-->
                                        </tr>

                                        <!--<tr>
                                            <td></td>

                                            <td width="25%" style="text-align:center"><label id="labelRms">RMS</label></td>

                                            <td width="25%" style="text-align:center"><label id="labelRmsByName">RMS By Name</label></td>

                                            <td width="25%" style="text-align:center"><label id="labelRmsByDL">RMS By DL</label></td>
       
                                            <td width="25%" style="text-align:center"><label id="labelSupplment">Supplement</label></td>

                                            <td width="25%" style="text-align:center"><label id="labelSupplementByName">Supplement By Name</label></td>
                                            <td width="25%" style="text-align:center"><label id="labelSupplementByDL">Supplement By DL</label></td>
                                        </tr>--> 

                                         <!--<tr>
                                            <td>
                                            </td>

                                            <td><center><input id="doRms" type="checkbox" name="doRms" /></center></td>

                                            <td><center><input id="doRmsByName" type="checkbox" name="doRmsByName" /></center></td>

                                            <td><center><input id="doRmsByDL" type="checkbox" name="doRmsByDL" /></center></td>

                                            <td><center><input id="doSupplement" type="checkbox" name="doSupplement" /></center>
                                            </td> 
                                            <td><center><input id="doSupplementByName" type="checkbox" name="doSupplementByName" /></center>
                                            </td>
                                            <td><center><input id="doSupplementByDL" type="checkbox" name="doSupplementByDL" /></center>
                                            </td>
                                        </tr>-->

                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td width="33%">Last Name:</td>
                                            <td width="33%">First Name:</td>
                                            <td width="33%">MiddleName:</td>
                                        </tr>
                                        <tr>
                                            <td><input size="15" type="text" name="lastname" id="lastname" /></td>
                                            <td><input size="15" type="text" name="firstname" id="firstname" /></td>
                                            <td><input size="15" type="text" name="MiddleName" id="MiddleName" /></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td width="25%">DOB <font size="1">(MMDDYYYY)</font>:</td>
                                            <td width="10%">Age:</td>
                                            <td width="10%">Sex:</td>
                                            <td width="15%">Race:</td>
                                        </tr>
                                        <tr>
                                            <td><input size="8" id="dob" type="text" name="dob" numeric="true" /></td>
                                            <td><input size="3" id="Age" type="text" name="Age" numeric="true" /></td>
                                            <td>
                                                <!--<INPUT size="2" id="Sex" type="text" name="Sex">-->
                                                <xml id="genderstyle" src="genericselect.xsl"></xml>
                                                <span type="selectlist" id="gendervals" name="racevals">
                                                    <xml id="gendersource" src="gender.xml"></xml>
                                                </span>
                                            </td>
                                            <td>
                                                <!--<INPUT size="3" id="Race" type="text" name="Race">-->
                                                <xml id="racestyle" src="genericselect.xsl"></xml>
                                                <span type="selectlist" id="racevals" name="racevals">
                                                    <xml id="racesource" src="race.xml"></xml>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td width="30%">DL Number:</td>
                                            <td width="70%">State:</td>
                                        </tr>
                                        <tr>
                                            <td><input size="14" id="DLNumber" type="text" name="DLNumber" /></td>
                                            <td>
                                                <!--<INPUT size="2" id="State" type="text" maxlength=2 name="State" value="CA">-->
                                                <xml id="statestyle" src="genericselect.xsl"></xml>
                                                <span type="selectlist" id="statevals" name="statevals">
                                                    <xml id="statesource" src="state.xml"></xml>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table id="TableAddress">
                                        <tr>
                                            <td width="50%">Address Digits:</td>
                                            <td width="50%">City:</td>
                                        </tr>
                                        <tr>
                                            <td><input id="AddressDigits" type="text" size="3" maxlength="3" name="AddressDigits" /></td>
                                            <td><input id="City" type="text" size="14" maxlength="14" name="City" /></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table id="Table3">
                                        <tr>
                                            <td width="50%">Criminal History Routing:</td>
                                             <!--<TD width="50%">Tiburon:</TD>-->
                                        </tr>
                                        <tr>
                                            <td><input id="CriminalHistoryRouting" type="text" size="23" name="CriminalHistoryRouting" /></td>
                                            <!--<TD><INPUT id="Tiburon" type="text" size="23" name="Tiburon"></TD>-->
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </p>
                    <!--when click on this button, it call the validation.-->
                    <input type="button" name="Query" id="Button" value="Submit" onkeypress="validatepage()" onclick="validatepage()" />
                    <!--when validation is ok, it will call this button to submit the page.  This button is not visible because of it width is zero.-->
                    <input style="width: 0px;" tabindex="-1" type="submit" name="Submit" id="Submit" value="Submit" />
                    </form>
                </td>
            </tr>
        </tbody>
    </table>
    <script type="text/javascript" src="clientutilities.js"></script>
    <script type="text/javascript" language="javascript">
        function window.onload() {

            // Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
            Form.lastname.focus();
            PrepareValidation(Form);

 
            statevals.innerHTML = GenerateSelectBox("State", statesource, statestyle, false, false, false, 1, false, false);
            racevals.innerHTML = GenerateSelectBox("race", racesource, racestyle, false, false, false, 1, false, false);
            gendervals.innerHTML = GenerateSelectBox("Sex", gendersource, genderstyle, false, false, false, 1, false, false);

            Form.innerHTML = Form.innerHTML + GeneratePersonSupplementHiddenFields(); //ensure that the supplement is correctly setup.
            Form.innerHTML = Form.innerHTML + GeneratePersonRmsHiddenFields(); //ensure that rms is correctly setup.


        }
        function window.validatepage() {
            if (!HandlePersonSupplementFieldsBeforeSubmit()) return;
            if (!HandlePersonRmsFieldsBeforeSubmit()) return;

            Form.Submit.click();

        }
    </script>
</body>
</html>
