﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Allied Agencies</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">
        $(document).ready(function () {
            //handle form submition
            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });
        });
        function AfterFillForm() {
            if ($("#incidentid").val() == '') {
                $("#message").show();
                DisableResetSubmit();
            }
        }
    </script>

</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">Allied Agencies</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="main" action="AlliedAgencies.aspx?queryfile=AlliedAgencies.qry" method="post" id="Form" name="Form">
        <div class="row" style="padding-top:20px">
            <div class="s6">
                <div class="input-field col s12 m4" style="margin-left:12px;">
                    <input name="incidentnumber" id="incidentnumber" type="text" style="color:black;" readonly placeholder="">
                    <label for="incidentnumber">Incident Number</label>
                </div>
                <div class="input-field col s12 m2">
                    <input type="hidden" name="incidentid" id="incidentid">
                </div>
            </div>
        </div>
        <div class="row" style="padding-top:20px">
            <div class="s6">
                <div class="input-field col s12 m4" style="margin-left:12px;">
                    <label id="message" style="font-size: 20px !important;" class="autoSubmitErrorMessage">This command requires you to select an incident. Your request was not submitted.</label>
                </div>
            </div>
        </div>
    </form>
</body>
</html>