<?xml version="1.0" encoding="utf-8" ?>
<xs:schema id="SchemaQueues" targetNamespace="http://tempuri.org/SchemaQueues.xsd" elementFormDefault="qualified" xmlns="http://tempuri.org/SchemaQueues.xsd" xmlns:mstns="http://tempuri.org/SchemaQueues.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:complexType name="UnitFieldType">
		<xs:sequence>
			<xs:element name="Type" minOccurs="1" maxOccurs="1">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="unit_name" /> <!-- has a link to the unit location on the map -->
						<xs:enumeration value="incidentid" />
						<xs:enumeration value="incident_number" />
						<xs:enumeration value="timestamp" />
						<xs:enumeration value="ElapsedTime" /> <!-- a counter shows the differance in seconds beteen timestamp and the current time -->
						<xs:enumeration value="current_status" /> <!-- colored according to the status color -->
						<xs:enumeration value="user" />
            <xs:enumeration value="city"/>
						<xs:enumeration value="current_sector" />
						<xs:enumeration value="home_sector" />
						<xs:enumeration value="Location" /> <!-- address field inside the avl info. Might contain either location name or street address. -->
						<xs:enumeration value="OtherUnitField" /> <!-- use "Name" tag to identify the field name -->
						<xs:enumeration value="AssignedIncidentField" /> <!-- use "IncidentField" tag to identify the incident field -->
            <xs:enumeration value="agency" />
          </xs:restriction>
				</xs:simpleType>
			</xs:element>
			
			<!-- for OtherUnitField -->
			<xs:element name="Name" type="xs:string" minOccurs="0" maxOccurs="1" />

			<!-- for AssignedIncidentFields -->
			<xs:element name="IncidentField" type="IncidentFieldType" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitColumnType">
		<xs:sequence>
			<xs:element name="Field" type="UnitFieldType" minOccurs="1" maxOccurs="1" />
			<xs:element name="Header" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Width" type="xs:unsignedShort" minOccurs="1" maxOccurs="1" />
			<xs:element name="LinkToAssignedIncident" type="xs:boolean" minOccurs="0" maxOccurs="1" /> <!-- true means: clicking this column will bring the relevant incident screen -->
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IncidentFieldType">
		<xs:sequence>
			<xs:element name="Type" minOccurs="1" maxOccurs="1">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="incidentid" />
						<xs:enumeration value="timestamp" />
						<xs:enumeration value="ElapsedTime" /> <!-- a counter shows the differance in call entered Queue time and the current time -->
						<xs:enumeration value="priority" />
						<xs:enumeration value="sector" />
						<xs:enumeration value="incident_number" />
						<xs:enumeration value="case_number" />
						<xs:enumeration value="incident_type" />
						<xs:enumeration value="premise_info" />
						<xs:enumeration value="problem_nature" />
						<xs:enumeration value="location_name" />
						<xs:enumeration value="crossing_street" />
						<xs:enumeration value="address" /> <!-- has a link to the incident location on the map -->
						<xs:enumeration value="apartment" />
						<xs:enumeration value="city" />
						<xs:enumeration value="state" />
						<xs:enumeration value="zip" />
						<xs:enumeration value="hazards" />
						<xs:enumeration value="caution_notes" />
						<xs:enumeration value="caller_name" />
						<xs:enumeration value="caller_address" />
						<xs:enumeration value="caller_phone" />
						<xs:enumeration value="assigned_units" /> <!-- Includes assigned_other -->
						<xs:enumeration value="OtherField" /> <!-- use "Name" tag to identify the field name -->
						<xs:enumeration value="caution_note_list" />
            <xs:enumeration value="stacked_unit" />
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			
			<!-- for OtherFields -->
			<xs:element name="Name" type="xs:string" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IncidentColumnType">
		<xs:sequence>
			<xs:element name="Field" type="IncidentFieldType" minOccurs="1" maxOccurs="1" />
			<xs:element name="Header" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Width" type="xs:unsignedShort" minOccurs="1" maxOccurs="1" />
			<xs:element name="LinkToIncident" type="xs:boolean" minOccurs="0" maxOccurs="1" /> <!-- true means: clicking this column will bring the relevant incident screen -->
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitQueueType">
		<xs:sequence>
			<xs:element name="Column" type="UnitColumnType" minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IncidentQueueType">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean" minOccurs="1" maxOccurs="1" />
			<xs:element name="Column" type="IncidentColumnType" minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="Percents">
		<xs:restriction base="xs:unsignedShort">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:element name="Queues">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="UnitQueue" type="UnitQueueType" minOccurs="1" maxOccurs="1" />
				<xs:element name="ActiveIncidentQueue" type="IncidentQueueType" minOccurs="1" maxOccurs="1" />
				<xs:element name="IncidentSplitterLocationInPercents" type="Percents" minOccurs="1" maxOccurs="1" /> <!-- default: 50 -->
				<xs:element name="WaitingIncidentQueue" type="IncidentQueueType" minOccurs="1" maxOccurs="1" />
			</xs:sequence>	
		</xs:complexType>
	</xs:element>
</xs:schema>
