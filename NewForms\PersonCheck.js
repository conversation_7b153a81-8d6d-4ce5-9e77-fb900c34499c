//start shared
var m_perLastNameFieldName = "lastname";
var m_perFirstNameFieldName = "firstname";
var m_perMiddleNameFieldName = "MiddleName";
var m_perDlFieldName = "DLNumber";
var m_perDobFieldName = "dob";
var m_perStateFieldName = "State";
//end share
//start supplement
var m_perSuppCheckboxName = "doSupplement";
var m_perSuppHiddenFieldLastNameName = "SupplementLastName";
var m_perSuppHiddenFieldFirstNameName = "SupplementFirstName";
var m_perSuppHiddenFieldDobName = "SupplementDOB";
var m_perSuppHiddenFieldDlName = "SupplementDLNumber";
var m_perSuppHiddenFieldStateName = "SupplementState";

var m_perSuppByNameCheckboxName = "doSupplementByName";
var m_perSuppByNameHiddenFieldLastNameName = "SupplementByNameLastName";
var m_perSuppByNameHiddenFieldFirstNameName = "SupplementByNameFirstName";
var m_perSuppByNameHiddenFieldDobName = "SupplementByNameDOB";

var m_perSuppByDlCheckboxName = "doSupplementByDL";
var m_perSuppByDLNumberHiddenFieldDlName = "SupplementByDLNumber";
var m_perSuppByDLNumberHiddenFieldStateName = "SupplementByDLState";

function CheckPersonSupplementRequiredFields()
{
    //use cases of supplement person:
    //1-doSupplement
    //2-doSupplementByName and/or doSupplementByDL.
    //we do not use doSupplement,doSupplementByName, and doSupplementByDL at the same time.
    var elementPerDoSupplement = document.getElementById(m_perSuppCheckboxName);
    var elementPerDoSupplementByName = document.getElementById(m_perSuppByNameCheckboxName);
    var elementPerDoSupplementByDL = document.getElementById(m_perSuppByDlCheckboxName);

    var elementPerLastName = document.getElementById(m_perLastNameFieldName);
    var elementPerFirstName = document.getElementById(m_perFirstNameFieldName);
    var elementPerDLNumber = document.getElementById(m_perDlFieldName);

    if (elementPerDoSupplement != null)
    {
        if ((elementPerDoSupplementByName != null) || (elementPerDoSupplementByDL != null))
        {
            alert(m_perSuppCheckboxName + " and ( " + m_perSuppByNameCheckboxName + " or " + m_perSuppByDlCheckboxName + ") cannot be enabled at the same time.");
            return false;
        }
        if ( (elementPerLastName == null) && (elementPerFirstName == null)  && (elementPerDLNumber == null) )
        {
            alert("Configuration error: " + m_perSuppCheckboxName + " requires (" + m_perLastNameFieldName + " and " + m_perFirstNameFieldName + ") or " + m_perDlFieldName + " field.");
            return false;
        }
        if ((elementPerDLNumber == null) && ((elementPerLastName == null) && (elementPerFirstName != null)))
        {
            alert("Configuration error: " + m_perSuppCheckboxName + " requires (" + m_perLastNameFieldName + " and " + m_perFirstNameFieldName + ") or " + m_perDlFieldName + " field.");
            return false;
        }
        if ((elementPerDLNumber == null) && ((elementPerLastName!= null) && (elementPerFirstName == null)))
        {
            alert("Configuration error: " + m_perSuppCheckboxName + " requires (" + m_perLastNameFieldName + " and " + m_perFirstNameFieldName + ") or " + m_perDlFieldName + " field.");
            return false;
        }
        return true;
    }
    else
    {

        if ((elementPerDoSupplementByName != null) || (elementPerDoSupplementByDL != null))
        {
            if (elementPerDoSupplementByName != null)
            {
                if ((elementPerLastName == null) || (elementPerFirstName == null))
                {
                    alert("Configuration error: " + m_perSuppByNameCheckboxName + " requires " + m_perLastNameFieldName + " and " + m_perFirstNameFieldName + ".");
                    return false;
                }
            }
            if (elementPerDoSupplementByDL != null)
            {
                if (elementPerDLNumber == null)
                {
                    alert("Configuration error: " + m_perSuppByDlCheckboxName + " requires " + m_perDlFieldName + " field.");
                    return false;
                }
            }
            return true;
        }
        else
        {
            return true;
        }
    }
}

function GeneratePersonSupplementHiddenFields()
{
    //this method is called as part of the window.onload.

    //check to see if the checkbox exist
    if (CheckPersonSupplementRequiredFields())
    {
        var elementPerDoSupplement = document.getElementById(m_perSuppCheckboxName);
        var elementPerDoSupplementName = document.getElementById(m_perSuppByNameCheckboxName);
        var elementPerDoSupplementByDl = document.getElementById(m_perSuppByDlCheckboxName);

        //add the hidden field
        if (elementPerDoSupplement != null)
        {
            return "<input type=\"hidden\" name=\"" + m_perSuppHiddenFieldLastNameName + "\" id=\"" + m_perSuppHiddenFieldLastNameName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_perSuppHiddenFieldFirstNameName + "\" id=\"" + m_perSuppHiddenFieldFirstNameName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_perSuppHiddenFieldDobName + "\" id=\"" + m_perSuppHiddenFieldDobName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_perSuppHiddenFieldDlName + "\" id=\"" + m_perSuppHiddenFieldDlName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_perSuppHiddenFieldStateName + "\" id=\"" + m_perSuppHiddenFieldStateName + "\" />";
        }
        else
        {

            if ((elementPerDoSupplementName != null) || (elementPerDoSupplementByDl != null))
            {
                var byNameFields = "";
                var byDlFields = "";
                if (elementPerDoSupplementName != null)
                {

                    byNameFields = "<input type=\"hidden\" name=\"" + m_perSuppByNameHiddenFieldLastNameName + "\" id=\"" + m_perSuppByNameHiddenFieldLastNameName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_perSuppByNameHiddenFieldFirstNameName + "\" id=\"" + m_perSuppByNameHiddenFieldFirstNameName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_perSuppByNameHiddenFieldDobName + "\" id=\"" + m_perSuppByNameHiddenFieldDobName + "\" />";
                }
                if (elementPerDoSupplementByDl != null)
                {

                    byDlFields = "<input type=\"hidden\" name=\"" + m_perSuppByDLNumberHiddenFieldDlName + "\" id=\"" + m_perSuppByDLNumberHiddenFieldDlName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_perSuppByDLNumberHiddenFieldStateName + "\" id=\"" + m_perSuppByDLNumberHiddenFieldStateName + "\" />";

                }
                return byNameFields + byDlFields;
            }
        }
    }
    else
    {
        return "";
    }
}
function HandlePersonSupplementFieldsBeforeSubmit()
{
    //how to setup supplement.
    //we do not use doSupplement,doSupplementByName, and doSupplementByDL at the same time.
    //use cases of supplement person:
    //1-doSupplement
    //  -make sure doSupplement and labelSupplement are not commented out.
    //  -make sure doSupplementByName and labelSupplementByName are commented out.
    //  -make sure doSupplementByDL and labelSupplementByDL are commented out.
    //2-doSupplementByName and/or doSupplementByDL.
    //  -make sure doSupplementByName and labelSupplementByName are not commented out.
    //  -make sure doSupplementByDL and labelSupplementByDL are not commented out.
    //  -make sure doSupplement and labelSupplement are commented out.
    //3-not using supplement.
    //  -make sure doSupplementByName and labelSupplementByName are commented out.
    //  -make sure doSupplementByDL and labelSupplementByDL are commented out.
    //  -make sure doSupplement and labelSupplement are commented out.
    //  -return true;

    // person supplement Query
    var elementPerDoSupplement = document.getElementById(m_perSuppCheckboxName);
    var elementPerLastName = document.getElementById(m_perLastNameFieldName);
    var elementPerFirstName = document.getElementById(m_perFirstNameFieldName);
    var elementPerDLNumber = document.getElementById(m_perDlFieldName);
    var elementPerDOB = document.getElementById(m_perDobFieldName);
    var elementPerState = document.getElementById(m_perStateFieldName);

    if ((elementPerDoSupplement != null) && (elementPerDoSupplement.checked))
    {
        if (CheckPersonSupplementRequiredFields())
        {
            // person supplement name dob check or dl check
            if ((((elementPerLastName != null) && (elementPerLastName.value != "")) && ((elementPerFirstName != null) && (elementPerFirstName.value != ""))) || ((elementPerDLNumber != null) && (elementPerDLNumber.value != "")))
            {

                // person supplement name dob check
                var elementPerSupplementLastName = document.getElementById(m_perSuppHiddenFieldLastNameName);
                if ((elementPerLastName != null) && (elementPerSupplementLastName != null))
                    elementPerSupplementLastName.value = elementPerLastName.value;

                var elementPerSupplementFirstName = document.getElementById(m_perSuppHiddenFieldFirstNameName);
                if ((elementPerFirstName != null) && (elementPerSupplementFirstName != null))
                    elementPerSupplementFirstName.value = elementPerFirstName.value;

                var elementPerSupplementDOB = document.getElementById(m_perSuppHiddenFieldDobName);
                if ((elementPerDOB != null) && (elementPerSupplementDOB != null))
                    elementPerSupplementDOB.value = elementPerDOB.value; //optional

                // person supplement dl check
                var elementPerSupplementDLNumber = document.getElementById(m_perSuppHiddenFieldDlName);
                if ((elementPerDLNumber != null) && (elementPerSupplementDLNumber != null))
                    elementPerSupplementDLNumber.value = elementPerDLNumber.value;

                var elementPerSupplementState = document.getElementById(m_perSuppHiddenFieldStateName);
                if ((elementPerState != null) && (elementPerSupplementState != null))
                    elementPerSupplementState.value = elementPerState.value; //optional

                return true;
            }
            else
            {
                //required field combinations are not met.
                alert("Last and first names or DL Number is needed to query person supplement.");
                return false;
            }
        }
    }
    else
    {
        var elementPerDoSupplementByName = document.getElementById(m_perSuppByNameCheckboxName);
        var elementPerDoSupplementByDL = document.getElementById(m_perSuppByDlCheckboxName);
        if (((elementPerDoSupplementByName != null) && (elementPerDoSupplementByName.checked)) || ((elementPerDoSupplementByDL != null) && (elementPerDoSupplementByDL.checked)))
        {
            if (CheckPersonSupplementRequiredFields())
            {
                if ((elementPerDoSupplementByName != null) && (elementPerDoSupplementByName.checked))
                {
                    if (((elementPerLastName != null) && (elementPerLastName.value != "")) && ((elementPerFirstName != null) && (elementPerFirstName.value != "")))
                    {

                        // person supplement name dob check

                        var elementPerSupplementByNameLastName = document.getElementById(m_perSuppByNameHiddenFieldLastNameName);
                        elementPerSupplementByNameLastName.value = elementPerLastName.value;

                        var elementPerSupplementByNameFirstName = document.getElementById(m_perSuppByNameHiddenFieldFirstNameName);
                        elementPerSupplementByNameFirstName.value = elementPerFirstName.value;

                        var elementPerSupplementByNameDOB = document.getElementById(m_perSuppByNameHiddenFieldDobName);
                        if ((elementPerDOB != null) && (elementPerSupplementByNameDOB != null))
                            elementPerSupplementByNameDOB.value = elementPerDOB.value; //optional

                    }
                    else
                    {
                        //required field combinations are not met.
                        alert("Last and first names are needed to query person supplement by name.");
                        return false;

                    }

                }
                if ((elementPerDoSupplementByDL != null) && (elementPerDoSupplementByDL.checked))
                {
                    if ((elementPerDLNumber != null) && (elementPerDLNumber.value != ""))
                    {
                        // person supplement dl check

                        var elementPerSupplementByDLNumberDl = document.getElementById(m_perSuppByDLNumberHiddenFieldDlName);
                        elementPerSupplementByDLNumberDl.value = elementPerDLNumber.value;
                        var elementPerSupplementByDLState = document.getElementById(m_perSuppByDLNumberHiddenFieldStateName);
                        if ((elementPerState != null) && (elementPerSupplementByDLState != null))
                            elementPerSupplementByDLState.value = elementPerState.value; //optional

                    }
                    else
                    {
                        //required field combinations are not met.
                        alert("Driver license is needed to query person supplement by driver license.");
                        return false;
                    }
                }
                return true;
            }
            else
            {
                //failed fields existant.
                return false;
            }
        }
        else
        {
            //no  supplement checkbox is checked.
            return true;
        }
    }
}
//end supplement
//start rms
var m_perDoRmsCheckboxName = "doRms";
var m_perRmsHiddenFieldLastNameName = "RmsLastName";
var m_perRmsHiddenFieldFirstNameName = "RmsFirstName";
var m_perRmsHiddenFieldMiddleNameName = "RmsMiddleName";
var m_perRmsHiddenFieldDobName = "RmsDOB";
var m_perRmsHiddenFieldDlName = "RmsDLNumber";
var m_perRmsHiddenFieldStateName = "RmsState";

var m_perDoRmsByNameCheckboxName = "doRmsByName";
var m_perRmsByNameHiddenFieldLastNameName = "RmsByNameLastName";
var m_perRmsByNameHiddenFieldFirstNameName = "RmsByNameFirstName";
var m_perRmsByNameHiddenFieldMiddleNameName = "RmsByNameMiddleName";
var m_perRmsByNameHiddenFieldDobName = "RmsByNameDOB";

var m_perDoRmsByDlCheckboxName = "doRmsByDL";
var m_perRmsByDlHiddenFieldDlName = "RmsByDlDLNumber";
var m_perRmsByDlHiddenFieldStateName = "RmsByDlState";
function CheckPersonRmsRequiredFields()
{
    //use cases of Rms:
    //1-doRms
    //2-doRmsByName and/or doRmsByDL.
    //we do not use doRms,doRmsByName, and doRmsByDL at the same time.
    var elementPerDoRms = document.getElementById(m_perDoRmsCheckboxName);
    var elementPerDoRmsByName = document.getElementById(m_perDoRmsByNameCheckboxName);
    var elementPerDoRmsByDL = document.getElementById(m_perDoRmsByDlCheckboxName);

    var elementPerLastName = document.getElementById(m_perLastNameFieldName);
    var elementPerFirstName = document.getElementById(m_perFirstNameFieldName);
    var elementPerDLNumber = document.getElementById(m_perDlFieldName);
    var elementPerState = document.getElementById(m_perStateFieldName);


    if (elementPerDoRms != null)
    {
        if ((elementPerDoRmsByName != null) || (elementPerDoRmsByDL != null))
        {
            alert(m_perDoRmsCheckboxName + " and (" + m_perDoRmsByNameCheckboxName + " or " + m_perDoRmsByDlCheckboxName + ") cannot be enabled at the same time.");
            return false;
        }
        if ((elementPerLastName == null) && (elementPerFirstName == null) && (elementPerDLNumber == null))
        {
            alert("Configuration error: " + m_perDoRmsCheckboxName + " requires (" + m_perLastNameFieldName + " and " + m_perFirstNameFieldName + ") or " + m_perDlFieldName + " field.");
            return false;
        }
        return true;
    }
    else
    {

        if ((elementPerDoRmsByName != null) || (elementPerDoRmsByDL != null))
        {
            if (elementPerDoRmsByName != null)
            {
                if ((elementPerLastName == null) || (elementPerFirstName == null))
                {
                    alert("Configuration error: " + m_perDoRmsByNameCheckboxName + " requires " + m_perLastNameFieldName + " and " + m_perFirstNameFieldName + ".");
                    return false;
                }
            }
            if (elementPerDoRmsByDL != null)
            {
                if ((elementPerDLNumber == null) || (elementPerState == null))
                {
                    alert("Configuration error: " + m_perDoRmsByDlCheckboxName + " requires " + m_perDlFieldName + " and " + m_perStateFieldName + ".");
                    return false;
                }
            }
            return true;
        }
        else
        {
            return true;
        }
    }
}
function GeneratePersonRmsHiddenFields()
{
    //this method is called as part of the window.onload.

    //check to see if the checkbox exist
    if (CheckPersonRmsRequiredFields())
    {



        var elementPerDoRms = document.getElementById(m_perDoRmsCheckboxName);
        if (elementPerDoRms != null)
        {
            return "<input type=\"hidden\" name=\"" + m_perRmsHiddenFieldLastNameName + "\" id=\"" + m_perRmsHiddenFieldLastNameName + "\" />"
                            + "<input type=\"hidden\" name=\"" + m_perRmsHiddenFieldFirstNameName + "\" id=\"" + m_perRmsHiddenFieldFirstNameName + "\" />"
                            + "<input type=\"hidden\" name=\"" + m_perRmsHiddenFieldMiddleNameName + "\" id=\"" + m_perRmsHiddenFieldMiddleNameName + "\" />"
                            + "<input type=\"hidden\" name=\"" + m_perRmsHiddenFieldDobName + "\" id=\"" + m_perRmsHiddenFieldDobName + "\" />"
                            + "<input type=\"hidden\" name=\"" + m_perRmsHiddenFieldDlName + "\" id=\"" + m_perRmsHiddenFieldDlName + "\" />"
                            + "<input type=\"hidden\" name=\"" + m_perRmsHiddenFieldStateName + "\" id=\"" + m_perRmsHiddenFieldStateName + "\" />";
        }
        else
        {
            var elementPerDoRmsByName = document.getElementById(m_perDoRmsByNameCheckboxName);
            var elementPerDoRmsByDl = document.getElementById(m_perDoRmsByDlCheckboxName);
            if ((elementPerDoRmsByName != null) || (elementPerDoRmsByDl != null))
            {
                var nameFields = "";
                var dlFields = "";
                if (elementPerDoRmsByName != null)
                {
                    nameFields = "<input type=\"hidden\" name=\"" + m_perRmsByNameHiddenFieldLastNameName + "\" id=\"" + m_perRmsByNameHiddenFieldLastNameName + "\" />"
                                + "<input type=\"hidden\" name=\"" + m_perRmsByNameHiddenFieldFirstNameName + "\" id=\"" + m_perRmsByNameHiddenFieldFirstNameName + "\" />"
                                + "<input type=\"hidden\" name=\"" + m_perRmsByNameHiddenFieldMiddleNameName + "\" id=\"" + m_perRmsByNameHiddenFieldMiddleNameName + "\" />"
                                + "<input type=\"hidden\" name=\"" + m_perRmsByNameHiddenFieldDobName + "\" id=\"" + m_perRmsByNameHiddenFieldDobName + "\" />";

                }
                if (elementPerDoRmsByDl != null)
                {
                    dlFields = "<input type=\"hidden\" name=\"" + m_perRmsByDlHiddenFieldDlName + "\" id=\"" + m_perRmsByDlHiddenFieldDlName + "\" />"
                                + "<input type=\"hidden\" name=\"" + m_perRmsByDlHiddenFieldStateName + "\" id=\"" + m_perRmsByDlHiddenFieldStateName + "\" />";

                }
                return nameFields + dlFields;
            }
            else
            {
                return "";
            }
        }
    }
    else
    {
        return "";
    }
}
function HandlePersonRmsFieldsBeforeSubmit()
{
    //how to setup Rms.
    //we do not use doRms,doRmsByName, and doRmsByDL at the same time.
    //use cases of Rms:
    //1-doRms
    //  -make sure doRms and labelRms are not commented out.
    //  -make sure doRmsByName and labelRmsByName are commented out.
    //  -make sure doRmsByDL and labelRmsByDL are commented out.
    //2-doRmsByName and/or doRmsByDL.
    //  -make sure doRmsByName and labelRmsByName are not commented out.
    //  -make sure doRmsByDL and labelRmsByDL are not commented out.
    //  -make sure doRms and labelRms are commented out.
    //3-not using Rms.
    //  -make sure doRmsByName and labelRmsByName are commented out.
    //  -make sure doRmsByDL and labelRmsByDL are commented out.
    //  -make sure doRms and labelRms are commented out.
    //  -return true;

    // person Rms Query

    var elementPerDoRms = document.getElementById(m_perDoRmsCheckboxName);

    var elementPerLastName = document.getElementById(m_perLastNameFieldName);
    var elementPerFirstName = document.getElementById(m_perFirstNameFieldName);
    var elementPerMiddleName = document.getElementById(m_perMiddleNameFieldName);
    var elementPerDLNumber = document.getElementById(m_perDlFieldName);
    var elementPerDOB = document.getElementById(m_perDobFieldName);
    var elementPerState = document.getElementById(m_perStateFieldName);

    if ((elementPerDoRms != null) && (elementPerDoRms.checked))
    {
        if (CheckPersonRmsRequiredFields())
        {
            // Rms 
            if ((((elementPerLastName != null) && (elementPerLastName.value != "")) && ((elementPerFirstName != null) && (elementPerFirstName.value != ""))) || ((elementPerDLNumber != null) && (elementPerDLNumber.value != "")))
            {
                // person Rms name dob check

                var elementPerRmsLastName = document.getElementById(m_perRmsHiddenFieldLastNameName);
                if ((elementPerLastName != null) && (elementPerRmsLastName != null))
                    elementPerRmsLastName.value = elementPerLastName.value; // could be optional if DL is present

                var elementPerRmsFirstName = document.getElementById(m_perRmsHiddenFieldFirstNameName);
                if ((elementPerFirstName != null) && (elementPerRmsFirstName != null))
                    elementPerRmsFirstName.value = elementPerFirstName.value; //optional

                var elementPerRmsMiddleName = document.getElementById(m_perRmsHiddenFieldMiddleNameName);
                if ((elementPerMiddleName != null) && (elementPerRmsMiddleName != null))
                    elementPerRmsMiddleName.value = elementPerMiddleName.value; //optional

                var elementPerRmsDOB = document.getElementById(m_perRmsHiddenFieldDobName);
                if ((elementPerDOB != null) && (elementPerRmsDOB != null))
                    elementPerRmsDOB.value = elementPerDOB.value; //optional


                // person Rms dl check
                var elementPerRmsDLNumber = document.getElementById(m_perRmsHiddenFieldDlName);
                if ((elementPerDLNumber != null) && (elementPerRmsDLNumber != null))
                    elementPerRmsDLNumber.value = elementPerDLNumber.value;

                var elementPerRmsState = document.getElementById(m_perRmsHiddenFieldStateName);
                if ((elementPerState != null) && (elementPerRmsState != null))
                    elementPerRmsState.value = elementPerState.value; //optional

                return true;
            }
            else
            {
                //required field combinations are not met.
                alert("(Last and first names) or DL Number is needed to query Rms.");
                return false;
            }
        }
        else
        {
            return false;
        }
    }
    else
    {
        var elementPerDoRmsByName = document.getElementById(m_perDoRmsByNameCheckboxName);
        var elementPerDoRmsByDL = document.getElementById(m_perDoRmsByDlCheckboxName);
        if (((elementPerDoRmsByName != null) && (elementPerDoRmsByName.checked)) || ((elementPerDoRmsByDL != null) && (elementPerDoRmsByDL.checked)))
        {
            if (CheckPersonRmsRequiredFields())
            {
                if ((elementPerDoRmsByName != null) && (elementPerDoRmsByName.checked))
                {
                    if (((elementPerLastName != null) && (elementPerLastName.value != "")) && ((elementPerFirstName != null) && (elementPerFirstName.value != "")))
                    {

                        // person Rms name dob check
                        var elementPerRmsByNameLastName = document.getElementById(m_perRmsByNameHiddenFieldLastNameName);
                        elementPerRmsByNameLastName.value = elementPerLastName.value;

                        var elementPerRmsByNameFirstName = document.getElementById(m_perRmsByNameHiddenFieldFirstNameName);
                        elementPerRmsByNameFirstName.value = elementPerFirstName.value;

                        var elementPerRmsByNameMiddleName = document.getElementById(m_perRmsByNameHiddenFieldMiddleNameName);
                        if ((elementPerMiddleName != null) && (elementPerRmsByNameMiddleName != null))
                            elementPerRmsByNameMiddleName.value = elementPerMiddleName.value; //optional

                        var elementPerRmsByNameDOB = document.getElementById(m_perRmsByNameHiddenFieldDobName);
                        if ((elementPerDOB != null) && (elementPerRmsByNameDOB != null))
                            elementPerRmsByNameDOB.value = elementPerDOB.value; //optional

                    }
                    else
                    {
                        //required field combinations are not met.
                        alert("Last and first names are needed to query Rms by name.");
                        return false;

                    }

                }
                if ((elementPerDoRmsByDL != null) && (elementPerDoRmsByDL.checked))
                {
                    if (((elementPerDLNumber != null) && (elementPerDLNumber.value != "")) && ((elementPerState != null) && (elementPerState.value != "")))
                    {
                        // person Rms dl check
                        var elementPerRmsByDlDl = document.getElementById(m_perRmsByDlHiddenFieldDlName);
                        elementPerRmsByDlDl.value = elementPerDLNumber.value;

                        var elementPerRmsByDlState = document.getElementById(m_perRmsByDlHiddenFieldStateName);
                        if ((elementPerState != null) && (elementPerRmsByDlState != null))
                            elementPerRmsByDlState.value = elementPerState.value;  
                    }
                    else
                    {
                        //required field combinations are not met.
                        alert("Driver license and state are needed to query Rms by driver license.");
                        return false;
                    }
                }
                return true;
            }
            else
            {
                //failed fields existant.
                return false;
            }
        }
        else
        {
            //no Rms checkbox is checked.
            return true;
        }
    }

}
////end rms