<?xml version="1.0" encoding="utf-8"?>
<ArrayOfComSetting xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <ComSetting>
    <Name xmlns="http://tempuri.org/SchemaComSetting.xsd">MagStripeCOMPort</Name>
    <Port xmlns="http://tempuri.org/SchemaComSetting.xsd">1</Port>
    <Baud xmlns="http://tempuri.org/SchemaComSetting.xsd">9600</Baud>
    <Parity xmlns="http://tempuri.org/SchemaComSetting.xsd">None</Parity>
    <DataBits xmlns="http://tempuri.org/SchemaComSetting.xsd">8</DataBits>
    <StopBits xmlns="http://tempuri.org/SchemaComSetting.xsd">One</StopBits>
    <FlowControlDsrDtr xmlns="http://tempuri.org/SchemaComSetting.xsd">false</FlowControlDsrDtr>
    <FlowControlXOnOff xmlns="http://tempuri.org/SchemaComSetting.xsd">false</FlowControlXOnOff>
    <FlowControlCtsRts xmlns="http://tempuri.org/SchemaComSetting.xsd">true</FlowControlCtsRts>
    <FlowControlRs485 xmlns="http://tempuri.org/SchemaComSetting.xsd">false</FlowControlRs485>
    <EnableDtr xmlns="http://tempuri.org/SchemaComSetting.xsd">true</EnableDtr>
    <EnableRts xmlns="http://tempuri.org/SchemaComSetting.xsd">true</EnableRts>
    <Handshake xmlns="http://tempuri.org/SchemaComSetting.xsd">RequestToSendXOnXOff</Handshake>
  </ComSetting>
  <ComSetting>
    <Name xmlns="http://tempuri.org/SchemaComSetting.xsd">GPSComPort</Name>
    <Port xmlns="http://tempuri.org/SchemaComSetting.xsd">7</Port>
    <Baud xmlns="http://tempuri.org/SchemaComSetting.xsd">9600</Baud>
    <Parity xmlns="http://tempuri.org/SchemaComSetting.xsd">None</Parity>
    <DataBits xmlns="http://tempuri.org/SchemaComSetting.xsd">8</DataBits>
    <StopBits xmlns="http://tempuri.org/SchemaComSetting.xsd">One</StopBits>
    <FlowControlDsrDtr xmlns="http://tempuri.org/SchemaComSetting.xsd">false</FlowControlDsrDtr>
    <FlowControlXOnOff xmlns="http://tempuri.org/SchemaComSetting.xsd">false</FlowControlXOnOff>
    <FlowControlCtsRts xmlns="http://tempuri.org/SchemaComSetting.xsd">true</FlowControlCtsRts>
    <FlowControlRs485 xmlns="http://tempuri.org/SchemaComSetting.xsd">false</FlowControlRs485>
    <EnableDtr xmlns="http://tempuri.org/SchemaComSetting.xsd">true</EnableDtr>
    <EnableRts xmlns="http://tempuri.org/SchemaComSetting.xsd">true</EnableRts>
    <Handshake xmlns="http://tempuri.org/SchemaComSetting.xsd">RequestToSendXOnXOff</Handshake>
  </ComSetting>
</ArrayOfComSetting>