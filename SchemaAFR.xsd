﻿<?xml version="1.0" encoding="utf-8" ?> 
<xs:schema targetNamespace="http://tritech.com/SchemaAFR.xsd"
                  elementFormDefault="qualified"
                  xmlns="http://tritech.com/SchemaAFR.xsd"
                  xmlns:mstns="http://tritech.com/SchemaAFR.xsd"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="FieldType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="Value" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="CrewType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="EmployeeID" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
 </xs:complexType>
  
  <xs:element name="IncidentData">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="IncidentNumber" type="xs:string" />

        <!-- an incident can have more than 1 case number -->
        <xs:element name="CaseNumber" type="xs:string" minOccurs="0" maxOccurs="unbounded" />

        <xs:element name="Agency" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="Address" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="City" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="State" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="Zip" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="Division" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="MapGrid" type="xs:string " minOccurs="0" maxOccurs="1" />
        <xs:element name="LocationName" type="xs:string " minOccurs="0" maxOccurs="1" />
        <xs:element name="LocationType" type="xs:string " minOccurs="0" maxOccurs="1" />
        <xs:element name="FirstOfficerEmployeeID" type="xs:string " minOccurs="0" maxOccurs="1" />
        <xs:element name="SecondOfficerEmployeeID" type="xs:string " minOccurs="0" maxOccurs="1" />
        <xs:element name="FirstAssignedUnit" type="xs:string " minOccurs="0" maxOccurs="1" />
        <xs:element name="SecondAssignedUnit" type="xs:string " minOccurs="0" maxOccurs="1" />
        <xs:element name="TimeCallEnteredQueue" type="xs:dateTime " minOccurs="0" maxOccurs="1" />
        <xs:element name="TimeFirstCallTakingKeystroke" type="xs:dateTime " minOccurs="0" maxOccurs="1" />

        <!-- all incident comments -->
        <xs:element name="Comment" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
              <xs:element name="Author" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="Text" type="xs:string" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        
        <xs:element name="AssignedUnit" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
    		    <xs:sequence>
      		    <xs:element name="UnitName" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="UnitCode" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="Crew" type="CrewType" minOccurs="0" maxOccurs="unbounded" /> 
            </xs:sequence>
	        </xs:complexType>
        </xs:element>

        <xs:element name="FieldReportRequestByUnit" type="xs:string " minOccurs="0" maxOccurs="1" />
        <xs:element name="FieldReportRequestDateTime" type="xs:dateTime " minOccurs="0" maxOccurs="1" />
        <xs:element name="FieldReportRequestAtLatitude" type="xs:double " minOccurs="0" maxOccurs="1" />
        <xs:element name="FieldReportRequestAtLongitude" type="xs:double " minOccurs="0" maxOccurs="1" />

        <!-- Use this for any other field -->
        <xs:element name="Field" type="FieldType" minOccurs="0" maxOccurs="unbounded" />     
   
        
      </xs:sequence>
    </xs:complexType>
  </xs:element>


  <xs:element name="RecordCheckData">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Field" type="FieldType" minOccurs="1" maxOccurs="unbounded" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
