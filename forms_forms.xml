<?xml version="1.0" encoding="utf-8"?>
<Forms xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://tempuri.org/SchemaForms.xsd">
  <FormGroup>
    <Name>Misc</Name>
    <Form Type="HTMLQuery" FilePath="NewForms">Login</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">Logout</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" AckExpirationTime="5400">ModifyLogon</Form>
  </FormGroup>
  <FormGroup>
    <Name>CAD</Name>
    <Form Type="HTMLQuery" FilePath="NewForms">addincidentcomment</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">GetIncidents_LOGIS</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">ActiveIncidents</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" AckExpirationTime="5400">AddActivityLogComment</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">AlliedAgencies</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">AtDestination</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">AtScene2ndLocation</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">ClearCall</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">DF.Links</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">DivisionUnitQuery</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">EnrouteToPost</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">GetIncident</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">GetIncidentPersonnel</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">GetPersonnelRadios</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">GetPremiseInfo</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" SupervisorOnly="false">IncidentSearch</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">IncidentSummaryQuery</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">InQuarters</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">OutOfService</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">PendingIncidents</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" SupervisorOnly="false">PersonnelSearch</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" AckExpirationTime="3600">PositionUpdate</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" AckExpirationTime="5400">PrimaryUnitRequest</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" SupervisorOnly="false">RadiusSearch</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">Responding2ndLocation</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" SupervisorOnly="true">SendPage</Form>
    <Form Type="HTMLQuery" FilePath="NewForms" AckExpirationTime="3600">SetIncidentDisposition</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">ReversePhoneSearch</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">SpecificUnitStatus</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">StationViewer</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">Transport</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">UnitHistory</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">UnitStatus</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">VehicleSearch</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">IncidentTimes</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">CardFileSearch</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">ChangeProblemNature</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">LocationCheck</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">TowRequest</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">ClearAllUnits</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">OutOfService.BusyUnavail</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">Pictometry</Form>
    <Form Type="HTMLQuery" FilePath="NewForms">SingleIncident</Form>
  </FormGroup>
</Forms>