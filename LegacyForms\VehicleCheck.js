//start share 
var m_vehVinFieldName = "VIN";
var m_vehLicFieldName = "LIC";
var m_vehStateFieldName = "State";
//end share
//---------------------start supplement
var m_vehSuppCheckboxName = "doSupplement";
var m_vehSuppHiddenFieldLicName = "SupplementLic";
var m_vehSuppHiddenFieldVinName = "SupplementVin";
var m_vehSuppHiddenFieldStateName = "SupplementState";

var m_vehSuppByLicCheckboxName = "doSupplementByLic";
var m_vehSuppByLicHiddenFieldLicName = "SupplementByLic";
var m_vehSuppByLicHiddenFieldStateName = "SupplementByLicState";

var m_vehSuppByVinCheckboxName = "doSupplementByVin";
var m_vehSuppByVinHiddenFieldVinName = "SupplementByVin";

function CheckVehicleSupplementRequiredFields()
{
    //use cases of supplement:
    //1-doSupplement
    //2-doSupplementByLic and/or doSupplementByVin.
    //we do not use doSupplement,doSupplementByLic, and doSupplementByVin at the same time.
    var elementVehDoSupplement = document.getElementById(m_vehSuppCheckboxName);
    var elementVehDoSupplementByLic = document.getElementById(m_vehSuppByLicCheckboxName);
    var elementVehDoSupplementByVin = document.getElementById(m_vehSuppByVinCheckboxName);
    var elementVehState = document.getElementById(m_vehStateFieldName);
    var elementVehVIN = document.getElementById(m_vehVinFieldName);
    var elementVehLIC = document.getElementById(m_vehLicFieldName);

    if (elementVehDoSupplement != null)
    {
        if ((elementVehDoSupplementByLic != null) || (elementVehDoSupplementByVin != null))
        {
            alert("Configuration error: " + m_vehSuppCheckboxName + " and (" + m_vehSuppByLicCheckboxName + " and/or " + m_vehSuppByVinCheckboxName + ") cannot be enabled at the same time.");
            return false;
        }
        if ((elementVehLIC == null) && (elementVehVIN == null))
        {
            alert("Configuration error: " + m_vehSuppCheckboxName + " requires " + m_vehLicFieldName + " or " + m_vehVinFieldName + " field.");
            return false;
        }
        return true;
    }
    else
    {

        if ((elementVehDoSupplementByLic != null) || (elementVehDoSupplementByVin != null))
        {
            if (elementVehDoSupplementByLic != null)
            {
                if (elementVehLIC == null)
                {
                    alert("Configuration error: " + m_vehSuppByLicCheckboxName + " requires " + m_vehLicFieldName + " field.");
                    return false;
                }
            }
            if (elementVehDoSupplementByVin != null)
            {
                if (elementVehVIN == null)
                {
                    alert("Configuration error: " + m_vehSuppByVinCheckboxName + " requires " + m_vehVinFieldName + " field.");
                    return false;
                }
            }
            return true;
        }
        else
        {
            return true ;
        }
    }
}
function GenerateVehicleSupplementHiddenFields()
{
    //this method is called as part of the window.onload.

    //check to see if the checkbox exist
    if (CheckVehicleSupplementRequiredFields())
    {
        var elementVehDoSupplement = document.getElementById(m_vehSuppCheckboxName);

        //add the hidden field
        if (elementVehDoSupplement != null)
        {
            return "<input type=\"hidden\" name=\"" + m_vehSuppHiddenFieldLicName + "\" id=\"" + m_vehSuppHiddenFieldLicName + "\" />"
              + "<input type=\"hidden\" name=\"" + m_vehSuppHiddenFieldStateName + "\" id=\"" + m_vehSuppHiddenFieldStateName + "\" />"
              + "<input type=\"hidden\" name=\"" + m_vehSuppHiddenFieldVinName + "\" id=\"" + m_vehSuppHiddenFieldVinName + "\" />";
        }
        else
        {
            var elementVehDoSupplementByLic = document.getElementById(m_vehSuppByLicCheckboxName);
            var elementVehDoSupplementByVin = document.getElementById(m_vehSuppByVinCheckboxName);
            if ((elementVehDoSupplementByLic != null) || (elementVehDoSupplementByVin != null))
            {
                var licFields = "";
                var vinFields = "";
                if (elementVehDoSupplementByLic != null)
                {
                    //hidden fields for supplement by lic and state-->
                    licFields = "<input type=\"hidden\" name=\"" + m_vehSuppByLicHiddenFieldLicName + "\" id=\"" + m_vehSuppByLicHiddenFieldLicName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_vehSuppByLicHiddenFieldStateName + "\" id=\"" + m_vehSuppByLicHiddenFieldStateName + "\" />";
                }
                if (elementVehDoSupplementByVin != null)
                {
                    //--hidden fields for supplement by vin
                    vinFields = "<input type=\"hidden\" name=\"" + m_vehSuppByVinHiddenFieldVinName + "\" id=\"" + m_vehSuppByVinHiddenFieldVinName + "\" />";
                }
                return licFields + vinFields;
            }
        }
    }
    else
    {
        return "";
    }
}
function HandleVehicleSupplementFieldsBeforeSubmit()
{
    //how to setup supplement.
    //we do not use doSupplement,doSupplementByLic, and doSupplementByVin at the same time.
    //use cases of supplement person:
    //1-doSupplement
    //  -make sure doSupplement and labelSupplement are not commented out.
    //  -make sure doSupplementByLic and labelSupplementByLic are commented out.
    //  -make sure doSupplementByVin and labelSupplementByVin are commented out.
    //2-doSupplementByLic and/or doSupplementByVin.
    //  -make sure doSupplementByLic and labelSupplementByLic are not commented out.
    //  -make sure doSupplementByVin and labelSupplementByVin are not commented out.
    //  -make sure doSupplement and labelSupplement are commented out.
    //3-not using supplement.
    //  -make sure doSupplementByLic and labelSupplementByLic are commented out.
    //  -make sure doSupplementByVin and labelSupplementByVin are commented out.
    //  -make sure doSupplement and labelSupplement are commented out.
    //  -return true;

    // supplement Query

    var elementVehDoSupplement = document.getElementById(m_vehSuppCheckboxName);
    var elementVehState = document.getElementById(m_vehStateFieldName);
    var elementVehVIN = document.getElementById(m_vehVinFieldName);
    var elementVehLIC = document.getElementById(m_vehLicFieldName);

    if ((elementVehDoSupplement != null) && (elementVehDoSupplement.checked))
    {
        if (CheckVehicleSupplementRequiredFields())
        {
            // supplement name lic check or vin check
            if (((elementVehLIC != null) && (elementVehLIC.value != "")) || ((elementVehVIN != null) && (elementVehVIN.value != "")))
            {
                // supplement lic and state check
                var elementVehSuppLic = document.getElementById(m_vehSuppHiddenFieldLicName);
                if ((elementVehLIC != null) && (elementVehSuppLic != null))
                    elementVehSuppLic.value = elementVehLIC.value;

                var elementVehSuppState = document.getElementById(m_vehSuppHiddenFieldStateName);
                if ((elementVehState != null) && (elementVehSuppState != null))
                    elementVehSuppState.value = elementVehState.value; //optional

                // supplement vin check
                var elementVehSuppVin = document.getElementById(m_vehSuppHiddenFieldVinName);
                if ((elementVehVIN != null) && (elementVehSuppVin != null))
                    elementVehSuppVin.value = elementVehVIN.value;

                return true;
            }
            else
            {
                //required field combinations are not met.
                alert(m_vehLicFieldName + " or " + m_vehVinFieldName + " is needed to query supplement.");
                return false;
            }
        }
    }
    else
    {
        var elementVehdoSupplementByLic = document.getElementById(m_vehSuppByLicCheckboxName);
        var elementVehdoSupplementByVin = document.getElementById(m_vehSuppByVinCheckboxName);
        if (((elementVehdoSupplementByVin != null) && (elementVehdoSupplementByVin.checked)) || ((elementVehdoSupplementByLic != null) && (elementVehdoSupplementByLic.checked)))
        {
            if (CheckVehicleSupplementRequiredFields())
            {
                if ((elementVehdoSupplementByLic != null) && (elementVehdoSupplementByLic.checked))
                {
                    if (elementVehLIC.value != "")
                    {

                        // supplement lic and state check
                        var elementVehSuppByLicLic = document.getElementById(m_vehSuppByLicHiddenFieldLicName);
                        elementVehSuppByLicLic.value = elementVehLIC.value;

                        var elementVehSuppByLicState = document.getElementById(m_vehSuppByLicHiddenFieldStateName);
                        if ((elementVehState != null) && (elementVehSuppByLicState != null))
                            elementVehSuppByLicState.value = elementVehState.value; //optional


                    }
                    else
                    {
                        //required field combinations are not met.
                        alert(m_vehLicFieldName + " is needed to query supplement by Lic.");
                        return false;

                    }

                }
                if ((elementVehdoSupplementByVin != null) && (elementVehdoSupplementByVin.checked))
                {
                    if (elementVehVIN.value != "")
                    {
                        // supplement vin check

                        var elementVehSuppByVinVin = document.getElementById(m_vehSuppByVinHiddenFieldVinName);
                        elementVehSuppByVinVin.value = elementVehVIN.value;

                    }
                    else
                    {
                        //required field combinations are not met.
                        alert(m_vehVinFieldName + " is needed to query supplement.");
                        return false;
                    }
                }
                return true;
            }
            else
            {
                //failed fields existant.
                return false;
            }
        }
        else
        {
            //non  supplement checkbox is checked.
            return true;
        }
    }

}
//---------------------end supplement

//--------------------- start rms
var m_vehdoRmsCheckboxName = "doRms";
var m_vehRmsHiddenFieldLicName = "RmsLic";
var m_vehRmsHiddenFieldVinName = "RmsVin";
var m_vehRmsHiddenFieldStateName = "RmsState";


var m_vehdoRmsByLicCheckboxName = "doRmsByLic";
var m_vehRmsByLicHiddenFieldLicName = "RmsByLicLic";
var m_vehRmsByLicHiddenFieldStateName = "RmsByLicState";


var m_vehRmsByVinCheckboxName = "doRmsByVin";
var m_vehRmsByVinHiddenFieldVinName = "RmsByVin";
function CheckVehicleRmsRequiredFields()
{
    //use cases of Rms:
    //1-doRms
    //2-doRmsByLic and/or doRmsByVin.
    //we do not use doRms,doRmsByLic, and doRmsByVin at the same time.
    var elementVehDoRms = document.getElementById(m_vehdoRmsCheckboxName);
    var elementVehDoRmsByLic = document.getElementById(m_vehdoRmsByLicCheckboxName);
    var elementVehDoRmsByVin = document.getElementById(m_vehRmsByVinCheckboxName);

    var elementVehState = document.getElementById(m_vehStateFieldName);
    var elementVehVIN = document.getElementById(m_vehVinFieldName);
    var elementVehLIC = document.getElementById(m_vehLicFieldName);

    if (elementVehDoRms != null)
    {
        if ((elementVehDoRmsByLic != null) || (elementVehDoRmsByVin != null))
        {
            alert("Configuration error: " + m_vehdoRmsCheckboxName + " and (" + m_vehdoRmsByLicCheckboxName + " or " + m_vehRmsByVinCheckboxName + ") cannot be enabled at the same time.");
            return false;
        }
        if ((elementVehLIC == null) && (elementVehVIN == null))
        {
            alert("Configuration error: " + m_vehdoRmsCheckboxName + " requires " + m_vehLicFieldName + " or " + m_vehVinFieldName + " field.");
            return false;
        }
        return true;
    }
    else
    {

        if ((elementVehDoRmsByLic != null) || (elementVehDoRmsByVin != null))
        {
            if (elementVehDoRmsByLic != null)
            {
                if (elementVehLIC == null)
                {
                    alert("Configuration error: " + m_vehdoRmsByLicCheckboxName + " requires " + m_vehLicFieldName + " field.");
                    return false;
                }
            }
            if (elementVehDoRmsByVin != null)
            {
                if (elementVehVIN == null)
                {
                    alert("Configuration error: " + m_vehRmsByVinCheckboxName + " requires " + m_vehVinFieldName + " field.");
                    return false;
                }
            }
            return true;
        }
        else
        {
            return true;
        }
    }
}
function GenerateVehicleRmsHiddenFields()
{
    //this method is called as part of the window.onload.

    //check to see if the checkbox exist
    if (CheckVehicleRmsRequiredFields())
    {
        var elementVehDoRms = document.getElementById(m_vehdoRmsCheckboxName);
        if (elementVehDoRms != null)
        {

            return "<input type=\"hidden\" name=\"" + m_vehRmsHiddenFieldLicName + "\" id=\"" + m_vehRmsHiddenFieldLicName + "\" />"
                + "<input type=\"hidden\" name=\"" + m_vehRmsHiddenFieldStateName + "\" id=\"" + m_vehRmsHiddenFieldStateName + "\" />"
                + "<input type=\"hidden\" name=\"" + m_vehRmsHiddenFieldVinName + "\" id=\"" + m_vehRmsHiddenFieldVinName + "\" />";
        }
        else
        {
            var elementVehDoRmsByLic = document.getElementById(m_vehdoRmsByLicCheckboxName);
            var elementVehDoRmsByVin = document.getElementById(m_vehRmsByVinCheckboxName);
            if ((elementVehDoRmsByLic != null) || (elementVehDoRmsByVin != null))
            {
                var vinFields = "";
                var licFields = "";
                if (elementVehDoRmsByLic != null)
                {
                    vinFields = "<input type=\"hidden\" name=\"" + m_vehRmsByLicHiddenFieldLicName + "\" id=\"" + m_vehRmsByLicHiddenFieldLicName + "\" />"
                        + "<input type=\"hidden\" name=\"" + m_vehRmsByLicHiddenFieldStateName + "\" id=\"" + m_vehRmsByLicHiddenFieldStateName + "\" />";
                }
                if (elementVehDoRmsByVin != null)
                {
                    licFields = "<input type=\"hidden\" name=\"" + m_vehRmsByVinHiddenFieldVinName + "\" id=\"" + m_vehRmsByVinHiddenFieldVinName + "\" />";
                }
                return licFields + vinFields;
            }
            else
            {
                return "";
            }
        }
    }
    else
    {
        return "";
    }
}
function HandleVehicleRmsFieldsBeforeSubmit()
{
    //how to setup Rms.
    //we do not use doRms,doRmsByLic, and doRmsByVin at the same time.
    //use cases of Rms person:
    //1-doRms
    //  -make sure doRms and labelRms are not commented out.
    //  -make sure doRmsByLic and labelRmsByLic are commented out.
    //  -make sure doRmsByVin and labelRmsByVin are commented out.
    //2-doRmsByLic and/or doRmsByVin.
    //  -make sure doRmsByLic and labelRmsByLic are not commented out.
    //  -make sure doRmsByVin and labelRmsByVin are not commented out.
    //  -make sure doRms and labelRms are commented out.
    //3-not using Rms.
    //  -make sure doRmsByLic and labelRmsByLic are commented out.
    //  -make sure doRmsByVin and labelRmsByVin are commented out.
    //  -make sure doRms and labelRms are commented out.
    //  -return true;

    // Rms Query

    var elementVehDoRms = document.getElementById(m_vehdoRmsCheckboxName);
    var elementVehState = document.getElementById(m_vehStateFieldName);
    var elementVehVIN = document.getElementById(m_vehVinFieldName);
    var elementVehLIC = document.getElementById(m_vehLicFieldName);

    if ((elementVehDoRms != null) && (elementVehDoRms.checked))
    {
        if (CheckVehicleRmsRequiredFields())
        {
            // Rms name lic check or vin check
            if (((elementVehLIC != null) && (elementVehLIC.value != "")) || ((elementVehVIN != null) && (elementVehVIN.value != "")))
            {
                // Rms lic and state check
                var elementVehRmsLic = document.getElementById(m_vehRmsHiddenFieldLicName);
                if ((elementVehLIC != null) && (elementVehRmsLic != null))
                    elementVehRmsLic.value = elementVehLIC.value;

                var elementVehRmsState = document.getElementById(m_vehRmsHiddenFieldStateName);
                if ((elementVehState != null) && (elementVehRmsState != null))
                    elementVehRmsState.value = elementVehState.value; //optional
                // Rms vin check
                var elementVehRmsVin = document.getElementById(m_vehRmsHiddenFieldVinName);
                if ((elementVehVIN != null) && (elementVehRmsVin != null))
                    elementVehRmsVin.value = elementVehVIN.value;

               return true;
            }
            else
            {
                //required field combinations are not met.
                alert(m_vehLicFieldName + " or " + m_vehVinFieldName + " is needed to query Rms.");
                return false;
            }
        }

    }
    else
    {
        var elementVehdoRmsByLic = document.getElementById(m_vehdoRmsByLicCheckboxName);
        var elementVehdoRmsByVin = document.getElementById(m_vehRmsByVinCheckboxName);
        if (((elementVehdoRmsByVin != null) && (elementVehdoRmsByVin.checked)) || ((elementVehdoRmsByLic != null) && (elementVehdoRmsByLic.checked)))
        {
            if (CheckVehicleRmsRequiredFields())
            {
                if ((elementVehdoRmsByLic != null) && (elementVehdoRmsByLic.checked))
                {
                    if (elementVehLIC.value != "")
                    {

                        // Rms lic and state check

                        var elementVehRmsByLicLic = document.getElementById(m_vehRmsByLicHiddenFieldLicName);
                        elementVehRmsByLicLic.value = elementVehLIC.value;

                        var elementVehRmsByLicState = document.getElementById(m_vehRmsByLicHiddenFieldStateName);
                        if ((elementVehState != null) && (elementVehRmsByLicState != null))
                            elementVehRmsByLicState.value = elementVehState.value; //optional

                    }
                    else
                    {
                        //required field combinations are not met.
                        alert(m_vehLicFieldName + " is needed to query Rms.");
                        return false;

                    }

                }
                if ((elementVehdoRmsByVin != null) && (elementVehdoRmsByVin.checked))
                {
                    if (elementVehVIN.value != "")
                    {
                        // Rms vin check

                        var elementVehRmsByVinVin = document.getElementById(m_vehRmsByVinHiddenFieldVinName);
                        elementVehRmsByVinVin.value = elementVehVIN.value;

                    }
                    else
                    {
                        //required field combinations are not met.
                        alert(m_vehVinFieldName + " is needed to query Rms.");
                        return false;
                    }
                }
                return true;
            }
            else
            {
                //failed fields existant.
                return false;
            }
        }
        else
        {
            //non  Rms check box is present.
            return true;
        }
    }

}
//---------------------end rms