<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Incident Comments</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Comments</H4>
						<P><h3>Comments for incident number <xsl:value-of select="/results/exemel/NewDataSet/Table/IncidentNumber"/></h3>

	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
							
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
		
	<table cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;color:white;border-width:1px;border-style:None;">
		<tr style="font-weight:bold;">
			<td>Date</td><td style="color:windowtext;background-color:window;"><xsl:value-of select="Date_Time"/></td>
			<td>By</td><td style="color:windowtext;background-color:window;"><xsl:value-of select="PerformedBy"/></td>
		</tr>
		<tr style="font-weight:bold;">
			<td>Comment</td><td style="color:windowtext;background-color:window;" colspan="3"><xsl:value-of select="Comment"/></td>
		</tr>
	</table>
	<p></p>

</xsl:template> 

</xsl:transform>