<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>All Events LOGIS</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
		<script type="text/javascript">
			setTimeout('Form.Query.click()',200);
		</script>
	</HEAD>
	<body>
	retrieving ......
	<form action="form.aspx?queryfile=GetIncidents_LOGIS.qry" method="post" id="Form" name="Form">
		<input type="hidden" name="radioname" id="radioname">
		<input type="hidden" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
		<input style="width:0px" type="submit" name="Query" id="Query" value="Submit">
	</form>
	</body>
	<script language="javascript">
	function window.onload() 
	{
		Form.Query.focus()
	}
	function OnAfterFormFill()
	{
		if (Form.radioname.value.substring(0,1) == 'B' || Form.radioname.value == 'DFLOGIS') {
			Form.radioname.value = 'Burnsville Law';
		}
		else
			Form.radioname.value = 'NONE'
	}
	</script>
</HTML>
