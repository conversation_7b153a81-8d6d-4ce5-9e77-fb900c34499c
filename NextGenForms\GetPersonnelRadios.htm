﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Get Personnel Radios</TITLE>
    <meta charset="utf-8" />

    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">
        $(document).ready(function () {

            //handle form submition
            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });

            $("#unitname").focus();
        });

    </script>

</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">GET PERSONNEL RADIOS</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="Flex-Form" action="GetPersonnelRadios.aspx?queryfile=GetPersonnelRadios.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <div class="row" style="margin-top:20px">
                <div class="col s8">
                    Unit Name:
                    <div class="input-field inline">
                        <input placeholder="" id="unitname" name="unitname" type="text">
                    </div>
                </div>
            </div>
        </div>
    </form>
</body>
</html>


