<?xml version="1.0"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
  <xsl:output method="html"/>
  <xsl:param name="listname"/>
  <xsl:param name="mandatory"/>
  <xsl:param name="parent"/>
  <xsl:param name="savelast"/>
  <xsl:param name="size"/>
  <xsl:param name="multiple"/>
  <xsl:param name="print"/>
  <xsl:param name="problemnature"/>
  <xsl:variable name="lowercase" select="'abcdefghijklmnopqrstuvwxyz'" />
  <xsl:variable name="uppercase" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'" />
  <xsl:template match="/">
    <!-- Only the root item has an .XSL transform defined. -->
    <xsl:element name="select">
      <xsl:attribute name="id">
        <xsl:value-of select="$listname"/>
      </xsl:attribute>
      <xsl:attribute name="name">
        <xsl:value-of select="$listname"/>
      </xsl:attribute>
      <xsl:attribute name="size">
        <xsl:value-of select="$size"/>
      </xsl:attribute>
      <xsl:attribute name="Class">Input</xsl:attribute>
      <xsl:attribute name="onKeyPress">SearchListBox(this, window.event)</xsl:attribute>
      <xsl:if test="$mandatory">
        <xsl:attribute name="style">background-color:lightblue;</xsl:attribute>
        <xsl:attribute name="mandatory">true</xsl:attribute>
      </xsl:if>
      <xsl:if test="$multiple">
        <xsl:attribute name="MULTIPLE">MULTIPLE</xsl:attribute>
      </xsl:if>
      <xsl:if test="$savelast">
        <xsl:attribute name="savelast">true</xsl:attribute>
      </xsl:if>
      <xsl:if test="$parent != ''">
        <xsl:attribute name="parent">
          <xsl:value-of select="$parent"/>
        </xsl:attribute>
      </xsl:if>
      <xsl:if test="not($mandatory)">
        <xsl:if test="not($multiple)">
          <xsl:element name="option"/>
        </xsl:if>
      </xsl:if>
      <xsl:if test="$print">
        <xsl:attribute name="print">true</xsl:attribute>
      </xsl:if>
      <xsl:for-each select="//ProblemNatures/ProblemNature[translate(@description, $lowercase, $uppercase)=translate($problemnature, $lowercase, $uppercase)]/Dispositions/Disposition">
        <xsl:element name="option">
          <xsl:choose>
            <xsl:when test="@selected">
              <xsl:attribute name="selected">
                <xsl:value-of select="true"/>
              </xsl:attribute>
            </xsl:when>
          </xsl:choose>
          <!-- The XML tag that will be used as the select value goes here. -->
          <xsl:attribute name="value">
            <xsl:value-of select="text()"/>
          </xsl:attribute>
          <!-- The XML tag that will be used as the option text goes here. -->
          <xsl:value-of select="text()"/>
        </xsl:element>
      </xsl:for-each>
    </xsl:element>
  </xsl:template>
</xsl:stylesheet>