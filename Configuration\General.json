{"Voice": "Microsoft David Desktop", "SpeechSpeed": 1, "PersistWatchList": true, "Watchlist": 1, "UseNextGenForms": true, "NewCommentsVoiceAlert": true, "IncidentUpdatesVoiceAlert": true, "NewMessagesVoiceAlert": true, "RecordCheckResponsesVoiceAlert": true, "BaseUnitFonts": 1, "PersonSpeechPattern": [{}], "VehicleSpeechPattern": [{}], "CryptographicAlgorithm": "", "UseHTMLQuery": true, "AddUserIDToCrewMemberList": true, "UseEncryption": true, "ConfidentialStrings": "", "InactivityTimeoutInSeconds": 9999999, "NoAckRetransmitSeconds": 120, "StatusRequestTimeoutInSeconds": 30, "DaysToKeepInArchive": 30, "AutomaticallySwitchToHotspot": true, "PlaySoundForNewWaitingIncident": true, "ShowMessageWhenConnectedOrDisconnected": true, "ShowMessageWhenUpdatesPauseDueToUserInactivity": true, "BrowserURL": "http://cadbrowser.logis.org/visinetbrowser/", "PictometryAPIKey": "", "PictometrySecretKey": "", "PictometryLoadURL": "http://pol.pictometry.com/ipa-analytics/load.php", "PictometryJavascriptURL": "http://pol.pictometry.com/ipa-analytics/embed/host.php?apikey=", "DisableRepeatResponsesAudibleAlert": true, "IsEnabled": true, "VersionNumber": 14}