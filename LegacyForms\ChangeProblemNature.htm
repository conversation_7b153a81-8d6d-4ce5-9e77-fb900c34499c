<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Change Problem Nature</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Change Problem Nature</H4>
						<form action="ChangeProblemNatureQuery.aspx?queryfile=ChangeProblemNature.qry" method="post" id="ChangeProblemNatureQuery" name="ChangeProblemNatureQuery">
							<table ID="Table2">								
								<tr>
									<td><b>Problem Nature:</b></td>
								</tr>
								<tr>
									<td>
										<!-- Root item requires a SPAN element to contain it. -->
										<XML id="problemnaturestyle" src="genericlist.xsl"></XML>
										<SPAN type="selectlist" id="problemnaturevals" name="problemnaturevals">
                                            <xml id="problemnaturesource" src="ProblemNature.xml"></xml>
										</SPAN>
									</td>
								</tr>																
							</table>
							<br>
							<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()" />
							<input type="hidden" id="IncidentID" name="IncidentID" />
                            <input type="hidden" id="UpgradeDowngradeReason" name="UpgradeDowngradeReason" value="" />
							<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit" />
						</form>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<script language="javascript">

	function window.onload()
	{
	    problemnaturevals.innerHTML = GenerateListBox("ProblemNature", "8", problemnaturesource, problemnaturestyle);
		ChangeProblemNatureQuery.ProblemNature.focus();
	}

	function window.validatepage()
	{
	    if (ChangeProblemNatureQuery.IncidentID.value)
		{
	        ChangeProblemNatureQuery.Submit.click();
		}
		else
		{
			alert('Must be assigned to an incident to submit this form.');			
		}
	}
	</script>
</HTML>
