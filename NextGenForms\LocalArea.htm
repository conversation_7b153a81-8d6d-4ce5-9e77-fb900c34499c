﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Local Area</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">

        $(document).ready(function () {

            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });
        });

        function AfterFillForm() {

            //Generate select input for states
            GenerateSelectBox("Station", "Station.xml", "genericselect.xsl", true, true, true, 1, false, true).then(function (result) {
                $("#stationvals").prepend(result);
                $('select').formSelect();
                $(".select-dropdown").focus();
            });

        }

    </script>
</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">LOCAL AREA</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="main" action="LocalArea.aspx?queryfile=LocalArea.qry" method="post" id="Form" name="Form">
        <div class="row" style="margin-top:20px">
            <div class="col s12">
                <div class="row">
                    <div class="input-field col s12 m4" type="selectlist" id="stationvals" name="stationvals">
                        <label for="Station">Station</label>
                    </div>
                </div>
            </div>
        </div>
    </form>
</body>
</html>