<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - On View</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top" width="800">
						<H4 align="center">On View</H4>
						<form action="Form.aspx?queryfile=OnView.qry" method="post" id="Form" name="Form">
							<table ID="Table2" >
								<tr>
									<td valign="top"><b>Problem:</b></td>
								</tr>
								<tr>
									<td width="400px">
										<!-- Root item requires a SPAN element to contain it. -->
										<XML id="pnstyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="pnvals" name="pnvals">
											<XML id="pnsource" src="problemnature.xml"></XML>
										</SPAN>
									</td>
									<td width="400px">
										<input onkeypress=validatepage() id=Query onclick=validatepage() type=button value=Submit name=Query>
									</td>
								</tr>
							</table>

							<table>
								<tr>
									<td valign="top" width="185"><b>Location:</b></td>
								</tr>
								<tr>
									<td align="right" >Current
										<input type="radio" class="bigradio" name="location" id="currentlocation" value="currentlocation" executefunction="ReverseGeoCode" parameters="Latitude=curlat&Longitude=curlong&Address=Address&City=City">
									</td>
									<td >
										<span id="curlat" name="curlat" formvalue="true"></span> &nbsp; &nbsp;
										<span id="curlong" name="curlong" formvalue="true"></span>
									</td>
								</tr>
								<tr>
									<td align="right" >Address
										<input type="radio" class="bigradio" name="location" id="addresslocation" value="addresslocation">
									</td>
									<td>
										<input type="text" name="Address" id="Address">
										City:&nbsp;&nbsp;<input type="text" name="City" id="City" size="15">
									</td>
								</tr>
								<tr>
									<td align="right" >Selected
										<input type="radio" class="bigradio" name="location" id="selectedlocation" value="selectedlocation" executefunction="ReverseGeoCode" parameters="Latitude=sellat&Longitude=sellong&Address=Address&City=City">
									</td>
									<td >
									Lat/Long&nbsp;&nbsp;&nbsp;&nbsp;
										<input type="text" id="sellat" name="sellat" fillbutton="selectbutton" size="9">&nbsp;&nbsp;
										<input type="text" id="sellong" name="sellong" fillbutton="selectbutton" size="9">

										<INPUT type="image" align="absMiddle" enablefill="true" value="Select from map" alt="Select from map" src="map.gif" id="selectbutton" name="selectbutton" simulateclickcontrolid="selectedlocation">

									</td>
								</tr>
							</table>

							<table height="6" width="491">
								<tr>
									<td valign="top" height="2" width="69"><b>Comment:</b></td>
									<td width="408" height="2"><textarea name="Comment" id="Comment" rows="2" cols="43" onkeyup="CheckTextAreaLength(this.form.Comment,200);"></textarea></td>
								</tr>
							</table>

							<br>
							<!-- Below are fields hidden for a variety of reasons. -->
							<input type="hidden" name="CallTaking_Performed_By" id="CallTaking_Performed_By">
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<input type="hidden" name="Lat" id="Lat" size="10">
							<input type="hidden" name="Long" id="Long" size="10">&nbsp;
                     <input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<script language="javascript">
	function window.onload()
	{
		PrepareValidation(Form);
		// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
		pnvals.innerHTML = GenerateSelectBox("problemnature", pnsource, pnstyle, true, false, true, 1, false, false);
	}
	function OnAfterFormFill()
	{
		if (!Form.currentlocation.checked &&
		    !Form.selectedlocation.checked &&
		    !Form.addresslocation.checked)
		{
			Form.currentlocation.click();
		}
	}
	function window.validatepage()
	{
		//if (Form.licensenumber.value == '')
		//{		
		//	Form.state.selectedIndex = -1;
		//}
		//else
		//{		
			//Append the licence number to the comment so that it will be saved in the incident
			//comment.
		//	Form.Comment.value += '\n License #: ' + Form.licensenumber.value;
		//}


		if (Form.currentlocation.checked)
		{
			if (curlat != null)
			{
				if (curlat.innerText == null)
					Form.Lat.value = '';
				else
					Form.Lat.value = curlat.innerText;
            		}
			else
				Form.Lat.value = '';

			if (curlong != null)
			{
				if (curlong.innerText == null)
					Form.Long.value = '';
				else
					Form.Long.value = curlong.innerText;
			}
			else
				Form.Long.value = '';

			// Form.Address.value = '';
		}
		else if (Form.selectedlocation.checked)
		{
			Form.Lat.value = Form.sellat.value;
			Form.Long.value = Form.sellong.value;
			// Form.Address.value = '';
		}
		else if (Form.addresslocation.checked)
		{
			Form.Lat.value = '';
			Form.Long.value = '';
		}

		if ((Form.selectedlocation.checked || Form.currentlocation.checked) && ((Form.Lat.value == 0) ||(Form.Long.value == 0)))
		{
			alert ("Latitude/Longitude cannot be zero");
			//Set focus
			if (Form.Lat.value == 0 )
			{
				Form.sellat.focus();
			}
			else
			{
				Form.sellong.focus();
			}
			return;
		}
		else if ((Form.addresslocation.checked) && (Form.Address.value.length <= 0))
		{
			alert("Please enter a location / address.");
			Form.Address.focus();
			return;
		}

			Form.Submit.click();
			
	}

	</script>
</HTML>
