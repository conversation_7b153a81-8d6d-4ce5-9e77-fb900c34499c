INPUT
{
	font-size: 12pt;
	font-family: <PERSON><PERSON>sole;
}

TEXTAREA
{
	font-size: 12pt;
	font-family: <PERSON><PERSON> Console;
}
SELECT
{
	font-size: 12pt;
	font-family: Verdana;
}
OPTION
{
	font-size: 12pt;
	font-family: Verdana;
}
A:link
{
	font-weight: bold;
	font-size: 12pt;
	font-family: Verdana;
	text-decoration: underline;
}
A:visited
{
	font-weight: bold;
	font-size: 12pt;
	font-family: Verdana;
	text-decoration: underline;
}
A:hover
{
	font-weight: bold;
	font-size: 12pt;

	font-family: Verdana;
	text-decoration: underline;
}

B
{
	font-weight: bold;
	font-size: 12pt;
	color: blue;
}
H2 {
	FONT-SIZE: 24px; COLOR: sienna; FONT-FAMILY: Verdana
}
H3 {
	BORDER-RIGHT: medium none; PADDING-RIGHT: 2px; BORDER-TOP: medium none; PADDING-LEFT: 2px; FONT-SIZE: 16px; PADDING-BOTTOM: 3px; TEXT-TRANSFORM: capitalize; BORDER-LEFT: medium none; COLOR: darkblue; PADDING-TOP: 3px; BORDER-BOTTOM: blue 1px solid; FONT-FAMILY: Verdana; TEXT-ALIGN: left
}
H4 {
	BORDER-RIGHT: medium none; BORDER-TOP: medium none; FONT-SIZE: 14pt; BORDER-LEFT: medium none; COLOR: blue; BORDER-BOTTOM: medium none; FONT-FAMILY: Verdana
}
H5 {
	FONT-SIZE: 10px; COLOR: darkgoldenrod; FONT-FAMILY: Verdana
}
BODY {
	FONT-SIZE: 12pt; COLOR: blue; FONT-FAMILY: Verdana; 
}
LI {
	LIST-STYLE-TYPE: square
}
TABLE {
	FONT-SIZE: 12pt; FONT-FAMILY: Verdana
}
HR {
	BORDER-RIGHT: medium none; BORDER-TOP: medium none; FONT-SIZE: 1px; FLOAT: left; BORDER-LEFT: medium none; COLOR: blue; BORDER-BOTTOM: medium none; POSITION: relative; HEIGHT: 1px
}
.base {
	BORDER-RIGHT: blue thin solid; PADDING-RIGHT: 10px; BORDER-TOP: blue thin solid; PADDING-LEFT: 10px; PADDING-BOTTOM: 10px; MARGIN: 10px; BORDER-LEFT: blue thin solid; PADDING-TOP: 10px; BORDER-BOTTOM: blue thin solid; 
}
.rightaligncell {
	PADDING-RIGHT: 2px; PADDING-LEFT: 2px; FONT-WEIGHT: bold; PADDING-BOTTOM: 2px; PADDING-TOP: 2px; TEXT-ALIGN: right
}
.topbottombordercell {
	BORDER-RIGHT: blue; PADDING-RIGHT: 2px; BORDER-TOP: blue 1px solid; PADDING-LEFT: 2px; PADDING-BOTTOM: 2px; BORDER-LEFT: blue; COLOR: blue; PADDING-TOP: 2px; BORDER-BOTTOM: blue 1px solid; TEXT-ALIGN: right
}
.bottombordercell {
	PADDING-RIGHT: 2px; PADDING-LEFT: 2px; PADDING-BOTTOM: 2px; PADDING-TOP: 2px; BORDER-BOTTOM: blue thin solid; 
}
.bottomborderpanel {
	BORDER-RIGHT: blue 1px; PADDING-RIGHT: 5px; BORDER-TOP: blue 1px; PADDING-LEFT: 5px; PADDING-BOTTOM: 10px; BORDER-LEFT: blue 1px; PADDING-TOP: 5px; BORDER-BOTTOM: blue 1px solid; TEXT-ALIGN: left
}
.topbordercell {
	PADDING-RIGHT: 10px; BORDER-TOP: blue thin solid; PADDING-LEFT: 10px; PADDING-BOTTOM: 10px; PADDING-TOP: 10px; TEXT-ALIGN: left
}
.rightbordercell {
	BORDER-RIGHT: blue 1px solid
}
.leftbordercell {
	BORDER-LEFT: blue 1px solid
}
.message {
	FONT-WEIGHT: bolder; FONT-SIZE: 12pt; TEXT-ALIGN: center
}
.presentationdelimiter {
	color:#ccffff
}
.xslsubheading {
	font-size: 8pt;
}
BODY
{
    FONT-SIZE: 12pt;
    FONT-FAMILY: Arial
}
BODY TD
{
    FONT-SIZE: 12pt;
}
BODY TD .title
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    COLOR: rgb(61, 147, 156);
    FONT-FAMILY: Arial;
    TEXT-ALIGN: right;
    TEXT-DECORATION: none
}
BODY TD .subtitle
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    FONT-FAMILY: Arial;
    TEXT-ALIGN: center;
    TEXT-DECORATION: none
}
BODY TD .InputNames
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    COLOR: rgb(61, 147, 156);
    FONT-FAMILY: Arial
}
BODY TD .InputData
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    FONT-FAMILY: Courier
}

.bigcheckbox
{
	WIDTH: 59px; 
	HEIGHT: 28px;
}

.bigradio
{
	WIDTH: 59px; 
	HEIGHT: 28px;
}
