﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Unit TimeStamp</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
					    <!-- Change header (H4 tag) to the name of the TimeStamp.For example, change "UpdateText" to "Check List Complete" -->
						<H4 align="center">UpdateText</H4>
						<form action="SetUnitTimeStamp.aspx?queryfile=SetUnitTimeStamp.qry" method="post" id="Form" name="Form">
							<table ID="Table2" align="center">
							    <tr>
									<td>
										<table>
											<td>
												Incident:
											</td>
											<td>
												<input style="width:0px;" type="text" name="IncidentID" id="IncidentID" readonly>
												<input type="text" name="IncidentNumber" id="IncidentNumber" readonly>
											</td>
										</table>
									</td>
								</tr>							
								<tr align="center">									
									<td>
										<!-- Change value to the Description/Name of the TimeStamp. For example, change " UpdateText" to " Check List Complete" -->
										<input type="hidden" name="UserTimeName" id="UserTimeName" value="UpdateText">
									</td>
								</tr>								
								<tr align="center">									
									<td>
							
							<input type="hidden" name="TimeStamp" id="TimeStamp">
							
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<div id=SubmitButton name=SubmitButton>
								<input type="submit" name="Query" id="Query" value="Submit">
							</div>
									</td>
								</tr>
							</table>								
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script LANGUAGE="VBScript" src="clientutilities.vbs"></script>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
	    function window.onload()
	    {
			// When unit not assigned, default IncidentID is zero. 
			Form.IncidentID.value = "0";
	    }

	    function OnAfterFormFill()
	    {
	        Form.TimeStamp.value = GetCurrentDate();
	    }
		
	</SCRIPT>
</HTML>
