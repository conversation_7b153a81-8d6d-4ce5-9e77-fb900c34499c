<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Incident Unit Details</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="IncidentUnitDetails.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Unit Details</H4>
						<P>Unit Details for Incident 

	<xsl:apply-templates select="results/exemel/NewDataSet"/>

						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet">
	<xsl:value-of select="Table[1]/IncidentNumber"/>
	<br></br>

	<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;color:white;border-width:1px;border-style:None;">
		<tr style="font-weight:bold;"><td>Unit</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Unit"/></td></tr>
		<tr style="font-weight:bold;"><td>Vehicle</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Vehicle"/></td></tr>
		<xsl:if test="Table[1]/ResponseNumber!=''">
			<tr style="font-weight:bold;">
				<td>Response Number</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="Table[1]/ResponseNumber"/>
				</td>
			</tr>
		</xsl:if>
		<tr style="font-weight:bold;"><td>Transported Person(s)</td>
			<td style="background-color:window;color:windowtext;">
			<table style="font-weight:bold">
				<xsl:for-each select="Table">
					<tr>
						<td>
							<xsl:value-of select="TransportFirstName"/>&#32;
							<xsl:value-of select="TransportLastName"/>
						</td>	
					</tr>
				</xsl:for-each>
			</table>
			</td>
		</tr>
		<tr style="font-weight:bold;"><td>Personnel</td>
			<td style="background-color:window;color:windowtext;">
			<table style="font-weight:bold">
				<xsl:for-each select="Table">
					<tr>
						<td><xsl:value-of select="EmployeeID"/></td>
						<td></td>
						<td><xsl:value-of select="PersonnelName"/></td>
					</tr>
				</xsl:for-each>
			</table>
			</td>
		</tr>
		<tr style="font-weight:bold;"><td>Sent From</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Station"/></td></tr>
		<tr style="font-weight:bold;"><td>Cancel Reason</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/CancelReason"/></td></tr>
		<tr style="font-weight:bold;"><td>Disposition</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Disposition"/></td></tr>
		<tr style="font-weight:bold;"><td>Transport Dest</td><td style="background-color:window;color:windowtext;">
			<xsl:if test="Table[1]/TransportLocation!=''">
				<xsl:value-of select="Table[1]/TransportLocation"/>:&#32;
			</xsl:if>
			<xsl:value-of select="Table[1]/TransportAddress"/>&#32;
			<xsl:if test="Table[1]/TransportApartment!=''">
				Apt&#32;<xsl:value-of select="Table[1]/TransportApartment"/>&#32;
			</xsl:if>
			<xsl:if test="Table[1]/TransportCity!=''">
				,&#32;<xsl:value-of select="Table[1]/TransportCity"/>
			</xsl:if>
		</td></tr>
		<tr style="font-weight:bold;"><td>Start Odometer</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/OdometerAtScene"/></td></tr>
		<tr style="font-weight:bold;"><td>End Odometer</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/OdometerAtDestination"/></td></tr>
		<tr style="font-weight:bold;"><td>Mileage Total</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TransportMileage"/></td></tr>
		<tr style="font-weight:bold;"><td>Assigned</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TimeAssigned"/></td></tr>
		<tr style="font-weight:bold;"><td>Enroute</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TimeEnroute"/></td></tr>
		<tr style="font-weight:bold;"><td>Staged</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TimeStaged"/></td></tr>
		<tr style="font-weight:bold;"><td>Arrived</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TimeAtScene"/></td></tr>
		<tr style="font-weight:bold;"><td>C4/At Patient</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TimeC4"/></td></tr>
		<tr style="font-weight:bold;"><td>Depart Scene</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TimeDepartScene"/></td></tr>
		<tr style="font-weight:bold;"><td>Arrived Dest</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TimeArriveDestination"/></td></tr>
		<tr style="font-weight:bold;"><td>Delayed Avail</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/TimeDelayedAvail"/></td></tr>
        </table>

</xsl:template> 

</xsl:transform>