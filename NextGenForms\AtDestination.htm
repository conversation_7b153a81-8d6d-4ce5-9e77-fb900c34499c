﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - At Destination</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />

    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">
        $(document).ready(function () {

            //handle form submition
            $("#Form").submit(function () {

                if ($(this)[0].checkValidity() == true) {
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;

            });

            //focus on registration id field
            $("#currentodometer").focus();

        });

        function decimalCheck(input, event) {
            var str = input.value;
            if (event.keyCode == 69) {
                return false;
            }
            else if (str.includes(".") && !isNaN(event.key)) {
                var index = str.indexOf(".");
                if (str.length - index > 1) {
                    return false;
                }
            }
            return true;
        }

        function lengthCheck(input) {
            if (input.value.includes(".")) {
                if (input.value.length > input.maxLength + 2) {
                    input.value = input.value.slice(0, input.maxLength);
                }
            }
            else {
                if (input.value.length > input.maxLength) {
                    input.value = input.value.slice(0, input.maxLength);
                }
            }
            return input;
        }

    </script>

</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">AT DESTINATION</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="main" action="AtDestinationQuery.aspx?queryfile=atdestination.qry" method="post" id="Form" name="Form">
        <div class="row">
            <div class="s12">
                <div class="input-field col s2" style="margin-top: 30px">
                    <input name="currentodometer" id="currentodometer" type="number" step=".1" maxlength="9" placeholder=""
                           oninput="return lengthCheck(this)"
                           onkeydown="return decimalCheck(this, event)" required>
                    <label for="currentodometer" class="active">Current Odometer</label>
                </div>
            </div>
        </div>
    </form>
</body>
</html>


