﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Clear Call</TITLE>
    <meta charset="utf-8" name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />
    <link rel="stylesheet" type="text/css" href="icons.css" />
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />

    <!-- Compiled and minified JavaScript -->
    <script src="jquery.min.js" type="text/javascript"></script>
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>
    <script language="javascript">

        var ActionTakenLoaded = false;
        var FirstFillFormTaken = false;

        $(document).ready(function () {

            $("#Form").submit(function () {
                if (($("input[name='ShowRPData']:checked").val() == 'Yes')) {
                    if (ValidateRadioRequiredFields($('input[required]:hidden')) && $(this)[0].checkValidity() == true) {
                        var $form = $(this);
                        PromptErrorRPData().then(function (valid) {
                            if (valid) {
                                PromptIncidentTags().then(function () {
                                    //remove unnecessary radio inputs
                                    $form.find("input[type=radio]").attr("disabled", true);
                                    $("#incidentnumber").attr("disabled", false);
                                    var values = $form.serialize();
                                    SubmitQuery(values, $form.attr('action'));
                                });
                            }
                        });
                    }
                    else {
                        $('select').filter('[required]').each(function () {
                            ValidateRequiredSingleSelectOnly($(this));
                        });
                    }
                }
                else if ($("#IsClosedIncident").val() == "true") {
                    $('#addbtn').hide();
                    openModal('Validation Error', "Must submit a change to form.").then(function () {
                        $('#addbtn').show();
                    });
                }
                else if(ValidateRadioRequiredFields($('#RPRadio')) && $(this)[0].checkValidity() == true) {
                    var $form = $(this);
                    PromptIncidentTags().then(function () {
                        var values = $form.serialize();
                        SubmitQuery(values, $form.attr('action'));
                    });
                }

                return false;
            });

            $("#incidentTagsVals").hide();

            $("#RPData").hide();

            $('.with-gap[name="ShowRPData"]').change(function () {
                if ($("input[name='ShowRPData']:checked").val() == 'Yes') {
                    ShowRPData(null);
                    $("#RPData").find("select").attr("disabled", false);
                    $("#RPData").find("input").attr("disabled", false);
                }
                else {
                    $("#RPData").hide();
                    $("#RPData").find("select").attr("disabled", true);
                    $("#RPData").find("input").attr("disabled", true);
                }
            });

        });

        function AfterFillForm() {

            if (!FirstFillFormTaken) {

                FirstFillFormTaken = true;

                $('.modal').modal();
                if (!CefSharpEnabled)//xamarin only
                {
                    InsertIncidentTagsSelect();
                }

                if ($("#IsClosedIncident").val() == "true") {
                    GetIncidentSummaryInfo().then(function (result) {
                        ShowRPData(result);
                    });

                    $("#textarea1").attr("disabled", true);

                    SingleSelect();
                    //  MultiSelect();
                }
                else {
                    GetIncidentNumber().then(function (result) {
                        $("#incidentnumber").val(result);
                        M.updateTextFields();
                    });

                    GetProblemNature().then(function (result) {
                        $("#pnvals").val(result);
                    });

                    GetIncidentId().then(function (result) {
                        $("#IncidentID").val(result);
                        M.updateTextFields();
                    });

                    var problemnature = $("#pnvals").val();

                    // < !--To allow users to select multiple dispositions: 1. Comment out SingleSelect(problemnature); 2. Uncomment MultiSelect(problemnature)-- >
                    SingleSelect(problemnature);
                    //  MultiSelect(problemnature);
                }

            }
        }
        function SingleSelect(problemnature) {

            // By default form does not set problem nature so it uses DispositionCodes.xml
            // To use DispositionCodesByProblemNature.xml 1. Comment out section labeled HIDE PROBLEM NATURE and Uncomment section labeled SHOW PROBLEM NATURE
            // 2. Comment out section labeled Use DispositionCodes.xml and Uncomment section labeled Use DispositionCodesByProblemNature.xml

            //<!--Use DispositionCodes.xml-->
            //<!--START-->
            GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
                $("#dispositionvals").prepend(result);
                $("#dispositionvals option:first").attr("selected", "selected");
                $('select').formSelect();
                SetSelectBoxFromParameters($("#ResponseDisposition"));
                $(".select-dropdown").focus();

            });
            //<!--END-->
            //<!--Use DispositionCodesByProblemNature.xml-->
            // Disposition Codes are limited by ProblemNature using the DispositionCodesByProblemNature.xml. If DispositionCodesByProblemNature.xml does not exist or Problem Nature is not pulled from incident then DispositionCodes.xml will be used.
            //<!-- START-->
            //if (problemnature != undefined && problemnature != "") {
            //        GenerateListBox("ResponseDisposition", 8, "DispositionCodesByProblemNature.xml", "dispositionbyproblemnaturelist.xsl", problemnature).then(function (result) {
            //            if (result != "") {
            //                $("#dispositionvals").prepend(result);
            //                $("#dispositionvals option:first").attr('selected', 'selected');
            //                $('select').formSelect();
            //                $(".select-dropdown").focus();
            //            }
            //            else {
            //                GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
            //                    $("#dispositionvals").prepend(result);
            //                    $("#dispositionvals option:first").attr('selected', 'selected');
            //                    $('select').formSelect();
            //                    $(".select-dropdown").focus();
            //                });
            //            }
            //        });
            //    }
            //    else {
            //        GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
            //            $("#dispositionvals").prepend(result);
            //            $("#dispositionvals option:first").attr('selected', 'selected');
            //            $('select').formSelect();
            //                $(".select-dropdown").focus();
            //        });
            //}
            //<!--END-->

        }
        function MultiSelect(problemnature) {

            // By default form does not set problem nature so it uses DispositionCodes.xml
            // To use DispositionCodesByProblemNature.xml 1. Comment out section labeled HIDE PROBLEM NATURE and Uncomment section labeled SHOW PROBLEM NATURE
            // 2. Comment out section labeled Use DispositionCodes.xml and Uncomment section labeled Use DispositionCodesByProblemNature.xml

            //<!--Use DispositionCodes.xml-->
            //<!--START-->
            GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
                $("#dispositionvals").prepend(result);
                $("#dispositionvals option:first").attr("selected", "selected");
                $('select').formSelect();
                SetMultiSelectBoxFromParameters($("#ResponseDisposition"));
                $("#ResponseDisposition").prop("required", true);
                $("#ResponseDisposition").change(ValidateRequiredSelect);
                $(".select-dropdown").focus();
            });
            //<!--END-->
            //<!--Use DispositionCodesByProblemNature.xml-->
            // Disposition Codes are limited by ProblemNature using the DispositionCodesByProblemNature.xml. If DispositionCodesByProblemNature.xml does not exist or Problem Nature is not pulled from incident then DispositionCodes.xml will be used.
            //<!-- START-->
            //if (problemnature != undefined && problemnature != "") {
            //        GenerateSelectBox("ResponseDisposition", "DispositionCodesByProblemNature.xml", "dispositionbyproblemnatureselect.xsl", false, true, true, 8, true, true, problemnature).then(function (result) {
            //            if (result != "") {
            //                $("#dispositionvals").prepend(result);
            //                $("#dispositionvals option:first").attr('selected', 'selected');
            //                $('select').formSelect();
            //                $(".select-dropdown").focus();
            //            }
            //            else {
            //                GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
            //                    $("#dispositionvals").prepend(result);
            //                    $("#dispositionvals option:first").attr('selected', 'selected');
            //                    $('select').formSelect();
            //                    $(".select-dropdown").focus();
            //                });
            //            }
            //        });
            //    }
            //    else {
            //        GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
            //            $("#dispositionvals").prepend(result);
            //            $("#dispositionvals option:first").attr('selected', 'selected');
            //            $('select').formSelect();
            //            $(".select-dropdown").focus();
            //        });
            //}
            //<!--END-->
        }
        function ShowRPData(parameters) {

            $("#RPData").show();

            if (!ActionTakenLoaded) {
                GenerateSelectBox("Field5_1", "LocationofStop.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                    $("#locationofstopvals").prepend(result);
                    $('select').formSelect();
                    $("#Field5_1").prop("required", true);
                    $("#Field5_1").change(function () {
                        ValidateRequiredSingleSelectOnly($(this));
                    });
                    GenerateSelectBox("Field7_1", "ReasonforStop.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                        $("#reasonforstopvals").prepend(result);
                        $('select').formSelect();
                        $("#Field7_1").prop("required", true);
                        $("#Field7_1").change(function () {
                            ValidateRequiredSingleSelectOnly($(this));
                        });
                        GenerateSelectBox("Field8_1", "PersonType.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                            $("#persontypevals").prepend(result);
                            $('select').formSelect();
                            $("#Field8_1").prop("required", true);
                            $("#Field8_1").change(function () {
                                ValidateRequiredSingleSelectOnly($(this));
                            });
                            GenerateSelectBox("Field9_1", "Race.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                                $("#racevals").prepend(result);
                                $('select').formSelect();
                                $("#Field9_1").prop("required", true);
                                $("#Field9_1").change(function () {
                                    ValidateRequiredSingleSelectOnly($(this));
                                });
                                GenerateSelectBox("Field10_1", "Gender.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                                    $("#gendervals").prepend(result);
                                    $('select').formSelect();
                                    $("#Field10_1").prop("required", true);
                                    $("#Field10_1").change(function () {
                                        ValidateRequiredSingleSelectOnly($(this));
                                    });
                                    GenerateSelectBox("Field13_1", "ActionTaken.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                                        $("#actiontakenvals").prepend(result);
                                        $('select').formSelect();
                                        $("#Field13_1").prop("required", true);
                                        $("#Field13_1").change(function () {
                                            ValidateRequiredSingleSelectOnly($(this));
                                        });
                                        GenerateSelectBox("Field14_1", "Charge.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                                            $("#chargevals").prepend(result);
                                            $('select').formSelect();
                                            $("#Field14_1").prop("required", true);
                                            $("#Field14_1").change(function () {
                                                ValidateRequiredSingleSelectOnly($(this));
                                            });
                                            GenerateSelectBox("Field16_1", "ReasonforArrest.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                                                $("#reasonforarrestvals").prepend(result);
                                                $('select').formSelect();
                                                GenerateSelectBox("Field18_1", "WhoWasInjured.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                                                    $("#whowasinjuredvals").prepend(result);
                                                    $('select').formSelect();
                                                    GenerateSelectBox("Field21_1", "ProbableCause.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                                                        $("#probablecauseforsearchvals").prepend(result);
                                                        $('select').formSelect();
                                                        GenerateSelectBox("Field24_1", "TypeofContraband.xml", "genericselect.xsl", false, false, false, 8, true, false).then(function (result) {
                                                            $("#typeofcontrabandfoundvals").prepend(result);
                                                            $('select').formSelect();
                                                            if (parameters != null) {
                                                                $(".header").find('h5').text("MODIFY RACIAL PROFILE DATA");
                                                                $("#dispositionvals").find(".select-dropdown").prop("disabled", true);
                                                                paramObjs = JSON.parse(parameters);
                                                                if (paramObjs != null && paramObjs.length > 0) {
                                                                    $("#RPDataRadio").prop('checked', true);
                                                                    $("#RPRadio").val("Yes");
                                                                    for (var i = 0; i < paramObjs.length; i++) {
                                                                        for (var j = 0; j < paramObjs[i].length; j++) {
                                                                            paramObjs[i][j].Key = paramObjs[i][j].Key + '_' + (i + 1)
                                                                        }

                                                                        if (i > 0) {
                                                                            AddActionsTakenField();
                                                                        }

                                                                        FillForm(JSON.stringify(paramObjs[i]), true);
                                                                        paramObjs = JSON.parse(parameters);
                                                                    }

                                                                    UpdateRadioFields();
                                                                }
                                                                else {
                                                                    $("#NoShowRPDataRadio").prop('checked', true);
                                                                    $("#RPRadio").val("No");
                                                                    $("#RPData").find("select").attr("disabled", true);
                                                                    $("#RPData").find("input").attr("disabled", true);
                                                                    $("#RPData").hide();
                                                                }
                                                            }
                                                        });
                                                    });
                                                });
                                            });
                                        });
                                    });
                                });
                            });
                        });
                    });
                });
            }

            ActionTakenLoaded = true;

        }
        function AddActionsTakenField() {
            //Add new action taken row
            var $actionsTakenFields = $(".actions-taken");
            var $clone = $actionsTakenFields.last().clone();
            $actionsTakenFields.find(".addActionTakenBtn").hide();
            RenameActionsTakenFields($clone);
            $("#actionsTakenAgainstPersonsDiv").append($clone);
            $actionsTakenFields.css({ "border-bottom": "1px solid #DADCE0", "margin-bottom": "20px" });
            //Clear new radio controls
            var $inputs = $clone.find('input[type=hidden]');
            $inputs.val("");
            $clone.find('.with-gap').prop('checked', false);
            $inputs.each(function () {
                $(this).parent().removeClass("invalid-radio-field");
            });
            //Clear and recreate new select controls
            var $selectInputFields = $clone.find('.input-field[type = selectlist]');
            $selectInputFields.each(function () {
                var $select = $(this).find('select');
                var $label = $(this).find('label').last();
                if ($select.length > 0 && $label.length > 0) {
                    $(this).empty();
                    $(this).append($select);
                    $(this).append($label);
                    $select.formSelect();
                    $label.removeClass("select-invalid");
                    var $input = $select.parent().find('input');
                    if ($input != null) {
                        $input.removeClass("select-invalid-underline");
                    }
                    if ($select.prop('required')) {
                        $select.change(function () {
                            ValidateRequiredSingleSelectOnly($(this));
                        });
                    }
                }
            });

            CheckVisibilityOfFields($clone);

            SetTabIndex();
        }
        function DeleteActionsTakenField(ev) {
            //Remove the current row
            var $actionsTakenFields = $(".actions-taken");
            if ($actionsTakenFields.length > 1) {
                $(ev.currentTarget).closest(".actions-taken").remove();
                $actionsTakenFields = $(".actions-taken");
                $actionsTakenFields.find(".addActionTakenBtn").hide();
                $actionsTakenFields.last().find(".addActionTakenBtn").show();
            }
            else {
                //Clear the only action taken section
                var $actionTakenSection = $(ev.currentTarget).closest(".actions-taken");
                $actionTakenSection.find('.with-gap').prop('checked', false);
                $actionTakenSection.find('input[type=hidden]').val("");
                ValidateRadioRequiredFields($actionTakenSection.find('input[required]:hidden'));
                var $selectInputFields = $actionTakenSection.find('.input-field[type = selectlist]');
                $selectInputFields.each(function () {
                    var $select = $(this).find('select');
                    if ($select.length > 0) {
                        var $selectMultiple = $(this).find('select[multiple=MULTIPLE]');
                        if ($selectMultiple.length > 0) {
                            var $options = $selectMultiple.find('option');
                            $options.prop("selected", false);
                        }
                        else {
                            var $option = $select.find('option').first();
                            $option.prop("selected", true);
                        }
                        $select.formSelect();

                        if ($select.prop('required')) {
                            ValidateRequiredSingleSelectOnly($select);
                        }
                    }
                });
            }

            $actionsTakenFields.last().css({ "border-bottom": "", "margin-bottom": "" });

            RenameActionsTakenFields();

            SetTabIndex();
        }
        function RenameActionsTakenFields($target) {
            if ($target != null) {
                var $fields = $target.find("input:not(:checkbox), select").not('.select-dropdown');
                if ($fields.length > 0) {
                    $fields.each(function () {
                        var array = $(this).attr("name").split('_');
                        var number = parseInt(array[1]);
                        $(this).attr("name", array[0] + '_' + (number + 1))
                    });
                }
            }
            else {
                var $actionsTakenFields = $(".actions-taken");
                if ($actionsTakenFields.length > 0) {
                    $actionsTakenFields.each(function (index) {
                        var $fields = $(this).find("input:not(:checkbox), select").not('.select-dropdown');
                        if ($fields.length > 0) {
                            $fields.each(function () {
                                var array = $(this).attr("name").split('_');
                                $(this).attr("name", array[0] + '_' + (index + 1))
                            });
                        }
                    });
                }
            }
        }
        async function PromptErrorRPData() {
            var validationText = "";
            //Can only have 1 Driver person record
            var $personsCheck = $("select[name^='Field8']");
            if ($personsCheck.length > 1) {
                var hasDriver = false;
                $personsCheck.each(function () {
                    if ($(this).val() == 'Driver') {
                        if (hasDriver) {
                            validationText += 'Error: Only 1 Driver record is allowed.<br>';
                        }
                        hasDriver = true;
                    }
                });
            }

            var $actionsTakenRows = $(".actions-taken");
            var reasonforArrest = true;
            var didOfficerMakeArrestMustBeTrue = true;
            var didOfficerMakeArrestMustBeFalse = true;
            var arrestConsistency = true;
            var physicalForceUsed = true;
            var specifySearchConsensual = true;
            var specifyContraBandFound = true;
            var leastOneContraband = true;
            var specifyPC = true;
            $actionsTakenRows.each(function () {
                //Reason for Arrest is required when "Did Officer Make an Arrest" = Yes.
                if ($(this).find("input[name^='Field15']").val() == 'Yes' && $(this).find("select[name^='Field16']").val().length < 1) {
                    if (reasonforArrest) {
                        validationText += 'Error: Reason for Arrest is required when an arrest is made.<br>';
                        reasonforArrest = false;
                    }
                }
                //Did Officer make an Arrest is required to be Yes when Action Taken is an Arrest.
                if ($(this).find("select[name^='Field13']").val().toUpperCase().includes("ARREST") && $(this).find("input[name^='Field15']").val() != 'Yes') {
                    if (didOfficerMakeArrestMustBeTrue) {
                        validationText += 'Error: Did Officer make an Arrest is required to be Yes when Action Taken is an Arrest.<br>';
                        didOfficerMakeArrestMustBeTrue = false;
                    }
                }
                //Did Officer make an Arrest is required to be No when Action Taken isn't an Arrest.
                if (!$(this).find("select[name^='Field13']").val().toUpperCase().includes("ARREST") && $(this).find("input[name^='Field15']").val() != 'No') {
                    if (didOfficerMakeArrestMustBeFalse) {
                        validationText += 'Error: Did Officer make an Arrest is required to be No when Action Taken is not an Arrest.<br>';
                        didOfficerMakeArrestMustBeFalse = false;
                    }
                }
                //Did Officer Make an Arrest must = Yes, when the finding of contraband resulted in Arrest.
                if ($(this).find("input[name^='Field15']").val() == 'No' && $(this).find("input[name^='Field23']").val() == 'Yes') {
                    if (arrestConsistency) {
                        validationText += 'Error: Did Officer Make an Arrest must = Yes, when the finding of contraband resulted in Arrest.<br>';
                        arrestConsistency = false;
                    }
                }
                //Was Physical Force Resulting in Bodily Injury required when "Was physical force resulting in bodily injury used during the stop" = Yes
                if ($(this).find("input[name^='Field17']").val() == 'Yes' && $(this).find("select[name^='Field18']").val().length < 1) {
                    if (physicalForceUsed) {
                        validationText += 'Error: Must specify who was injured when an injury occurs.<br>';
                        physicalForceUsed = false;
                    }
                }
                //Was Search Consensual required when "Was Search Conducted" = Yes
                if ($(this).find("input[name^='Field19']").val() == 'Yes' && $(this).find("input[name^='Field20']").val().length < 1) {
                    if (specifySearchConsensual) {
                        validationText += 'Error: Must specify Was Search Consensual when a search was conducted.<br>';
                        specifySearchConsensual = false;
                    }
                }
                //Contraband Found(Yes / No) is required when when "Was Search Conducted" = Yes
                if ($(this).find("input[name^='Field19']").val() == 'Yes' && $(this).find("input[name^='Field22']").val().length < 1) {
                    if (specifyContraBandFound) {
                        validationText += 'Error: Must specify Contraband Found when a search was conducted.<br>';
                        specifyContraBandFound = false;
                    }
                }
                //Probable Cause required when "Was Search Consensual" = No
                if ($(this).find("input[name^='Field20']").val() == 'No' && $(this).find("select[name^='Field21']").val().length < 1) {
                    if (specifyPC) {
                        validationText += 'Error: PC for Search is required when the search conducted was not consensual.<br>';
                        specifyPC = false;
                    }
                }
                //Must select at least 1 item in Contraband Found as a Result of Search when "Contraband Found" = Yes
                if ($(this).find("input[name^='Field22']").val() == 'Yes' && $(this).find("select[name^='Field24']").val().length < 1) {
                    if (leastOneContraband) {
                        validationText += 'Error: Must select at least 1 contraband when contraband was found.<br>';
                        leastOneContraband = false
                    }
                }
            });

            if (validationText.length > 0) {
                $('#addbtn').hide();
                await openModal('Validation Error', validationText);
                $('#addbtn').show();
                return false;
            }

            return true;
        }
        function UpdateRadioFields() {
            var $RadioFields = $("#Form").find(".RadioGroup");
            if ($RadioFields.length > 0) {
                $RadioFields.each(function () {
                    var value = $(this).find('input[type=hidden]').val();
                    $(this).find(".with-gap[value='" + value + "']").prop('checked', true);;
                });
            }
            $(".actions-taken").each(function () {
                CheckVisibilityOfFields($(this));
            });
        }
        function CheckVisibilityOfFields($actionTakenEntry) {
            var $radioGroup = $actionTakenEntry.find("#DidOfficerMakeanArrestRadioGroup");
            var $row = $radioGroup.parent();
            if ($radioGroup.find('.with-gap:checked').val() == "Yes") {
                $row.find('#reasonforarrestvals').show();
            }
            else {
                $row.find("#reasonforarrestvals option:first").prop("selected", true);
                $row.find('select').formSelect();
                $row.find('#reasonforarrestvals').hide();
            }

            $radioGroup = $actionTakenEntry.find("#WasPysicalForceUsedRadioGroup");
            $row = $radioGroup.parent();
            if ($radioGroup.find('.with-gap:checked').val() == "Yes") {
                $row.find("#whowasinjuredvals").show();
            }
            else {
                $row.find("#whowasinjuredvals option:first").prop("selected", true);
                $row.find('select').formSelect();
                $row.find('#whowasinjuredvals').hide();
            }

            $radioGroup = $actionTakenEntry.find("#WasSearchConductedRadioGroup");
            $row = $radioGroup.parent();
            var $secondRow = $row.next();
            if ($radioGroup.find('.with-gap:checked').val() == "Yes") {
                $radioGroup.next().show();
                $secondRow.show();
            }
            else {
                $row.find("#WasSearchConsensual").prop('checked', false);
                $row.find("#NotSearchConsensual").prop('checked', false);
                $radioGroup.next().find('input[type=hidden]').val("");
                $row.find("#probablecauseforsearchvals option:first").prop("selected", true);
                $row.find('select').formSelect();
                $secondRow.find("#ContrabandFound").prop('checked', false);
                $secondRow.find("#NotContrabandFound").prop('checked', false);
                $secondRow.find("#ContrabandResultInArrest").prop('checked', false);
                $secondRow.find("#NotContrabandResultInArrest").prop('checked', false);
                $secondRow.find('input[type=hidden]').val("");
                $secondRow.find('input[type=hidden]').removeAttr('required');
                $secondRow.find(".RadioGroup").first().next().removeClass("invalid-radio-field");
                $options = $secondRow.find("#typeofcontrabandfoundvals").find("option");
                $options.each(function () {
                    $(this).prop("selected", false);
                });
                $secondRow.find('select').formSelect();

                $radioGroup.next().hide();
                $radioGroup.next().next().hide();
                $secondRow.find(".RadioGroup").first().next().hide();
                $secondRow.find('#typeofcontrabandfoundvals').hide();
                $secondRow.hide();
            }

            $radioGroup = $actionTakenEntry.find("#WasSearchConsensualRadioGroup");
            $row = $radioGroup.parent();
            if ($radioGroup.find('.with-gap:checked').val() != "No") {
                $row.find("#probablecauseforsearchvals option:first").prop("selected", true);
                $row.find('select').formSelect();
                $row.find('#probablecauseforsearchvals').hide();

            }
            else {
                $row.find('#probablecauseforsearchvals').show();
            }

            $radioGroup = $actionTakenEntry.find("#ContrabandFoundRadioGroup");
            $row = $radioGroup.parent();
            if ($radioGroup.find('.with-gap:checked').val() == "Yes") {
                $row.find('#typeofcontrabandfoundvals').show();
                $radioGroup.next().show();
                $radioGroup.next().find('input[type=hidden]').prop('required', true);
            }
            else {
                $radioGroup.next().find("#ContrabandResultInArrest").prop('checked', false);
                $radioGroup.next().find("#NotContrabandResultInArrest").prop('checked', false);
                $radioGroup.next().find('input[type=hidden]').removeAttr('required');
                $radioGroup.next().removeClass("invalid-radio-field");
                $radioGroup.next().find('input[type=hidden]').val("");
                $options = $row.find("#typeofcontrabandfoundvals").find("option");
                $options.each(function () {
                    $(this).prop("selected", false);
                });
                $row.find('select').formSelect();

                $radioGroup.next().hide();
                $row.find('#typeofcontrabandfoundvals').hide();
            }
        }

        function VisibilityCheckRadioChangedHandler(ev) {
            RadioChangedHandler(ev);          
            CheckVisibilityOfFields($(ev.currentTarget).closest('.actions-taken'));
        }
   
    </script>
</head>
<body class="FlexBody">

    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">CLEAR CALL</h5>
                </div>
            </div>
        </div>
    </div>

    <form class="Flex-Form" action="ClearCallQuery.aspx?queryfile=clearcall.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <div class="row" style="margin-top:20px">
                <div class="input-field col s12 m5">
                    <input name="incidentnumber" id="incidentnumber" type="text" disabled>
                    <label for="incidentnumber">Incident Number</label>
                </div>
                <div class="input-field col s12 m5" type="selectlist" id="dispositionvals" name="dispositionvals">
                    <label for="ResponseDisposition">Response Disposition</label>
                </div>
            </div>
            <div class="row">
                <!--HIDE PROBLEM NATURE-->
                <!--Start-->
                <input name="ProblemNature" id="ProblemNature" type="hidden">
                <!--End-->
                <!--SHOW PROBLEM NATURE-->
                <!--Start-->
                <!--<div class="input-field col s12 m5">
                    <input name="pnvals" id="pnvals" type="text" disabled style="color:black;">
                    <label for="pnvals">Problem Nature</label>
                </div>-->
                <!--End-->
                <!--SHOW PATIENT SEEN-->
                <!--Start-->
                <!--<div class="input-field col s12 m5">
                    <input name="pnvals" id="pnvals" type="text" disabled style="color:black;">
                    <label for="pnvals">Problem Nature</label>
                </div>-->
                <!--End-->
                <div class="input-field col s12 m5">
                    <textarea id="textarea1" placeholder="Enter Comment Here" class="materialize-textarea" name="Comments"></textarea>
                    <label for="textarea1" class="active">Comments</label>
                </div>
                <div class="input-field col s12 m5" id="incidentTagsVals" name="incidentTagsVals">
                    <select id="IncidentTags" name="IncidentTags" multiple></select>
                    <label for="IncidentTags">Tags</label>
                </div>
            </div>
            <div class="row">
                <div class="RadioGroup col s12" onchange="RadioChangedHandler(event)">
                    <div class="row">
                        <label>Is Racial Profiling Data required for this incident?</label>
                    </div>
                    <div class="row">
                        <label>
                            <input class="with-gap" name="ShowRPData" type="radio" id="RPDataRadio" value="Yes" />
                            <span>Yes</span>
                        </label>
                        <label style="margin-left: 10px;">
                            <input class="with-gap" name="ShowRPData" type="radio" id="NoShowRPDataRadio" value="No" />
                            <span>No</span>
                        </label>
                    </div>
                    <input type="hidden" name="RPRadio" id="RPRadio" required>
                </div>
            </div>
            <!--RPData-->
            <div id="RPData">
                <!--Hidden Inputs that are passed to the query server - only tied to RPData -->
                <input type="hidden" name="IsClosedIncident" id="IsClosedIncident">
                <input type="hidden" name="Field1_1" id="IncidentID">
                <input type="hidden" name="Field2_1" id="DateTime">
                <input type="hidden" name="Field3_1" id="IncidentSector">
                <input type="hidden" name="Field4_1" id="Unit">
                <input type="hidden" name="Officer" id="Officer">

                <!--Vehicle-->
                <div class="row valign-wrapper subheader">
                    <h4 style="margin-left: 30px;">VEHICLE</h4>
                </div>
                <div class="row">
                    <!--field5-->
                    <div class="input-field col s12 m4" type="selectlist" id="locationofstopvals" name="locationofstopvals">
                        <label for="Field5_1">Location of Stop</label>
                    </div>
                    <!--field6-->
                    <div class="input-field col s12 m4">
                        <input placeholder="" id="Address" name="Field6_1" type="text" maxlength="200">
                        <label for="Field6_1" class="active">Address</label>
                    </div>
                    <!--field7-->
                    <div class="input-field col s12 m4" type="selectlist" id="reasonforstopvals" name="reasonforstopvals">
                        <label for="Field7_1">Reason for Stop</label>
                    </div>
                </div>
                <!--Actions Taken Against Person(s)-->
                <div class="row valign-wrapper subheader">
                    <h4 style="margin-left: 30px;">ACTIONS TAKEN AGAINST PERSON(S)</h4>
                </div>
                <div id="actionsTakenAgainstPersonsDiv">
                    <div class="actions-taken">
                        <input type="hidden" name="IncidentSummaryLogID_1" id="IncidentSummaryLogID_1">
                        <div class="row">
                            <!--field8-->
                            <div class="input-field col s12 m3" type="selectlist" id="persontypevals" name="persontypevals">
                                <label for="Field8_1">Person Type</label>
                            </div>
                            <!--field9-->
                            <div class="input-field col s12 m3" type="selectlist" id="racevals" name="racevals">
                                <label for="Field9_1">Race</label>
                            </div>
                            <!--field10-->
                            <div class="input-field col s12 m3" type="selectlist" id="gendervals" name="gendervals">
                                <label for="Field10_1">Gender</label>
                            </div>
                            <div class="col" style="margin-top:30px;">
                                <a class="btn-flat btn-icon-18" onclick="DeleteActionsTakenField(event);">
                                    <i class="icon-delete icon-18"></i>
                                </a>
                            </div>
                            <div class="col addActionTakenBtn" style="padding:0px;margin-top:30px;">
                                <a class="btn-flat btn-icon-18" onclick="AddActionsTakenField();">
                                    <i class="icon-add icon-18"></i>
                                </a>
                            </div>
                        </div>
                        <div class="row">
                            <!--field11-->
                            <div class="RadioGroup col s12 m3" onchange="RadioChangedHandler(event)">
                                <div class="row">
                                    <label>Race of Person Known Prior to Stop?</label>
                                </div>
                                <div class="row">
                                    <label>
                                        <input class="with-gap" name="RaceKnownPrior_1" type="radio" id="RaceKnownPrior" value="Yes" />
                                        <span>Yes</span>
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <input class="with-gap" name="RaceKnownPrior_1" type="radio" id="NotRaceKnownPrior" value="No" />
                                        <span>No</span>
                                    </label>
                                </div>
                                <input type="hidden" name="Field11_1" id="Field11_1" required>
                            </div>
                            <div class="RadioGroup col s12 m3" onchange="RadioChangedHandler(event)">
                                <!--field12-->
                                <div class="row">
                                    <label>Is Resident of Municipality?</label>
                                </div>
                                <div class="row">
                                    <label>
                                        <input class="with-gap" name="ResidentMunicipality_1" type="radio" id="ResidentOfMunicipality" value="Yes" />
                                        <span>Yes</span>
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <input class="with-gap" name="ResidentMunicipality_1" type="radio" id="NotResidentOfMunicipality" value="No" />
                                        <span>No</span>
                                    </label>
                                </div>
                                <input type="hidden" name="Field12_1" id="Field12_1" required>
                            </div>
                        </div>
                        <div class="row">
                            <!--field13-->
                            <div class="input-field col s12 m3" type="selectlist" id="actiontakenvals" name="actiontakenvals">
                                <label for="Field13_1">Action Taken</label>
                            </div>
                            <!--field14-->
                            <div class="input-field col s12 m3" type="selectlist" id="chargevals" name="chargevals">
                                <label for="Field14_1">Charge</label>
                            </div>
                            <!--field15-->
                            <div class="RadioGroup col s12 m2" id="DidOfficerMakeanArrestRadioGroup" onchange="VisibilityCheckRadioChangedHandler(event)">
                                <div class="row">
                                    <label>Did Officer Make an Arrest?</label>
                                </div>
                                <div class="row">
                                    <label>
                                        <input class="with-gap" name="OfficerMakeArrest_1" type="radio" id="OfficerMakeArrest" value="Yes" />
                                        <span>Yes</span>
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <input class="with-gap" name="OfficerMakeArrest_1" type="radio" id="NotOfficerMakeArrest" value="No" />
                                        <span>No</span>
                                    </label>
                                </div>
                                <input type="hidden" name="Field15_1" id="Field15_1">
                            </div>
                            <!--field16-->
                            <div class="input-field col s12 m3" style="display: none;" type="selectlist" id="reasonforarrestvals" name="reasonforarrestvals">
                                <label for="Field16_1">Reason for Arrest</label>
                            </div>
                        </div>
                        <div class="row">
                            <!--field17-->
                            <div class="RadioGroup col s12 m6"  id="WasPysicalForceUsedRadioGroup" onchange="VisibilityCheckRadioChangedHandler(event)">
                                <div class="row">
                                    <label>Was physical force resulting in bodily injury used during the stop?</label>
                                </div>
                                <div class="row">
                                    <label>
                                        <input class="with-gap" name="WasPysicalForceUsed_1" type="radio" id="WasPysicalForceUsed" value="Yes" />
                                        <span>Yes</span>
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <input class="with-gap" name="WasPysicalForceUsed_1" type="radio" id="NotPysicalForceUsed" value="No" />
                                        <span>No</span>
                                    </label>
                                </div>
                                <input type="hidden" name="Field17_1" id="Field17_1" required>
                            </div>
                            <!--field18-->
                            <div class="input-field col s12 m4" style="display: none;" type="selectlist" id="whowasinjuredvals" name="whowasinjuredvals">
                                <label for="Field18_1">Was there injury to Suspect, Officer or Both?</label>
                            </div>
                        </div>
                        <div class="row">
                            <!--field19-->
                            <div class="RadioGroup col s12 m3" id="WasSearchConductedRadioGroup" onchange="VisibilityCheckRadioChangedHandler(event)">
                                <div class="row">
                                    <label>Was Search Conducted?</label>
                                </div>
                                <div class="row">
                                    <label>
                                        <input class="with-gap" name="WasSearchConducted_1" type="radio" id="WasSearchConducted" value="Yes" />
                                        <span>Yes</span>
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <input class="with-gap" name="WasSearchConducted_1" type="radio" id="NotSearchConducted" value="No" />
                                        <span>No</span>
                                    </label>
                                </div>
                                <input type="hidden" name="Field19_1" id="Field19_1" required>
                            </div>
                            <!--field20-->
                            <div class="RadioGroup col s12 m3" style="display: none;" id="WasSearchConsensualRadioGroup" onchange="VisibilityCheckRadioChangedHandler(event)">
                                <div class="row">
                                    <label>Was Search Consensual?</label>
                                </div>
                                <div class="row">
                                    <label>
                                        <input class="with-gap" name="WasSearchConsensual_1" type="radio" id="WasSearchConsensual" value="Yes" />
                                        <span>Yes</span>
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <input class="with-gap" name="WasSearchConsensual_1" type="radio" id="NotSearchConsensual" value="No" />
                                        <span>No</span>
                                    </label>
                                </div>
                                <input type="hidden" name="Field20_1" id="Field20_1">
                            </div>
                            <!--field21-->
                            <div class="input-field col s12 m4" style="display: none;" type="selectlist" id="probablecauseforsearchvals" name="probablecauseforsearchvals">
                                <label for="Field21_1">PC for Search</label>
                            </div>
                        </div>
                        <div class="row" style="display: none;">
                            <!--field22-->
                            <div class="RadioGroup col s12 m3" id="ContrabandFoundRadioGroup" onchange="VisibilityCheckRadioChangedHandler(event)">
                                <div class="row">
                                    <label>Contraband Found?</label>
                                </div>
                                <div class="row">
                                    <label>
                                        <input class="with-gap" name="ContrabandFound_1" type="radio" id="ContrabandFound" value="Yes" />
                                        <span>Yes</span>
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <input class="with-gap" name="ContrabandFound_1" type="radio" id="NotContrabandFound" value="No" />
                                        <span>No</span>
                                    </label>
                                </div>
                                <input type="hidden" name="Field22_1" id="Field22_1">
                            </div>
                            <!--field23-->
                            <div class="RadioGroup col s12 m3" style="display: none;" onchange="RadioChangedHandler(event)">
                                <div class="row">
                                    <label>Did the finding of contraband result in Arrest?</label>
                                </div>
                                <div class="row">
                                    <label>
                                        <input class="with-gap" name="ContrabandResultInArrest_1" type="radio" id="ContrabandResultInArrest" value="Yes" />
                                        <span>Yes</span>
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <input class="with-gap" name="ContrabandResultInArrest_1" type="radio" id="NotContrabandResultInArrest" value="No" />
                                        <span>No</span>
                                    </label>
                                </div>
                                <input type="hidden" name="Field23_1" id="Field23_1">
                            </div>
                            <!--field24-->
                            <div class="input-field col s12 m4" style="display: none;" type="selectlist" id="typeofcontrabandfoundvals" name="typeofcontrabandfoundvals">
                                <label for="Field24_1">Contraband Found as a Result of Search</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="modal" class="modal">
            <div class="header valign-wrapper modal-header">
                <h5>ADD TAG</h5>
            </div>
            <div class="modal-content">
                <p>add this tag</p>
            </div>
            <div class="modal-footer">
                <a id="addbtn" class="modal-close btn">ADD TAG</a>
                <a id="closebtn" class="modal-close btn-flat">DISMISS</a>
            </div>
        </div>
    </form>

</body>
</html>