<?xml version="1.0" standalone="yes"?>
<CustomList xmlns="http://tempuri.org/SchemaCustomization.xsd">
  <Text>
    <name>Incident</name>
    <text>Call</text>
  </Text>
  <Text>
    <name>Group</name>
    <text>Home Station</text>
  </Text>
  <Text>
    <name>Sector</name>
    <text>Sector</text>
  </Text>
  <Text>
    <name>User</name>
    <text>User</text>
  </Text>
  <Text>
    <name>Problem</name>
    <text>Problem</text>
  </Text>
  <Text>
    <name>Unit</name>
    <text>Unit</text>
  </Text>
  <Text>
    <name>Form</name>
    <text>Query</text>
  </Text>
  <Text>
    <name>Forms</name>
    <text>Queries</text>
  </Text>
  <Text>
    <name>GPSType</name>
    <text>TAIP over UDP</text>
  </Text>
  <Text>
    <name>HelpLink</name>
    <text>VisiNET Mobile.hlp</text>
  </Text>
  <Text>
    <name>DefaultStatusFilter</name>
    <text>IQ</text>
  </Text>
  <Text>
    <name>AFRIncidentDataDirectoryPath</name>
    <text>C:\temp</text>
  </Text>
  <Text>
    <name>AFRApplicationPath</name>
    <text>.\Clear.Form.Launch.exe</text>
  </Text>
  <Text>
    <name>ConfidentialStrings</name>
    <text />
  </Text>
  <Text>
    <name>CryptographicAlgorithm</name>
    <text />
  </Text>
  <Text>
    <name>InsertCommentButtonCaption</name>
    <text>Insert Comment</text>
  </Text>
  <Text>
    <name>SpeechReplacedAlphaNumericValues</name>
    <text />
  </Text>
  <Text>
    <name>SpeechVoiceUsed</name>
    <text />
  </Text>
  <Text>
    <name>SpeechTryToUnderstandMode</name>
    <text />
  </Text>
  <Text>
    <name>SpeechCommandForDeactivateVoiceCommands</name>
    <text />
  </Text>
  <Text>
    <name>SpeechCommandForActivateVoiceCommands</name>
    <text />
  </Text>
  <Text>
    <name>SpeechCommandForWhatCanISayNow</name>
    <text />
  </Text>
  <Text>
    <name>SpeechVoiceCommandEngine</name>
    <text>SAPI</text>
  </Text>
  <Text>
    <name>SpeechTextToSpeechEngine</name>
    <text>SAPI</text>
  </Text>
  <Text>
    <name>MagStripePersonCheckFormName</name>
    <text />
  </Text>
  <Text>
    <name>MagStripeParseFormat</name>
    <text>1,T1,S"%",D,E"?",T2,S";",D,E"?",T3,S"%",S"+",S"#",S"!",S";",D,E"?"</text>
  </Text>
  <Text>
    <name>IDecodeKey</name>
    <text />
  </Text>
  <Text>
    <name>AFRLicensePlateRegex</name>
    <text />
  </Text>
  <Text>
    <name>SendToControllingDispatcherCheckboxLabelText</name>
    <text>C.Disp</text>
  </Text>
  <Text>
    <name>BrowserURL</name>
    <text>http://cadbrowser.logis.org/visinetbrowser/</text>
  </Text>
  <Text>
    <name>PictometryAPIKey</name>
    <text />
  </Text>
  <Text>
    <name>PictometrySecretKey</name>
    <text />
  </Text>
  <Text>
    <name>PictometryLoadURL</name>
    <text>http://pol.pictometry.com/ipa-analytics/load.php</text>
  </Text>
  <Text>
    <name>PictometryJavascriptURL</name>
    <text>http://pol.pictometry.com/ipa-analytics/embed/host.php?apikey=</text>
  </Text>
  <Text>
    <name>WebRMSURL</name>
    <text />
  </Text>
  <Font>
    <name>TreeView</name>
    <size>14</size>
    <fontname>Microsoft Sans Serif</fontname>
  </Font>
  <Font>
    <name>ListView</name>
    <size>14</size>
    <fontname>Microsoft Sans Serif</fontname>
  </Font>
  <Font>
    <name>MailText</name>
    <size>14</size>
    <fontname>Courier New</fontname>
  </Font>
  <Font>
    <name>StatusSummary</name>
    <size>14</size>
    <fontname>Microsoft Sans Serif</fontname>
  </Font>
  <Font>
    <name>StatusDetails</name>
    <size>12</size>
    <fontname>Microsoft Sans Serif</fontname>
  </Font>
  <Font>
    <name>IncidentSummary</name>
    <size>14</size>
    <fontname>Microsoft Sans Serif</fontname>
  </Font>
  <Font>
    <name>DialogWindows</name>
    <size>11</size>
    <fontname>Microsoft Sans Serif</fontname>
  </Font>
  <Flag>
    <name>EnableMap</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>DemoVersion</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>DebugMode</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AskForEmergencyConfirmation</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>EnableGPS</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>EnableDrivingDirections</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>SendAvlWithEveryMsg</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>UseHTMLQuery</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>MailBodyOnTop</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>AllowMailToGroup</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AllowMailToSector</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>AllowManuelUnitsStatusRefresh</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>AddUserIDToCrewMemberList</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>WarnWhenUserIsNotInCrew</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>UseEncryption</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>UseXmlReduction</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>GettingWaitingIncidentUpdates</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>GettingOnlyActiveIncidentsAssignedToMe</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>EnableIncidentScreen</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>AutoTimeSyncToGPS</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>SendIndentedXml</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>PlaySoundForNewWaitingIncident</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>BringToFrontOnAssignment</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>ShowProblemNatureInIncidentsTree</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>OnPressEmergencySendEmailToInitiatingUnit</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AppendSenderInfoToMail</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AutomaticallyIncludeControllingDispatcherWhenSendingMailToSector</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>SendMailToSupervisorOnIncidentAssignment</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>NotifyUserOnSectorChange</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>OpenNavBarOnStartup</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>ByDefaultShowUnitsInMyHomeSector</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>ShowUnitsInMyHomeSectorCheckboxIsVisible</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>ShowMessageWhenConnectedOrDisconnected</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>ShowMessageWhenUpdatesPauseDueToUserInactivity</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>ShowUnitResponsePriority</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>UseAutoZoomModeForDrivingDirections</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>WhenReceivingEmergencyMsgEnterItToInBox</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>WhenReceivingEmergencyMsgShowPopupBox</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>EnableAFRIntegration</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>AutomaticallySwitchToHotspot</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>IgnoreOldGPsData</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AcceptQueryResponseBeforeUnitIsOnDuty</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AFRAutomaticallyGenerateFileForQueryReturns</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AFRAutomaticallyGenerateFileUponIncidentAssignment</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>ShowUnitResponseNumber</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>PersistInboxColumnWidthsBetweenLaunch</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>EnableSpeech</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AllowAttachmentRequestForIncidentNotAssignedToMe</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>PersistWatchList</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>SpeechPressToSpeakMode</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>SpeechRequireHighConfidence</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>EnableMagStripe</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AutoSubmitMagStripePersonCheck</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>OutOfVehicleDisplayTransparent</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>GpsDebugMode</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>ReopenGpsComPortWhenInactivityDetected</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>UseSubmitLatLongOnIncident</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>EnableRemoteController</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>BringToFrontUponHighPriortyRecordCheckReturn</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>EnableSendToControllingDispatcherCheckbox</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>AFRAutomaticallyGenerateFileUponIncidentUpdate</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>EnableEmergencyFromOffDutyDevice</name>
    <flag>false</flag>
  </Flag>
  <Flag>
    <name>EnableViewOtherAgenciesUnits</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>EnableConnectedMailboxes</name>
    <flag>true</flag>
  </Flag>
  <Flag>
    <name>AutoSyncClientToServer</name>
    <flag>false</flag>
  </Flag>
  <Color>
    <name>Highlight</name>
    <Red>190</Red>
    <Green>240</Green>
    <Blue>240</Blue>
  </Color>
  <Color>
    <name>NotificationColorDay</name>
    <Red>0</Red>
    <Green>150</Green>
    <Blue>255</Blue>
  </Color>
  <Color>
    <name>NotificationColorNight</name>
    <Red>200</Red>
    <Green>200</Green>
    <Blue>200</Blue>
  </Color>
  <Color>
    <name>NightTextColor</name>
    <Red>232</Red>
    <Green>95</Green>
    <Blue>55</Blue>
  </Color>
  <Color>
    <name>RouteColor</name>
    <Red>0</Red>
    <Green>0</Green>
    <Blue>255</Blue>
  </Color>
  <Color>
    <name>RouteArrowColor</name>
    <Red>0</Red>
    <Green>0</Green>
    <Blue>0</Blue>
  </Color>
  <Color>
    <name>HyperlinkColorDay</name>
    <Red>0</Red>
    <Green>21</Green>
    <Blue>255</Blue>
  </Color>
  <Color>
    <name>HyperlinkColorNight</name>
    <Red>255</Red>
    <Green>0</Green>
    <Blue>0</Blue>
  </Color>
  <Int>
    <name>DaysToPurgeOldMail</name>
    <val>14</val>
  </Int>
  <Int>
    <name>NoAckRetransmitSeconds</name>
    <val>120</val>
  </Int>
  <Int>
    <name>StatusRequestTimeoutInSeconds</name>
    <val>30</val>
  </Int>
  <Int>
    <name>MaximumAttachmentsSize</name>
    <val>10485760</val>
  </Int>
  <Int>
    <name>DaysToKeepInArchive</name>
    <val>30</val>
  </Int>
  <Int>
    <name>GPSReadRate</name>
    <val>1</val>
  </Int>
  <Int>
    <name>GPSUdpPort</name>
    <val>5555</val>
  </Int>
  <Int>
    <name>AvlUpdateServerMinimumSecondsBetweenUpdates</name>
    <val>5</val>
  </Int>
  <Int>
    <name>AvlUpdateServerMaximumSecondsBetweenUpdates</name>
    <val>300</val>
  </Int>
  <Int>
    <name>AvlUpdateServerIfLocationChangedMoreThan</name>
    <val>50</val>
  </Int>
  <Int>
    <name>PollUnitsInfoWhileOnMapOrUnitsQueueEveryXSeconds</name>
    <val>15</val>
  </Int>
  <Int>
    <name>InactivityTimeoutInSeconds</name>
    <val>18000000</val>
  </Int>
  <Int>
    <name>ShowCloseMsgForIncidentNotAssignedToMe</name>
    <val>2</val>
  </Int>
  <Int>
    <name>DisconnectionTimeInMinutesThatCausesResync</name>
    <val>300000</val>
  </Int>
  <Int>
    <name>AllowSelfDispatch</name>
    <val>0</val>
  </Int>
  <Int>
    <name>UserCanAddIncidentComment</name>
    <val>0</val>
  </Int>
  <Int>
    <name>IncidentZoomAreaInMiles</name>
    <val>5</val>
  </Int>
  <Int>
    <name>CurrentAddressUpdateIntervalInSeconds</name>
    <val>3</val>
  </Int>
  <Int>
    <name>CurrentAddressDefaultZoomInHundredthOfMile</name>
    <val>30</val>
  </Int>
  <Int>
    <name>DrivingDirectionsBackToRouteTriggerInMetters</name>
    <val>30</val>
  </Int>
  <Int>
    <name>DrivingDirectionsVoiceNotifyInHundredthOfMile0</name>
    <val>1</val>
  </Int>
  <Int>
    <name>DrivingDirectionsVoiceNotifyInHundredthOfMile1</name>
    <val>10</val>
  </Int>
  <Int>
    <name>DrivingDirectionsVoiceNotifyInHundredthOfMile2</name>
    <val>50</val>
  </Int>
  <Int>
    <name>DrivingDirectionsVoiceNotifyInHundredthOfMile3</name>
    <val>200</val>
  </Int>
  <Int>
    <name>ElapsedTimerIntervalInSeconds</name>
    <val>1</val>
  </Int>
  <Int>
    <name>IncidentAttachmentSizeLimitInBytes</name>
    <val>10485760</val>
  </Int>
  <Int>
    <name>PhraseLengthToOpenTextSpeakerWindow</name>
    <val>100000</val>
  </Int>
  <Int>
    <name>SpeechInactivitySecondsForAutoDeactivate</name>
    <val>10</val>
  </Int>
  <Int>
    <name>SpeechTryToUnderstandTimeoutInSeconds</name>
    <val>8</val>
  </Int>
  <Int>
    <name>SpeechVolume</name>
    <val>100</val>
  </Int>
  <Int>
    <name>SpeechRate</name>
    <val>0</val>
  </Int>
  <Int>
    <name>SpeechHighConfidenceLevel</name>
    <val>90</val>
  </Int>
  <Int>
    <name>SpeechNormalConfidenceLevel</name>
    <val>80</val>
  </Int>
  <Int>
    <name>MaxCommentLength</name>
    <val>2000</val>
  </Int>
  <Int>
    <name>DistanceMeasurementScale</name>
    <val>0</val>
  </Int>
  <Int>
    <name>RouteWidth</name>
    <val>7</val>
  </Int>
  <Int>
    <name>RouteArrowInterval</name>
    <val>40</val>
  </Int>
  <Int>
    <name>DelayMagStripeComPortInitializationInSeconds</name>
    <val>0</val>
  </Int>
  <Int>
    <name>ShowNextHighestPriorityMsgByDateTime</name>
    <val>0</val>
  </Int>
  <Int>
    <name>MapImplementationType</name>
    <val>2</val>
  </Int>
  <Int>
    <name>MapConfigTabs</name>
    <val>1</val>
  </Int>
  <Int>
    <name>CardReaderType</name>
    <val>1</val>
  </Int>
  <Int>
    <name>ProblemNatureLabelOnMap</name>
    <val>30</val>
  </Int>
</CustomList>