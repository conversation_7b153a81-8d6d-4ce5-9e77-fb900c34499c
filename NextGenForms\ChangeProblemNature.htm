﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Change Problem Nature</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <!-- Compiled and minified JavaScript -->
    <script src="jquery.min.js" type="text/javascript"></script>
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">

        $(document).ready(function () {
            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });
        });

        function AfterFillForm() {

            GetIncidentNumber().then(function (result) {
                $("#incidentnumber").val(result);
                M.updateTextFields();
            });

            GenerateSelectBox("problemnature", "ProblemNature.xml", "genericselect.xsl", true, false, false, 1, false, false).then(function (result) {
                $("#pnvals").prepend(result);
                $('select').formSelect();
                SetSelectBoxFromParameters($("#problemnature"));
                GenerateSelectBox("UpgradeDowngradeReason", "UpDownGradeReason.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                    $("#updownreasonvals").prepend(result);
                    $('select').formSelect();
                    SetSelectBoxFromParameters($("#UpgradeDowngradeReason"));
                    $(".select-dropdown").first().focus();
                });
            });
        }

    </script>

</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">CHANGE PROBLEM NATURE</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="main" action="ChangeProblemNatureQuery.aspx?queryfile=ChangeProblemNature.qry" method="post" id="Form" name="Form">
        <div class="row">
            <div class="col s8">
                <div class="row" style="margin-top:20px">
                    <div class="input-field col s12 m5">
                        <input name="incidentnumber" id="incidentnumber" type="text" disabled style="color:black;">
                        <label for="incidentnumber">Incident Number</label>
                    </div>
                    <div class="input-field col s12 m5">
                        <input name="IncidentID" id="IncidentID" type="hidden">
                        <label for="IncidentID"> </label>
                    </div>                  
                </div>
                <div class="row">
                    <div class="input-field col s6" type="selectlist" id="pnvals" name="pnvals">
                        <label for="problemnature">Problem Nature</label>
                    </div>
                </div>               
                <div class="row">
                    <div class="input-field col s6" type="selectlist" id="updownreasonvals" name="updownreasonvals">
                    <label for="UpgradeDowngradeReason">Upgrade/Downgrade Reason</label>
                    </div>                    
                </div>
            </div>
        </div>
    </form> 
</body>
</html>