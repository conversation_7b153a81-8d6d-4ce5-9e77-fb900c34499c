﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Unit TimeStamp</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />

    <!-- Compiled and minified JavaScript -->
    <script src="jquery.min.js" type="text/javascript"></script>
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <style>
        .user-time-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: 10px;
            margin-top: 20px;
            width: 100%;
            max-width: 600px;
        }
        
        .user-time-btn {
            height: 60px;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            white-space: normal;
            line-height: 1.2;
            padding: 10px;
        }
        
        .user-time-btn.selected {
            background-color: #26a69a;
        }
    </style>

    <script language="javascript">

        $(document).ready(function () {
            console.log("Document ready");
            console.log("usertimevals element exists:", $("#usertimevals").length > 0);
            
            $("#Form").submit(function () {

                if ($(this)[0].checkValidity() == true) {
                    $("#TimeStamp").val(GetCurrentDate())
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }

                ValidateRequiredSingleSelect($("#UserTimeName"));

                return false;
            });

        });

        function AfterFillForm() {
            GetIncidentNumber().then(function (result) {
                $("#incidentnumber").val(result);
                M.updateTextFields();
            });

            // Replace select box with buttons in a grid
            $.ajax({
                url: "UserTime.xml",
                dataType: "xml",
                success: function(xml) {
                    console.log("UserTime.xml loaded successfully");
                    
                    const container = $("<div class='user-time-buttons'></div>");
                    const items = $(xml).find("item");
                    
                    // Create buttons for each item
                    items.each(function(index) {
                        const description = $(this).find("description").text();
                        
                        if (description.trim() !== "") {
                            const button = $(`
                                <button type="button" class="waves-effect waves-light btn user-time-btn" 
                                        data-value="${description}">
                                    ${description}
                                </button>
                            `);
                            
                            button.click(function() {
                                // Set the hidden input value
                                $("#UserTimeName").val($(this).data("value"));
                                
                                // Highlight the selected button
                                $(".user-time-btn").removeClass("selected");
                                $(this).addClass("selected");
                                
                                // Submit the form
                                $("#Form").submit();
                            });
                            
                            container.append(button);
                        }
                    });
                    
                    // Replace the select container with our grid of buttons
                    $("#usertimevals").html(container);
                },
                error: function(xhr, status, error) {
                    console.error("Error loading UserTime.xml:", error);
                    createFallbackButtons();
                }
            });
            
            // Add hidden input for the selected value
            if ($("#UserTimeName").length === 0) {
                $("form").append('<input type="hidden" name="UserTimeName" id="UserTimeName">');
            }
        }

        // Fallback function in case AJAX fails
        function createFallbackButtons() {
            console.log("Using fallback button creation");
            
            // Hardcoded values from the XML file we saw earlier
            const timeValues = [
                "Extrication Comp",
                "Fire Out",
                "Loss Stopped",
                "Patient Contact",
                "Primary Search Comp",
                "Secondary Search Comp"
            ];
            
            const container = $("<div class='user-time-buttons'></div>");
            
            timeValues.forEach(function(description) {
                const button = $(`
                    <button type="button" class="waves-effect waves-light btn user-time-btn" 
                            data-value="${description}">
                        ${description}
                    </button>
                `);
                
                button.click(function() {
                    // Set the hidden input value
                    $("#UserTimeName").val($(this).data("value"));
                    
                    // Highlight the selected button
                    $(".user-time-btn").removeClass("selected");
                    $(this).addClass("selected");
                    
                    // Submit the form
                    $("#Form").submit();
                });
                
                container.append(button);
            });
            
            // Replace the select container with our grid of buttons
            $("#usertimevals").html(container);
        }

    </script>
</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">TIMESTAMP</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="Flex-Form" action="SetUnitTimeStamp.aspx?queryfile=SetUnitTimeStamp.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <div class="col s8">
                <div class="row" style="margin-top:20px">
                    <div class="input-field col s12 m5">
                        <input name="incidentnumber" id="incidentnumber" type="text" disabled style="color:black;">
                        <label for="incidentnumber">Incident Number</label>
                    </div>
                    <div class="input-field col s12 m5">
                        <input name="IncidentID" id="IncidentID" type="hidden">
                        <label for="IncidentID"> </label>
                    </div>
                </div>
                <div class="row">
                    <div class="col s12" id="usertimevals">
                        <!-- Buttons will be dynamically inserted here in a 3x2 grid -->
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="TimeStamp" id="TimeStamp">
    </form>
</body>
</html>
