<!DOCTYPE html>
<html lang="en-US">
<head>
	<TITLE>VisiNET Mobile - Boat Inquiry</TITLE>
	<meta charset="utf-8" />
	<!-- Compiled and minified CSS -->
	<link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

	<!-- Compiled and minified JavaScript -->
	<link rel="stylesheet" type="text/css" href="MobileStyle.css" />

	<script src="jquery.min.js" type="text/javascript"></script>
	<!-- Compiled and minified JavaScript -->
	<script src="materialize.minv1.0.0.js" type="text/javascript"></script>
	<script src="ClientUtilities.js" type="text/javascript"></script>


	<script language="javascript">
		$(document).ready(function () {
			//handle form submition
			$("#Form").submit(function () {
				if ($(this)[0].checkValidity() == true) {
					var values = $(this).serialize();
					SubmitQuery(values, $(this).attr('action'));
				}

				return false;

			});
		});
	</script>

</head>
<body>
	<div class="header">
		<div class="row">
			<div class="s12">
				<div class="valign-wrapper">
					<h5 style="margin-left: 20px;">GET INCIDENT</h5>
				</div>
			</div>
		</div>
	</div>
	<form class="main"  action="GetIncident.aspx?queryfile=getincident.qry" method="post" id="Form" name="Form">
		<div class="row" style="padding-top:20px">
			<div class="s6">
				<div class="input-field col s12 m4">
					<input placeholder="" id="incidentnumber" type="text" name="incidentnumber">
					<label for="RegID" class="active">Incident Number</label>
				</div>
			</div>
			<div class="s6">
				
			</div>
		</div>
		

		<input type="hidden" name="pagenumber" id="pagenumber" value="1" size="1">

	</form>

</body>
</html>