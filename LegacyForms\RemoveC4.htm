<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Remove Code 4</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Remove Code 4</H4>
						<form action="RemoveCodeFourQuery.aspx?queryfile=removecodefour.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td><div id=Text name=Text>Incident Number:</div></td>
									<td><span id="incidentnumber" name="incidentnumber" formvalue="true"></span></td>
									<td><input type="hidden" name="incidentid" id="incidentid"></td>
								</tr>
							</table>
							<br>
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<div id=SubmitButton name=SubmitButton>
								<input type="submit" name="Query" id="Query" value="Submit">
							</div>
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<SCRIPT language="javascript">
		function window.onload()
		{
			Form.Query.focus();
		}


		function OnAfterFormFill()
		{
			if (Form.incidentid.value == '')
			{
				Text.innerText = 'This command requires you to be assigned to an incident. Your request was not submitted.';
				SubmitButton.innerText = '';
			}
		}

	</SCRIPT>
</HTML>
