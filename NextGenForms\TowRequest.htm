﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Tow Request</TITLE>
    <meta charset="utf-8" />

    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />

    <!-- Compiled and minified JavaScript -->
    <script src="jquery.min.js" type="text/javascript"></script>
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">

        $(document).ready(function () {

            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {

                    if ($('#HoldForEvidence').is(':checked')) {
                        $('#HoldForEvidence').val(true);
                    }
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });
        });

        //Necessary to select parameters from FormDefualts.xml
        function AfterFillForm() {

            GetIncidentNumber().then(function (result) {
                $("#IncidentNumber").val(result);
                M.updateTextFields();
            });

            GetLoggedinUser().then(function (result) {
                $("#RequestedByUser").val(result);
                M.updateTextFields();
            });

            GetIncidentId().then(function (result) {
                $("#IncidentID").val(result);
                M.updateTextFields();
            });

            GenerateSelectBox("State", "state.xml", "genericselectvalue.xsl", false, false, false, 1, false, false).then(function (result) {
                $("#statevals").prepend(result);
                SetSelectBoxFromParameters($("#State"));
                GenerateSelectBox("TowCategoryID", "RotationCategoryByAgency.xml", "genericselect.xsl", true, false, true, 1, false, false).then(function (result) {
                    $("#category").prepend(result);
                    var $option = $("#category option:first");
                    $option.prop("selected", true);
                    SetSelectBoxFromParameters($("#TowCategoryID"));
                    GenerateSelectBox("Make", "VehicleMake.xml", "genericselect.xsl", false, false, true, 1, false, false).then(function (result) {
                        $("#makevals").prepend(result);
                        SetSelectBoxFromParameters($("#Make"));
                        GenerateSelectBox("LicenseTypeID", "LicensePlateType.xml", "genericselect.xsl", false, false, false, 1, false, false).then(function (result) {
                            $("#licensetype").prepend(result);
                            SetSelectBoxFromParameters($("#LicenseTypeID"));
                        });
                    });
                });
            });

            $(".select-dropdown").first().focus();
        }

    </script>
</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">TOW REQUEST</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="Flex-Form" action="TowRequest.aspx?queryfile=TowRequest.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <div class="col s8">
                <div class="row" style="margin-top:20px">
                    <div class="input-field col s12 m4" type="selectlist" id="category" name="category">
                        <label for="category">Tow Category</label>
                    </div>
                    <div class="input-field col s12 m4" style="margin-left:12px;">
                        <input name="IncidentNumber" id="IncidentNumber" type="text" disabled style="color:black;">
                        <label for="IncidentNumber">Incident Number</label>
                    </div>
                    <div class="input-field col s12 m2">
                        <input type="hidden" name="IncidentID" id="IncidentID">
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s12 m4">
                        <input id="RequestedByUser" type="text" name="RequestedByUser" required>
                        <label for="RequestedByUser">Requested By</label>
                    </div>
                    <label class="col s4">
                        <input type="checkbox" name="HoldForEvidence" id="HoldForEvidence" class="filled-in checkbox-color" />
                        <span>Hold for Evidence</span>
                    </label>
                </div>
                <div class="row">
                    <div class="valign-wrapper">
                        <h6 class="subsection"><b>Vehicle Info</b></h6>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s12 m4" type="selectlist" id="makevals" name="makevals">
                        <label for="makevals">Vehicle Make</label>
                    </div>
                    <div class="input-field col s12 m4">
                        <input placeholder="" id="Model" type="text" name="Model" maxlength="20">
                        <label for="Model" class="active">Vehicle Model</label>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s12 m3">
                        <input placeholder="" id="VehicleYear" type="number" name="VehicleYear" maxlength="4" min="0" class="input-validate-year"
                               oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                        <label for="VehicleYear" class="active">Vehicle Year</label>
                    </div>
                    <div class="input-field col s12 m3">
                        <input placeholder="" id="Style" type="text" name="Style" maxlength="20">
                        <label for="Style" class="active">Vehicle Style</label>
                    </div>
                    <div class="input-field col s12 m3">
                        <input placeholder="" id="Color" type="text" name="Color" maxlength="20">
                        <label for="Color" class="active">Vehicle Color</label>
                    </div>
                </div>
                <div class="row">
                    <div class="valign-wrapper">
                        <h6 class="subsection"><b>License Plate Info</b></h6>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s12 m3">
                        <input placeholder="" id="Plate" type="text" name="Plate" maxlength="10">
                        <label for="Plate" class="active">License Plate #</label>
                    </div>
                    <div class="input-field col s12 m3" type="selectlist" id="statevals" name="statevals">
                        <label>State</label>
                    </div>
                    <div class="input-field col s12 m3">
                        <input placeholder="" id="VIN" type="text" name="VIN" maxlength="30">
                        <label for="VIN" class="active">VIN</label>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s12 m3">
                        <input placeholder="" id="LicenseMonth" type="number" name="LicenseMonth" min="0" maxlength="2" class="input-validate-month"
                               oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                        <label for="LicenseMonth" class="active">License Month</label>
                    </div>
                    <div class="input-field col s12 m3">
                        <input placeholder="" id="LicenseYear" type="number" name="LicenseYear" min="0" maxlength="4" class="input-validate-year"
                               oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                        <label for="LicenseYear" class="active">License Year</label>
                    </div>
                    <div class="input-field col s12 m3" type="selectlist" id="licensetype" name="licensetype">
                        <label>License Type</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="Flex-Form-Comment">
            <div class="comment">
                <div class="row">
                    <div class="input-field col s12">
                        <textarea id="textarea1" placeholder="Enter Comment Here" class="materialize-textarea" name="Comment"></textarea>
                        <label for="textarea1" class="active">Comments</label>
                    </div>
                </div>
            </div>
        </div>
    </form>  
</body>
</html>