<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Vehicle Search</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="left"><input type="button" value="Submit" onclick='Form.Query.click()'>&nbsp;&nbsp;&nbsp;Vehicle Search</H4>
						<form action="VehicleSearch.aspx?queryfile=VehicleSearch.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td>Unit:</td>
									<td><input type="text" name="UnitName" id="UnitName"></td>
								</tr>
								<tr>
									<td>Vehicle:</td>
									<td><input type="text" name="VehicleName" id="VehicleName"></td>
								</tr>
								<tr>
									<td>Sector:</td>
									<td><input type="text" name="Sector" id="Sector"></td>
								</tr>
								<tr>
									<td>Jurisdiction:</td>
									<td><input type="text" name="Jurisdiction" id="Jurisdiction"></td>
								</tr>
								<tr>
									<td>Division:</td>
									<td><input type="text" name="Division" id="Division"></td>
								</tr>
								<tr>
									<td>Capabilities:</td>
									<td>
										<input type="text" size="6" name="CapabilityA" id="CapabilityA">
										<input type="text" size="6" name="CapabilityB" id="CapabilityB">
										<input type="text" size="6" name="CapabilityC" id="CapabilityC">
										<input type="text" size="6" name="CapabilityD" id="CapabilityD">
										<input type="text" size="6" name="CapabilityE" id="CapabilityE">
									</td>
								</tr>
								<tr>
									<td colspan=2><small>Note: The use of partial is acceptable</small></td>
								</tr>
							</table>
							
							<input type="submit" name="Query" id="Query" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<SCRIPT language="javascript">
		function window.onload()
		{
			Form.UnitName.focus();
		}
	</SCRIPT>
</HTML>
