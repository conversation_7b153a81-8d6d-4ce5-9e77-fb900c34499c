{"UnitQueueConfiguration": {"Name": "UNITS", "EmptyLabelText": "No Units", "Columns": [{"Type": 1, "HeaderText": "Incident ID", "SourcePropertyName": "IncidentIdShortened", "Width": "*", "MinWidth": 50.0}, {"Type": 3, "HeaderText": "Unit", "SourcePropertyName": "Unit", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 1, "HeaderText": "Problem", "SourcePropertyName": "ProblemName", "Width": "2*", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 5, "HeaderText": "Status", "SourcePropertyName": "CurrentStatus", "Width": "*", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 6, "HeaderText": "Elapsed Time", "SourcePropertyName": "ElapsedTime", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Timestamp", "SourcePropertyName": "TimestampDisplay", "Width": "*", "MinWidth": 50.0}, {"Type": 4, "HeaderText": "Unit Location", "SourcePropertyName": "Location", "Width": "2*", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 2, "HeaderText": "Address", "SourcePropertyName": "AddressExtended", "Width": "2*", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 2, "HeaderText": "Address", "SourcePropertyName": "Address", "Width": "2*", "MinWidth": 50.0}, {"Type": 1, "HeaderText": "Call #", "SourcePropertyName": "IncidentNumber", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Agency", "SourcePropertyName": "Agency", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Current Sector", "SourcePropertyName": "CurrentSector", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Home Sector", "SourcePropertyName": "HomeSector", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "User", "SourcePropertyName": "User", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Case #", "SourcePropertyName": "CaseNumber", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "City", "SourcePropertyName": "City", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Cross Street", "SourcePropertyName": "CrossStreet", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "County", "SourcePropertyName": "County", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Division", "SourcePropertyName": "Division", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Incident Sector", "SourcePropertyName": "Sector", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Incident Type", "SourcePropertyName": "IncidentType", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Juris<PERSON>", "SourcePropertyName": "Juris<PERSON>", "Width": "*", "MinWidth": 50.0}, {"Type": 2, "HeaderText": "Location Name", "SourcePropertyName": "LocationName", "Width": "2*", "MinWidth": 50.0}, {"HeaderText": "Priority", "SourcePropertyName": "Priority", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Response Area", "SourcePropertyName": "ResponseArea", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "State", "SourcePropertyName": "State", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Zip", "SourcePropertyName": "Zip", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Beat", "SourcePropertyName": "Beat", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Destination", "SourcePropertyName": "Destination", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "PersonnelCount", "SourcePropertyName": "PersonnelCount", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "VehicleName", "SourcePropertyName": "VehicleName", "Width": "*", "MinWidth": 50.0}, {"Type": 3, "HeaderText": "Unit Alias", "SourcePropertyName": "UnitAlias", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Home Station", "SourcePropertyName": "UnitHomeStation", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "AVL Enabled", "SourcePropertyName": "A<PERSON>LEnabled", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Certification Type", "SourcePropertyName": "CertificationType", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Home Battalion", "SourcePropertyName": "HomeBattalion", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "MDT Enabled", "SourcePropertyName": "MDTEnabled", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Out of Vehicle", "SourcePropertyName": "OutOfVehicle", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Primary Radio Channel", "SourcePropertyName": "PrimaryRadioChannel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Shift Start", "SourcePropertyName": "ShiftStart", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Shift End", "SourcePropertyName": "ShiftEnd", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Station", "SourcePropertyName": "Station", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Supervisor Radio Name", "SourcePropertyName": "SupervisorRadioName", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Unit Code", "SourcePropertyName": "UnitCode", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Primary Resource Type", "SourcePropertyName": "PrimaryResourceType", "Width": "*", "MinWidth": 50.0}], "IsEnabled": true}, "EnableViewOtherAgenciesUnits": true, "StopRequestingUpdatesWhileInactive": true, "Buttons": [{"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 12, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 13, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 14, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 15, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 16, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 17, "MobileAction": 3}], "IsEnabled": true, "VersionNumber": 9}