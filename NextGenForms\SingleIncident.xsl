<?xml version="1.0" ?>


<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">

	
	<xsl:variable name="currentpage">
		<xsl:value-of select="/results/exemel/NewDataSet/Table[1]/CurrentPage"/>
	</xsl:variable>	
	<xsl:variable name="nextpage">
		<xsl:value-of select="$currentpage + 1"/>
	</xsl:variable>		
	<xsl:variable name="prevpage">
		<xsl:value-of select="$currentpage - 1"/>
	</xsl:variable>
	<xsl:variable name="totalpages">
		<xsl:value-of select="/results/exemel/NewDataSet/Table[1]/TotalPages"/>
	</xsl:variable>
	<xsl:variable name="incidentNumber">
		<xsl:value-of select="/results/exemel/NewDataSet/Table[1]/incidentnumber"/>
	</xsl:variable>
	
	<xsl:output method="xml" />
	<xsl:preserve-space elements="*"/>



	<xsl:template match="/">
		<HTML>
			<HEAD>
				<TITLE>Mobile Enterprise - Incident</TITLE>
				<LINK href="style.css" type="text/css" rel="stylesheet"/>
			</HEAD>
			<BODY>
				<form id="queryform" name="queryform" action="GetIncident.aspx?queryfile=GetIncident.qry" method="post">				
					<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1" width="98%">
					
						<TBODY>
							<TR>
								<TD vAlign="top">
									<H4 align="center">Incident</H4>										
										<input id="incidentnumber" type="text" value="{$incidentNumber}" style="display:none"/>
										<input id="pagenumber" type="hidden" value="1" style="display:none"/>
										<input type="submit" style="width:0px" id="submitbutton" name="submitbutton"/>
									<xsl:apply-templates select="results/exemel"/>	
								</TD>
							</TR>
						</TBODY>
					
				</TABLE>			
			</form>
		</BODY>
	<script type="text/javascript">
	var current = parse(<xsl:value-of select="$currentpage"/>) || 0;
	var totalpages = parse(<xsl:value-of select="$totalpages"/>) || 0;
	var nextpage = current + 1
	var previousPage = current - 1
	
	function parse(param)
	{
		return param;
	}

	window.onload = function()
	{
		document.getElementById("pagenumber").value = 1;
		if(totalpages === 0)
		{
		    return;
		}
		else if(totalpages == 1)
		{
			document.getElementById("CommentsTop").style.display = "none"
			document.getElementById("CommentsBottom").style.display = "none"
		}
		else if(current == 1)
		{
			document.getElementById("previousPageTop").style.display = "none"
			document.getElementById("previousPageBottom").style.display = "none"						
		}
		else if(current == totalpages)
		{
			document.getElementById("nextPageTop").style.display = "none"
			document.getElementById("nextPageBottom").style.display = "none"
		}
		else
		{
			document.getElementById("CommentsTop").style.display = "inline"
			document.getElementById("CommentsBottom").style.display = "inline" 
		}
	}
			
	function processNextClick()
	{
		document.getElementById("pagenumber").value = nextpage;
		document.getElementById("submitbutton").click();
	}
	
	function processPreviousClick()
	{
		document.getElementById("pagenumber").value = previousPage;
		document.getElementById("submitbutton").click();
	}

	
	function printIncident()
	{
		document.getElementById("printBtn").style.visibility = "hidden";
		window.print();
		document.getElementById("printBtn").style.visibility = "visible";
	}
			</script>
		</HTML>
	</xsl:template>

	<xsl:template match="/results/exemel">

		<xsl:variable name="unique-ids" 
		select="NewDataSet
			/Table[not(ID=preceding-sibling::Table[1]/ID)]
			/ID"
	/>
		<xsl:for-each select="$unique-ids">
			<xsl:variable name="incidentid" select="//Table[ID=current()]/ID"/>
			<table width="100%">
				<tr>
					<td width="40%">
						<strong>
							<font color="blue" face="Verdana">Master Inc. # </font> 
							<font face="Verdana">
								<xsl:value-of select="//Table[ID=current()]/incidentnumber"/>
							</font>
						</strong>
					</td>
					<td width="40%">
						<strong>
							<font color="blue" face="Verdana">  Case #</font>
							<font face="Verdana">
								<xsl:value-of select="//Table[ID=current()]/casenumber"/>
							</font>
						</strong>
					</td>
					<td width="20%" align="right">
						<button id="printBtn" type="button" value="Print" onclick="printIncident()"  style="background-color:Transparent; border:none; cursor:pointer;">
							<img src="..\Bitmaps\printer.png" style="width:40px;"/>
						</button>
					</td>
				</tr>
			</table>

			<xsl:variable name="recordid">
				<xsl:value-of select="//Table[1]/ID"/>
			</xsl:variable>


			<table class="tableStyle1" width="100%">
				<tr class="trHeader">
					<td width="40%">
          Call Details - 
						<xsl:if test="//Table[ID=current()]/Call_Is_Active='1'">
							<font color="#009900">ACTIVE</font>
						</xsl:if>
						<xsl:if test="//Table[1]/Call_Is_Active='0'">
							<font color="#990033">CLOSED</font>
						</xsl:if>
					</td>
					<td width="40%"/>
					<td width="20%">Caller Information</td>
				</tr>
				<tr>
					<td width="40%">
						<strong>Agency: </strong>
						<xsl:value-of select="//Table[ID=current()]/agency_type"/>
						<br/>
						<strong>Date: </strong>
						<xsl:value-of select="//Table[ID=current()]/responsedate"/>
						<br/>
						<strong>Problem: </strong>
						<xsl:value-of select="//Table[ID=current()]/problem"/>
						<br/>
					</td>
					<td width="40%" align="left">
						<xsl:if test='//Table[ID=current()]/Call_Is_Active=0'>
							<strong>Time Closed: </strong>
							<xsl:value-of select="//Table[ID=current()]/TimeClosed"/>
							<br/>
						</xsl:if>
						<strong>Disposition: </strong>
						<xsl:value-of select="//Table[ID=current()]/Call_Disposition"/>
						<br/>
						<strong>Call Taker: </strong>
						<xsl:value-of select="//Table[ID=current()]/CallTaker"/>
						<br/>
						<xsl:if test="//Table[ID=current()]/PrimaryTacChannel!=''">
							<strong>Primary TAC Channel: </strong>
							<xsl:value-of select="//Table[ID=current()]/PrimaryTacChannel"/>
							<br/>
						</xsl:if>
						<xsl:if test="//Table[ID=current()]/AlternateTacChannel!=''">
							<strong>Secondary TAC Channel: </strong>
							<xsl:value-of select="//Table[ID=current()]/AlternateTacChannel"/>
						</xsl:if>
					</td>

					<td width="20%" style="border-left:1px black solid;border-bottom:1px black solid;">
						<table> 
							<tr valign="top">
								<td>
									<strong>Method Call Received:</strong>
								</td>
								<td>
									<xsl:value-of select="//Table[ID=current()]/methodofcallreceived"/>
								</td>
							</tr>
							<tr>
								<td>
									<strong>Name: </strong>
								</td>
								<td>
									<xsl:value-of select="//Table[ID=current()]/callername"/>
								</td>
							</tr>
							<tr>
								<td>
									<strong>Phone: </strong>
								</td>
								<td>
									<xsl:value-of select="//Table[ID=current()]/callerphone"/>
								</td>
							</tr>
							<tr>
								<td>
									<strong>Address: </strong>
								</td>
								<td>
									<xsl:if test="//Table[ID=current()]/calleraddress!=''">
										<xsl:value-of select="//Table[ID=current()]/calleraddress"/>
										<br/>
									</xsl:if>
									<xsl:if test="//Table[ID=current()]/calleraddress2!=''">
										<xsl:value-of select="//Table[ID=current()]/calleraddress2"/>
									</xsl:if>
								</td>              
							</tr>
						</table>
					</td>
				</tr>			
			</table>
			<!-- TABLE IS THE FIRST ROW-->


			<p/>
			<table class="tableStyle1" width="100%">
				<td width="40%">
					<strong>Sector: </strong>
					<xsl:value-of select="//Table[ID=current()]/sectordesc"/>
					<br/>
					<strong>Response Area: </strong>
					<xsl:value-of select="//Table[ID=current()]/ResponseArea"/>
					<br/>
					<strong>Priority: </strong>
					<xsl:value-of select="//Table[ID=current()]/priority"/>
					<br/>
					<strong>Primary Unit: </strong>
					<xsl:if test="//Table[ID=current()]/primaryunit!=''">
						<xsl:variable name="element_value" select="//Table[ID=current()]/primaryunit"/>
						<xsl:element name="A">
							<xsl:attribute name="href">IncidentUnitDetails.aspx?IncidentID=<xsl:value-of select="$recordid"/>&#38;Unit=<xsl:value-of select="$element_value"/>&#38;queryfile=IncidentUnitDetails.qry</xsl:attribute>
							<xsl:value-of select="$element_value"/>
						</xsl:element>
					</xsl:if>
					<br/>
					<strong>Assigned Units: </strong>
					<xsl:if test="//Table[ID=current()]/assignedunit!=''">
						<xsl:call-template name="tokenize">
							<xsl:with-param name="source_str" select="//Table[ID=current()]/assignedunit"/>
							<xsl:with-param name="delimiter" select="','"/>
							<xsl:with-param name="element_name" select="'A'"/>
							<xsl:with-param name="incidentID" select="$recordid"/>
						</xsl:call-template>
					</xsl:if>
					<br/>
				</td>
				<td width="60%" >
					<!--</td>align="left">style="padding-left:15px"> -->
					<strong>Time First Assigned:  
						<xsl:if test="//Table[ID=current()]/primaryunit!=''">
							<xsl:variable name="element_value" select="//Table[ID=current()]/firstunitassigned"/>
							<xsl:element name="A">
								<xsl:attribute name="href">IncidentUnitDetails.aspx?IncidentID=<xsl:value-of select="$recordid"/>&#38;Unit=<xsl:value-of select="$element_value"/>&#38;queryfile=IncidentUnitDetails.qry</xsl:attribute>
								<xsl:value-of select="$element_value"/>
							</xsl:element>
						</xsl:if>
						<xsl:text disable-output-escaping="yes">
							<![CDATA[&nbsp;&nbsp;&nbsp;]]>
						</xsl:text>
						<xsl:value-of select="//Table[ID=current()]/Time_First_Unit_Assigned"/>
					</strong>
					<br/>
					<strong>Time First Arrived :  
						<xsl:if test="//Table[ID=current()]/primaryunit!=''">
							<xsl:variable name="element_value" select="//Table[ID=current()]/firstunitarrived"/>
							<xsl:element name="A">
								<xsl:attribute name="href">IncidentUnitDetails.aspx?IncidentID=<xsl:value-of select="$recordid"/>&#38;Unit=<xsl:value-of select="$element_value"/>&#38;queryfile=IncidentUnitDetails.qry</xsl:attribute>
								<xsl:value-of select="$element_value"/>
							</xsl:element>
						</xsl:if>
						<xsl:text disable-output-escaping="yes">
							<![CDATA[&nbsp;&nbsp;&nbsp;]]>
						</xsl:text>				
						<xsl:value-of select="//Table[ID=current()]/Time_First_Unit_Arrived"/>
					</strong>
					<br/>
					<xsl:if test="//Table[ID=current()]/TimeFirstPTContact!=''">
						<strong>Time First Point Of Contact: </strong>
						<xsl:value-of select="//Table[ID=current()]/TimeFirstPTContact"/>
						<br/>
					</xsl:if>
				</td>
				<!-- <td width="20%"></td> -->
				<!--width="500px"></td>-->
				<!-- </table> -->
				<!--TABLE ABOVE IS THE SECOND SECTION OF THE REPORT -->
				<p/>
				<!-- <table class="tableStyle1"> -->
				<tr>
					<td>
						<strong>Location: </strong>
						<xsl:if test="//Table[ID=current()]/location!=''">
							<xsl:variable name="locationname">
								<xsl:value-of select="//Table[ID=current()]/location"/>
							</xsl:variable>
							<a href="GetPremiseDetails.aspx?location={$locationname}&#38;queryfile=GetPremiseDetails.qry">
								<xsl:value-of select="//Table[ID=current()]/location"/>
							</a>
						</xsl:if>
						<br/>
						<strong>Address: </strong>
						<xsl:value-of select="//Table[ID=current()]/address"/>
						<xsl:value-of select="//Table[ID=current()]/address2"/>
						<br/>
						<strong>Phone #: </strong>
						<xsl:value-of select="//Table[ID=current()]/Call_Back_Phone"/>
						<xsl:text disable-output-escaping="yes">&amp;nbsp;&amp;nbsp;&amp;nbsp;</xsl:text>
						<strong>Extension: </strong>
						<xsl:value-of select="//Table[ID=current()]/Call_Back_Phone_Ext"/>
						<br/>
						<xsl:if test="//Table[ID=current()]/incidenttype!=''">
							<strong>Call Type: </strong>
							<xsl:value-of select="//Table[ID=current()]/incidenttype"/>
							<br/>
						</xsl:if>
						<xsl:if test="//Table[ID=current()]/FireBox!=''">
							<strong>Fire Box: </strong>
							<xsl:value-of select="//Table[ID=current()]/FireBox"/>
							<br/>
						</xsl:if>
						<xsl:if test="//Table[ID=current()]/Preplan!=''">
							<strong>Preplan: </strong>
							<xsl:element name="a">
								<xsl:attribute name="href">#</xsl:attribute>
								<xsl:attribute name="id">OpenPrePlanAttachment</xsl:attribute>
								<xsl:attribute name="executefunction">OpenPrePlanAttachment</xsl:attribute>
								<xsl:attribute name="parameters">Preplan=<xsl:value-of select="//Table[ID=current()]/Preplan"/>
								</xsl:attribute>
								<xsl:value-of select="//Table[ID=current()]/Preplan"/>
							</xsl:element>
						</xsl:if>
					</td>

					<td>
						<!-- <xsl:if test="//Table[ID=current()]/Building!=''"> -->
						<strong>&#32;Apt #:</strong>
						<xsl:text disable-output-escaping="yes">
							<![CDATA[&nbsp;&nbsp;]]>
						</xsl:text>				
						<xsl:value-of select="//Table[ID=current()]/Apartment"/>
						<xsl:text disable-output-escaping="yes">
							<![CDATA[&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;]]>
						</xsl:text>
						<strong>&#32;Bldg #:</strong>
						<xsl:text disable-output-escaping="yes">
							<![CDATA[&nbsp;&nbsp;]]>
						</xsl:text>				
						<xsl:value-of select="//Table[ID=current()]/Building"/>

						<br/>
						<strong>Cross Street: </strong>
						<xsl:value-of select="//Table[ID=current()]/crossstreet"/>
						<br/>
						<strong>Map Info: </strong>
						<xsl:if test="//Table[ID=current()]/MapInfo!=''">
							Map#: 
							<xsl:value-of select="//Table[ID=current()]/MapInfo"/>,&#32;
						</xsl:if>
						<xsl:if test="//Table[ID=current()]/Latitude!=''">
            Coordinates:
							<xsl:element name="a">
								<xsl:attribute name="href">#</xsl:attribute>
								<xsl:attribute name="id">MapIt</xsl:attribute>
								<xsl:attribute name="executefunction">MapIt</xsl:attribute>
								<xsl:attribute name="parameters">Latitude=<xsl:value-of select="//Table[ID=current()]/Latitude"/>&amp;Longitude=<xsl:value-of select="//Table[ID=current()]/Longitude"/>
								</xsl:attribute>
								<xsl:value-of select="//Table[ID=current()]/Latitude"/>,<xsl:value-of select="//Table[ID=current()]/Longitude"/>
							</xsl:element>
							<br/>
						</xsl:if>
					</td>
				</tr>
				<!-- <td width="20%"></td> -->
				<!-- </table> -->
				<p/>
				<!-- <table class="tableStyle1"> -->
				<tr>
					<td>
						<xsl:if test="//Table[ID=current()]/TransportID!=''">
							<xsl:for-each select="//Table[ID=current() and not(TransportID=preceding-sibling::Table[ID=current()]/TransportID)]">

								<td width="70px">
									<strong>Pickup: </strong>
								</td>
								<td>
									<xsl:if test="PickupLocationName!=''">
										<xsl:value-of select="PickupLocationName"/>
										<br/>
									</xsl:if>
									<xsl:if test="PickupAddress!=''">
										<xsl:value-of select="PickupAddress"/>
									</xsl:if>
									<xsl:if test="PickupBuilding!=''">
                ,&#32;Bldg:<xsl:value-of select="PickupBuilding"/>
									</xsl:if>
									<xsl:if test="PickupApartment!=''">
                ,&#32;Apt:<xsl:value-of select="PickupApartment"/>
										<br/>
									</xsl:if>
									<xsl:if test="PickupCity!=''">
										<xsl:value-of select="PickupCity"/>
									</xsl:if>
								</td>
								<td width="110px">
									<strong>Destination: </strong>
								</td>
								<td>
									<xsl:if test="TimeArriveDestination!=''">
										<xsl:value-of select="TimeArriveDestination"/>
										<br/>
									</xsl:if>
									<xsl:if test="DestinationLocation!=''">
										<xsl:value-of select="DestinationLocation"/>
										<br/>
									</xsl:if>
									<xsl:if test="DestinationAddress!=''">
										<xsl:value-of select="DestinationAddress"/>
										<br/>
									</xsl:if>
									<xsl:if test="DestinationCity!=''">
										<xsl:value-of select="DestinationCity"/>
									</xsl:if>
								</td>

							</xsl:for-each>
						</xsl:if>
					</td>
				</tr>
			</table>
			<P>
				<div id="CommentsTop" align="Left" style="display:initial;">
					<b>
						<div align="left">
							<a id="previousPageTop" name="previousPageTop" href="#" onclick="processPreviousClick()" style="display:inline-block; font-size:1em; width:20%; float:left">Previous Page</a>
						</div>
						<div align="right">
							<a id="nextPageTop" name="nextPageTop" href="#" onclick="processNextClick()" style="display:inline-block; font-size:1em; width:80%; float:right">Next Page</a>
						</div>
					</b>					
				</div>
			<br/>
			</P>
			<br/>
			<table class="tableStyle1">
				<tr class="trHeader">
					<td colspan="3">Comments</td>
				</tr>
				<tr class="trSubHeader">
					<td width="185">
                 Date
					</td>
					<td style="padding-left:15px;padding-right:15px">
                 Comment
					</td>
					<td>
                 User
					</td>
				</tr>
				<xsl:for-each select="//Table[ID=current() and not(CommentID=preceding-sibling::Table[ID=current()]/CommentID)]">
					<xsl:sort select="Date_Time" order="descending"/>
					<xsl:if test="Comment!=''">
						<tr>
							<td width="185">
								<xsl:value-of select="Date_Time"/>
							</td>
							<td style="padding-left:15px;padding-right:15px">
								<xsl:call-template name="ReplaceNewLineWithBR">
									<xsl:with-param name="Comment" select="Comment"/>
								</xsl:call-template>
							</td>
							<td>
								<xsl:value-of select="Performed_By"/>
							</td>
						</tr>
					</xsl:if>
				</xsl:for-each>
			</table>
			<P>
				<div id="CommentsBottom" align="Left" style="display:initial">
					<b>						
						<div align="left">						
							<a id="previousPageBottom" name="previousPageBottom" href="#" onclick="processPreviousClick()" style="display:inline-block; font-size:1em; width:20%; float:left">Previous Page</a>
						</div>
						<div align="right">
							<a id="nextPageBottom" name="nextPageBottom" href="#" onclick="processNextClick()" style="display:inline-block; font-size:1em; width:80%; float:right">Next Page</a>
						</div>
					</b>							
				</div>
			<br/>
			</P>						
			<!--<p>
			<a href="IncidentComments.aspx?ID={$recordid}&#38;queryfile=incidentcomments.qry">Get Incident Comments</a>
		</p>-->		
                        <br/>	
			<p>
				<a href="PersonnelSearch.aspx?IncludeOnDuty=true&#38;IncludeOffDuty=true&#38;IncludeFromUnits=true&#38;IncludeFromUnits=true&#38;IncidentID={$recordid}&#38;queryfile=PersonnelSearch.qry" location="remote">Get Personnel&#39;s Current Status</a>
			</p>
			<p>
				<a href="LinkedIncidents.aspx?ID={$incidentid}&#38;queryfile=LinkedIncidents.qry" location="remote">Other Linked Incidents</a>
			</p>
			<p>	
				<xsl:if test="//Table[ID=current()]/HasPersonSupplement='true'">
					<a href="SupplementPerson.aspx?IncidentID={$recordid}&#38;queryfile=IncidentSupplementPerson.qry">Get Supplement Person</a>
				</xsl:if>		
			</p>
			<p>			
				<xsl:if test="//Table[ID=current()]/HasVehicleSupplement='true'">				
					<a href="SupplementVehicle.aspx?IncidentID={$recordid}&#38;queryfile=IncidentSupplementVehicle.qry">Get Supplement Vehicle</a>
				</xsl:if>			
			</p>
			<p>
				<xsl:if test="//Table[ID=current()]/HasPropertySupplement='true'">
					<a href="SupplementProperty.aspx?IncidentID={$recordid}&#38;queryfile=IncidentSupplementProperty.qry">Get Supplement Property</a>
				</xsl:if>
			</p>
			<p>
				<xsl:if test="//Table[ID=current()]/HasWeaponSupplement='true'">
					<a href="SupplementWeapon.aspx?IncidentID={$recordid}&#38;queryfile=IncidentSupplementWeapon.qry">Get Supplement Weapon</a>
				</xsl:if>
			</p>		
			<br/>
		</xsl:for-each>
	</xsl:template> 

	<xsl:template name="tokenize">
		<xsl:param name="source_str"/>
		<xsl:param name="delimiter" select="' '"/>
		<xsl:param name="element_name" select="'item'"/>
		<xsl:param name="incidentID" select="''"/>
		<xsl:variable name="temp_str" select="normalize-space($source_str)"/>

		<xsl:variable name="source_str_1">
			<xsl:choose>
				<xsl:when test="substring($temp_str,string-length($temp_str)
            - string-length($delimiter) 
            + 1,string-length($delimiter))!=$delimiter">
					<xsl:value-of select="$temp_str"/>
					<xsl:value-of select="$delimiter"/>
				</xsl:when>
				<xsl:otherwise>
					<xsl:value-of select="$temp_str"/>
				</xsl:otherwise>
			</xsl:choose>
		</xsl:variable>

		<xsl:choose>
			<xsl:when test="contains($source_str_1,$delimiter)">
				<xsl:variable name="element_value" select="substring-before($source_str_1,$delimiter)"/>
				<xsl:element name="{$element_name}">
					<xsl:attribute name="href">IncidentUnitDetails.aspx?IncidentID=<xsl:value-of select="$incidentID"/>&#38;Unit=<xsl:value-of select="$element_value"/>&#38;queryfile=IncidentUnitDetails.qry</xsl:attribute>
					<xsl:value-of select="$element_value"/>
				</xsl:element>
			</xsl:when>
			<xsl:otherwise>
				<xsl:if test="$source_str_1!=''">
					<xsl:element name="{$element_name}">
						<xsl:value-of select="$source_str_1"/>
					</xsl:element>
				</xsl:if>
			</xsl:otherwise>
		</xsl:choose>
	&#32;

		<xsl:variable name="new_string" 
         select="substring-after($source_str_1,$delimiter)"/>
		<xsl:if test="not($new_string = '')">
			<xsl:value-of select="$delimiter"/>
			<xsl:call-template name="tokenize">
				<xsl:with-param name="source_str" select="$new_string"/>
				<xsl:with-param name="delimiter" select="$delimiter"/>
				<xsl:with-param name="element_name" select="$element_name"/>
				<xsl:with-param name="incidentID" select="$incidentID"/>
			</xsl:call-template>
		</xsl:if>
	</xsl:template>

	<xsl:template name="ReplaceNewLineWithBR">
		<xsl:param name="Comment" select="." />
		<xsl:choose>
			<xsl:when test="not($Comment)" />
			<xsl:when test="contains($Comment, '&#10;')">
				<xsl:value-of select="substring-before($Comment, '&#10;')" />
				<br/>
				<xsl:call-template name="ReplaceNewLineWithBR">
					<xsl:with-param name="Comment"
                          select="substring-after($Comment, '&#10;')" />
				</xsl:call-template>
			</xsl:when>
			<xsl:otherwise>
				<xsl:value-of select="$Comment" />
				<br/>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>


</xsl:transform>