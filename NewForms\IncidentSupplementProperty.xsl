<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>Mobile Enterprise - Incident Supplement Property</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Supplement Property</H4>
						<P><h3>Property Supplement Info for Incident Number : <xsl:value-of select="/results/exemel/NewDataSet/Table/IncidentNumber"/></h3>

	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
							
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
		
	<table cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;color:white;border-width:1px;border-style:None;">
		<tr style="font-weight:bold;">
			<td style="color:windowtext;background-color:window;" colspan="3" >
				<xsl:if test="Article != ''">				
				<xsl:value-of select="Article"/>
				</xsl:if>			
				<xsl:if test="PropertyType != ''">
				<xsl:value-of select="concat(' - ', PropertyType)"/>
				</xsl:if>	
			</td>		
		</tr>	
		<tr style="font-weight:bold;">
			<xsl:if test="Brand != ''">
				<td>Brand</td><td style="color:windowtext;background-color:window;" colspan="3" >
				<xsl:value-of select="Brand"/></td>
			</xsl:if>
			<xsl:if test="SerialNumber != ''">
				<td>Serial Number</td><td style="color:windowtext;background-color:window;" colspan="3" >
				<xsl:value-of select="SerialNumber"/></td>
			</xsl:if>
							
		</tr>	
	</table>
	<p></p>

</xsl:template> 

</xsl:transform>