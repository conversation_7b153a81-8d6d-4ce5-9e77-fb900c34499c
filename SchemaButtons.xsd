<?xml version="1.0" encoding="utf-8" ?>
<xs:schema id="SchemaButtons" 
			targetNamespace="http://tempuri.org/SchemaButtons.xsd" 
			elementFormDefault="qualified" 
			xmlns="http://tempuri.org/SchemaButtons.xsd" 
			xmlns:mstns="http://tempuri.org/SchemaButtons.xsd" 
			xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:complexType name="RunType">
		<xs:sequence>
			<xs:element name="FileName" type="xs:string" minOccurs="1" maxOccurs="1" /> <!-- if the type is Run, then this is the file being opened -->
			<xs:element name="Arguments" type="xs:string" minOccurs="0" maxOccurs="1" /> <!-- if the type is Run, then this is the file being opened -->
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FieldType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Value" type="xs:string" minOccurs="1" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ColorType">
		<xs:sequence>
			<xs:element name="Color" type="xs:int" minOccurs="0" maxOccurs="1" />
			<xs:element name="ColorBGR" type="xs:int" minOccurs="0" maxOccurs="1" />
			<xs:element name="ColorRed" type="xs:unsignedByte" minOccurs="0" maxOccurs="1" />
			<xs:element name="ColorGreen" type="xs:unsignedByte" minOccurs="0" maxOccurs="1" />
			<xs:element name="ColorBlue" type="xs:unsignedByte" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SizeType">
		<xs:sequence>
			<xs:element name="Width" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" />
			<xs:element name="Height" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MoveCaretType">
		<xs:sequence>
			<xs:element name="NewLine" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="HorizontalDistance" type="xs:short" minOccurs="0" maxOccurs="1" />
			<xs:element name="VerticalDistance" type="xs:short" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ImageType">
		<xs:sequence>
			<xs:element name="ImageFile" type="xs:string" minOccurs="1" maxOccurs="1" /> <!-- including file path and name. Relative to Application startup path. Can be any type: bmp, jif, jpg, etc.-->
			<xs:element name="UseImageOnly" type="xs:boolean" minOccurs="0" maxOccurs="1" /> <!-- if true, then the button GUI is based on a picture box with no border, instead of a button -->
		</xs:sequence>
	</xs:complexType>
  <xs:complexType name="ControlSpeechEnablingType">
    <xs:sequence>
      <xs:element name="ControlID" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="ClickCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="ReadValueCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="SetValueCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="CheckCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="ClearCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="FocusCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ButtonIDList">
    <xs:sequence minOccurs="0" maxOccurs="unbounded">
      <xs:element name="ButtonID" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="FormSpeechEnablingType">
    <xs:sequence>
      <xs:element name="Enabled" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="ControlSpeechEnabling" type="ControlSpeechEnablingType" minOccurs="1" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="Button_type">
		<xs:sequence>
			<xs:element name="Type" minOccurs="1" maxOccurs="1">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="Backward" />
						<xs:enumeration value="Forward" />
						<xs:enumeration value="NavBarToggle" />
						<xs:enumeration value="InfoBarToggle" />
						<xs:enumeration value="DayNightToggle" />
						<xs:enumeration value="Map" />
						<xs:enumeration value="Messages" />
						<xs:enumeration value="NewMessage" />
						<xs:enumeration value="Incidents" />
						<xs:enumeration value="Form" />
						<xs:enumeration value="Status" />
						<xs:enumeration value="OtherTreeNode" />
						<xs:enumeration value="Emergency" />
						<xs:enumeration value="FindAddress" />
            			<xs:enumeration value="StopSpeaking" />
            			<xs:enumeration value="ActivateVoiceCommands" />
            			<xs:enumeration value="OutOfVehicleToggle" />
            			<xs:enumeration value="CardReader" />
						<xs:enumeration value="Run" />
						<xs:enumeration value="Toolbar" />
						<xs:enumeration value="Separator" />
						<xs:enumeration value="ChangePassword" />
						<xs:enumeration value="ResetPassword" />
						<xs:enumeration value="DelayMessage" />
            <xs:enumeration value="ViewIncidentDetails" />
            <xs:enumeration value="FormWebRMS" />
            <xs:enumeration value="Chat" />
          </xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Caption" type="xs:string" minOccurs="0" maxOccurs="1" />

      		<!-- Voice command(s) to click this button -->
      		<xs:element name="VoiceCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      
      		<xs:element name="Open" type="RunType" minOccurs="0" maxOccurs="unbounded" />
      <!-- if the StatusValue is an integer status ID that exists in Configuration_Statuses.xml, then the StatusValue,back and fore colors will be taken from Configuration_Statuses.xml -->
      <xs:element name="StatusValue" type="xs:string" minOccurs="0" maxOccurs="1" />
      <!-- When the unit is at this status, we should define which "next" status buttons are avilable for the user to click. We can't just say which statuses, because you could have multiple status buttons for the same status. For example, ClearCall and Available and FireLog -->
      <xs:element name="NextAvailableStatusButtonsCaptions" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="SubmitStatusAndFormRequest" type="xs:boolean" minOccurs="0" maxOccurs="1" /> <!-- relevant for status buttons only -->
			<xs:element name="DontShowStatusOverrideMsg" type="xs:boolean" minOccurs="0" maxOccurs="1" /> <!-- relevant for status buttons only -->
			<xs:element name="AutomaticOutOfVehicle" type="xs:boolean" minOccurs="0" maxOccurs="1" /> <!-- relevant for status buttons only -->
			<xs:element name="StatusRequestRequires" minOccurs="0" maxOccurs="1"> <!-- relevant for status buttons only -->
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="AnIncident" />	<!-- An assigned incident is required for requesting this staus -->
						<xs:enumeration value="NoIncident" />	<!-- Unit should be unassigned when requesting this status -->
						<xs:enumeration value="NA" />			<!-- Default. Status can be requested when either assigned or unassigned -->
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="AvlUpdateServerMinimumSecondsBetweenUpdates" type="xs:int" minOccurs="0" maxOccurs="1" /> <!-- relevant for status buttons only. If not set, then default is taken from Configuration.xml -->
			<xs:element name="AvlUpdateServerMaximumSecondsBetweenUpdates" type="xs:int" minOccurs="0" maxOccurs="1" /> <!-- relevant for status buttons only. If not set, then default is taken from Configuration.xml -->
			<xs:element name="AvlUpdateServerIfLocationChangedMoreThan" type="xs:int" minOccurs="0" maxOccurs="1" /> <!-- relevant for status buttons only. If not set, then default is taken from Configuration.xml -->
			<xs:element name="TreeNodeParent" type="xs:string" minOccurs="0" maxOccurs="1" />
			<xs:element name="TreeNodeLeaf" type="xs:string" minOccurs="0" maxOccurs="1" />
			<xs:element name="Field" type="FieldType" minOccurs="0" maxOccurs="unbounded" />
      		<xs:element name="FormSpeechEnabling" type="FormSpeechEnablingType" minOccurs="0" maxOccurs="1" />
      		<xs:element name="Size" type="SizeType" minOccurs="0" maxOccurs="1" />
			<xs:element name="MoveCaret" type="MoveCaretType" minOccurs="0" maxOccurs="1" />
			<xs:element name="BackgroundImage" type="ImageType" minOccurs="0" maxOccurs="1" />
			<xs:element name="IconID" type="xs:int" minOccurs="0" maxOccurs="1" />
			<xs:element name="FontSize" type="xs:float" minOccurs="0" maxOccurs="1" />
			<xs:element name="FKeyNumber" type="xs:int" minOccurs="0" maxOccurs="1" />
			<xs:element name="HoldAltKey" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="HoldCtrlKey" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="HoldShiftKey" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="BackColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="ForeColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="IsHidden" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="IsFavorite" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="Toolbar" type="Toolbar_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="NotificationInfo" type="xs:string" minOccurs="0" maxOccurs="1" />		
      <xs:element name="Context" type="ContextType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="Position" type="xs:int" minOccurs="0" maxOccurs="1"/>
      <xs:element name="IsEnabled" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="ButtonType" type="ButtonType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="IconType" type="IconType" minOccurs="0" maxOccurs="1"/>
      <xs:element name="ChildrenIDs" type="ButtonIDList" minOccurs="0" maxOccurs="1" />
      <xs:element name="ButtonID" type="xs:int" minOccurs="0" maxOccurs="0"/>
      <xs:element name="MobileActionID" type="xs:int" minOccurs="0" maxOccurs="1"/>
      <xs:element name="FormName" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="RecordsCheckPage" type="RecordsCheckPageType" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Toolbar_type">
		<xs:sequence>
			<xs:element name="RelativeLocationX" type="xs:short" minOccurs="0" maxOccurs="1" />
			<xs:element name="RelativeLocationY" type="xs:short" minOccurs="0" maxOccurs="1" />
			<xs:element name="Button" type="Button_type" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
  <xs:simpleType name="ContextType" final="restriction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None"/>
      <xs:enumeration value="IncidentsQueue"/>
      <xs:enumeration value="UnitsQueue"/>
      <xs:enumeration value="IncidentDetails"/>
      <xs:enumeration value="Messages"/>
      <xs:enumeration value="Global"/>      
      <xs:enumeration value="RecordsCheck"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="RecordsCheckPageType" final="restriction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="QueryForm"/>
      <xs:enumeration value="AttachResponse"/>
      <xs:enumeration value="ForwardResponse"/>
      <xs:enumeration value="Responses"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ButtonType" final="restriction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Form"/>
      <xs:enumeration value="Status"/>
      <xs:enumeration value="General"/>
      <xs:enumeration value="RecordsCheck"/>
      <xs:enumeration value="External"/>
    </xs:restriction>
  </xs:simpleType>
	<!-- Toolbar element (used in Configuration_Buttons.xml) -->
	<xs:element name="Toolbar" type="Toolbar_type" />

	<xs:complexType name="Status_type">
		<xs:sequence>
			<xs:element name="ID" type="xs:int" minOccurs="1" maxOccurs="1" />
			<xs:element name="StatusValue" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="BackColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="ForeColor" type="ColorType" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
	</xs:complexType>
	<xs:complexType name="Statuses_type">
		<xs:sequence>
			<xs:element name="Status" type="Status_type" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<!-- Statuses element (used in Configuration_Statuses.xml that is built automatically by ListBuilder -->
	<xs:element name="Statuses" type="Statuses_type"/>

  <xs:simpleType name="IntsList">
    <xs:list itemType="xs:integer"/>
  </xs:simpleType>
  
  <xs:simpleType name="IconTypeByName" final="restriction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None"/>
      <xs:enumeration value="TriTechIcon"/>
      <xs:enumeration value="MailIcon"/>
      <xs:enumeration value="SettingsIcon"/>
      <xs:enumeration value="ConnectedIcon"/>
      <xs:enumeration value="DisconnectedIcon"/>
      <xs:enumeration value="DestinationIcon"/>
      <xs:enumeration value="CurrentLocationIcon"/>
      <xs:enumeration value="MapsIcon"/>
      <xs:enumeration value="PhoneIcon"/>
      <xs:enumeration value="PeopleIcon"/>
      <xs:enumeration value="BackIcon"/>
      <xs:enumeration value="HorizontalEllipsisIcon"/>
      <xs:enumeration value="ClipboardIcon"/>
      <xs:enumeration value="RightArrowIcon"/>
      <xs:enumeration value="ClosingWindowIcon"/>
      <xs:enumeration value="FolderIcon"/>
      <xs:enumeration value="CheckboxBlankIcon"/>
      <xs:enumeration value="CheckboxInderminateIcon"/>
      <xs:enumeration value="CheckboxMarkedIcon"/>
      <xs:enumeration value="ArrowMenuDownIcon"/>
      <xs:enumeration value="ArrowMenuUpIcon"/>
      <xs:enumeration value="FilterVariantIcon"/>
      <xs:enumeration value="AccountIcon"/>
      <xs:enumeration value="LinkIcon"/>
      <xs:enumeration value="InfoIcon"/>
      <xs:enumeration value="HomeIcon"/>
      </xs:restriction>
  </xs:simpleType>
    
  <xs:simpleType name="IconType">
    <xs:union memberTypes="IconTypeByName xs:int"/>
  </xs:simpleType>
</xs:schema>
