<?xml version="1.0" encoding="utf-8"?>
<Queues xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://tempuri.org/SchemaQueues.xsd">
  <UnitQueue>
    <Column>
      <Field>
        <Type>unit_name</Type>
      </Field>
      <Header>Unit</Header>
      <Width>85</Width>
    </Column>
    <Column>
      <Field>
        <Type>current_status</Type>
      </Field>
      <Header>St</Header>
      <Width>43</Width>
    </Column>
    <Column>
      <Field>
        <Type>ElapsedTime</Type>
      </Field>
      <Header>Time</Header>
      <Width>82</Width>
    </Column>
    <Column>
      <Field>
        <Type>AssignedIncidentField</Type>
        <IncidentField>
          <Type>problem_nature</Type>
        </IncidentField>
      </Field>
      <Header>Problem</Header>
      <Width>160</Width>
      <LinkToAssignedIncident>true</LinkToAssignedIncident>
    </Column>
    <Column>
      <Field>
        <Type>AssignedIncidentField</Type>
        <IncidentField>
          <Type>address</Type>
        </IncidentField>
      </Field>
      <Header>Incd Addr</Header>
      <Width>250</Width>
    </Column>
    <Column>
      <Field>
        <Type>AssignedIncidentField</Type>
        <IncidentField>
          <Type>location_name</Type>
        </IncidentField>
      </Field>
      <Header>Common Name</Header>
      <Width>225</Width>
    </Column>
    <Column>
      <Field>
        <Type>Location</Type>
      </Field>
      <Header>AVL Location</Header>
      <Width>300</Width>
    </Column>
    <Column>
      <Field>
        <Type>current_sector</Type>
      </Field>
      <Header>Sector</Header>
      <Width>100</Width>
    </Column>
  </UnitQueue>
  <ActiveIncidentQueue>
    <Enabled>true</Enabled>
    <Column>
      <Field>
        <Type>priority</Type>
      </Field>
      <Header>P</Header>
      <Width>25</Width>
    </Column>
    <Column>
      <Field>
        <Type>incidentid</Type>
      </Field>
      <Header>ID</Header>
      <Width>44</Width>
      <LinkToIncident>true</LinkToIncident>
    </Column>
    <Column>
      <Field>
        <Type>timestamp</Type>
      </Field>
      <Header>Time</Header>
      <Width>100</Width>
    </Column>
    <Column>
      <Field>
        <Type>problem_nature</Type>
      </Field>
      <Header>Problem</Header>
      <Width>200</Width>
      <LinkToIncident>true</LinkToIncident>
    </Column>
    <Column>
      <Field>
        <Type>assigned_units</Type>
      </Field>
      <Header>Assigned Units</Header>
      <Width>200</Width>
    </Column>
    <Column>
      <Field>
        <Type>address</Type>
      </Field>
      <Header>Address</Header>
      <Width>320</Width>
    </Column>
    <Column>
      <Field>
        <Type>location_name</Type>
      </Field>
      <Header>Location</Header>
      <Width>320</Width>
    </Column>
    <Column>
      <Field>
        <Type>OtherField</Type>
        <Name>division</Name>
      </Field>
      <Header>Jurisdiction</Header>
      <Width>100</Width>
    </Column>
  </ActiveIncidentQueue>
  <IncidentSplitterLocationInPercents>50</IncidentSplitterLocationInPercents>
  <WaitingIncidentQueue>
    <Enabled>true</Enabled>
    <Column>
      <Field>
        <Type>priority</Type>
      </Field>
      <Header>P</Header>
      <Width>25</Width>
    </Column>
    <Column>
      <Field>
        <Type>incidentid</Type>
      </Field>
      <Header>ID</Header>
      <Width>44</Width>
      <LinkToIncident>true</LinkToIncident>
    </Column>
    <Column>
      <Field>
        <Type>timestamp</Type>
      </Field>
      <Header>Time</Header>
      <Width>100</Width>
    </Column>
    <Column>
      <Field>
        <Type>problem_nature</Type>
      </Field>
      <Header>Problem</Header>
      <Width>310</Width>
      <LinkToIncident>true</LinkToIncident>
    </Column>
    <Column>
      <Field>
        <Type>address</Type>
      </Field>
      <Header>Address</Header>
      <Width>350</Width>
    </Column>
    <Column>
      <Field>
        <Type>location_name</Type>
      </Field>
      <Header>Location</Header>
      <Width>375</Width>
    </Column>
    <Column>
      <Field>
        <Type>OtherField</Type>
        <Name>division</Name>
      </Field>
      <Header>Jurisdiction</Header>
      <Width>100</Width>
    </Column>
  </WaitingIncidentQueue>
</Queues>