<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Position Update</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Position Update</H4>
						<form action="PositionUpdateQuery.aspx?queryfile=positionupdate.qry" method="post" id="PositionUpdateQuery"
							name="PositionUpdateQuery">
							<table ID="Table2">
								<tr><td>&nbsp;</td></tr>
								<tr>
									<td align="right">Selected<input type="radio" class="bigradio" name="locationtype" id="selectedlocation" value="selectedlocation" executefunction="ReverseGeoCode" parameters="Latitude=lat&Longitude=long&Address=address&City=city&CrossStreet=cross_street&SnappedLatitude=snapped_latitude&SnappedLongitude=snapped_longitude&SnappedDistance=snapped_distance"></td>
									<td>Lat.&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="lat" name="lat" fillbutton="selectbutton" size="9">&nbsp;&nbsp;<INPUT type="image" align="absmiddle" enablefill="true" value="Select from map" alt="Select from map"
											src="map.gif" id="selectbutton" name="selectbutton" align="middle" simulateclickcontrolid="selectedlocation"></td>
								</tr>
								<tr>
									<td></td>
									<td>Long.&nbsp;<input type="text" id="long" name="long" fillbutton="selectbutton" size="9"></td>
								</tr>
								<tr><td>&nbsp;</td></tr>
								<tr>
									<td align="right" valign="top">Address<input type="radio" class="bigradio" name="locationtype" id="addresslocation" value="addresslocation"></td>
									<td>
<table id="innertable">
    <tr><td>Address:</td><td><input type="text" name="address" id="address"></td></tr>
    <tr><td>City:</td><td><input type="text" name="city" id="city"></td></tr>
    <tr><td>Cross Street:</td><td><input type="text" name="cross_street" id="cross_street"></td></tr>
</table>
									</td>
									
								</tr>
								</tr>
							</table>
							<br>
							<input type="hidden" name="snapped_latitude" id="snapped_latitude">
							<input type="hidden" name="snapped_longitude" id="snapped_longitude">
							<input type="hidden" name="snapped_distance" id="snapped_distance">
							<input type="submit" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script language="javascript">

	function window.onload()
	{
		PositionUpdateQuery.address.focus();
		PositionUpdateQuery.addresslocation.checked = true;
	}
	
	function window.validatepage()
	{
		if (PositionUpdateQuery.addresslocation.checked)
			clearlatlong();
	}

	function clearlatlong()
	{
		PositionUpdateQuery.lat.value = ''; 
		PositionUpdateQuery.long.value = '';
	}

	function clearaddress()
	{
		PositionUpdateQuery.address.value = ''; 
		PositionUpdateQuery.city.value = ''; 
		PositionUpdateQuery.cross_street.selectedIndex = '';
	}
	</script>
</HTML>
