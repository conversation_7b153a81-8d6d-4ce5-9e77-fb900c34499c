<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>
<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Allied Agencies</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="AlliedAgencies.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Allied Agencies</H4>
	<xsl:apply-templates select="results/errormessage"/>
						<p>
							<table cellpadding="3" border="0">
								<tr style="text-decoration:underline;font-weight:bold">
									<td>Agency</td>
									<td>Problem Nature</td>
									<td>Units</td>
								</tr>
	<xsl:apply-templates select="results/exemel"/>
							</table>


						</p>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>


<xsl:template match="/results/exemel">
	<xsl:variable name="unique-agencies" 
		select="NewDataSet
			/Table[not(AgencyName=preceding-sibling::Table[1]/AgencyName)]
			/AgencyName"
	/>
	<xsl:for-each select="$unique-agencies">
		<TR>
			<TD><xsl:value-of select="."/></TD>
			<TD><xsl:value-of select="//Table[AgencyName=current()]/ProblemNature"/></TD>
			<xsl:for-each select="//Table[AgencyName=current()]">
			
				<xsl:variable name="BackColor"><xsl:value-of select="BackColor"/></xsl:variable>
				<xsl:variable name="ForeColor"><xsl:value-of select="ForeColor"/></xsl:variable>
				<TD style="background-color:#{$BackColor};">
					<xsl:element name="a">
				                <xsl:attribute name="style">color:#<xsl:value-of select="$ForeColor"/></xsl:attribute>
				                <xsl:attribute name="href">#</xsl:attribute>
	                			<xsl:attribute name="id">MapIt</xsl:attribute>
				                <xsl:attribute name="executefunction">MapIt</xsl:attribute>
	        			        <xsl:attribute name="parameters">Latitude=<xsl:value-of select="CurrentLat"/>&amp;Longitude=<xsl:value-of select="CurrentLon"/></xsl:attribute>
						<xsl:value-of select="UnitName"/>
	        		        </xsl:element>
				</TD>
			</xsl:for-each>
		</TR>	
	</xsl:for-each>
</xsl:template> 


<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
