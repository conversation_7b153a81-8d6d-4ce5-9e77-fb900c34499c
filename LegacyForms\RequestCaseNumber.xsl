<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - HTMLQuery</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Case Number Requested</H4>
						<P>A case number was requested.
							<br></br>
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
</HTML>

</xsl:template>


</xsl:transform>