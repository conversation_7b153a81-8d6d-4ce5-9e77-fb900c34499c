<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Property Check</TITLE><LINK href="normalstyle.css" type="text/css" rel="stylesheet">
			<script language="javascript" id="clientEventHandlersJS">
			</script>
	</HEAD>
	<body>
		<TABLE class="base" id="Table1" cellPadding="10" align="center" border="0">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Property Check</H4>
						<form id="Form" name="Form" action="PropertyCheck.aspx?queryfile=PropertyCheck.qry" method="post">
							<P>&nbsp;</P>
                            <P>
								<TABLE>
                                    <!--<tr>
                                        <td style="text-align:center">
                                            <input id="doSupplement" type="checkbox" name="doSupplement"/>
                                            <label id="labelSupplement">Supplement</label>
                                         </td>
                                     </tr>
                                    <tr>
                                        <td style="text-align:center">
                                            <input id="doRMS" type="checkbox" name="doRMS" />
                                            <label id="labelRMS">RMS</label>
                                        </td>
                                    </tr>
                                    -->
									<TR>
										<TD>
											<TABLE>
												<TBODY>
													<TR>
														<TD width="28%"><STRONG><INPUT id="UseSerialNo" type="radio" CHECKED value="UseSerialNo" name="RadioGroup" onclick="Form.OAN.disabled=true;Form.SerialNo.disabled=false;Form.OAN.value=''"></STRONG>&nbsp;Serial 
															#:</TD>
														<TD><input id="SerialNo" type="text" maxLength="20" size="20" name="SerialNo"></TD>
										</TD>
									</TR>

									<TR>
										<TD><INPUT id="UseOAN" type="radio" value="UseOAN" name="RadioGroup" onclick="Form.SerialNo.disabled=true;Form.OAN.disabled=false;Form.SerialNo.value=''">&nbsp;OAN:</TD>
										<TD><input id="OAN" type="text" maxLength="20" size="20" name="OAN"></TD>
									</TR>
								</TABLE>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE>
							<TR>
								<TD colspan=2>Category Code:
<!--
									<SELECT id="CatCode" name="CatCode">
										<OPTION value="" selected></OPTION>
										<OPTION value="B">B - Bicycle</OPTION>
										<OPTION value="C">C - Camera</OPTION>
										<OPTION value="D">D - Data Processing Equip</OPTION>
										<OPTION value="E">E - Equipment/Tools</OPTION>
										<OPTION value="F">F - Furniture</OPTION>
										<OPTION value="G">G - Games and Gambling App</OPTION>
										<OPTION value="H">H - Household Appliances</OPTION>
										<OPTION value="I">I - Identification Cards, etc.</OPTION>
										<OPTION value="K">K - Keepsakes &amp; Collectibles</OPTION>
										<OPTION value="L">L - Livestock</OPTION>
										<OPTION value="M">M - Musical Instruments</OPTION>
										<OPTION value="O">O - Office Equipment</OPTION>
										<OPTION value="P">P - Personal Accessories</OPTION>
										<OPTION value="R">R - Radio and Stereos</OPTION>
										<OPTION value="S">S - Sports Equip and Toys</OPTION>
										<OPTION value="T">T - Television</OPTION>
										<OPTION value="V">V - Optical Viewing Equip</OPTION>
										<OPTION value="W">W - Well Drilling Equip</OPTION>
										<OPTION value="Y">Y - Other</OPTION>
										<OPTION value="Z">Z - Stolen Credit Cards (CJIS Only</OPTION>
									</SELECT>
-->
										<XML id="categorystyle" src="genericselect.xsl"></XML>										
										<SPAN type="selectlist" id="categoryvals" name="categoryvals">
											<XML id="categorysource" src="RecordsCheckCategory.xml"></XML>
										</SPAN>

								</TD>
							</TR><TR>
								<TD>Article: <input id="Article" type="text" maxLength="6" size="6" name="Article"></TD>
								<TD>Brand: <input id="Brand" type="text" maxLength="6" size="6" name="Brand"></TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE id="Table2">
							<TR>
								<TD width="20%"><b>FCN Search</b> Only: <INPUT id="FCNSearch" type="text" maxLength="13" size="13" name="FCNSearch"></TD>
							</TR>
							<TR>
								<TD>
									<TABLE>
										<TR>
											<TD><input type="checkbox" name="associateWithIncident" id="RCAssociation" value="associateRCWithIncident" checked>Associate this Records Check with incident?</TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
			</TBODY></TABLE>
		</P>
        <!--when click on this button, it call the validation.-->
        <input type="button" name="Query" id="Button" value="Submit" onkeypress="validatepage()" onclick="validatepage()" />
        <!--when validation is ok, it will call this button to submit the page.  This button is not visible because of it width is zero.-->
        <input style="width: 0px;" tabindex="-1" type="submit" name="Submit" id="Submit" value="Submit" /></form></TD></TR>
        </TBODY></TABLE>
		<script src="clientutilities.js"></script>
		<SCRIPT language="javascript">
		function window.onload()
		{

			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.SerialNo.focus();
			PrepareValidation(Form);

			categoryvals.innerHTML = GenerateSelectBox("CatCode", categorysource, categorystyle, false, false, false, 1, false, false);
			Form.CatCode.value = 'Y';

			Form.innerHTML = Form.innerHTML + GeneratePropertySupplementHiddenFields();
			Form.innerHTML = Form.innerHTML + GeneratePropertyRMSHiddenFields();

			Form.UseSerialNo.click();
			
		}

		function window.validatepage()
		{
		    if (!HandlePropertySupplementFieldsBeforeSubmit()) return;
		    if (!HandlePropertyRMSFieldsBeforeSubmit()) return;
            Form.Submit.click();
		}

		</SCRIPT>
	</body>
</HTML>
