<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Vehicle Search Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="VehicleSearch.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Vehicle Search Results</H4>
						<P>
							<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
								<tr style="font-weight:bold;color:white;">
									<td>Unit (Vehicle)</td>
									<td>Status</td>
									<td>Location</td>
									<td>Incident</td>
									<td>Personnel (ID)</td>
									<td>Capabilities</td>
									<td>Jurisdiction, Division</td>
								</tr>

	<xsl:apply-templates select="results/exemel"/>	
							
							</table>
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel">
	<xsl:variable name="unique-vehicles" 
		select="NewDataSet
			/Table[not(ID=preceding-sibling::Table[1]/ID)]
			/ID"
	/>
	<xsl:for-each select="$unique-vehicles">
		<TR style="color:windowtext;background-color:window;">
			<xsl:variable name="v_rec"><xsl:value-of select="//Table[ID=current()]/Unit"/></xsl:variable>
			<xsl:variable name="BackColor"><xsl:value-of select="//Table[ID=current()]/BackColor"/></xsl:variable>
			<xsl:variable name="ForeColor"><xsl:value-of select="//Table[ID=current()]/ForeColor"/></xsl:variable>

			<td><a href="UnitStatus.aspx?radioname={$v_rec}&#38;queryfile=UnitStatus.qry" location="remote"><xsl:value-of select="$v_rec"/></a> (<xsl:value-of select="//Table[ID=current()]/Vehicle"/>)</td>
			<td style="color:#{$ForeColor};background-color:#{$BackColor};"><xsl:value-of select="//Table[ID=current()]/Status"/></td>
			<td>
				<xsl:element name="a">
			                <xsl:attribute name="href">#</xsl:attribute>
	                		<xsl:attribute name="id">MapIt</xsl:attribute>
		        	        <xsl:attribute name="executefunction">MapIt</xsl:attribute>
	        		        <xsl:attribute name="parameters">Latitude=<xsl:value-of select="//Table[ID=current()]/Latitude"/>&amp;Longitude=<xsl:value-of select="//Table[ID=current()]/Longitude"/></xsl:attribute>
					<xsl:value-of select="//Table[ID=current()]/Location"/>
		                </xsl:element>
			</td>
			<td>
				<table>
					<tr><td></td></tr>
					<xsl:for-each select="//Table[ID=current()]">
						<TR><td>
						<xsl:variable name="incidentid"><xsl:value-of select="IncidentID"/></xsl:variable>
						<a href="SingleIncidentQuery.aspx?ID={$incidentid}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="IncidentNumber"/></a>
						</td></TR>
					</xsl:for-each>
				</table>
			</td>
			<td><xsl:value-of select="//Table[ID=current()]/Personnel"/></td>
			<td><xsl:value-of select="//Table[ID=current()]/Capabilities"/></td>
			<td>
				<table>
					<tr><td></td></tr>
					<xsl:if test="//Table[ID=current()]/Division!=''">
						<tr><td><b>Division: </b><xsl:value-of select="//Table[ID=current()]/Division"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/HomeDivision!='' and //Table[ID=current()]/HomeDivision!=//Table[ID=current()]/Division">
						<tr><td><b>Home Division: </b><xsl:value-of select="//Table[ID=current()]/HomeDivision"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/Jurisdiction!=''">
						<tr><td><b>Jurisdiction: </b><xsl:value-of select="//Table[ID=current()]/Jurisdiction"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/Sector!=''">
						<tr><td><b>Sector: </b><xsl:value-of select="//Table[ID=current()]/Sector"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/HomeSector!='' and //Table[ID=current()]/HomeSector!=//Table[ID=current()]/Sector">
						<tr><td><b>Home Sector: </b><xsl:value-of select="//Table[ID=current()]/HomeSector"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/CurrentStation!=''">
						<tr><td><b>Station: </b><xsl:value-of select="//Table[ID=current()]/CurrentStation"/></td></tr>
					</xsl:if>
					<xsl:if test="//Table[ID=current()]/HomeStation!='' and //Table[ID=current()]/HomeStation!=//Table[ID=current()]/CurrentStation">
						<tr><td><b>Home Station: </b><xsl:value-of select="//Table[ID=current()]/HomeStation"/></td></tr>
					</xsl:if>
				</table>
			</td>
		</TR>	
	</xsl:for-each>
</xsl:template> 


</xsl:transform>