<?xml version="1.0" encoding="utf-8"?>
<MapConfig xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.voyagersystemsinc.com/VoyagerMobile/SchemaMapConfig.xsd">
  <GeneralConstants>
    <RecenterTolerance>0.05</RecenterTolerance>
    <RotationTolerance>10</RotationTolerance>
    <MinAutoZoomMiles>0.25</MinAutoZoomMiles>
  </GeneralConstants>
  <DefaultExtent>
    <Dimensions>
      <HeightMiles>70.6784058</HeightMiles>
      <WidthMiles>146.3808</WidthMiles>
    </Dimensions>
    <Center>
      <Latitude>44.535306152652979</Latitude>
      <Longitude>-93.2014708227242</Longitude>
    </Center>
  </DefaultExtent>
  <OverviewMapSettings>
    <MinZoomMiles>5</MinZoomMiles>
    <BackgroundColor name="RGB" red="255" green="255" blue="255" />
    <BorderColor name="RGB" red="255" green="128" />
  </OverviewMapSettings>
  <MapItemsInfoBoxSettings>
    <Opacity>0.95</Opacity>
  </MapItemsInfoBoxSettings>
  <ClickedLocationOutputFields>
    <OutputFieldName>Address</OutputFieldName>
    <OutputFieldName>Street</OutputFieldName>
    <OutputFieldName>City</OutputFieldName>
    <OutputFieldName>County</OutputFieldName>
    <OutputFieldName>ZIP</OutputFieldName>
    <OutputFieldName>Schools</OutputFieldName>
    <OutputFieldName>Parks</OutputFieldName>
    <OutputFieldName>Water</OutputFieldName>
  </ClickedLocationOutputFields>
  <AddressOutputFields>
    <AddrOutputFieldName AddrFieldType="StAddress">Addr</AddrOutputFieldName>
    <AddrOutputFieldName AddrFieldType="City">City</AddrOutputFieldName>
    <AddrOutputFieldName AddrFieldType="CrossStreet">CrossSt</AddrOutputFieldName>
  </AddressOutputFields>
  <LayerSourceDbs>
    <LayerSourceDb>
      <DatabaseType>ESRI_Shape</DatabaseType>
      <DatabaseRef type="appRelativePath">MAPDATA\LAYERS</DatabaseRef>
      <GeoDataSets>
        <GeoDataSet name="PARKS">
          <TableName>PARKS</TableName>
        </GeoDataSet>
        <GeoDataSet name="WATER_LINE_FEATURES">
          <TableName>WATER_LINE_FEATURES</TableName>
        </GeoDataSet>
        <GeoDataSet name="RAILROADS">
          <TableName>RAILROADS</TableName>
        </GeoDataSet>
        <GeoDataSet name="FULL_EXTENT">
          <TableName>FULL_EXTENT</TableName>
        </GeoDataSet>
        <GeoDataSet name="WATER_POLYGON_FEATURES">
          <TableName>WATER_POLYGON_FEATURES</TableName>
        </GeoDataSet>
      </GeoDataSets>
    </LayerSourceDb>
    <LayerSourceDb>
      <DatabaseType>ESRI_Shape</DatabaseType>
      <DatabaseRef type="appRelativePath">MAPDATA\STREETS</DatabaseRef>
      <GeoDataSets>
        <GeoDataSet name="STREETS_D4">
          <TableName>STREETS_D4</TableName>
        </GeoDataSet>
        <GeoDataSet name="STREETS_D3">
          <TableName>STREETS_D3</TableName>
        </GeoDataSet>
        <GeoDataSet name="STREETS_D1">
          <TableName>STREETS_D1</TableName>
        </GeoDataSet>
        <GeoDataSet name="STREETS_D2">
          <TableName>STREETS_D2</TableName>
        </GeoDataSet>
      </GeoDataSets>
    </LayerSourceDb>
  </LayerSourceDbs>
  <Layers>
    <PolygonLayer sourceGeoDataSet="FULL_EXTENT" forOverviewMap="true">
      <LayerName>EXTENT</LayerName>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <DensificationTolerance>200</DensificationTolerance>
      <DefaultPolygonSymbol>
        <FillStyle>TransparentFill</FillStyle>
        <FillColor name="RGB" red="255" green="255" blue="255" />
        <DrawOutline>false</DrawOutline>
        <OutlineSize>1</OutlineSize>
        <OutlineColor name="RGB" red="192" green="192" blue="192" />
      </DefaultPolygonSymbol>
    </PolygonLayer>
    <LineLayer sourceGeoDataSet="RAILROADS">
      <LayerName>RAILROAD</LayerName>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <DensificationTolerance>200</DensificationTolerance>
      <DefaultLineSymbol>
        <LineStyle>SolidLine</LineStyle>
        <Size>1</Size>
        <Color name="Black" />
      </DefaultLineSymbol>
    </LineLayer>
    <PolygonLayer sourceGeoDataSet="PARKS">
      <LayerName>PARKS</LayerName>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <DensificationTolerance>200</DensificationTolerance>
      <DefaultPolygonSymbol>
        <FillStyle>SolidFill</FillStyle>
        <FillColor name="RGB" green="255" />
        <DrawOutline>true</DrawOutline>
        <OutlineSize>1</OutlineSize>
        <OutlineColor name="RGB" />
      </DefaultPolygonSymbol>
    </PolygonLayer>
    <LineLayer sourceGeoDataSet="WATER_LINE_FEATURES">
      <LayerName>WATER</LayerName>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <DensificationTolerance>200</DensificationTolerance>
      <DefaultLineSymbol>
        <LineStyle>SolidLine</LineStyle>
        <Size>1</Size>
        <Color name="Black" />
      </DefaultLineSymbol>
    </LineLayer>
    <PolygonLayer sourceGeoDataSet="WATER_POLYGON_FEATURES">
      <LayerName>WATER</LayerName>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>false</InvertForNightMode>
      <DensificationTolerance>200</DensificationTolerance>
      <DefaultPolygonSymbol>
        <FillStyle>SolidFill</FillStyle>
        <FillColor name="Cyan" />
        <DrawOutline>true</DrawOutline>
        <OutlineSize>1</OutlineSize>
        <OutlineColor name="Blue" />
      </DefaultPolygonSymbol>
    </PolygonLayer>
    <LineLayer sourceGeoDataSet="STREETS_D1">
      <LayerName>STREETS</LayerName>
      <DisplayZoomRange>
        <MaxZoomMiles>2</MaxZoomMiles>
      </DisplayZoomRange>
      <OverviewDisplayZoomRange>
        <MaxZoomMiles>2</MaxZoomMiles>
      </OverviewDisplayZoomRange>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <SetMapCoordsysSame>true</SetMapCoordsysSame>
      <DensificationTolerance>200</DensificationTolerance>
      <LabelPlacer>
        <LabelFieldName>LABEL</LabelFieldName>
        <DefaultTextSymbol>
          <Font>
            <FontName>Microsoft Sans Serif</FontName>
            <Size>10</Size>
            <Bold>false</Bold>
            <Italic>false</Italic>
            <Strikethrough>false</Strikethrough>
            <Underline>false</Underline>
            <Weight>FW_DONTCARE</Weight>
          </Font>
          <TextForeColor name="RGB" />
          <Fitted>true</Fitted>
          <Height>0</Height>
          <HorizontalAlignment>AlignCenter</HorizontalAlignment>
          <Rotation>0</Rotation>
          <VerticalAlignment>AlignCenter</VerticalAlignment>
        </DefaultTextSymbol>
        <AllowDuplicates>false</AllowDuplicates>
        <MaskLabels>true</MaskLabels>
        <MaskColor name="RGB" red="252" green="252" blue="248" />
        <PlaceAbove>true</PlaceAbove>
        <PlaceBelow>false</PlaceBelow>
        <PlaceOn>false</PlaceOn>
        <SymbolWidth>7</SymbolWidth>
        <SymbolHeight>7</SymbolHeight>
        <TextSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <ValueTextSymbols>
            <TextSymbolForValue>
              <Value>MH</Value>
              <TextSymbol>
                <Font>
                  <FontName>Microsoft Sans Serif</FontName>
                  <Size>9</Size>
                  <Bold>true</Bold>
                  <Italic>false</Italic>
                  <Strikethrough>false</Strikethrough>
                  <Underline>false</Underline>
                  <Weight>FW_DONTCARE</Weight>
                </Font>
                <TextForeColor name="RGB" />
                <Fitted>false</Fitted>
                <Height>0</Height>
                <HorizontalAlignment>AlignCenter</HorizontalAlignment>
                <Rotation>0</Rotation>
                <VerticalAlignment>AlignCenter</VerticalAlignment>
              </TextSymbol>
            </TextSymbolForValue>
            <TextSymbolForValue>
              <Value>HW</Value>
              <TextSymbol>
                <Font>
                  <FontName>Microsoft Sans Serif</FontName>
                  <Size>9</Size>
                  <Bold>true</Bold>
                  <Italic>false</Italic>
                  <Strikethrough>false</Strikethrough>
                  <Underline>false</Underline>
                  <Weight>FW_DONTCARE</Weight>
                </Font>
                <TextForeColor name="RGB" />
                <Fitted>false</Fitted>
                <Height>0</Height>
                <HorizontalAlignment>AlignCenter</HorizontalAlignment>
                <Rotation>0</Rotation>
                <VerticalAlignment>AlignCenter</VerticalAlignment>
              </TextSymbol>
            </TextSymbolForValue>
            <TextSymbolForValue>
              <Value>CR</Value>
              <TextSymbol>
                <Font>
                  <FontName>Microsoft Sans Serif</FontName>
                  <Size>9</Size>
                  <Bold>true</Bold>
                  <Italic>false</Italic>
                  <Strikethrough>false</Strikethrough>
                  <Underline>false</Underline>
                  <Weight>FW_DONTCARE</Weight>
                </Font>
                <TextForeColor name="RGB" />
                <Fitted>false</Fitted>
                <Height>0</Height>
                <HorizontalAlignment>AlignCenter</HorizontalAlignment>
                <Rotation>0</Rotation>
                <VerticalAlignment>AlignCenter</VerticalAlignment>
              </TextSymbol>
            </TextSymbolForValue>
          </ValueTextSymbols>
        </TextSymbolByFieldValue>
      </LabelPlacer>
      <ClickInfoDisplayFields ignoreWhenNotVisible="true">
        <ClickInfoDisplayField clickedLocationDisplayLabel="Street">
          <DisplayLabel>Name</DisplayLabel>
          <WhenContentEmpty action="SuppressLabel" />
          <DisplayContent>
            <Field tblField="LABEL" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>From Left</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="FROMLEFT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>To Left</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="TOLEFT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>From Right</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="FROMRIGHT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>To Right </DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="TORIGHT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>Routing Speed</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="SPEEDLIMIT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField clickedLocationDisplayLabel="Addr">
          <DisplayLabel>Block</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="FromRight" />
            <Separator text="-" />
            <Field tblField="ToRight" />
          </DisplayContent>
        </ClickInfoDisplayField>
      </ClickInfoDisplayFields>
      <AddrMatchSpec>
        <MatchRules>us_addr1.mat</MatchRules>
        <IntersectionMatchRules>us_intsc1.mat</IntersectionMatchRules>
        <StandardizingRules>us_addr.stn</StandardizingRules>
        <IntersectionStandardizingRules>us_intsc.stn</IntersectionStandardizingRules>
        <GeocodingField>
          <FieldName>FROMLEFT</FieldName>
          <MatchVariable indexType="None">FromLeft</MatchVariable>
          <RevGcOutputField revGcFieldCat="LeftFrom" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>FROMRIGHT</FieldName>
          <MatchVariable indexType="None">FromRight</MatchVariable>
          <RevGcOutputField revGcFieldCat="RightFrom" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>TOLEFT</FieldName>
          <MatchVariable indexType="None">ToLeft</MatchVariable>
          <RevGcOutputField revGcFieldCat="LeftTo" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>TORIGHT</FieldName>
          <MatchVariable indexType="None">ToRight</MatchVariable>
          <RevGcOutputField revGcFieldCat="RightTo" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>PRE_DIR</FieldName>
          <MatchVariable indexType="None">PreDir</MatchVariable>
          <RevGcOutputField revGcFieldCat="Street" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>PRE_TYPE</FieldName>
          <MatchVariable indexType="None">PreType</MatchVariable>
          <RevGcOutputField revGcFieldCat="Street" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>ST_NAME</FieldName>
          <MatchVariable indexType="Soundex">StreetName</MatchVariable>
          <RevGcOutputField revGcFieldCat="Street" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>SUF_TYPE</FieldName>
          <MatchVariable indexType="None">StreetType</MatchVariable>
          <RevGcOutputField revGcFieldCat="Street" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>SUF_DIR</FieldName>
          <MatchVariable indexType="None">SufDir</MatchVariable>
          <RevGcOutputField revGcFieldCat="Street" />
        </GeocodingField>
        <GeocodingField>
          <FieldName>L_CITY</FieldName>
          <MatchVariable indexType="Normal">LeftZone</MatchVariable>
          <RevGcOutputField revGcFieldCat="LeftOtherField">City</RevGcOutputField>
        </GeocodingField>
        <GeocodingField>
          <FieldName>R_CITY</FieldName>
          <MatchVariable indexType="Secondary" withPrimaryFieldName="L_CITY">RightZone</MatchVariable>
          <RevGcOutputField revGcFieldCat="RightOtherField">City</RevGcOutputField>
        </GeocodingField>
        <MatchVariableIntersectionLink>
          <MatchVariable>PreDir</MatchVariable>
          <LinkGroup>Primary</LinkGroup>
          <IntersectionMatchVariable>PreDir1</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>PreDir</MatchVariable>
          <LinkGroup>Secondary</LinkGroup>
          <IntersectionMatchVariable>PreDir2</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>PreType</MatchVariable>
          <LinkGroup>Primary</LinkGroup>
          <IntersectionMatchVariable>PreType1</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>PreType</MatchVariable>
          <LinkGroup>Secondary</LinkGroup>
          <IntersectionMatchVariable>PreType2</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>StreetName</MatchVariable>
          <LinkGroup>Primary</LinkGroup>
          <IntersectionMatchVariable>StreetName1</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>StreetName</MatchVariable>
          <LinkGroup>Secondary</LinkGroup>
          <IntersectionMatchVariable>StreetName2</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>StreetType</MatchVariable>
          <LinkGroup>Primary</LinkGroup>
          <IntersectionMatchVariable>StreetType1</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>StreetType</MatchVariable>
          <LinkGroup>Secondary</LinkGroup>
          <IntersectionMatchVariable>StreetType2</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>SufDir</MatchVariable>
          <LinkGroup>Primary</LinkGroup>
          <IntersectionMatchVariable>SufDir1</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>SufDir</MatchVariable>
          <LinkGroup>Secondary</LinkGroup>
          <IntersectionMatchVariable>SufDir2</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>LeftZone</MatchVariable>
          <LinkGroup>Primary</LinkGroup>
          <IntersectionMatchVariable>LeftZone1</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>LeftZone</MatchVariable>
          <LinkGroup>Secondary</LinkGroup>
          <IntersectionMatchVariable>LeftZone2</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>RightZone</MatchVariable>
          <LinkGroup>Primary</LinkGroup>
          <IntersectionMatchVariable>RightZone1</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <MatchVariableIntersectionLink>
          <MatchVariable>RightZone</MatchVariable>
          <LinkGroup>Secondary</LinkGroup>
          <IntersectionMatchVariable>RightZone2</IntersectionMatchVariable>
        </MatchVariableIntersectionLink>
        <SearchQueries>SN? &amp; ZN</SearchQueries>
        <SearchQueries>SN?</SearchQueries>
        <ZoneField>City</ZoneField>
      </AddrMatchSpec>
      <LineRenderers>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <DefaultLineSymbol>
            <LineStyle>SolidLine</LineStyle>
            <Size>7</Size>
            <Color name="RGB" red="131" green="88" blue="61" />
          </DefaultLineSymbol>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>MH</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>7</Size>
                <Color name="RGB" red="128" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>HW</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>7</Size>
                <Color name="RGB" red="128" green="64" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>CR</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>7</Size>
                <Color name="RGB" red="131" green="88" blue="61" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>RP</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="131" green="88" blue="61" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <DefaultLineSymbol>
            <LineStyle>SolidLine</LineStyle>
            <Size>5</Size>
            <Color name="RGB" red="255" green="255" blue="255" />
          </DefaultLineSymbol>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>MH</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="226" green="54" blue="54" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>HW</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="244" green="122" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>CR</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="219" green="198" blue="157" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>RP</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>3</Size>
                <Color name="RGB" red="219" green="198" blue="157" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
      </LineRenderers>
    </LineLayer>
    <LineLayer sourceGeoDataSet="STREETS_D1">
      <LayerName>STREETS 2-6 MI</LayerName>
      <DisplayZoomRange>
        <MinZoomMiles>2</MinZoomMiles>
        <MaxZoomMiles>6</MaxZoomMiles>
      </DisplayZoomRange>
      <OverviewDisplayZoomRange>
        <MaxZoomMiles>2</MaxZoomMiles>
      </OverviewDisplayZoomRange>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <SetMapCoordsysSame>true</SetMapCoordsysSame>
      <DensificationTolerance>200</DensificationTolerance>
      <ClickInfoDisplayFields ignoreWhenNotVisible="true">
        <ClickInfoDisplayField clickedLocationDisplayLabel="Street">
          <DisplayLabel>Name</DisplayLabel>
          <WhenContentEmpty action="SuppressLabel" />
          <DisplayContent>
            <Field tblField="LABEL" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>From Left</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="FROMLEFT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>To Left</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="TOLEFT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>From Right</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="FROMRIGHT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>To Right </DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="TORIGHT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField>
          <DisplayLabel>Routing Speed</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="SPEEDLIMIT" />
          </DisplayContent>
        </ClickInfoDisplayField>
        <ClickInfoDisplayField clickedLocationDisplayLabel="Addr">
          <DisplayLabel>Block</DisplayLabel>
          <WhenContentEmpty action="DisplayDefault">N/A</WhenContentEmpty>
          <DisplayContent>
            <Field tblField="FromRight" />
            <Separator text="-" />
            <Field tblField="ToLeft" />
          </DisplayContent>
        </ClickInfoDisplayField>
      </ClickInfoDisplayFields>
      <LineRenderers>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>CS</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>1</Size>
                <Color name="RGB" red="159" green="135" blue="108" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>RD</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>1</Size>
                <Color name="RGB" red="159" green="135" blue="108" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
      </LineRenderers>
    </LineLayer>
    <LineLayer sourceGeoDataSet="STREETS_D2">
      <LayerName>MAJOR ROADS</LayerName>
      <DisplayZoomRange>
        <MinZoomMiles>2</MinZoomMiles>
        <MaxZoomMiles>10</MaxZoomMiles>
      </DisplayZoomRange>
      <OverviewDisplayZoomRange>
        <MaxZoomMiles>7</MaxZoomMiles>
      </OverviewDisplayZoomRange>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <DensificationTolerance>200</DensificationTolerance>
      <LabelPlacer>
        <LabelFieldName>LABEL</LabelFieldName>
        <DefaultTextSymbol>
          <Font>
            <FontName>Microsoft Sans Serif</FontName>
            <Size>9</Size>
            <Bold>true</Bold>
            <Italic>false</Italic>
            <Strikethrough>false</Strikethrough>
            <Underline>false</Underline>
            <Weight>FW_DONTCARE</Weight>
          </Font>
          <TextForeColor name="RGB" />
          <Fitted>true</Fitted>
          <Height>0</Height>
          <HorizontalAlignment>AlignCenter</HorizontalAlignment>
          <Rotation>0</Rotation>
          <VerticalAlignment>AlignCenter</VerticalAlignment>
        </DefaultTextSymbol>
        <AllowDuplicates>false</AllowDuplicates>
        <MaskLabels>true</MaskLabels>
        <MaskColor name="RGB" red="252" green="252" blue="248" />
        <PlaceAbove>true</PlaceAbove>
        <PlaceBelow>false</PlaceBelow>
        <PlaceOn>false</PlaceOn>
        <SymbolWidth>7</SymbolWidth>
        <SymbolHeight>7</SymbolHeight>
      </LabelPlacer>
      <ClickInfoDisplayFields ignoreWhenNotVisible="true">
        <ClickInfoDisplayField clickedLocationDisplayLabel="Street" clickedLocationFallBack="1" displayLayerItem="false">
          <DisplayLabel>NAME</DisplayLabel>
          <WhenContentEmpty action="SuppressLabel" />
          <DisplayContent>
            <Field tblField="Label" />
          </DisplayContent>
        </ClickInfoDisplayField>
      </ClickInfoDisplayFields>
      <LineRenderers>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <DefaultLineSymbol>
            <LineStyle>SolidLine</LineStyle>
            <Size>6</Size>
            <Color name="RGB" red="131" green="88" blue="61" />
          </DefaultLineSymbol>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>MH</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>7</Size>
                <Color name="RGB" red="128" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>HW</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>7</Size>
                <Color name="RGB" red="128" green="64" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>RP</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>4</Size>
                <Color name="RGB" red="131" green="88" blue="61" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <DefaultLineSymbol>
            <LineStyle>SolidLine</LineStyle>
            <Size>4</Size>
            <Color name="RGB" red="219" green="198" blue="157" />
          </DefaultLineSymbol>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>MH</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="226" green="54" blue="54" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>HW</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="244" green="122" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>RP</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>2</Size>
                <Color name="RGB" red="219" green="198" blue="157" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
      </LineRenderers>
    </LineLayer>
    <LineLayer sourceGeoDataSet="STREETS_D3" forOverviewMap="true">
      <LayerName>HIGHWAYS</LayerName>
      <DisplayZoomRange>
        <MinZoomMiles>10</MinZoomMiles>
        <MaxZoomMiles>9999</MaxZoomMiles>
      </DisplayZoomRange>
      <OverviewDisplayZoomRange>
        <MaxZoomMiles>9999</MaxZoomMiles>
      </OverviewDisplayZoomRange>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <DensificationTolerance>200</DensificationTolerance>
      <LabelPlacer>
        <LabelFieldName>LABEL</LabelFieldName>
        <DefaultTextSymbol>
          <Font>
            <FontName>Microsoft Sans Serif</FontName>
            <Size>9</Size>
            <Bold>true</Bold>
            <Italic>false</Italic>
            <Strikethrough>false</Strikethrough>
            <Underline>false</Underline>
            <Weight>FW_DONTCARE</Weight>
          </Font>
          <TextForeColor name="RGB" />
          <Fitted>true</Fitted>
          <Height>0</Height>
          <HorizontalAlignment>AlignCenter</HorizontalAlignment>
          <Rotation>0</Rotation>
          <VerticalAlignment>AlignCenter</VerticalAlignment>
        </DefaultTextSymbol>
        <AllowDuplicates>false</AllowDuplicates>
        <MaskLabels>true</MaskLabels>
        <MaskColor name="RGB" red="252" green="252" blue="248" />
        <PlaceAbove>true</PlaceAbove>
        <PlaceBelow>false</PlaceBelow>
        <PlaceOn>false</PlaceOn>
        <SymbolWidth>5</SymbolWidth>
        <SymbolHeight>5</SymbolHeight>
      </LabelPlacer>
      <ClickInfoDisplayFields ignoreWhenNotVisible="true">
        <ClickInfoDisplayField clickedLocationDisplayLabel="Street" clickedLocationFallBack="2" displayLayerItem="false">
          <DisplayLabel>NAME</DisplayLabel>
          <WhenContentEmpty action="SuppressLabel" />
          <DisplayContent>
            <Field tblField="Label" />
          </DisplayContent>
        </ClickInfoDisplayField>
      </ClickInfoDisplayFields>
      <LineRenderers>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <DefaultLineSymbol>
            <LineStyle>SolidLine</LineStyle>
            <Size>1</Size>
            <Color name="RGB" />
          </DefaultLineSymbol>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>MH</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="128" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>HW</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="128" green="64" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <DefaultLineSymbol>
            <LineStyle>SolidLine</LineStyle>
            <Size>1</Size>
            <Color name="RGB" />
          </DefaultLineSymbol>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>MH</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>3</Size>
                <Color name="RGB" red="226" green="54" blue="54" />
              </LineSymbol>
            </LineSymbolForValue>
            <LineSymbolForValue>
              <Value>HW</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>3</Size>
                <Color name="RGB" red="244" green="122" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
      </LineRenderers>
    </LineLayer>
    <LineLayer sourceGeoDataSet="STREETS_D4" forOverviewMap="true">
      <LayerName>MAJOR HIGHWAYS</LayerName>
      <DisplayZoomRange>
        <MinZoomMiles>40</MinZoomMiles>
        <MaxZoomMiles>9999</MaxZoomMiles>
      </DisplayZoomRange>
      <OverviewDisplayZoomRange>
        <MinZoomMiles>40</MinZoomMiles>
        <MaxZoomMiles>99999</MaxZoomMiles>
      </OverviewDisplayZoomRange>
      <InitiallySuppressDisplay>false</InitiallySuppressDisplay>
      <InvertForNightMode>true</InvertForNightMode>
      <DensificationTolerance>200</DensificationTolerance>
      <LabelPlacer>
        <LabelFieldName>LABEL</LabelFieldName>
        <DefaultTextSymbol>
          <Font>
            <FontName>Microsoft Sans Serif</FontName>
            <Size>9</Size>
            <Bold>true</Bold>
            <Italic>false</Italic>
            <Strikethrough>false</Strikethrough>
            <Underline>false</Underline>
            <Weight>FW_DONTCARE</Weight>
          </Font>
          <TextForeColor name="RGB" />
          <Fitted>true</Fitted>
          <Height>0</Height>
          <HorizontalAlignment>AlignCenter</HorizontalAlignment>
          <Rotation>0</Rotation>
          <VerticalAlignment>AlignCenter</VerticalAlignment>
        </DefaultTextSymbol>
        <AllowDuplicates>false</AllowDuplicates>
        <MaskLabels>true</MaskLabels>
        <MaskColor name="RGB" red="252" green="252" blue="248" />
        <PlaceAbove>true</PlaceAbove>
        <PlaceBelow>false</PlaceBelow>
        <PlaceOn>false</PlaceOn>
        <SymbolWidth>5</SymbolWidth>
        <SymbolHeight>5</SymbolHeight>
      </LabelPlacer>
      <ClickInfoDisplayFields ignoreWhenNotVisible="true">
        <ClickInfoDisplayField clickedLocationDisplayLabel="Street" clickedLocationFallBack="2" displayLayerItem="false">
          <DisplayLabel>NAME</DisplayLabel>
          <WhenContentEmpty action="SuppressLabel" />
          <DisplayContent>
            <Field tblField="Label" />
          </DisplayContent>
        </ClickInfoDisplayField>
      </ClickInfoDisplayFields>
      <LineRenderers>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <DefaultLineSymbol>
            <LineStyle>SolidLine</LineStyle>
            <Size>1</Size>
            <Color name="RGB" />
          </DefaultLineSymbol>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>MH</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>5</Size>
                <Color name="RGB" red="128" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
        <LineSymbolByFieldValue>
          <FieldName>Symbol</FieldName>
          <DefaultLineSymbol>
            <LineStyle>SolidLine</LineStyle>
            <Size>1</Size>
            <Color name="RGB" />
          </DefaultLineSymbol>
          <ValueSymbols>
            <LineSymbolForValue>
              <Value>MH</Value>
              <LineSymbol>
                <LineStyle>SolidLine</LineStyle>
                <Size>3</Size>
                <Color name="RGB" red="226" green="54" blue="54" />
              </LineSymbol>
            </LineSymbolForValue>
          </ValueSymbols>
        </LineSymbolByFieldValue>
      </LineRenderers>
    </LineLayer>
  </Layers>
</MapConfig>