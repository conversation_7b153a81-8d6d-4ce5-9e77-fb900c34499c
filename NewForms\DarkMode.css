B {
    color: #BFC3C9;
}

input, input[type="password"],input[type="text"],textarea {
    color: #BFC3C9;
    border: 1px solid #303846;
    background: #040910;
}

input:hover {
    background: #151e2e;
}

select {
    background: #2964a6;
    color: #fcfaf3;
}
option {
    background: #040910;
    color: #BFC3C9;
}
    option:hover {
        background: #151e2e;
        color: #BFC3C9;
    }
    
/* width */
::-webkit-scrollbar {
    background: #050d1a;
}

/* Track */
::-webkit-scrollbar-track {
    background: #050d1a;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #111b2b;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #192231;
}

H2 {
    FONT-SIZE: 24px;
    COLOR: #BFC3C9;
    FONT-FAMILY: Verdana;
}

H3 {
    COLOR: #BFC3C9;
}

H4 {
    COLOR: #BFC3C9;
}

H5 {
    COLOR: #BFC3C9;
}

BODY {
    COLOR: #BFC3C9;
    background: #040910;
    
}

HR {
    COLOR: #BFC3C9;
}

.base {
    BORDER-RIGHT: #050D1A thin solid;
    BORDER-TOP: #050D1A thin solid;
    BORDER-LEFT: #050D1A thin solid;
    BORDER-BOTTOM: #050D1A thin solid;
}

.topbottombordercell {
    BORDER-RIGHT: #050D1A;
    BORDER-TOP: #050D1A 1px solid;
    BORDER-LEFT: #050D1A;
    COLOR: #050D1A;
    BORDER-BOTTOM: #050D1A 1px solid;
}

.bottombordercell {
    BORDER-BOTTOM: #050D1A thin solid;
}

.bottombordercell img {
        width: 0;
        height: 0;
        padding: 76px 0 0 444px; 
        background: url(logo.png) no-repeat;
        background-size: 444px 76px
}

.bottomborderpanel {
    BORDER-RIGHT: #050D1A 1px;
    BORDER-TOP: #050D1A 1px;
    BORDER-LEFT: #050D1A 1px;
    BORDER-BOTTOM: #050D1A 1px solid;
}

.topbordercell {
    BORDER-TOP: #050D1A thin solid;
}

.rightbordercell {
    BORDER-RIGHT: #050D1A 1px solid;
}

.leftbordercell {
    BORDER-LEFT: #050D1A 1px solid;
}

.presentationdelimiter {
    color: #ccffff;
}


BODY TD .title {
    COLOR: #FFBFC3C9;
}


BODY TD .InputNames {
    COLOR: #FFBFC3C9;
}


TABLE {
    COLOR: #BFC3C9;
    background-color: #040910;
}

HR {
    COLOR: #BFC3C9;
}

.base {
    BORDER-RIGHT: #050D1A thin solid;
    BORDER-TOP: #050D1A thin solid;
    BORDER-LEFT: #050D1A thin solid;
    BORDER-BOTTOM: #050D1A thin solid;
    BACKGROUND-COLOR: #040910;
}

.topbottombordercell {
    BORDER-RIGHT: #050D1A;
    BORDER-TOP: #050D1A 1px solid;
    BORDER-LEFT: #050D1A;
    COLOR: #BFC3C9;
    BORDER-BOTTOM: #050D1A 1px solid;
}

.bottombordercell {
    BORDER-BOTTOM: #050D1A thin solid;
    BACKGROUND-COLOR: #040910;
}

.bottomborderpanel {
    BORDER-RIGHT: #050D1A 1px;
    BORDER-TOP: #050D1A 1px;
    BORDER-LEFT: #050D1A 1px;
    BORDER-BOTTOM: #050D1A 1px solid;
    BACKGROUND-COLOR: #040910;
}

.topbordercell {
    BORDER-TOP: #050D1A thin solid;
    BACKGROUND-COLOR: #040910;
}

.rightbordercell {
    BORDER-RIGHT: #050D1A 1px solid
}

.leftbordercell {
    BORDER-LEFT: #050D1A 1px solid
}

.presentationdelimiter {
    color: #050D1A
}



.tableStyle1 {
    border-color: #050D1A;
}

.trHeader {
    border-color: #050D1A;
    background-color: #040910;
    color: #BFC3C9;
}

.trSubHeader td {
    border-bottom: 1px solid #050D1A;
}

font {
    color: #BFC3C9;
}

a {
    color: #3B90EE;
}


