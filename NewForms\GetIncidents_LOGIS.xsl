<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="html" />
<xsl:key name="divisionList" match="/results/exemel/NewDataSet/Table" use="Division"/>

<xsl:template match="/">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Incident Times Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
		<STYLE>
		  .Header  { background-color:#F0E68C; color:black; }
		  .white { background-color:window;color:windowtext; font-weight:bold; }
		  .gray { background-color:lightgray; color:black; font-weight:bold; }
		  .fire_red { background-color:red; color:white; font-weight:bold; }
		</STYLE>

	</HEAD>
	<BODY>
		<form action="IncidentTimesResult.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<p>
							<xsl:apply-templates select="results/errormessage"/>
							<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
								<tr style="font-weight:bold;color:white;">
									<td align="center" colspan="6" class="header">LOGIS Active Incidents</td>
								</tr>
								<tr style="font-weight:bold;color:white;">
									<td>Agency</td>
									<td>Inc#</td>
									<td>Problem</td>
									<td>Address / Location</td>
									<td>City</td>
									<td>Created</td>
								</tr>
								<xsl:call-template name="process-rows" />
							</table>
						</p>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/errormessage">
	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>
</xsl:template> 

<xsl:template name="process-rows">
   <xsl:for-each select="/results/exemel/NewDataSet/Table[generate-id(.) = generate-id(key('divisionList', Division))]">
		<xsl:variable name="local_div" select="Division"/>
		<xsl:variable name="agency" select="TYPE"/>

		<xsl:variable name="color">
			<xsl:choose>
				<xsl:when test="position() mod 2 = 0">gray</xsl:when>
				<xsl:otherwise>white</xsl:otherwise>
			</xsl:choose>
		</xsl:variable>
		<xsl:for-each select="../Table[Division=$local_div]">
		
		<tr>
			<xsl:attribute name="class"><xsl:value-of select="$color"/></xsl:attribute>
			
					<xsl:choose>
						<xsl:when test="$agency = 'FIRE'">
							<td class="fire_red">
								<xsl:value-of select="Division"/>
							</td>
						</xsl:when>
						<xsl:otherwise>
							<td>					
								<xsl:value-of select="Division"/>
							</td>
						</xsl:otherwise>
					</xsl:choose>

					<td>
					<a href="SingleIncidentQuery.aspx?ID={ID}&#38;queryfile=SingleIncident.qry" location="remote">
						<xsl:value-of select="Incident_Number"/>
					</a>
				</td>
				<td><xsl:value-of select="Problem"/></td>
				<td>
					<xsl:value-of select="Address"/>
					<xsl:if test="Building!=''">	
						Bldg: <xsl:value-of select="Building"/>
					</xsl:if>
					<xsl:if test="Apartment!=''">
						Apt: <xsl:value-of select="Apartment"/>
					</xsl:if>
					<xsl:if test="Location_Name!=''">
						(<xsl:value-of select="Location_Name"/>)
					</xsl:if>
				</td>
				<td><xsl:value-of select="City"/></td>
				<td><xsl:value-of select="Time"/></td>
			</tr>
		</xsl:for-each>
	</xsl:for-each>
</xsl:template>

</xsl:transform>
