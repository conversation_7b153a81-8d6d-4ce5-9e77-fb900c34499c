<?xml version="1.0" encoding="utf-16"?>
<CADListDataInformation xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/MobileSchema">
  <Hierarchy>
    <AgencyInfo>
      <AgencyID>6</AgencyID>
      <Capabilities>
        <CodeInfo>
          <ID>14</ID>
          <Name>SM Swat Team Member</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>16</ID>
          <Name>K9 officer</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>19</ID>
          <Name>Spanish Speaker</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>20</ID>
          <Name>Negotiator</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>21</ID>
          <Name>EA swat</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>24</ID>
          <Name>Hmong Speaker</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>34</ID>
          <Name>LA Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>35</ID>
          <Name>Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>36</ID>
          <Name>Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>37</ID>
          <Name>LA Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>38</ID>
          <Name>AV Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>39</ID>
          <Name>AV Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>41</ID>
          <Name>AV area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>42</ID>
          <Name>LA area 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>43</ID>
          <Name>AV area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>44</ID>
          <Name>EA area 10</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>45</ID>
          <Name>AV area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>52</ID>
          <Name>EA area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>53</ID>
          <Name>AV area 70</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>54</ID>
          <Name>EA area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>55</ID>
          <Name>EA area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>56</ID>
          <Name>EA area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>57</ID>
          <Name>EA cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>58</ID>
          <Name>EA Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>59</ID>
          <Name>EA Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>106</ID>
          <Name>LA Reserve</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>107</ID>
          <Name>LA cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>109</ID>
          <Name>BV swat</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>112</ID>
          <Name>LA non sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>133</ID>
          <Name>RS area 10</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>134</ID>
          <Name>RS area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>135</ID>
          <Name>RS Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>136</ID>
          <Name>RS cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>137</ID>
          <Name>RS Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>147</ID>
          <Name>AV cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>170</ID>
          <Name>CVI - commercial vehicle inspector</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>171</ID>
          <Name>DRE - drug recognition expert</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>173</ID>
          <Name>AV Patrol Substitute</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>185</ID>
          <Name>HA Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>186</ID>
          <Name>HA area 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>187</ID>
          <Name>HA area 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>188</ID>
          <Name>HA area 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>189</ID>
          <Name>HA area 4</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>190</ID>
          <Name>HA area 6</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>191</ID>
          <Name>HA area 7</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>192</ID>
          <Name>HA K9 officer</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>193</ID>
          <Name>HA Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>196</ID>
          <Name>EA non sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>199</ID>
          <Name>EA Animal Control</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>203</ID>
          <Name>LA area 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>204</ID>
          <Name>LA area 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>205</ID>
          <Name>LA area 4</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>206</ID>
          <Name>BV Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>207</ID>
          <Name>DC Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>208</ID>
          <Name>FA Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>209</ID>
          <Name>IG Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>210</ID>
          <Name>MH Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>211</ID>
          <Name>SS Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>212</ID>
          <Name>WS Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>213</ID>
          <Name>HA area 5</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>214</ID>
          <Name>Z3 Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>215</ID>
          <Name>IG area 80</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>216</ID>
          <Name>IG area 60</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>217</ID>
          <Name>IG area 70</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>218</ID>
          <Name>IG area 90</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>220</ID>
          <Name>IG Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>221</ID>
          <Name>IG PatrolSub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>222</ID>
          <Name>IG cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>223</ID>
          <Name>IG non sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>224</ID>
          <Name>SS area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>225</ID>
          <Name>SS area 60</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>226</ID>
          <Name>SS area 10</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>227</ID>
          <Name>SS cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>228</ID>
          <Name>SS Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>229</ID>
          <Name>SS non sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>230</ID>
          <Name>Z3-SS Patrol Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>231</ID>
          <Name>FA area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>232</ID>
          <Name>FA area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>233</ID>
          <Name>FA area 90</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>236</ID>
          <Name>FA Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>237</ID>
          <Name>LA area 7</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>238</ID>
          <Name>LA area 9</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>248</ID>
          <Name>EA area 70</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>260</ID>
          <Name>RS area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>261</ID>
          <Name>RS non sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>262</ID>
          <Name>RS Reserve</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>264</ID>
          <Name>DC area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>265</ID>
          <Name>DC area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>266</ID>
          <Name>DC area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>267</ID>
          <Name>DC Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>268</ID>
          <Name>DC Park Ranger</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>270</ID>
          <Name>AV Non Sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>272</ID>
          <Name>BV area 10</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>273</ID>
          <Name>BV area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>274</ID>
          <Name>BV area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>275</ID>
          <Name>BV area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>276</ID>
          <Name>BV Non Sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>277</ID>
          <Name>BV cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>279</ID>
          <Name>BV Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>288</ID>
          <Name>BV area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>289</ID>
          <Name>BV area 60</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>290</ID>
          <Name>MH Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>298</ID>
          <Name>DC Patrol All</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>302</ID>
          <Name>DC Non Sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>316</ID>
          <Name>WS cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>317</ID>
          <Name>WS Reserve</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>318</ID>
          <Name>WS Sgt</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>319</ID>
          <Name>WS non sworn</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>337</ID>
          <Name>MH Reserve</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>340</ID>
          <Name>SS Reserve</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>341</ID>
          <Name>SS Park Ranger</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>342</ID>
          <Name>HA Reserve</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>343</ID>
          <Name>HA cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>359</ID>
          <Name>EA area 60</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>360</ID>
          <Name>EA Traffic Car</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>370</ID>
          <Name>DC Transport</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>372</ID>
          <Name>Cambodian/Khmer speaker</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>373</ID>
          <Name>Dive Team</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>375</ID>
          <Name>IG area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>376</ID>
          <Name>SS Crisis Intervention</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>381</ID>
          <Name>SS Patrol Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>382</ID>
          <Name>DC Inver Hills</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>383</ID>
          <Name>DC dctc</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>421</ID>
          <Name>Water Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>427</ID>
          <Name>BV East Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>428</ID>
          <Name>BV West Patrol</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>443</ID>
          <Name>MH Cso</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>450</ID>
          <Name>DC Western Service Center Security</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>451</ID>
          <Name>DC Northern Service Center Security</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>452</ID>
          <Name>Russian Speaker</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>454</ID>
          <Name>Mff Strike Team</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>457</ID>
          <Name>Somali Speaker</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>460</ID>
          <Name>DC CRU Referral</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>461</ID>
          <Name>DC area 53</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>463</ID>
          <Name>DC Hastings Government Center Security</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>471</ID>
          <Name>Phlembotomist</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>474</ID>
          <Name>Drone Operator</Name>
        </CodeInfo>
      </Capabilities>
      <Divisions>
        <CodeInfo>
          <ID>20</ID>
          <Name>AV Area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>108</ID>
          <Name>AV Area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>109</ID>
          <Name>AV Area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>110</ID>
          <Name>AV MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>21</ID>
          <Name>BV MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>111</ID>
          <Name>BV Area 10</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>112</ID>
          <Name>BV Area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>113</ID>
          <Name>BV Area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>114</ID>
          <Name>BV Area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>22</ID>
          <Name>DC Area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>115</ID>
          <Name>DC Area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>116</ID>
          <Name>DC Area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>117</ID>
          <Name>DC Area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>118</ID>
          <Name>DC MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>23</ID>
          <Name>EA Area 10</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>119</ID>
          <Name>EA Area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>120</ID>
          <Name>EA Area 30</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>121</ID>
          <Name>EA Area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>122</ID>
          <Name>EA Area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>141</ID>
          <Name>EA MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>156</ID>
          <Name>EA Area 60</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>24</ID>
          <Name>FA Area 40</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>124</ID>
          <Name>FA MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>161</ID>
          <Name>FA Area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>25</ID>
          <Name>HA Area 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>125</ID>
          <Name>HA MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>158</ID>
          <Name>HA Area 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>159</ID>
          <Name>HA Area 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>160</ID>
          <Name>HA Area 4</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>26</ID>
          <Name>IG Area NE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>126</ID>
          <Name>IG Area NW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>127</ID>
          <Name>IG MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>162</ID>
          <Name>IG Area SE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>163</ID>
          <Name>IG Area SW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>27</ID>
          <Name>LA Area 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>128</ID>
          <Name>LA Area 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>129</ID>
          <Name>LA Area 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>130</ID>
          <Name>LA Area 4</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>131</ID>
          <Name>LA MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>28</ID>
          <Name>MH MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>132</ID>
          <Name>MH Area 90</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>29</ID>
          <Name>RS Area 10</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>133</ID>
          <Name>RS Area 20</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>134</ID>
          <Name>RS MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>30</ID>
          <Name>SS MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>135</ID>
          <Name>SS Area 10</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>136</ID>
          <Name>SS Area 50</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>137</ID>
          <Name>SS Area 60</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>31</ID>
          <Name>WS Area 90</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>138</ID>
          <Name>WS MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>123</ID>
          <Name>EM All</Name>
        </CodeInfo>
      </Divisions>
      <Jurisdictions>
        <CodeInfo>
          <ID>20</ID>
          <Name>Apple Valley Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>21</ID>
          <Name>Burnsville Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>22</ID>
          <Name>Dakota County Sheriff</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>23</ID>
          <Name>Eagan Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>24</ID>
          <Name>Farmington Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>25</ID>
          <Name>Hastings Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>26</ID>
          <Name>Inver Grove Heights Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>27</ID>
          <Name>Lakeville Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>28</ID>
          <Name>Mendota Heights Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>29</ID>
          <Name>Rosemount Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>30</ID>
          <Name>South St. Paul Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>31</ID>
          <Name>West St. Paul Law</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>56</ID>
          <Name>Empire</Name>
        </CodeInfo>
      </Jurisdictions>
      <Name>Dakota Law</Name>
      <ResourceTypes>
        <CodeInfo>
          <ID>12</ID>
          <Name>AV LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>13</ID>
          <Name>BV LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>14</ID>
          <Name>DC LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>15</ID>
          <Name>EA LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>64</ID>
          <Name>FA LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>65</ID>
          <Name>HA LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>66</ID>
          <Name>IG LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>67</ID>
          <Name>LA LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>68</ID>
          <Name>MH LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>69</ID>
          <Name>RS LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>70</ID>
          <Name>SS LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>71</ID>
          <Name>WS LAW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>72</ID>
          <Name>EM AGENCY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>197</ID>
          <Name>WS ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>312</ID>
          <Name>STACKABLE CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>435</ID>
          <Name>CLONE MEDICAL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>448</ID>
          <Name>SS ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>449</ID>
          <Name>RS ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>450</ID>
          <Name>MH ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>451</ID>
          <Name>LA ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>452</ID>
          <Name>IG ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>453</ID>
          <Name>HA ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>454</ID>
          <Name>FA ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>455</ID>
          <Name>EA ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>456</ID>
          <Name>DC ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>457</ID>
          <Name>BV ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>458</ID>
          <Name>AV ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>519</ID>
          <Name>CHECK SOP</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>545</ID>
          <Name>DC PROBATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>552</ID>
          <Name>SWAT</Name>
        </CodeInfo>
      </ResourceTypes>
      <Sectors>
        <CodeInfo>
          <ID>33</ID>
          <Name>LA</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>34</ID>
          <Name>AV</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>35</ID>
          <Name>BV</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>36</ID>
          <Name>DC</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>37</ID>
          <Name>EA</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>38</ID>
          <Name>EM</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>39</ID>
          <Name>FA</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>40</ID>
          <Name>HA</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>41</ID>
          <Name>IG</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>42</ID>
          <Name>MH</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>43</ID>
          <Name>RS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>44</ID>
          <Name>SS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>45</ID>
          <Name>WS</Name>
        </CodeInfo>
      </Sectors>
    </AgencyInfo>
    <AgencyInfo>
      <AgencyID>7</AgencyID>
      <Capabilities>
        <CodeInfo>
          <ID>130</ID>
          <Name>BLS Capabilities</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>138</ID>
          <Name>Ambulance</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>145</ID>
          <Name>Other Agency</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>146</ID>
          <Name>Mutual Aid Fire Dept</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>240</ID>
          <Name>Health East Ambulance</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>244</ID>
          <Name>Hold for an engine</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>247</ID>
          <Name>SMALL Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>251</ID>
          <Name>SM Ambulance</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>252</ID>
          <Name>SM Amb 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>253</ID>
          <Name>SM Amb 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>254</ID>
          <Name>Health East sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>256</ID>
          <Name>Allina Amb sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>257</ID>
          <Name>Allina Ambulance</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>283</ID>
          <Name>1st Responder</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>284</ID>
          <Name>LA Extrication</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>286</ID>
          <Name>2 Extrication</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>287</ID>
          <Name>AED on vehicle</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>292</ID>
          <Name>AF 1st Responder</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>293</ID>
          <Name>AF Extrication</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>294</ID>
          <Name>B2ALL Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>295</ID>
          <Name>No Response</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>312</ID>
          <Name>Engine Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>315</ID>
          <Name>B2 Medic</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>321</ID>
          <Name>Eagan Duty Crew</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>322</ID>
          <Name>EF All Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>325</ID>
          <Name>SM Xstaffed Vehicle</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>326</ID>
          <Name>SM Amb Sub for WS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>327</ID>
          <Name>SM Engine Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>328</ID>
          <Name>SM Amb Sub for SS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>329</ID>
          <Name>SM Unit Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>330</ID>
          <Name>B2 Chiefs Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>331</ID>
          <Name>B2 All Sub2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>332</ID>
          <Name>B2 Engine Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>333</ID>
          <Name>SM Xstaffed Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>334</ID>
          <Name>SM Ladder  Sub 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>335</ID>
          <Name>SM Ladder Sub 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>336</ID>
          <Name>SM Xstaffed Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>339</ID>
          <Name>Eagan station 2-5</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>344</ID>
          <Name>Apple Valley Duty Crew</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>374</ID>
          <Name>ALS Capabilities</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>377</ID>
          <Name>SM On Duty Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>378</ID>
          <Name>If Station 1 Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>379</ID>
          <Name>If Station 3 Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>380</ID>
          <Name>Lakeville Duty Crew</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>429</ID>
          <Name>SM Eng Sub For SS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>430</ID>
          <Name>SM Eng Sub for WS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>431</ID>
          <Name>SM Lad Sub 1david</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>432</ID>
          <Name>SM Lad Sub 2david</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>433</ID>
          <Name>SM Amb Eng Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>434</ID>
          <Name>EF Eagan Fire EMS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>435</ID>
          <Name>EF Duty Engine</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>439</ID>
          <Name>EF Duty Engine Only</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>440</ID>
          <Name>EF Duty Engine Ladder</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>441</ID>
          <Name>Farmington Duty Crew</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>444</ID>
          <Name>Rescue Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>445</ID>
          <Name>Grass Rig</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>446</ID>
          <Name>Rescue</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>447</ID>
          <Name>FF Rescue</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>448</ID>
          <Name>FF Rescue Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>449</ID>
          <Name>FF Grass Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>453</ID>
          <Name>H2 Engine Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>455</ID>
          <Name>River Falls Engine</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>456</ID>
          <Name>River Falls Medic</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>459</ID>
          <Name>Rescue Swimmer</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>462</ID>
          <Name>IF Engine Sub</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>472</ID>
          <Name>EF AB Response</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>473</ID>
          <Name>EF Ems Sub</Name>
        </CodeInfo>
      </Capabilities>
      <Divisions>
        <CodeInfo>
          <ID>7</ID>
          <Name>AF Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>79</ID>
          <Name>AF Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>80</ID>
          <Name>AF Station 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>81</ID>
          <Name>AF MA AREA</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>8</ID>
          <Name>B2 Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>82</ID>
          <Name>B2 Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>83</ID>
          <Name>B2 MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>148</ID>
          <Name>B2 Admin</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>9</ID>
          <Name>DF Fire East</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>84</ID>
          <Name>DF Northfield</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>85</ID>
          <Name>DF Fire West</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>149</ID>
          <Name>DF SOT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>10</ID>
          <Name>EF Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>86</ID>
          <Name>EF Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>87</ID>
          <Name>EF Station 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>88</ID>
          <Name>EF Station 4</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>89</ID>
          <Name>EF Station 5</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>90</ID>
          <Name>EF MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>11</ID>
          <Name>FF Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>91</ID>
          <Name>FF Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>92</ID>
          <Name>FF MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>17</ID>
          <Name>RH Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>102</ID>
          <Name>RH Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>103</ID>
          <Name>RH MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>12</ID>
          <Name>H2 Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>93</ID>
          <Name>H2 MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>13</ID>
          <Name>IF Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>94</ID>
          <Name>IF Station 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>95</ID>
          <Name>IF MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>182</ID>
          <Name>IF Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>14</ID>
          <Name>LF Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>96</ID>
          <Name>LF Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>97</ID>
          <Name>LF Station 4</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>98</ID>
          <Name>LF Station 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>99</ID>
          <Name>LF MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>15</ID>
          <Name>MF Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>100</ID>
          <Name>MF MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>16</ID>
          <Name>MV Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>101</ID>
          <Name>MV MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>18</ID>
          <Name>R1 Station 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>104</ID>
          <Name>R1 Station 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>105</ID>
          <Name>R1 MA Area</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>19</ID>
          <Name>SM Station 1 WS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>106</ID>
          <Name>SM Station 2 SS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>107</ID>
          <Name>SM MA Area</Name>
        </CodeInfo>
      </Divisions>
      <Jurisdictions>
        <CodeInfo>
          <ID>7</ID>
          <Name>Apple Valley Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>8</ID>
          <Name>Burnsville Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>9</ID>
          <Name>Dakota Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>10</ID>
          <Name>Eagan Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>11</ID>
          <Name>Farmington Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>12</ID>
          <Name>Randolph-Hampton Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>13</ID>
          <Name>Hastings Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>14</ID>
          <Name>Inver Grove Heights Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>15</ID>
          <Name>Lakeville Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>16</ID>
          <Name>Mendota Heights Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>17</ID>
          <Name>Miesville Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>18</ID>
          <Name>Rosemount Fire</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>19</ID>
          <Name>South Metro Fire</Name>
        </CodeInfo>
      </Jurisdictions>
      <Name>Dakota Fire</Name>
      <ResourceTypes>
        <CodeInfo>
          <ID>16</ID>
          <Name>DF FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>17</ID>
          <Name>AF FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>18</ID>
          <Name>B2 FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>19</ID>
          <Name>FF FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>20</ID>
          <Name>EF FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>21</ID>
          <Name>IF FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>22</ID>
          <Name>H2 FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>73</ID>
          <Name>LF FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>74</ID>
          <Name>MF FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>75</ID>
          <Name>R1 FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>76</ID>
          <Name>SM FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>77</ID>
          <Name>MV FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>78</ID>
          <Name>RH FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>133</ID>
          <Name>AMBULANCE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>140</ID>
          <Name>AF ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>141</ID>
          <Name>EF ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>142</ID>
          <Name>ALLINA AMBULANCE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>148</ID>
          <Name>B2 ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>149</ID>
          <Name>ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>152</ID>
          <Name>MHEALTH FAIRVIEW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>157</ID>
          <Name>SM AMBULANCE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>158</ID>
          <Name>SM ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>159</ID>
          <Name>SM LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>165</ID>
          <Name>EF REHAB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>166</ID>
          <Name>EF OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>168</ID>
          <Name>B2 MEDIC</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>175</ID>
          <Name>LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>176</ID>
          <Name>AF FS2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>177</ID>
          <Name>AF DUTY - search as capability</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>178</ID>
          <Name>R1 STATION 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>179</ID>
          <Name>R1 STATION 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>180</ID>
          <Name>FF ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>181</ID>
          <Name>FF ENGINE-2ND OUT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>205</ID>
          <Name>MUTUAL AID FIRE DEPT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>206</ID>
          <Name>BLOOMINGTON</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>207</ID>
          <Name>SM AMBULANCE 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>208</ID>
          <Name>SM AMBULANCE 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>209</ID>
          <Name>SM ENGINE 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>210</ID>
          <Name>SM ENGINE 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>211</ID>
          <Name>SM ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>212</ID>
          <Name>IF ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>213</ID>
          <Name>MF ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>214</ID>
          <Name>SM PAGE CALLBACK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>215</ID>
          <Name>IF LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>216</ID>
          <Name>MF RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>218</ID>
          <Name>EF LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>219</ID>
          <Name>IF RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>221</ID>
          <Name>MF LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>227</ID>
          <Name>LF LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>228</ID>
          <Name>LF ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>231</ID>
          <Name>LF ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>232</ID>
          <Name>COMMAND VEHICLE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>233</ID>
          <Name>LF TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>234</ID>
          <Name>LF BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>235</ID>
          <Name>BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>236</ID>
          <Name>DC DIVE TEAM</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>237</ID>
          <Name>SOT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>238</ID>
          <Name>LF OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>239</ID>
          <Name>LF FIRE MARSHAL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>240</ID>
          <Name>LF RESCUE STN 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>241</ID>
          <Name>LF DUTY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>242</ID>
          <Name>LF STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>243</ID>
          <Name>STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>244</ID>
          <Name>BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>245</ID>
          <Name>LF RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>246</ID>
          <Name>RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>247</ID>
          <Name>LF BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>248</ID>
          <Name>OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>249</ID>
          <Name>FIRE MARSHAL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>250</ID>
          <Name>TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>251</ID>
          <Name>LF TENDER STN 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>252</ID>
          <Name>SOT DUTY PAGE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>254</ID>
          <Name>B2 OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>255</ID>
          <Name>B2 BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>256</ID>
          <Name>B2 COMMAND VEHICLE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>257</ID>
          <Name>B2 RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>258</ID>
          <Name>B2 STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>259</ID>
          <Name>B2 TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>260</ID>
          <Name>B2 UTILITY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>261</ID>
          <Name>B2 BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>262</ID>
          <Name>B2 LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>263</ID>
          <Name>B2 FIRE MARSHAL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>264</ID>
          <Name>MEDICS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>265</ID>
          <Name>UTILITY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>266</ID>
          <Name>MDEWANKETON MEDICS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>267</ID>
          <Name>HCMC</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>270</ID>
          <Name>AF OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>271</ID>
          <Name>AF FIRE MARSHALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>272</ID>
          <Name>AF ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>273</ID>
          <Name>AF COMMAND VEHICLE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>274</ID>
          <Name>AF STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>275</ID>
          <Name>AF LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>276</ID>
          <Name>AF UTILITY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>277</ID>
          <Name>AF BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>278</ID>
          <Name>AF BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>279</ID>
          <Name>AF RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>280</ID>
          <Name>AF CHIEF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>281</ID>
          <Name>FF RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>282</ID>
          <Name>FF BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>283</ID>
          <Name>FF STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>284</ID>
          <Name>FF TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>285</ID>
          <Name>FF OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>286</ID>
          <Name>FF RESCUE STN 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>287</ID>
          <Name>FF FIRE MARSHAL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>288</ID>
          <Name>FF UTILITY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>289</ID>
          <Name>R1 ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>290</ID>
          <Name>R1 BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>291</ID>
          <Name>R1 OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>292</ID>
          <Name>R1 ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>293</ID>
          <Name>R1 LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>294</ID>
          <Name>R1 RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>295</ID>
          <Name>R1 TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>296</ID>
          <Name>R1 UTILITY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>297</ID>
          <Name>BV CHAPLAINS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>298</ID>
          <Name>B2 PAGE CALLBACK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>299</ID>
          <Name>B2 ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>304</ID>
          <Name>EF RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>305</ID>
          <Name>EF ATV</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>306</ID>
          <Name>EF BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>308</ID>
          <Name>EF BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>309</ID>
          <Name>EF TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>310</ID>
          <Name>EF STATION 4</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>311</ID>
          <Name>EF FIRE MARSHALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>313</ID>
          <Name>EF INVESTIGATOR</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>314</ID>
          <Name>EF STATION 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>315</ID>
          <Name>EF STATION 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>316</ID>
          <Name>EF STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>317</ID>
          <Name>REHAB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>318</ID>
          <Name>ELECTRIC CO</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>320</ID>
          <Name>LA CHAPLAINS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>321</ID>
          <Name>IF STATION 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>322</ID>
          <Name>IF STATION 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>323</ID>
          <Name>IF ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>326</ID>
          <Name>NORTHFIELD AMB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>327</ID>
          <Name>NORTHFIELD FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>328</ID>
          <Name>EF ENGINE 1ST OUT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>329</ID>
          <Name>EF UTILITY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>330</ID>
          <Name>SM BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>332</ID>
          <Name>H2 ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>333</ID>
          <Name>H2 MEDIC</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>334</ID>
          <Name>H2 LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>335</ID>
          <Name>H2 ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>336</ID>
          <Name>H2 TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>337</ID>
          <Name>H2 RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>338</ID>
          <Name>H2 BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>339</ID>
          <Name>H2 BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>340</ID>
          <Name>MV ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>341</ID>
          <Name>COTTAGE GROVE ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>342</ID>
          <Name>PRESCOTT ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>343</ID>
          <Name>PRESCOTT AMBULANCE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>344</ID>
          <Name>PRESCOTT TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>345</ID>
          <Name>COTTAGE GROVE TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>346</ID>
          <Name>ST PAUL PARK ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>347</ID>
          <Name>LOWER ST CROIX TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>348</ID>
          <Name>MV TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>349</ID>
          <Name>RH ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>350</ID>
          <Name>RH TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>351</ID>
          <Name>RED WING AMBULANCE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>352</ID>
          <Name>PRESCOTT BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>353</ID>
          <Name>CANNON FALLS AMBULANCE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>354</ID>
          <Name>CANNON FALLS TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>356</ID>
          <Name>NORTHFIELD ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>357</ID>
          <Name>IF TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>359</ID>
          <Name>CANNON FALLS FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>363</ID>
          <Name>EF ENGINE 2ND OUT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>364</ID>
          <Name>IF OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>365</ID>
          <Name>MV ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>438</ID>
          <Name>DF SOP</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>442</ID>
          <Name>EF DUTY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>443</ID>
          <Name>MF ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>445</ID>
          <Name>EF COMMAND VEHICLE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>447</ID>
          <Name>EF STATION SUB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>459</ID>
          <Name>MF OFFICER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>460</ID>
          <Name>MF BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>461</ID>
          <Name>MF BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>462</ID>
          <Name>MF FIRE MARSHALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>463</ID>
          <Name>MF TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>464</ID>
          <Name>MF UTILITY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>465</ID>
          <Name>NO RESPONSE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>466</ID>
          <Name>R1 STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>467</ID>
          <Name>SM LADDER 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>468</ID>
          <Name>LF LADDER 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>469</ID>
          <Name>POLICE ONLY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>470</ID>
          <Name>SM LADDER 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>471</ID>
          <Name>R1 MUTUAL AID ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>472</ID>
          <Name>R1 MUTUAL AID LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>473</ID>
          <Name>R1 MUTUAL AID TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>474</ID>
          <Name>R1 MUTUAL AID RESCUE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>495</ID>
          <Name>IF BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>496</ID>
          <Name>ST PAUL PARK BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>497</ID>
          <Name>IF MUTUAL AID ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>498</ID>
          <Name>IF MUTUAL AID TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>499</ID>
          <Name>MF PD RESPONSE ONLY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>500</ID>
          <Name>FF SUBSTATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>501</ID>
          <Name>H2 SQUAD</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>502</ID>
          <Name>EF STATION 5</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>503</ID>
          <Name>EF MUTUAL AID ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>504</ID>
          <Name>EF PAGE 2ND ALARM</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>505</ID>
          <Name>EF PAGE 3RD ALARM</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>508</ID>
          <Name>ST PAUL MEDIC</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>509</ID>
          <Name>B2 NO RESPONSE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>510</ID>
          <Name>EF CHIEF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>511</ID>
          <Name>IF DUTY CREW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>512</ID>
          <Name>HAZMAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>513</ID>
          <Name>BLOOMINGTON ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>514</ID>
          <Name>BLOOMINGTON OFFICERS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>515</ID>
          <Name>BLOOMINGTON BOAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>516</ID>
          <Name>H2 STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>518</ID>
          <Name>MV BRUSH TRUCK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>520</ID>
          <Name>FF STATION 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>521</ID>
          <Name>FF STATION 1 SUB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>522</ID>
          <Name>FF STATION 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>523</ID>
          <Name>FF STATION 2 SUB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>524</ID>
          <Name>AF ENGINE 2ND</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>525</ID>
          <Name>IF STATION 1 SUB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>526</ID>
          <Name>IF STATION 3 SUB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>527</ID>
          <Name>RH ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>528</ID>
          <Name>LF ENGINE 2ND</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>530</ID>
          <Name>SOT UNITS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>531</ID>
          <Name>MHEALTH FAIRVIEW EAST</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>532</ID>
          <Name>MHEATLH FAIRVIEW WEST</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>533</ID>
          <Name>B2 ENGINE ONLY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>534</ID>
          <Name>SM SUBSTITUTE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>535</ID>
          <Name>SM SUBSTITUTE 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>536</ID>
          <Name>LF ENGINE SUB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>547</ID>
          <Name>SOS- Emergency Unit</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>548</ID>
          <Name>H2 ADMIN</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>549</ID>
          <Name>FF MUTUAL AID ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>550</ID>
          <Name>FF MUTUAL AID LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>551</ID>
          <Name>FF MUTUAL AID TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>553</ID>
          <Name>LF STATION (NO RECOMMEND)</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>555</ID>
          <Name>EF BATALLION CHIEF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>556</ID>
          <Name>EF ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>557</ID>
          <Name>EF CHIEFS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>558</ID>
          <Name>FF ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>559</ID>
          <Name>RH MUTUAL AID ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>560</ID>
          <Name>RH MUTUAL AID TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>561</ID>
          <Name>CANNON FALLS ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>562</ID>
          <Name>MF MUTUAL AID ENG</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>563</ID>
          <Name>MF MUTUAL AID TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>564</ID>
          <Name>MF MUTUAL AID LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>565</ID>
          <Name>TEMP DUTY CREW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>567</ID>
          <Name>IF STATION 2 SUB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>568</ID>
          <Name>IF STATION 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>569</ID>
          <Name>IF MUTUAL AID LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>570</ID>
          <Name>IF STATION</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>572</ID>
          <Name>SM UTILITY 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>573</ID>
          <Name>EF ALL SUB</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>574</ID>
          <Name>EF PART TIME CALLBACK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>575</ID>
          <Name>EF FULL TIME CALLBACK</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>576</ID>
          <Name>EF MUTUAL AID LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>577</ID>
          <Name>EF MUTUAL AID TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>578</ID>
          <Name>EF EMS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>582</ID>
          <Name>COTTAGE GROVE LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>583</ID>
          <Name>COTTAGE GROVE MEDIC</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>584</ID>
          <Name>LAKEVIEW MEDICS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>585</ID>
          <Name>LOWER ST CROIX ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>586</ID>
          <Name>RED WING ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>587</ID>
          <Name>ST PAUL PARK TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>588</ID>
          <Name>WOODBURY ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>589</ID>
          <Name>WOODBURY MEDICS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>590</ID>
          <Name>H2 ENGINE 4</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>591</ID>
          <Name>H2 ENGINE 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>592</ID>
          <Name>H2 CALLBACK PAGE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>593</ID>
          <Name>H2 ENGINE 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>594</ID>
          <Name>H2 MUTUAL AID ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>597</ID>
          <Name>RIVER FALLS ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>598</ID>
          <Name>RIVER FALLS MEDIC</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>599</ID>
          <Name>H2 MUTUAL AID TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>600</ID>
          <Name>MF ENGINE 11</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>601</ID>
          <Name>SM ENGINE 12</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>602</ID>
          <Name>SM CHIEF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>603</ID>
          <Name>FF ENGINE 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>604</ID>
          <Name>H2 CHIEFS</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>605</ID>
          <Name>EF LADDER 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>606</ID>
          <Name>R1 ENGINE 21</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>607</ID>
          <Name>IF LADDER 34</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>608</ID>
          <Name>DK TASK FORCE 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>609</ID>
          <Name>DK TASK FORCE 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>610</ID>
          <Name>DK TASK FORCE 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>613</ID>
          <Name>FF LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>614</ID>
          <Name>FF DUTY CREW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>615</ID>
          <Name>FF FAIR DUTY CREW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>616</ID>
          <Name>LF DRONE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>617</ID>
          <Name>STAFFED ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>621</ID>
          <Name>IF ENGINE 17</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>622</ID>
          <Name>IF ENGINE 21</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>623</ID>
          <Name>IFMA STAFFED ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>625</ID>
          <Name>IF STAFFED ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>626</ID>
          <Name>IF LADDER 13</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>627</ID>
          <Name>MV MUTUAL AID TENDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>628</ID>
          <Name>MV MUTUAL AID ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>629</ID>
          <Name>CANNON FALLS LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>630</ID>
          <Name>COTTAGE GROVE FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>631</ID>
          <Name>ST PAUL PARK FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>632</ID>
          <Name>MDEWANKETON FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>633</ID>
          <Name>NEW MARKET FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>634</ID>
          <Name>PRIOR LAKE FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>635</ID>
          <Name>SAVAGE FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>636</ID>
          <Name>SHAKOPEE FIRE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>637</ID>
          <Name>RICHFIELD ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>638</ID>
          <Name>BLOOMINGTON LADDER</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>639</ID>
          <Name>SAVAGE ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>640</ID>
          <Name>AIRPORT ENGINE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>641</ID>
          <Name>SM RESCUE 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>649</ID>
          <Name>SOT DUTY</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>650</ID>
          <Name>SOT ALL CALL</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>651</ID>
          <Name>SOT RESCUE 1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>652</ID>
          <Name>SOT RESCUE 2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>653</ID>
          <Name>SOT RESCUE 3</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>654</ID>
          <Name>SOT HAZMAT</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>655</ID>
          <Name>SOT LUMBER TRUCK</Name>
        </CodeInfo>
      </ResourceTypes>
      <Sectors>
        <CodeInfo>
          <ID>13</ID>
          <Name>AF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>20</ID>
          <Name>DFE</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>21</ID>
          <Name>H2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>22</ID>
          <Name>IF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>23</ID>
          <Name>MF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>24</ID>
          <Name>MV</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>25</ID>
          <Name>RH</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>26</ID>
          <Name>SM</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>27</ID>
          <Name>B2</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>28</ID>
          <Name>EF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>29</ID>
          <Name>FF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>30</ID>
          <Name>LF</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>31</ID>
          <Name>DFW</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>32</ID>
          <Name>R1</Name>
        </CodeInfo>
        <CodeInfo>
          <ID>79</ID>
          <Name>SOT</Name>
        </CodeInfo>
      </Sectors>
    </AgencyInfo>
  </Hierarchy>
  <Tags xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <cadStatus xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>0</d2p1:Key>
      <d2p1:Value>
        <BackColor>#00FFFFFF</BackColor>
        <Description>OFF</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>true</HasDefaultColors>
        <NextAvailableStatuses>1,2</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>1</d2p1:Key>
      <d2p1:Value>
        <BackColor>#00FFFFFF</BackColor>
        <Description>AV</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>true</HasDefaultColors>
        <NextAvailableStatuses>0,1,2,3,4,5,6,8,11,15,17,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>2</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFEAEAEA</BackColor>
        <Description>IQ</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>0,1,2,5,6,17</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>3</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFC7BBC9</BackColor>
        <Description>UNUSED STATUS</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>0,1,2,5,6,17,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>5</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFD0B783</BackColor>
        <Description>OS</Description>
        <ForeColor>#FF4225BB</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,2,5,6,7,9,10,11,12,13,14,17,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>6</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FF87CC9C</BackColor>
        <Description>DI</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,7,9,10,11,12,14,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>7</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFFFFA9C</BackColor>
        <Description>EN</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,5,9,10,11,12,14,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>8</d2p1:Key>
      <d2p1:Value>
        <BackColor>#00FFFFFF</BackColor>
        <Description>TO</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>true</HasDefaultColors>
        <NextAvailableStatuses>0,1,2,5,6,7,9,10,11,12,13,14,17,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>9</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFF0A3A3</BackColor>
        <Description>C5</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,5,6,7,9,10,11,12,14,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>10</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFF0A3A3</BackColor>
        <Description>AR</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,5,6,7,9,10,11,12,14,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>11</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFCCCCCC</BackColor>
        <Description>C4</Description>
        <ForeColor>#FF066AD0</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,5,6,7,9,10,11,12,14,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>12</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFCCCCCC</BackColor>
        <Description>TP</Description>
        <ForeColor>#FF9F12BA</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,2,5,6,7,9,10,11,12,13,14,17,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>13</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFF0A3A3</BackColor>
        <Description>ET</Description>
        <ForeColor>#FF4225BB</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,2,5,6,7,9,10,11,12,13,14,17,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>14</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFF0A3A3</BackColor>
        <Description>DA</Description>
        <ForeColor>#FF4225BB</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,2,5,6,7,9,10,11,12,13,14,17,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>16</d2p1:Key>
      <d2p1:Value>
        <BackColor>#00FFFFFF</BackColor>
        <Description>SP</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>true</HasDefaultColors>
        <NextAvailableStatuses>0,1,2</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>17</d2p1:Key>
      <d2p1:Value>
        <BackColor>#00FFFFFF</BackColor>
        <Description>AP</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>true</HasDefaultColors>
        <NextAvailableStatuses>0,1,2,5,6,17,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>18</d2p1:Key>
      <d2p1:Value>
        <BackColor>#00FFFFFF</BackColor>
        <Description>MA</Description>
        <ForeColor>#FF000000</ForeColor>
        <HasDefaultColors>true</HasDefaultColors>
        <NextAvailableStatuses>1,5,6,17,18,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>19</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FF87CC9C</BackColor>
        <Description>D2</Description>
        <ForeColor>#FF066AD0</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,2,5,11,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>20</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFFFFA9C</BackColor>
        <Description>E2</Description>
        <ForeColor>#FF066AD0</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,2,5,10,11,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
    <d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
      <d2p1:Key>21</d2p1:Key>
      <d2p1:Value>
        <BackColor>#FFF0A3A3</BackColor>
        <Description>A2</Description>
        <ForeColor>#FF066AD0</ForeColor>
        <HasDefaultColors>false</HasDefaultColors>
        <NextAvailableStatuses>1,2,5,9,11,12,19,20,21</NextAvailableStatuses>
      </d2p1:Value>
    </d2p1:KeyValueOfintStatusInfoSNBYj_Sg1>
  </cadStatus>
</CADListDataInformation>