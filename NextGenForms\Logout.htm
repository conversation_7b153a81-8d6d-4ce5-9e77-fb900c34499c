﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Loginy</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />
   
    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />

    <script src="jquery.min.js" type="text/javascript"></script>
    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">
        $(document).ready(function () {

           

            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }

                return false;

            });

            ////focus on password field
            $("#password").focus();
        });

        function AfterFillForm() {

           // M.updateTextFields();

            $('input.autocomplete').autocomplete({
                data: {
                },
            });

            $('input.autocomplete').on('input', function () {
                GetDataList($(this));
            });
        }

    </script>

</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper" >
                    <img style="display:block;margin-left:auto;margin-right:auto" src="logo.png"/>
                </div>
            </div>
        </div>
    </div>
    <form class="main" action="Logout.aspx?queryfile=logout.qry" method="post" id="Form" name="Form">
        <div class="row" style="padding-top:20px">
            <div class="col s4"></div>
            <div class="col s4">
                <h5>Logout</h5>
            </div>
            <div class="col s4"></div>
        </div>
        <div class="row" style="padding-top:20px">
            <div class="col s4"></div>
            <div class="col s4">
                <h6>Are you sure you wish to log out of the current session?</h6>
            </div>
            <div class="col s4"></div>
        </div>

    </form>

</body>
</html>


