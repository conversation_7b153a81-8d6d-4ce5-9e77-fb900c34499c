<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Active Incidents Search</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Active Incidents Search</H4>
						<form action="ActiveIncidentsQuery.aspx?queryfile=activeincidents.qry" method="post" id="ActiveIncidentsQuery" name="ActiveIncidentsQuery">
							<table ID="Table2">
								<tr>
									<td colspan="2">Get all active incidents for the following hierarchy group:</td>
								</tr>
								<tr>
									<td>&nbsp;</td>
								</tr>
								<tr>
									<td id="labelJurisdictionOrSector"></td>
									<td>
										<SPAN type="selectlist" id="sectorvals">
											<XML id="sectorstyle" src="sectors.xsl"></XML>
											<XML id="sectorsource" src="sectors.xml"></XML>
										</SPAN>
									</td>
								</tr>
								<tr>
									<td>Division:</td>
									<td>
										<span id="divisionvals" type="selectlist" parent="sectorvals">
											<XML id="divisionsource" src="divisions.xml"></XML>
											<select id="divisions" name="divisions" >
											</select>
										</span>
									</td>
								</tr>
							</table>
							<br>
							<input type="hidden" name="level" id="level">
							<input type="hidden" name="itemid" id="itemid">
							<input type="submit" name="Query" id="Query" value="Query" onclick="setvalue()">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
	    var isUsingJurisdiction = true;
		function window.onload()
		{
			ActiveIncidentsQuery.Query.focus();

			// Note: setting savelast to true causes problems because the onchanged event doesn't get fired
			// and the divisions are out of synch with the secotors.
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			sectorvals.innerHTML = GenerateSelectBox("sector", sectorsource, sectorstyle, true, false, false, 1, false, false);

			// The root level .XSL file contains an onchange event defined for the 
			// root level select object.  It must be called on window loading.
			sectorchanged();

			// set the Jurisdiction/Sector label
			if (isUsingJurisdiction) {
			    document.getElementById("labelJurisdictionOrSector").innerHTML = "Jurisdiction:";
			}
			else {
			    document.getElementById("labelJurisdictionOrSector").innerHTML = "Sector:";
			}
		}

		function setvalue()
		{
			if (ActiveIncidentsQuery.divisions.selectedIndex == 0)
			{
				ActiveIncidentsQuery.itemid.value = ActiveIncidentsQuery.sector.options[ActiveIncidentsQuery.sector.selectedIndex].value;
				if (isUsingJurisdiction) 
                {
				    ActiveIncidentsQuery.level.value = 'jurisdiction';
				}
				else 
                {
				    ActiveIncidentsQuery.level.value = 'sector';
				}
			}
			else
			{
				ActiveIncidentsQuery.itemid.value = ActiveIncidentsQuery.divisions.options[ActiveIncidentsQuery.divisions.selectedIndex].value;
				ActiveIncidentsQuery.level.value = "division";
			}
		}

		function sectorchanged()
		{
			// Non-leaf items will have a 'Changed' event.  
			// These events will contain the following code:
			
			// This gets the newly selected value from the changed select control.
			var sectorID = ActiveIncidentsQuery.sector.options[ActiveIncidentsQuery.sector.selectedIndex].value;

			// This line gets all elements that have a 'foreign key' of the newly
			// selected value.
			var nodes = divisionsource.XMLDocument.documentElement.selectNodes("//divisions/item[parentlistid=" + sectorID + "]");
			
			// Clear the select combo that is to be repopulated.
			ActiveIncidentsQuery.divisions.options.length = 0;

			// Repopulate the select combo with the desired data from the retrieve nodes..
			var oOption = document.createElement("OPTION");
			ActiveIncidentsQuery.divisions.options.add(oOption, 0);
			oOption.innerText = "Include All";
			oOption.value = '0';
			for(var i=0; i<nodes.length; i++)
			{
				var oOption = document.createElement("OPTION");
				ActiveIncidentsQuery.divisions.options.add(oOption, 1);
				oOption.value = nodes[i].selectSingleNode("id").text;
				oOption.innerText = nodes[i].selectSingleNode("description").text;
			}
		}

	</script>
</HTML>
