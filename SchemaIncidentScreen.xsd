<?xml version="1.0" encoding="utf-8" ?>
<xs:schema id="SchemaIncidentScreen" targetNamespace="http://tempuri.org/SchemaIncidentScreen.xsd" elementFormDefault="qualified" xmlns="http://tempuri.org/SchemaIncidentScreen.xsd" xmlns:mstns="http://tempuri.org/SchemaIncidentScreen.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:simpleType name="SystemColorType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ActiveBorder" />
			<xs:enumeration value="ActiveCaption" />
			<xs:enumeration value="ActiveCaptionText" />
			<xs:enumeration value="AppWorkspace" />
			<xs:enumeration value="Control" />
			<xs:enumeration value="ControlDark" />
			<xs:enumeration value="ControlDarkDark" />
			<xs:enumeration value="ControlLight" />
			<xs:enumeration value="ControlLightLight" />
			<xs:enumeration value="ControlText" />
			<xs:enumeration value="Desktop" />
			<xs:enumeration value="GrayText" />
			<xs:enumeration value="Highlight" />
			<xs:enumeration value="HighlightText" />
			<xs:enumeration value="HotTrack" />
			<xs:enumeration value="InactiveBorder" />
			<xs:enumeration value="InactiveCaption" />
			<xs:enumeration value="InactiveCaptionText" />
			<xs:enumeration value="Info" />
			<xs:enumeration value="InfoText" />
			<xs:enumeration value="Menu" />
			<xs:enumeration value="MenuText" />
			<xs:enumeration value="ScollBar" />
			<xs:enumeration value="Window" />
			<xs:enumeration value="WindowFrame" />
			<xs:enumeration value="WindowText" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ColorType">
		<xs:sequence>
				<xs:element name="SystemColor" type="SystemColorType" minOccurs="0" maxOccurs="1" />
				<xs:element name="Color" type="xs:int" minOccurs="0" maxOccurs="1" />
				<xs:element name="ColorRed" type="xs:unsignedByte" minOccurs="0" maxOccurs="1" />
				<xs:element name="ColorGreen" type="xs:unsignedByte" minOccurs="0" maxOccurs="1" />
				<xs:element name="ColorBlue" type="xs:unsignedByte" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FontType">
		<xs:sequence>
			<xs:element name="FamilyName" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Size" type="xs:float" minOccurs="1" maxOccurs="1" />
			<xs:element name="Bold" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="Italic" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="Underline" type="xs:boolean" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AnchorType">
		<xs:sequence>
			<xs:element name="Top" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="Left" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="Right" type="xs:boolean" minOccurs="0" maxOccurs="1" />
			<xs:element name="Bottom" type="xs:boolean" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="TextAlignType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="BottomCenter" />
			<xs:enumeration value="BottomLeft" />
			<xs:enumeration value="BottomRight" />
			<xs:enumeration value="MiddleCenter" />
			<xs:enumeration value="MiddleLeft" />
			<xs:enumeration value="MiddleRight" />
			<xs:enumeration value="TopCenter" />
			<xs:enumeration value="TopLeft" />
			<xs:enumeration value="TopRight" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BorderStyleType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Fixed3D" />
			<xs:enumeration value="FixedSingle" />
			<xs:enumeration value="None" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ControlType">
		<xs:sequence>
			<xs:element name="Type" minOccurs="1" maxOccurs="1">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="incidentid" />
						<xs:enumeration value="timestamp" />
						<xs:enumeration value="priority" />
						<xs:enumeration value="sector" />
						<xs:enumeration value="incident_number" />
						<xs:enumeration value="case_number" />
						<xs:enumeration value="incident_type" />
						<xs:enumeration value="premise_info" />
						<xs:enumeration value="prem_hist" />
						<xs:enumeration value="attachment" />
						<xs:enumeration value="problem_nature" />
						<xs:enumeration value="location_name" />
						<xs:enumeration value="crossing_street" />
						<xs:enumeration value="address" />
						<xs:enumeration value="apartment" />
						<xs:enumeration value="city" />
						<xs:enumeration value="state" />
						<xs:enumeration value="zip" />
						<xs:enumeration value="hazards" />
						<xs:enumeration value="caution_notes" />
						<xs:enumeration value="caller_name" />
						<xs:enumeration value="caller_address" />
						<xs:enumeration value="caller_phone" />
						<xs:enumeration value="assigned_units" />
						<xs:enumeration value="assigned_other" />
						<xs:enumeration value="OtherField" />
						<xs:enumeration value="LabelControl" />
						<xs:enumeration value="PreplanButton" />
						<xs:enumeration value="SelfAssignButton" />
						<xs:enumeration value="AddCommentButton" />
						<xs:enumeration value="RefreshButton" />
						<xs:enumeration value="MapItButton" />
						<xs:enumeration value="IncidentZoomButton" />
						<xs:enumeration value="AutoZoomButton" />
						<xs:enumeration value="DrivingDirectionsButton" />
						<xs:enumeration value="ClearNotificationButton" />
			            		<xs:enumeration value="ReadIncidentButton" />
			            		<xs:enumeration value="GenerateAFRButton" />
						<xs:enumeration value="MessageToUnitsButton" />
						<xs:enumeration value="SupplementButton" />
          </xs:restriction>
				</xs:simpleType>
			</xs:element>
			
			<!-- for buttons, and labels -->
			<xs:element name="Text" type="xs:string" minOccurs="0" maxOccurs="1" />
			
			<!-- for OtherFields -->
			<xs:element name="Name" type="xs:string" minOccurs="0" maxOccurs="1" />

      <!-- for buttons, and fields -->
      <!-- For buttons: saying the command will press the button. For fields: saying the command will mkae the field visibble, and read its value -->
      <xs:element name="VoiceCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />

      <!-- for ReadIncidentButton -->
      <!-- When ReadIncidentButton is pressed, ReadIncident string will be read. -->
      <!-- To insert field values into this string use {FiledName}. -->
      <!-- For example: "{problem_nature} at {address}. Primary TAC Channel is {Primary TAC Channel}. -->
      <xs:element name="ReadIncidentText" type="xs:string" minOccurs="0" maxOccurs="1" />

      <!-- for any control -->
			<xs:element name="Left" type="xs:unsignedShort" minOccurs="1" maxOccurs="1" />
			<xs:element name="Top" type="xs:unsignedShort" minOccurs="1" maxOccurs="1" />
			<xs:element name="Width" type="xs:unsignedShort" minOccurs="1" maxOccurs="1" />
			<xs:element name="Height" type="xs:unsignedShort" minOccurs="1" maxOccurs="1" />
			<xs:element name="BackColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="ForeColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="Font" type="FontType" minOccurs="0" maxOccurs="1" />
			<xs:element name="Anchor" type="AnchorType" minOccurs="0" maxOccurs="1" />
			
			<!-- for fields and labels -->
			<xs:element name="TextAlign" type="TextAlignType" minOccurs="0" maxOccurs="1" />
			<xs:element name="BorderStyle" type="BorderStyleType" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TabPageType">
		<xs:sequence>
      <xs:element name="Text" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="BackColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="ForeColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="Font" type="FontType" minOccurs="0" maxOccurs="1" />
			<xs:element name="DefaultTabPage" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="SendActivityLog" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="VoiceCommand" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="Control" type="ControlType" minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TabControlType">
		<xs:sequence>
			<xs:element name="Left" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" />
			<xs:element name="Top" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" />
			<xs:element name="Width" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" />
			<xs:element name="Height" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" />
			<!--DDD
			<xs:element name="BackColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="ForeColor" type="ColorType" minOccurs="0" maxOccurs="1" />
			-->
			<xs:element name="Font" type="FontType" minOccurs="0" maxOccurs="1" />
			<xs:element name="Anchor" type="AnchorType" minOccurs="0" maxOccurs="1" />
			<xs:element name="TabPage" type="TabPageType" minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:element name="IncidentScreen">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="BackColor" type="ColorType" minOccurs="0" maxOccurs="1" />
				<xs:element name="ForeColor" type="ColorType" minOccurs="0" maxOccurs="1" />
				<xs:element name="Height" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" />
				<xs:element name="Font" type="FontType" minOccurs="0" maxOccurs="1" />
				<xs:element name="Control" type="ControlType" minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="TabControl" type="TabControlType" minOccurs="1" maxOccurs="1" />
				<xs:element name="CommentHeaderFont" type="FontType" minOccurs="0" maxOccurs="1" />
				<xs:element name="CommentFont" type="FontType" minOccurs="0" maxOccurs="1" />
			</xs:sequence>	
		</xs:complexType>
	</xs:element>
</xs:schema>
