<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <title>VisiNET Mobile - Unit History Query</title>
    <link href="bigstyle.css" type="text/css" rel="stylesheet">
</head>
<body>
    <table class="base" cellpadding="10" align="center" border="0" id="Table1">
        <tbody>
            <tr>
                <td valign="top">
                    <h4 align="center">Unit History Query</h4>
                    <form action="UnitHistory.aspx?queryfile=UnitHistory.qry" method="post" id="UnitHistory" name="UnitHistory">
                        Search for unit history by the following criteria:
                        <p>
                            <br>
                        </p>
                        <table id="Table2">
                            <tr>
                                <td valign="top"><b>Radio / Unit Name:</b></td>
                                <td>
                                    <xml id="radiostyle" src="configurationunits.xsl"></xml>
                                    <span type="selectlist" id="radiovals" name="radiovals">
                                        <xml id="radiosource" src="../Configuration_Units.xml"></xml>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                            </tr>
                            <tr><td><b>Date:</b></td></tr>
                            <tr>
                                <td>From <input type="text" name="startdate" size="11" id="startdate"><a href="javascript:void(0)" onclick='calendar(document.UnitHistory.startdate, "date");' hidefocus><img name="popcal1" id="popcal1" src="calbtn.gif" width="34" height="22" border="0" alt=""></a></td>
                                <td>&nbsp;&nbsp;&nbsp;Start time (HH:MM): <input type="text" name="starttime" size="5" maxlength="5" id="starttime"></td>
                            <tr>
                            <tr>
                                <td>To &nbsp;&nbsp;&nbsp;&nbsp;<input type="text" name="enddate" size="11" id="enddate"><a href="javascript:void(0)" onclick='calendar(document.UnitHistory.enddate, "date");' hidefocus><img name="popcal1" id="popcal1" src="calbtn.gif" width="34" height="22" border="0" alt=""></a></td>
                                <td>&nbsp;&nbsp;&nbsp;End time (HH:MM):&nbsp;&nbsp;<input type="text" name="endtime" size="5" maxlength="5" id="endtime"></td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td><b>Records return per page</b></td>
                                <td>
                                    <span type="selectlist">
                                        <select id="RowsPerPage" name="RowsPerPage" style="background-color:lightblue;">
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </span>
                                </td>
                            </tr>

                        </table>
                        <br>
                        <input type="hidden" name="PageNumber" id="PageNumber" value="1" size="1">
                        <input type="hidden" id="datetimestyle" value="101">
                        <input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
                        <input style="width:0px;" tabindex=-1 type="submit" name="Submit" id="Submit" value="Submit">
                    </form>
                </td>
            </tr>
        </tbody>

    </table>
    <!--  PopCalendar(tag name and id must match) Tags should sit at the page bottom -->
    <iframe width="168" height="190" name="gToday:normal:agenda.js" id="gToday:normal:agenda.js" src="ipopeng.htm" scrolling="no" frameborder="0" style="border:2px ridge; visibility:visible; z-index:999; position:absolute; left:-500px; top:0px;"></iframe>
</body>
	</body>
<script language="VBScript" src="clientutilities.vbs"></script>
<script src="clientutilities.js"></script>
<script language="javascript">
    function window.onload()
    {
        // Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
        radiovals.innerHTML = GenerateSelectBox("radioname", radiosource, radiostyle, true, false, false, 1, false, false);
        UnitHistory.radioname.focus();

        var today = new Date();
        var yesterday = new Date();
        var hourstr;
        var minutestr;
        Date.addDate(yesterday, Date.DAY, -1);


        UnitHistory.startdate.value = FormatDate(yesterday);
        UnitHistory.enddate.value = FormatDate(today);


        /* UnitHistory.startdate.value = (yesterday.getMonth() + 1) + '/' + yesterday.getDate() + '/' + yesterday.getYear();
        UnitHistory.enddate.value = (today.getMonth() + 1) + '/' + today.getDate() + '/' + today.getYear(); */

        if (today.getHours().toString().length == 1)
            hourstr = '0' + today.getHours().toString();
        else
            hourstr = today.getHours().toString();

        if (today.getMinutes().toString().length == 1)
            minutestr = '0' + today.getMinutes().toString();
        else
            minutestr = today.getMinutes().toString();
        UnitHistory.starttime.value = hourstr + ':' + minutestr;
        UnitHistory.endtime.value = hourstr + ':' + minutestr;
    }

    function calendar(ctrl, formattype)
    {
        gfPop.fPopCalendar(ctrl);
        return false;
    }

    function checkdates()
    {
        if (!isDate(UnitHistory.startdate.value))
        {
            UnitHistory.startdate.focus();
            return false;
        }
        if (!isDate(UnitHistory.enddate.value))
        {
            UnitHistory.enddate.focus();
            return false;
        }
        return true;
    }

    function checkTime()
    {
        if (!isTime(UnitHistory.starttime.value))
        {
            UnitHistory.starttime.focus();
            return false;
        }
        if (!isTime(UnitHistory.endtime.value))
        {
            UnitHistory.endtime.focus();
            return false;
        }
        return true;
    }
    function window.validatepage()
    {
        if (checkdates() && checkTime())
        {
            UnitHistory.startdate.value = FormatToStandardDate(UnitHistory.startdate.value);
            UnitHistory.enddate.value = FormatToStandardDate(UnitHistory.enddate.value);
			UnitHistory.PageNumber.value = 1;
			//document.getElementById("PageNumber").value = 1;
            UnitHistory.Submit.click();
        }
    }



</script>
</html>
