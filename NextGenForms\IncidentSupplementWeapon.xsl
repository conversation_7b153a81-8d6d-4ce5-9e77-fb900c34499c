<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>Mobile Enterprise - Incident Supplement Weapon</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Supplement Weapon</H4>
						<P><h3>Weapon Supplement Info for Incident Number : <xsl:value-of select="/results/exemel/NewDataSet/Table/IncidentNumber"/></h3>

	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
							
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
		
	<table cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;color:white;border-width:1px;border-style:None;">
		<tr style="font-weight:bold;">	
			<xsl:if test="Make != ''">
				<td>Make</td><td style="color:windowtext;background-color:window;">
				<xsl:value-of select="Make"/></td>
			</xsl:if>
			<xsl:if test="WeaponType != ''">
				<td>Weapon Type</td><td style="color:windowtext;background-color:window;">
				<xsl:value-of select="WeaponType"/></td>
			</xsl:if>			
		</tr>	
		<tr style="font-weight:bold;">
			
			<xsl:if test="Caliber != ''">
				<td>Caliber</td><td style="color:windowtext;background-color:window;">
				<xsl:value-of select="Caliber"/></td>
			</xsl:if>
			<xsl:if test="SerialNumber != ''">
				<td>Serial Number</td><td style="color:windowtext;background-color:window;">
				<xsl:value-of select="SerialNumber"/></td>
			</xsl:if>
		</tr>		
	</table>
	<p></p>

</xsl:template> 

</xsl:transform>