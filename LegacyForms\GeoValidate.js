/* geovalidation.js
 *
 * This file defines shared code to handle geovalidation for
 * the following forms:
 * 		Traffic Stop
 * 		Nature Unknown
 * 		On Site
 * 		Subject Stop
 * 		View Incident
 *		Modify Incident Address
 */

var geoFindPlacesResultsElementId = "VerifyLocationResults";
var geoAddressListItemElementIdPrefix = "addressListItem";
var geoVerifiedLocationsListElementId = "verifiedLocationsList";
var geoVerifiedLocationsListCountElementId = "lblVerifiedLocationsListCount";
var geoFindPlacesIndexElementId = "SelectedAddressIndex";
var geoVerifyLocationButtonElementId = "verifyLocationButton";
var geoverifyLocationButtonMoreElementId = "verifyLocationButtonMore";
var geoSelectedAddressItemBackgroundColor = "Highlight";
var geoSelectedAddressItemTextColor = "White";
var geoCurrentLatLonFunctionParams = "Latitude=curlat&Longitude=curlong";
var geoSelectedLatLonFunctionParams = "Latitude=sellat&Longitude=sellong";
var geoAddressCityFunctionParams = "Address=Address&City=City&VerifyMoreN=VerifyMoreN";
var geoExecuteFunctionAttributeName = "executefunction";
var geoExecuteFunctionAttributeValue = "VerifyLocation";
var geoExecuteFunctionParamsAttributeName = "parameters";
var geoVerifiedLocationListItemClassName = "verifiedLocationListItem";
var geoCurLatElementId = "curlat";
var geoCurLonElementId = "curlong";
var geoSelLatElementId = "sellat";
var geoSelLonElementId = "sellong";
var geoAddressElementId = "Address";
var geoAddressesDelimiter = "###";
var verifyLocationResultsMoreN = "VerifyMoreN";
var verifyLocationResultsMaxReturned = "VerifyMaxResultsReturned";

function clearHiddenAddressResults()
{
    try
    {
        document.getElementById(geoFindPlacesResultsElementId).value = "";
    }
    catch (err) { }
}

// should be called within OnAfterFormFill
function populateListOfAddressResults()
{
    try
    {
        var parseField = document.getElementById(geoFindPlacesResultsElementId).value;
        var select = document.getElementById(geoVerifiedLocationsListElementId);
        var lbl = document.getElementById(geoVerifiedLocationsListCountElementId);
        clearListOfAddresses();

        var hasAddresses = false;

        // loop through the results and create a new entry for each one
        if (parseField !== "")
        {
            var listOfAddresses = parseField.split(geoAddressesDelimiter);

            var maxedResultsTxt = document.getElementById(verifyLocationResultsMaxReturned);
            var maxResults = (maxedResultsTxt != null && maxedResultsTxt.value.toLowerCase() == "true");

            hasAddressesButNotMax = listOfAddresses.length != 0 && !maxResults;

            for (var i = 0; i < listOfAddresses.length; i++)
            {
                var listItem = document.createElement("li");
                listItem.id = geoAddressListItemElementIdPrefix + i;
                listItem.className = geoVerifiedLocationListItemClassName;

                if (listOfAddresses[i] == "No results")
                {
                    listItem.style.cursor = "default";
                }
                else
                {
                    listItem.onclick = addressListItemClick;
                }

                listItem.appendChild(document.createTextNode(listOfAddresses[i]));
                select.appendChild(listItem);
            }

            if (lbl != null)
            {
                if (listOfAddresses.length === 1)
                    lbl.innerText = listOfAddresses.length + " record found";
                else
                    lbl.innerText = listOfAddresses.length + " records found";
            }
        }

        moreButtonDisable(!(hasAddressesButNotMax && Form.addresslocation.checked));
    }
    catch (err) { }
}

function clearListOfAddresses()
{
    document.getElementById(geoVerifiedLocationsListElementId).innerHTML = "";
    document.getElementById(geoFindPlacesIndexElementId).value = -1;

    var lbl = document.getElementById(geoVerifiedLocationsListCountElementId);
    if (lbl != null)
    {
        lbl.innerText = "0 records found";
    }
}

function addressListItemClick()
{
    var idxHiddenField = document.getElementById(geoFindPlacesIndexElementId);
    var oldIdx = idxHiddenField.value;
    var newIdx = parseInt(this.id.replace(geoAddressListItemElementIdPrefix, ""));

    deselectCurrentAddressListItem();

    if (oldIdx != newIdx)
    {
        this.style.backgroundColor = geoSelectedAddressItemBackgroundColor;
        this.style.color = geoSelectedAddressItemTextColor;
        idxHiddenField.value = newIdx;

        if (!Form.verifiedlocation.checked)
        {
            Form.verifiedlocation.checked = true;
            radioChange();
        }
    }
}

function deselectCurrentAddressListItem()
{
    var idxHiddenField = document.getElementById(geoFindPlacesIndexElementId);
    var currentIdx = idxHiddenField.value;

    try
    {
        var currentSelection = document.getElementById(geoAddressListItemElementIdPrefix + currentIdx);
        currentSelection.style.backgroundColor = "";
        currentSelection.style.color = "";
    }
    catch (err) { }

    idxHiddenField.value = -1;
}

function radioChange()
{
    if (!Form.verifiedlocation.checked)
    {
        // disable verify button if they have selected that option
        // clear list so it is empty after reverse geocode function executes
        clearHiddenAddressResults();
        clearListOfAddresses();
        var verifyButton = document.getElementById(geoVerifyLocationButtonElementId);
        verifyButton.disabled = false;
        verifyButton.setAttribute(geoExecuteFunctionAttributeName, geoExecuteFunctionAttributeValue);
        Form.verifiedlocation.disabled = true;
    }
    else
    {
        // current, selected, or address is selected, enable the verify button
        var verifyButton = document.getElementById(geoVerifyLocationButtonElementId);
        verifyButton.disabled = true;
        verifyButton.setAttribute(geoExecuteFunctionAttributeName, "");
        Form.verifiedlocation.disabled = false;
    }

    moreButtonDisable(true);

    var textboxMore = document.getElementById(verifyLocationResultsMoreN);
    if (textboxMore != null)
    {
        textboxMore.value = "0";
    }
}

function moreButtonDisable(disable)
{
    var moreButton = document.getElementById(geoverifyLocationButtonMoreElementId);
    if (moreButton == null) return;

    moreButton.disabled = disable;

    if (disable)
    {
        moreButton.setAttribute(geoExecuteFunctionAttributeName, "");
    }
    else
    {
        moreButton.setAttribute(geoExecuteFunctionAttributeName, geoExecuteFunctionAttributeValue);
    }
}

function verifyLocationButtonKeyDown(event)  //backwards compability for custom html pages by customers
{
    verifyLocationButtonKeyDown(event, false);
}

function verifyLocationButtonKeyDown(event, moreClicked)
{
    try
    {
        // if space button is being pressed, execute as if the button was clicked
        if (event.keyCode === 32)
        {
            verifyLocationButtonClicked(moreClicked);
        }
    }
    catch (err) { }
}

function verifyLocationButtonClicked() //backwards compability for custom html pages by customers
{
    verifyLocationButtonClicked(false);
}

function verifyLocationButtonClicked(moreClicked)
{
    try
    {
        var moreButton = document.getElementById(geoverifyLocationButtonMoreElementId);
        if (moreClicked && moreButton != null && moreButton.disabled)
        {
            return false;
        }

        clearListOfAddresses();

        var moreN = 0;
        var textboxMore = document.getElementById(verifyLocationResultsMoreN);
        if (textboxMore != null)
        {
            if (moreClicked != null && moreClicked === true)
            {
                var valText = textboxMore.value;
                if (valText == null)
                {
                    moreN = 0;
                }
                else
                {
                    moreN = parseInt(valText)
                }

                moreN++;
            }
            textboxMore.value = moreN; //If moreclicked=false/null, then resets to 0, aka "verify" was clicked. Otherwise stores ++ value. 
        }

        if (Form.verifiedlocation.checked)
        {
            alert("Please select one of the locations to verify first.");
        }
        else
        {
            var listItem = document.createElement("li");
            listItem.style.listStyleType = "none";
            listItem.appendChild(document.createTextNode("Loading..."));
            document.getElementById(geoVerifiedLocationsListElementId).appendChild(listItem);

            var verifyButton = document.getElementById(geoVerifyLocationButtonElementId);
            var params = "";

            // set the parameters for executefunction based on what is selected
            // these are the same for all the forms
            if (Form.currentlocation.checked)
            {
                // remove the Loading... label if validation fails, the executefunction handler will display a message
                if (document.getElementById(geoCurLatElementId).value == '' ||
                    document.getElementById(geoCurLonElementId).value == '' ||
                    document.getElementById(geoCurLatElementId).value == 0 ||
                    document.getElementById(geoCurLonElementId).value == 0)
                {
                    clearListOfAddresses();
                }

                params = geoCurrentLatLonFunctionParams;
            }
            else if (Form.selectedlocation.checked)
            {
                // remove the Loading... label if validation fails, the executefunction handler will display a message
                if (document.getElementById(geoSelLatElementId).value == '' ||
                    document.getElementById(geoSelLonElementId).value == '' ||
                    document.getElementById(geoSelLatElementId).value == 0 ||
                    document.getElementById(geoSelLonElementId).value == 0)
                {
                    clearListOfAddresses();
                }

                params = geoSelectedLatLonFunctionParams;
            }
            else if (Form.addresslocation.checked)
            {
                // remove the Loading... label if validation fails, the executefunction handler will display a message
                if (document.getElementById(geoAddressElementId).value.length <= 0)
                {
                    clearListOfAddresses();
                }

                params = geoAddressCityFunctionParams;
            }

            if (moreClicked && moreButton != null)
            {
                moreButton.setAttribute(geoExecuteFunctionParamsAttributeName, params);
            }
            else if (verifyButton != null)
            {
                verifyButton.setAttribute(geoExecuteFunctionParamsAttributeName, params);
            }
        }
    }
    catch (err) { alert("Error occurred trying to verify location."); }
}