<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
        <TITLE>Mobile Enterprise - Incident Summary Information</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Summary Query</H4>
						<form action="IncidentSummaryQuery.aspx?queryfile=IncidentSummaryQuery.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td colspan=2>Note: The use of partial is acceptable</td>
								</tr>
								<tr>
									<td>Incident #:</td>
									<td><input size=25 type="text" name="IncidentNumber" id="IncidentNumber"></td>
								</tr>
								<tr>
									<td>Unit Name:</td>
									<td><input size=25 type="text" name="unitname" id="unitname"></td>
								</tr>
								<tr>
									<td>Date From:</td>
									<td><input type="text" name="startdate" size="10" maxlength=10 ID="startdate"><a href="javascript:void(0)" onclick='calendar(document.Form.startdate, "date");' HIDEFOCUS><img name="popcal1" id="popcal1" src="calbtn.gif" width="34" height="22" border="0" alt=""></a></td>
								</tr>
								<tr>
									<td>Start Time:</td>
									<td><input type="text" name="starttime" size="5" maxlength=5 ID="starttime"> (HH:MM)</td>
								</tr>
								<tr>
									<td>Date To:</td>
									<td><input type="text" name="enddate" size="10" maxlength=10 ID="enddate"><a href="javascript:void(0)" onclick='calendar(document.Form.enddate, "date");' HIDEFOCUS><img name="popcal1" id="popcal1" src="calbtn.gif" width="34" height="22" border="0" alt=""></a></td>
								</tr>
								<tr>
									<td>End Time:</td>
									<td><input type="text" name="endtime" size="5" maxlength=5 ID="endtime"> (HH:MM)</td>
								</tr>
							</table>
		<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
		<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
		<!--  PopCalendar(tag name and id must match) Tags should sit at the page bottom -->
		<iframe width="168" height="190" name="gToday:normal:agenda.js" id="gToday:normal:agenda.js" src="ipopeng.htm" scrolling="no" frameborder="0" style="border:2px ridge; visibility:visible; z-index:999; position:absolute; left:-500px; top:0px;"></iframe>
	</body>
	<script LANGUAGE="VBScript" src="clientutilities.vbs"></script>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
		function window.onload()
		{
			Form.unitname.focus();

			var today = new Date();
			var yesterday = new Date();
			var hourstr;
			var minutestr;
			Date.addDate(yesterday, Date.DAY, -1);

			Form.startdate.value = FormatDate(yesterday);
			Form.enddate.value = FormatDate(today);
		}

		function calendar(ctrl, formattype)
		{
			gfPop.fPopCalendar(ctrl);
			return false;
		}

		function checkdates()
		{
			if (!isDate(Form.startdate.value))
			{
				Form.startdate.focus();
				return false;
			}
			if (!isDate(Form.enddate.value))
			{
				Form.enddate.focus();
				return false;
			}
			return true;
		}

		function checkTime()
		{
			if (!isTime(Form.starttime.value))
			{
				Form.starttime.focus();
				return false;
			}
			if (!isTime(Form.endtime.value))
			{
				Form.endtime.focus();
				return false;
			}
			return true;
		}
		function window.validatepage()
		{
			if (checkdates() && checkTime())
			{
			    Form.startdate.value = FormatToStandardDate(Form.startdate.value);
			    Form.enddate.value = FormatToStandardDate(Form.enddate.value);
				Form.Submit.click();
			}
		}

	</SCRIPT>
</HTML>
