<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <title>Mobile Enterprise - Add Incident Comment</title>
    <link href="bigstyle.css" type="text/css" rel="stylesheet">
</head>
<body>
    <table class="base" cellpadding="10" align="center" border="0" id="Table1">
        <tbody>
            <tr>
                <td valign="top">
                    <h4 align="center">Add Incident Comment</h4>
                    <form action="AddIncidentComment.aspx?queryfile=AddIncidentComment.qry" method="post" id="Form" name="Form">
                        <table id="Table2">
                            <tr>
                                <td>
                                    <input style="width:500px;" type="text" name="IncidentNumber" id="IncidentNumber" readonly>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input style="width:500px;" type="text" name="Problem" id="Problem" readonly>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input style="width:500px;" type="text" name="Address" id="Address" readonly>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <textarea style="width:500px;" size="8" rows="8" name="Comment" id="Comment" mandatory="true" onkeyup="CheckTextAreaLength(this.form.Comment,500);"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type=checkbox id="IsConfidentialInternal" name="IsConfidentialInternal">Confidential
                                </td>
                            </tr>
                        </table>
                        <br>
                        <input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()" />
                        <input type="hidden" id="IncidentID" name="IncidentID" />
                        <input type="hidden" id="IsConfidential" name="IsConfidential" />
                        <input style="width:0px;" tabindex=-1 type="submit" name="Submit" id="Submit" value="Submit" />
                    </form>
                </td>
            </tr>
        </tbody>
    </table>
</body>
<script src="clientutilities.js"></script>
<script language="javascript">

    function window.onload()
    {
        PrepareValidation(Form);
        Form.Comment.focus();
    }

    function window.validatepage()
    {
        if (!Form.IncidentID.value)
        {
            alert('Cannot submit the form. IncidentID is not provided.');
            return;
        }
        if (Form.Comment.value == '')
        {
            alert('Comment is required.');
            Form.Comment.focus();
            return;
        }

        // force a True or False value into IsConfidentail.
        // note: if you want to remove the Confidetial Checkbox and let the system use the default configuration, then remove the code below. Also remove the CheckBox from the htm.
        if(Form.IsConfidentialInternal.checked)
        {
            Form.IsConfidential.value = "True";
        }
        else
        {
            Form.IsConfidential.value = "False";
        }

        Form.Submit.click();
    }
</script>
</html>
