﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
        <TITLE>Mobile Enterprise - Unit TimeStamp</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">TimeStamp</H4>
						<form action="SetUnitTimeStamp.aspx?queryfile=SetUnitTimeStamp.qry" method="post" id="Form" name="Form">
							<table ID="Table2" align="center">
							    <tr>
									<td>
										<table>
											<td>
												Incident:
											</td>
											<td>
												<input style="width:0px;" type="text" name="IncidentID" id="IncidentID" readonly>
												<input type="text" name="IncidentNumber" id="IncidentNumber" readonly>
											</td>
										</table>
									</td>
								</tr>							
								<tr align="center">									
									<td>
										<XML id="usertimestyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="usertimevals" name="usertimevals">
											<XML id="usertimesource" src="UserTime.xml"></XML>
										</SPAN>
									</td>
								</tr>								
								<tr align="center">									
									<td>
							
							<input type="hidden" name="TimeStamp" id="TimeStamp">
							
							<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
							<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
									</td>
								</tr>
							</table>								
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script LANGUAGE="VBScript" src="clientutilities.vbs"></script>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
	    function window.onload()
	    {
	        // Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
	        usertimevals.innerHTML = GenerateSelectBox("UserTimeName", usertimesource, usertimestyle, true, false, false, 10, false, false);
			// When unit not assigned, default IncidentID is zero. 
			Form.IncidentID.value = "0";
	    }

	    function window.validatepage()
	    {
	        Form.TimeStamp.value = GetCurrentDate();
	        Form.Submit.click();
	    }
		
	</SCRIPT>
</HTML>
