<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Admin Reset Password</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="middle"><input type="button" value="Submit" onclick='Form.Query.click()'>&nbsp;&nbsp;&nbsp;Admin Reset Password</H4>
						<form action="AdminResetPassword.aspx?queryfile=AdminResetPassword.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td valign="top">Login Name:</td>
									<td>
										<XML id="userstyle" src="configurationusers.xsl"></XML>
										<SPAN type="selectlist" id="uservals" name="uservals">
											<XML id="usersource" src="../Configuration_Users.xml"></XML>
										</SPAN>

									</td>
								</tr>
								<tr>
									<td>Admin Password:</td>
									<td><input type="password" maxlength="100" name="AdminPassword" id="AdminPassword" mandatory="true"></td>
								</tr>
								<tr>
									<td>New Password:</td>
									<td><input type="password" maxlength="100" name="NewPassword" id="NewPassword" mandatory="true"></td>
								</tr>
								<tr>
									<td>New Password Retype:</td>
									<td><input type="password" maxlength="100" name="NewPasswordRetype" id="NewPasswordRetype" mandatory="true"></td>
								</tr>
							</table>
							
							<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
							<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
		function window.onload()
		{
			PrepareValidation(Form);
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			uservals.innerHTML = GenerateSelectBox("LoginName", usersource, userstyle, true, false, true, 1, false, false);
			Form.LoginName.focus();
		}

	function window.validatepage()
	{
		if (Form.NewPassword.value != Form.NewPasswordRetype.value)
		{
			alert("New Password Retype is different than New Password.");
			Form.NewPassword.focus();
		}
		else
		{
			Form.Submit.click();
		}
	}

	</SCRIPT>
</HTML>
