<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Clear All Assigned Units</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Clear All Units</H4>
						<form action="ClearAllUnitsQuery.aspx?queryfile=ClearAllUnits.qry" method="post" id="ClearAllUnitsQuery" name="ClearAllUnitsQuery">
							<table ID="Table2">								
							    <tr>
							        <td><b>Response Disposition:</b></td>
							    </tr>
                                <tr>
                                    <td><input type="hidden" name="problemnature" id="problemnature"></td>
                                </tr>
								<tr>
									<td>
										<!-- Root item requires a SPAN element to contain it. -->
										<SPAN type="selectlist" id="dispositionvals" name="dispositionvals">
										</SPAN>
									</td>
								</tr>																
							</table>
							<br>
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
							<input type="hidden" id="CurrentIncidentID" name="CurrentIncidentID">
							<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<script language="javascript">

        function OnAfterFormFill(){  
            
            var problemnature = document.getElementById("problemnature").value;
            var styleAndSource = getDispositionStyleAndSource(problemnature);
            dispositionvals.innerHTML = GenerateListBox("ResponseDisposition", "8", styleAndSource.dispositionsource, styleAndSource.dispositionstyle, problemnature);
        
            ClearAllUnitsQuery.ResponseDisposition.focus();
        }

        function getDispositionStyleAndSource(problemnature) {
            if (problemnature) {
                return {
                    dispositionstyle: { src: "dispositionbyproblemnaturelist.xsl" },
                    dispositionsource: { src: "DispositionCodesByProblemNature.xml" }
                };
            } else {
                return {
                    dispositionstyle: { src: "genericlist.xsl" },
                    dispositionsource: { src: "DispositionCodes.xml" }
                };
            }
        }

	    function window.validatepage()
	    {
		    if (ClearAllUnitsQuery.CurrentIncidentID.value)
		    {
			    ClearAllUnitsQuery.Submit.click();
		    }
		    else
		    {
			    alert('Must be assigned to an incident to submit this form.');			
		    }
	    }
	</script>
</HTML>
