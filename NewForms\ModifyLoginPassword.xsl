<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Modify Login Password Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="ModifyLoginPassword.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Modify Login Password Results</H4>
	<xsl:apply-templates select="results/errormessage"/>
							<p>
<table>
	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
</table>
							</p>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
	<tr><td>
	<xsl:value-of select="Row"/>
	</td></tr>
</xsl:template> 

<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Modify Login Password Failed.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
