<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ESRI.ArcGIS.Client.Local</name>
    </assembly>
    <members>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.BaudRate">
            <summary>
            Used to specify connection speed of the serial port.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud1200">
            <summary>
            1200 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud2400">
            <summary>
            2400 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud4800">
            <summary>
            4800 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud9600">
            <summary>
            9600 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud14400">
            <summary>
            14400 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud19200">
            <summary>
            19200 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud38400">
            <summary>
            38400 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud56000">
            <summary>
            56000 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud57600">
            <summary>
            57600 baud
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.BaudRate.Baud115200">
            <summary>
            115200 baud
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher">
            <summary>
            Parse a file containing NMEA 0183 sentences with GPS data.
            </summary>
            <remarks>
            <para>This class uses an ASCII text file of stored NMEA 0183 sentences to
            replay GPS information. To receive GPS information, you must provide the class
            with a text file containing NMEA 0183 sentences (GGA, GSA, GSV, RMC and VTG
            sentence formatters are supported). Once a connection to this file is established, 
            the positional information is accessible through the NMEASentenceEventArgs class.
            </para>
            <para>Note that the FileGpsCoordinateWatcher will NOT automatically return back to the beginning 
            of the text file after it reaches the last line of the text unless the LoopPlayback property  
            is set to True (Default is False).</para>
            </remarks>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher">
            <summary>
            Abstract class for IGeoPositionWatcher.
            This abstract class serves as the base for the SerialPortGpsCoordinateWatcher and FileGpsCoordinateWatcher classes.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher._nativeDriverHandle">
            <summary>
            Native GPS driver handle.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Finalize">
            <summary>
            Destructor
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Start">
            <summary>
            Initiate the acquisition of location data.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Start(System.Boolean)">
            <summary>
            Initiate the acquisition of location data.
            </summary>
            <param name="suppressPermissionPrompt">This param is not used.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Stop">
            <summary>
            Invoked when close.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.TryStart(System.Boolean,System.TimeSpan)">
            <summary>
            Start acquiring location data, specifying an initialization timeout.
            </summary>
            <param name="suppressPermissionPrompt">This param is not used.</param>
            <param name="timeout">Time in milliseconds to wait for initialization to complete.</param>
            <returns>true if succeeded, false if timed out.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.RaisePositionChanged(System.DateTime,ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate)">
            <summary>
            Raises position changed event.
            </summary>
            <param name="dateTime">Date Time</param>
            <param name="geoCoordniate">EsriGeoCoordinate</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.RaiseStatusChanged(System.Device.Location.GeoPositionStatus)">
            <summary>
            Raises Status changed event.
            </summary>
            <param name="status">GeoPositionStatus</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.RaiseNmeaSentenceReceived(System.String)">
            <summary>
            Raises gps sentence received event.
            </summary>
            <param name="sentence">GPS sentence</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.RaiseSatellitesInViewChanged(System.Collections.Generic.Dictionary{System.Int32,ESRI.ArcGIS.Client.Local.Gps.Satellite})">
            <summary>
            Raises SatellitesInView changed event.
            </summary>
            <param name="satellites"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.LocationUpdateHandler(System.Int32)">
            <summary>
            Handles the location update callback from the native gps driver.
            </summary>
            <param name="changeType">update type</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.StatusUpdateHandler">
            <summary>
            Handles the status update callback from the native gps driver.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.UpdateSatellitesInfo(ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate)">
            <summary>
            Updates the satellites state info from the native gps driver.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.UpdateLocationInfo(ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate)">
            <summary>
            Updates the location state info from the native gps driver.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Dispose">
            <summary>
            Stops the GPS connection and disposes the object.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Dispose(System.Boolean)">
            <summary>
             Stops the GPS connection and disposes the object. 
            </summary>
            <param name="disposing">True is disposing</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.NotifyPropertyChanged(System.String)">
            <summary>
            Raised when porpety is changed.
            </summary>
            <param name="propertyName">Name of the Property to notify</param>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.NmeaSentenceReceived">
            <summary>Occurs when a new sentence is received.</summary>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.SatellitesinViewChanged">
            <summary>
            Raised when the number of Satellites in view changed.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.SatellitesInView">
            <summary>
            Gets the Dictionary of visible satellites.
            </summary>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.PositionChanged">
            <summary>
            Raised when a new position is acquired.
            </summary>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.StatusChanged">
            <summary>
            Raised when GPS connection status is changed.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Position">
            <summary>
            Gets the location data.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Status">
            <summary>
             The status of location data.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.NativeDriverHandle">
            <summary>
            Returns native driver pointer.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.Context">
            <summary>
            Gets the thread context in which this object was created from.
            </summary>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.Gps.GpsCoordinateWatcher.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher._filePath">
            <summary>
            String to hold the file name of the GPS information.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher._enabled">
            <summary>
            Designates if the GpsConnection is Enabled.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher._playbackInterval">
            <summary>
            Interval value used to vary the timer for the ReadLine.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher.Start">
            <summary>
            Initiate the acquisition of location data.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher.Start(System.Boolean)">
            <summary>
            Initiate the acquisition of location data.
            </summary>
            <param name="suppressPermissionPrompt">This param is not used.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher.TryStart(System.Boolean,System.TimeSpan)">
            <summary>
            Start acquiring location data, specifying an initialization timeout. This
            method returns synchronously.
            </summary>
            <param name="suppressPermissionPrompt">This param is not used.</param>
            <param name="timeout">Time in milliseconds to wait for initialization to complete.</param>
            <returns>true if succeeded, false if timed out.</returns>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher.Enabled">
            <summary>
            Gets or sets the Enabled state associated to the FileGpsConnection.
            </summary>
            <remarks>The default value is true.</remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher.Path">
            <summary>
            Gets or sets the string containing the file path information.
            </summary>
            <remarks>
            The associated file must contain NMEA 0183 sentences.  The GGA, GSA, 
            GSV, RMC and VTG sentence formatters are supported.  Sentences with other
            formatters can be present in the file. 
            </remarks>
            <exception cref="T:System.InvalidOperationException">Property cannot be changed if connection is open.</exception>
            <exception cref="T:System.ArgumentNullException">File doesn't exists</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher.PlaybackRate">
            <summary>
            Gets or sets the interval of which the FileGPSConnection reads sentence.
            </summary>
            <remarks>
            This interval upon elapse causes a timer to occur reading the next available line from the file.
            The default value is set to 1000 ms (1 sec).
            </remarks>
            <exception cref="T:System.ArgumentException">Timer interval value must be greater than zero.</exception>    
            <exception cref="T:System.InvalidOperationException">Property cannot be changed if connection is open.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.FileGpsCoordinateWatcher.LoopPlayback">
            <summary>
            Gets or sets the flag for looped playback of the file.
            </summary>
            <remarks>
            This flag indicates if playback of the file will loop to the begining when end of file will be reached.
            The default value is set to false.
            </remarks>
            <exception cref="T:System.InvalidOperationException">Property cannot be changed if connection is open.</exception>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.FixStatus">
            <summary>
            Fix quality of a position based on the quality indicator in a GGA NMEA sentence.
            </summary>
            <remarks>
            <para>The fix status indicates the type of signal or technique being used by the
            GPS receiver to determine its location. The fix status is important for the GPS
            consumer, as it indicates the quality of the signal, or the accuracy and
            reliability of the location being reported. The GPS receiver includes the fix
            status with the NMEA sentence broadcast</para>
            </remarks>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.Invalid">
            <summary>
            Invalid Gps fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.GpsFix">
            <summary>
            Gps fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.DifferentialGpsFix">
            <summary>
            Differential Gps fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.PrecisePositioningServiceFix">
            <summary>
            Precise Positioning Service Fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.RealTimeKinematic">
            <summary>
            Real Time Kinematic (RTK) fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.FloatRealTimeKinematic">
            <summary>
            Float Real Time Kinematic (Float RTK) fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.Estimated">
            <summary>
            Estimated fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.ManualInputMode">
            <summary>
            Manual fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.FixStatus.SimulationMode">
            <summary>
            Simulated fix.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.ERROR_INVALID_OPERATION">
            <summary>
            Error message when invalid operation occurs on the COM port
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.CheckResult(System.IntPtr)">
            <summary>
            Checks value returned from native SDC call. Raises last Win32 exception if error.
            </summary>
            <param name="resultValue">Pointer to check</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.CheckResult(System.Int32)">
            <summary>
            Checks value returned from native SDC call. Raises last Win32 exception if error.
            </summary>
            <param name="resultValue">Integer value to check</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.CheckResult(System.Boolean)">
            <summary>
            Checks value returned from native SDC call. Raises last Win32 exception if error.
            </summary>
            <param name="resultValue">Boolean value to check</param>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields">
            <summary>
            GpsLocationValidFields enumeration is used to identify valid values in GPSLocationInfo
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.Latitude">
            <summary>
            Latitude value is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.Longitude">
            <summary>
            Longitude value is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.Speed">
            <summary>
            Speed is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.Course">
            <summary>
            Course is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.MagneticCourse">
            <summary>
            Magnetic course is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.Altitude">
            <summary>
            Altitude is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.EllipsoidHeight">
            <summary>
            Ellipsoid height is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.PositionDop">
            <summary>
            PDOP value is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.HorizontalDop">
            <summary>
            HDOP value is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.VerticalDop">
            <summary>
            VDOP value is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsLocationValidFields.SatellitesUsed">
            <summary>
            Satellites used value is valid
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsStatus.CriticalFailure">
            <summary>
            Occurs on memory error (or any other critical error). In most cases GPSDriver should be recreated 
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsStatus.ParseFailure">
            <summary>
            Occurs on incorrect message format from GPS device (bad connection, data corrupted, incorrect port settings)
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsStatus.NotConnected">
            <summary>
            Occurs if GPSDriver failed to read from COM port(COM port HANDLE is not longer valid)
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsStatus.Timeout">
            <summary>
            Occurs if there are no gps messages from the port
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsStatus.OK">
            <summary>
            Driver receives valid GPS sentences
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsDateTimeType">
            <summary>
            Used to specify the way date and time values are set in the C++ library.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsDateTimeType.UtcDateTime">
            <summary>
            Time and date have been taken from GPS data
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsDateTimeType.SystemDateUtcTime">
            <summary>
            Time has been taken from GPS data but date has been set with system date
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.NativeMethods.GpsDateTimeType.SystemDateTime">
            <summary>
            Time and date have not been specified in GPS data so they have been set with system date/time
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate">
            <summary>
            GpsGeoCoordinate class extends the GeoCoordinate class to provide advanced Gps information
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.FixStatus">
            <summary>
            Indicates the current fix status of the signal
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.FixSatelliteCount">
            <summary>
            Indicates the current count of satellites acquired for the fix
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.CourseMagnetic">
            <summary>
            Gets the trend -or- course (magnetic) along the fix line (in degrees).
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.GeoidHeight">
            <summary>
            Indicates the geoid height of the fix
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.PositionDilutionOfPrecision">
            <summary>
            Indicates the Position Dilution of Precision of the fix
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.HorizontalDilutionOfPrecision">
            <summary>
            Indicates the Horizontal Dilution of Precision of the fix
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.VerticalDilutionOfPrecision">
            <summary>
            Indicates the Vertical Dilution of Precision of the fix
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.SatellitesUsedInFix">
            <summary>
            Gets the list of satellites available at a Location.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.GpsGeoCoordinate.PositionChangeType">
            <summary>
            The type of GPS change event.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.NmeaSentenceEventArgs">
            <summary>
            Contains National Marine Electronic Association sentences for GPS information.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.NmeaSentenceEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of NmeaSentenceEventArgs class.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.NmeaSentenceEventArgs.Sentence">
            <summary>
            Gets the sentence.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.PositionChangeType">
            <summary>Type of GPS change that occurs.</summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.PositionChangeType.NoChange">
            <summary>
            No change.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.PositionChangeType.Position">
            <summary>
            Position has changed.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.PositionChangeType.Altitude">
            <summary>
            Geoid or above the sea altitude has changed.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.PositionChangeType.CourseOrSpeed">
            <summary>
            Course or speed has changed.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.PositionChangeType.Satellites">
            <summary>
            Satellite's information has changed.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.PositionChangeType.Quality">
            <summary>
            The quality properties have changed (dilution of position).
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.PositionChangeType.FixStatus">
            <summary>
            the Fix status has changed.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.Satellite">
            <summary>
            Contains information about a particular satellite.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.Satellite.#ctor">
            <summary>
            Creates a new instance of Satellite class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.Satellite.#ctor(ESRI.ArcGIS.Client.Local.Gps.Satellite)">
            <summary>
            Creates a new instance of Satellite class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.Satellite.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a new instance of Satellite class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.Satellite.Clear">
            <summary>
            Clears the information about the satellite.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.Satellite.Id">
            <summary>
            Gets the satellite's identifier.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.Satellite.Elevation">
            <summary>
            Gets the elevation of the satellite in degrees from 0° to 90°.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.Satellite.Azimuth">
            <summary>
            Gets the azimuth of the satellite (0-359 degrees).
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.Satellite.SignalStrength">
            <summary>
            Gets the strength of the satellite's signal. Represents signal to noise ratio in
            dBHz (0 to around 35).
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.Satellite.SatelliteSystem">
            <summary>
            Gets the Satellite System based on ID.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.SatellitesInViewChangedEventArgs">
            <summary>
            SatellitesInViewChangedEventArgs class
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SatellitesInViewChangedEventArgs.#ctor(System.Collections.Generic.Dictionary{System.Int32,ESRI.ArcGIS.Client.Local.Gps.Satellite})">
            <summary>
            Creats a new instance
            </summary>
            <param name="satellitesInView">Dictioanry of Satellites In View</param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.SatellitesInViewChangedEventArgs.SatellitesInView">
            <summary>
            Gets the Dictionary of visible satellites.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.SatelliteSystem">
            <summary>
            Satellite system based on the ID
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.SatelliteSystem.Gps">
            <summary>
            Global postioning system
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.SatelliteSystem.Sbas">
            <summary>
            Satellite based augmentation system.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.SatelliteSystem.Glonass">
            <summary>
            Russian global navigation satellite system
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher">
            <summary>
            The SerialPortGpsCoordinateWatcher allows communication with a GPS receiver over a serial port using the NMEA standard.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.OpenPort">
            <summary>
            Creates native serial port handle with the specified port name and baud rate
            </summary>
            <exception cref="T:System.InvalidOperationException">Invalid operation on the COM port.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.FindGPSEnabledPort(System.Int32)">
             <summary>
             Searches for GPS enabled serial ports
             </summary>
            <param name="maxPort">Maximum no. of ports to scan. Default is 32</param>
             <returns>Returns the list of GPS enabled serial port names which can be used to start the GPS.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.Start">
            <summary>
            Initiate the acquisition of location data.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.Start(System.Boolean)">
            <summary>
            Initiate the acquisition of location data.
            </summary>
            <param name="suppressPermissionPrompt">This param is not used.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.TryStart(System.Boolean,System.TimeSpan)">
            <summary>
            Start acquiring location data, specifying an initialization timeout. This
            method returns synchronously.
            </summary>
            <param name="suppressPermissionPrompt">This param is not used.</param>
            <param name="timeout">Time in milliseconds to wait for initialization to complete.</param>
            <returns>true if succeeded, false if time out occured.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.Stop">
            <summary>
            Invoked when close.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.Finalize">
            <summary>
            Destructor
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.Dispose(System.Boolean)">
            <summary>
            Stops the serial port communication and disposes the object
            </summary>
            <param name="disposing">True if disposing</param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.NativePortHandle">
            <summary>
            Native port description handle
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.PortName">
            <summary>
            Gets or sets port name.
            Port name can be as follows:
            COMn - COM port (n - any number). E.g. "COM1", "COM5", or "COM999"
            </summary>
            <exception cref="T:System.ArgumentNullException">Port name is invalid, e.g. it is negative.</exception>
            <exception cref="T:System.InvalidOperationException">Property cannot be changed if connection is open.</exception>
            <exception cref="T:System.ArgumentException">The PortName property was set to a value that starts with "\\".</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.BaudRate">
            <summary>
            Gets or sets the serial baud rate.
            </summary>
            <remarks>
            The serial baud rate must be supported by the user's serial driver.  The default
            value is 4800 bits per second (bps).
            </remarks>
            <exception cref="T:System.InvalidOperationException">Property cannot be changed if connection is open.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The baud rate specified is less than or equal to zero, or is greater than the maximum allowable baud rate for the device.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.Parity">
            <summary>
            Gets or sets the parity-checking protocol.
            </summary>
            <remarks>
            Parity is an error-checking procedure in which the number of 1s must always be the same—either even or odd—for each group of bits that is 
            transmitted without error. In modem-to-modem communications, parity is often one of the parameters that must be agreed upon by sending parties 
            and receiving parties before transmission can take place.
            </remarks>
            <exception cref="T:System.InvalidOperationException">Property cannot be changed if connection is open.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The Parity value passed is not a valid value in the Parity enumeration.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.StopBits">
            <summary>
            Gets or sets the standard number of stopbits per byte.
            </summary>
            <remarks>
            The default value for StopBits is One.
            </remarks>
            <exception cref="T:System.InvalidOperationException">Property cannot be changed if connection is open.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The StopBits value is StopBits.None</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Gps.SerialPortGpsCoordinateWatcher.DataBits">
            <summary>
            Gets or sets the standard length of data bits per byte.
            </summary>
            <remarks>The range of values for this property is from 5 through 8. The default value is 8.</remarks>
            <exception cref="T:System.InvalidOperationException">Property cannot be changed if connection is open.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The data bits value is less than 5 or more than 8.</exception>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Gps.WindowsExceptionHelper">
            <summary>
            Helper class to work with Win 32 errors.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.WindowsExceptionHelper.ERROR_FILE_NOT_FOUND">
            <summary>
            Win32 error: The system cannot find the file specified.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.WindowsExceptionHelper.ERROR_OUTOFMEMORY">
            <summary>
            Win32 error: Not enough storage is available to complete this operation.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.WindowsExceptionHelper.ERROR_NOT_SUPPORTED">
            <summary>
            Win32 error: The request is not supported.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.WindowsExceptionHelper.ERROR_INVALID_PARAMETER">
            <summary>
            Win32 error: The parameter is incorrect.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Gps.WindowsExceptionHelper.ERROR_FILE_INVALID">
            <summary>
            Win32 error: The volume for a file has been externally altered so that the opened file is no longer valid.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Gps.WindowsExceptionHelper.ThrowWin32ErrorCode(System.Int32)">
            <summary>
            Throws corresponding exception according to the error code
            </summary>
            <param name="errorCode">Win32 error code returned by Marshal.GetLastWin32Error</param>
        </member>
        <member name="T:StringExtensions">
            <summary>
            StringExtensions allows compiling under .net 3.5 and 4.0.
            </summary>
        </member>
        <member name="M:StringExtensions.IsNullOrWhiteSpace(System.String)">
            <summary>
            Checks for NULL or white space under .net 3.5 and 4.0.
            </summary>
        </member>
        <member name="T:EnviromentExtensions">
            <summary>
            EnviromentExtensions allows compiling under .net 3.5 and 4.0.
            </summary>
        </member>
        <member name="M:EnviromentExtensions.ModuleContainsFunction(System.String,System.String)">
            <summary>
            Gets a function under .net 3.5 and 4.0.
            </summary>
        </member>
        <member name="P:EnviromentExtensions.Is64BitProcess">
            <summary>
            Checks for 64 bit process under .net 3.5 and 4.0.
            </summary>
        </member>
        <member name="P:EnviromentExtensions.Is64BitOperatingSystem">
            <summary>
            Checks for 64 bit operating system under .net 3.5 and 4.0.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Internal.Logger">
            <summary>
            For writing out events to the arcgisruntime log.
            Events with levels >= INFO also get written out to the .NET trace log, so you see them in the output window in Visual Studio.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo">
            <summary>
            Information about a REST API geocode service. The REST API geocode service resource represents a geocode 
            service. This resource works only with the default data frame of a published geocode service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo.CurrentVersion">
            <summary>
            The geocode service version.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo.ServiceDescription">
            <summary>
            The geocode service description.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo.addressFields">
            <summary>
            The addressFields available through the geocode service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo.singleLineAddressField">
            <summary>
            The singleLineAddressField available through the geocode service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo.candidateFields">
            <summary>
            The candidateFields available through the geocode service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo.intersectionCandidateFields">
            <summary>
            The intersectionCandidateFields available through the geocode service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo.SpatialReference">
            <summary>
            The spatial reference of the geocode service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeServiceInfo.locatorProperties">
            <summary>
            The locatorProperties available through the geocode service.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.AddressFieldInfo">
            <summary>
            Describes a AddressField in an ArcGIS REST geocode service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.AddressFieldInfo.Name">
            <summary>
            The name of the addressField.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.AddressFieldInfo.alias">
            <summary>
            The alias of the addressField.
            </summary>
            <value>The alias.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.AddressFieldInfo.required">
            <summary>
            Whether the addressField is required.
            </summary>
            <value><c>true</c> if addressField is required; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.AddressFieldInfo.type">
            <summary>
            The type of the addressField.
            </summary>
            <value>The type.</value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.CandidateFieldInfo">
            <summary>
            Describes a CandidateField in an ArcGIS REST geocode service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.CandidateFieldInfo.Name">
            <summary>
            The name of the candidateField.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.CandidateFieldInfo.alias">
            <summary>
            The alias of the candidateField.
            </summary>
            <value>The alias.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.CandidateFieldInfo.type">
            <summary>
            The type of the candidateField.
            </summary>
            <value>The type.</value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.IntersectionConnectorsPropertyInfo">
            <summary>
            Describes a locatorProperty in an ArcGIS REST geocode service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.IntersectionConnectorsPropertyInfo.IntersectionConnectors">
            <summary>
            The key of the LocatorProperty.
            </summary>
            <value>The key.</value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfoInternal">
            <summary>
            Information about a REST API geocode service. The REST API geocode service resource represents a geocode 
            service. This resource works only with the default data frame of a published geocode service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfoInternal.Name">
            <summary>
            The name of the geoprocessing task.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfoInternal.DisplayName">
            <summary>
            The name displayed for the geoprocessing task.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfoInternal.Category">
            <summary>
            The category of the geoprocessing task.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfoInternal.HelpUrl">
            <summary>
            A string that represent an URL of an online help for the geoprocessing task.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfoInternal.ExecutionType">
            <summary>
            The type of execution for the geoprocessing task.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfoInternal.Parameters">
            <summary>
            The parameters of the geoprocessing task.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParametersInfo">
            <summary>
            Information about a REST API geocode service. The REST API geocode service resource represents a geocode 
            service. This resource works only with the default data frame of a published geocode service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParametersInfo.Name">
            <summary>
            The name of the task parameter.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParametersInfo.DataType">
            <summary>
            The type of data for the task parameter.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParametersInfo.DisplayName">
            <summary>
            The name displayed for the task parameter.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParametersInfo.Direction">
            <summary>
            The direction of the task parameter.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParametersInfo.ParameterType">
            <summary>
            The type of the task parameter.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParametersInfo.Category">
            <summary>
            The category of the task parameter.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParametersInfo.ChoiceList">
            <summary>
            The list of choices fo the task parameter.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GeoprocessingServiceInfo">
            <summary>
            Information about a REST API geoprocessing service. The REST API geoprocessing service resource represents a geoprocessing 
            service. This resource works only with the default data frame of a published geoprocessing service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingServiceInfo.CurrentVersion">
            <summary>
            The geoprocessing service version.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingServiceInfo.ServiceDescription">
            <summary>
            The geoprocessing service description.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingServiceInfo.Tasks">
            <summary>
            The tasks available through the geoprocessing service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingServiceInfo.ExecutionType">
            <summary>
            The type of execution of the geoprocessing service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingServiceInfo.ResultMapServerName">
            <summary>
            The Name of the Map Server return by the geoprocessing service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingServiceInfo.MaximumRecords">
            <summary>
            The maximun number of Records accepted by the geoprocessing service.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Internal.MethodMode">
            <summary>
            Specified the way a method should execute, either asynchronously or blocking
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Internal.MethodMode.Async">
            <summary>
            Method execute asynchronously. So initiate the action and return before its complete
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.Internal.MethodMode.Blocking">
            <summary>
            Method should block until the action is complete.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.FeatureServiceInfo">
            <summary>
            Information about a REST API map service. The REST API map service resource represents a map 
            service. This resource works only with the default data frame of a published map document. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.FeatureServiceInfo.ServiceDescription">
            <summary>
            The map service description.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Internal.DesignTimeDetection.InDesignMode">
            <summary>
            Gets a value indicating whether the control is in design mode (running in Blend
            or Visual Studio).
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.FileUtils.MakeFullPath(System.String)">
            <summary>
            Turns relative paths into absolute path using the current .exe as the location it also 
            sets the path to lower case, and suses "\" for separators. So this path can be used as a "canonical"
            path to check for other duplicate paths.
            </summary>
            <param name="path">file path</param>
            <returns>Full path</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.FileUtils.IsHttp(System.String)">
            <summary>
            Returns true if this string looks like an url starting with http or https
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.FileUtils.IsValidPathCharacters(System.String)">
            <summary>
            Checks if the path contains any invalid characters.
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.FileUtils.IsRelativePath(System.String)">
            <summary>
            Returns true if this looks like a relative file path
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.Json.TryDeserialize``1(System.String)">
            <summary>
            Deserialize json string to the specified type T, returning null if unsuccessful.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="json"></param>
            <returns></returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.Json.Deserialize``1(System.String)">
            <summary>
            Deserialize a json string to the specified type T, throwing an exception if unsuccessful.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="json"></param>
            <returns></returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.ProcessUtils.TerminateProcessTree(System.Diagnostics.Process,System.Int32)">
            <summary>
            Terminate a process tree
            </summary>
            <param name="rootProcess">The root <see cref="T:System.Diagnostics.Process"/></param>
            <param name="exitCode">The exit code of the process</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.RegistryUtils.GetRegistryStringValue(System.String,Microsoft.Win32.RegistryHive,Microsoft.Win32.RegistryView)">
            <summary>
            Opens a string value from the registry in a particular hive and view (32 or 64 bit). Path include the value name
            e.g. SOFTWARE\ESRI\ArcGISRuntime400\InstallDir
            </summary>
            <param name="path">path below the hive including the value name</param>
            <param name="hive"></param>
            <param name="view"></param>
            <returns></returns>
            
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Internal.RequestInfo">
            <summary>
            Encapsulates details of a request/response, including the response body, and the original request URL.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.QueryParameters.Add(ESRI.ArcGIS.Client.Local.Internal.QueryParameters)">
            <summary>
            Adds more params to the existing instance, if any keys are duplicated the new key overwrites the original
            </summary>
            <param name="moreParams"></param>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Internal.Requests">
            <summary>
            Utility class for making async requests to the server and parsing the resulting JSON
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.Internal.Requests.Start``1(System.String,ESRI.ArcGIS.Client.Local.Internal.QueryParameters,System.Boolean,ESRI.ArcGIS.Client.Local.Internal.MethodMode,System.Action{``0,ESRI.ArcGIS.Client.Local.Internal.RequestInfo})">
            <summary>
            Start a request to the server.
            </summary>
            <typeparam name="TExpectedResponse">The response will be deserialized to an instance of this type.</typeparam>
            <param name="url">The request URL, excluding query parameters</param>
            <param name="queryParameters">The query parameters (excluding the ?)</param>
            <param name="post">True if HTTP POST should be used, in which case the parameters are encoded in the body.</param>
            <param name="mode">The mode.</param>
            <param name="callback">The callback to call when the request is completed.</param>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.DeleteServiceResponse">
            <summary>
            Response from a request to delete a service
            <!--POST admin/deleteService ? serviceName={serviceName}&serviceType={serviceType}&f=json-->
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.ErrorResponseInfo">
            <summary>
            Response that gets returned when a ArcGIS Runtime request is made that it is unable to service (sometimes you get a status response instead)
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LayersInfo">
            <summary>
            Describes a layer in an ArcGIS REST map service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayersInfo.Layers">
            <summary>
            The ID of the layer.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayersInfo.Tables">
            <summary>
            The ID of the layer.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LayerInfo">
            <summary>
            Describes a layer in an ArcGIS REST map service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerInfo.ID">
            <summary>
            The ID of the layer.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerInfo.Description">
            <summary>
            The description of the layer.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerInfo.CopyrightText">
            <summary>
            The copyrightText of the layer.
            </summary>
            <value>The copyrightText.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerInfo.Extent">
            <summary>
            The extent of the layer.
            </summary>
            <value>The extent of the layer.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerInfo.GeometryType">
            <summary>
            The type of geometry of the layer.
            </summary>
            <value>The type of geometry.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerInfo.HasAttachments">
            <summary>
            If the layer has attachments or not.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerInfo.Capabilities">
            <summary>
            List the capabilities supported by the Layer (Map,Query,Data and Create,Delete,Query,Update,Editing).
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerInfo.MaxRecordCount">
            <summary>
            The represents the maximum number of records that can be returned by query, find, and identify operations.
            </summary>
            <value>The maximum number of records returned.</value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LOD">
            <summary>
            An ArcGISTiledMapServiceLayer has a number of LODs (Levels of Detail). Each LOD corresponds to a map at a given scale or resolution. LOD has no constructor. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LOD.Level">
            <summary>
            ID for each level. The top most level is 0. The ID is returned in Map.getLevel() and set in Map.setLevel().
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LOD.Resolution">
            <summary>
            Resolution in map units of each pixel in a tile for each level.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LOD.Scale">
            <summary>
            Scale for each level.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.MapServiceInfo">
            <summary>
            Information about a REST API map service. The REST API map service resource represents a map 
            service. This resource works only with the default data frame of a published map document. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.CurrentVersion">
            <summary>
            The map service version.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.ServiceDescription">
            <summary>
            The map service description.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.MapName">
            <summary>
            The name of the map.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.Description">
            <summary>
            The map description.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.CopyrightText">
            <summary>
            The maps copyright text.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.SingleFusedMapCache">
            <summary>
            Whether the map is a single fused map cache.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.TileInfo">
            <summary>
            Information about the map tile cache if the map service is cached.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.SpatialReference">
            <summary>
            The spatial reference of the map.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.InitialExtent">
            <summary>
            The initial extent of the map.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.FullExtent">
            <summary>
            The full extent of the map.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.Units">
            <summary>
            The units of the map, e.g. esriDecimalDegrees
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.Layers">
            <summary>
            The layers contained by the map.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.Type">
            <summary>
            Gets or sets the type.
            </summary>
            <value>The type.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.IsReference">
            <summary>
            Gets or sets a value indicating whether this instance is reference
            i.e. the layer should be drawn on top of all operational layers.
            </summary>
            <value>
            	<c>true</c> if this instance is reference; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.Name">
            <summary>
            The name of the image file.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.Extent">
            <summary>
            The full extent of the image service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.PixelSizeX">
            <summary>
            Pixel Size X
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.PixelSizeY">
            <summary>
            Pixel Size Y
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceInfo.Capabilities">
            <summary>
            The map service capabilities (v10SP1+).
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.MapServiceLayerInfo">
            <summary>
            Describes a layer in an ArcGIS REST map service. 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceLayerInfo.ID">
            <summary>
            The ID of the layer.
            </summary>
            <value>The ID.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceLayerInfo.Name">
            <summary>
            The name of the layer.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceLayerInfo.DefaultVisibility">
            <summary>
            Whether the layer is visible by default.
            </summary>
            <value><c>true</c> if layer is visible by default; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceLayerInfo.SubLayerIds">
            <summary>
            The IDs of sublayers, if any.
            </summary>
            <value>The sub layer IDs.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceLayerInfo.MinScale">
             <summary>
            Minimum scale.
             </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.MapServiceLayerInfo.MaxScale">
             <summary>
            Maximum scale.
             </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.ServiceError">
            <summary>
            Exception that represents an error returned from the REST API
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ServiceError.Code">
            <summary>
            The service error code.
            </summary>
            <value>The code.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ServiceError.Details">
            <summary>
            The service error details.
            </summary>
            <value>The details.</value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.TileInfo">
            <summary>
            Describes a map tile cache.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.TileInfo.Rows">
            <summary>
            The number of rows within the tile cache.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.TileInfo.Cols">
            <summary>
            The number of columns within the tile cache.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.TileInfo.DPI">
            <summary>
            The DPI of the tile cache.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.TileInfo.Format">
            <summary>
            The image format of the tile cache.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.TileInfo.CompressionQuality">
            <summary>
            The compression quality of the tile cache; applies to JPEG format only.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.TileInfo.Origin">
            <summary>
            The origin (upper left corner) of the tiling scheme in coordinates of the spatial reference 
            of the source map document.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.TileInfo.SpatialReference">
            <summary>
            The spatial reference of the map cache.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.TileInfo.LODs">
            <summary>
            The levels of details contained in the map cache.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.CreateServiceResponse">
            <summary>
            The response from a successful request to create a new a new service.
            (You will get an ErrorResponse if there's a problem but the server is able to return a response).
            <!--POST admin/publish ? path={filepath}&p=json-->
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ServerLaunchConfig.ServerExe">
            <summary>
            Path of java.exe (relative to launch.cfg)
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ServerLaunchConfig.Args">
            <summary>
            Arguments to pass to the server exe (with temp folder and client process ID appended as two further command-line arguments
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ServerLaunchConfig.ServerLocationEnvironmentVariable">
            <summary>
            The name of the environment variable that gets set to the folder location of launch.cfg.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Internal.Loggers.LoggingDllFunctions">
            <summary>
            For dynamically loading and unloading native logging functions from ArcGISRuntimeLogging.dll.
            DLL is loaded from the Client32 or Client64 folder of the LocalServer depending on the process architecture.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer">
            <summary>
            Represents an ArcGIS dynamic map service layer for which the supporting service is a local map service hosted by the runtime local server.
            </summary>
            <remarks>
            <para>
            An ArcGISLocalDynamicMapServiceLayer allows you to work with a dynamic map service resource serving local data content 
            exposed by the ArcGIS REST API. A dynamic map service generates images on the fly.
            </para>
            <para>
            ArcGISLocalDynamicMapServiceLayer inherits from ArcGISDynamicMapServiceLayer and provides 
            overloaded constructors, overridden methods, new event handlers and new properties for working 
            with local data content. 
            </para>
            <para>
            ArcGISLocalDynamicMapServiceLayers can be created via one of the overloaded constructors or 
            by setting the the Path property then explicitly calling the overridden Initialize method. 
            In all cases, whether passed as a string in the constructor or used as the seed for a LocalMapService 
            the, the fundamental element is a path to an ArcGIS map package.
            </para>
            <para>
            The ArcGISLocalDynamicMapServiceLayer initialization will result in one of two events being raised, 
            either <see cref="E:ESRI.ArcGIS.Client.Layer.Initialized"/> or 
            <see cref="E:ESRI.ArcGIS.Client.Layer.InitializationFailed"/>. 
            It is recommended that these events are handled in order to provide feedback to the user, particularly 
            in the case of an initialization failure where the 
            <see cref="P:ESRI.ArcGIS.Client.Layer.InitializationFailure"/> property will contain the Exception.
            </para>
            <para>
            ArcGISDynamicMapServiceLayers are not shown at design time. 
            </para>
            </remarks>
            <example>
            The following example demonstrates how to define an ArcGISLocalDynamicMapServiceLayer in XAML:
            <code title="ArcGISLocalDynamicMapServiceLayer in XAML" description="" lang="XAML">
              &lt;esri:Map x:Name="_map"&gt;              
                &lt;esri:ArcGISLocalDynamicMapServiceLayer ID="arcGISLocalDynamicMapServiceLayer" 
                         Path="C:\Program Files (x86)\ArcGIS SDKs\WPF1.0\SDK\Samples\Data\MPKs\USCitiesStates.mpk"/&gt;            
              &lt;/esri:Map&gt;  
            </code>
            </example>
            <example>
            The following examples demonstrate how to define an ArcGISLocalDynamicMapServiceLayer in code using the static GetServiceAsync:
            <code title="ArcGISLocalDynamicMapServiceLayer in Code" description="" lang="CS">
            // Create a new local map service instance and supply an ArcGIS map package path as string
            LocalMapService.GetServiceAsync(@"C:\Program Files (x86)\ArcGIS SDKs\WPF1.0\SDK\Samples\Data\MPKs\USCitiesStates.mpk",
                delegateService =&gt; 
                {
                    // Create a new local dynamic layer instance and provide the local map service
                    ArcGISLocalDynamicMapServiceLayer arcGISLocalDynamicMapServiceLayer
                        = new ArcGISLocalDynamicMapServiceLayer(delegateService);
                        
                    // Add the local dynamic layer to the map. The map will handle the initialization of the layer.
                    _map.Layers.Add(arcGISLocalDynamicMapServiceLayer); 
                });
            </code>
            <code title="ArcGISLocalDynamicMapServiceLayer in Code" description="" lang="VB.NET">
            ' Create a new local map service instance and supply an ArcGIS map package path as string
            LocalMapService.GetServiceAsync("C:\Program Files (x86)\ArcGIS SDKs\WPF1.0\SDK\Samples\Data\MPKs\USCitiesStates.mpk", 
                Function(delegateService) 
                    ' Create a new local dynamic layer instance and provide the local map service
                    Dim arcGISLocalDynamicMapServiceLayer As New ArcGISLocalDynamicMapServiceLayer(delegateService)
            
                    ' Add the local dynamic layer to the map. The map will handle the initialization of the layer which 
                    ' in turn will start the local map service if required.  
                    _map.Layers.Add(arcGISLocalDynamicMapServiceLayer)
                End Function)
            </code>
            </example>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.#ctor">
            <summary>
            Initializes a new instance of the ArcGISLocalDynamicMapServiceLayer class.
            </summary>
            <remarks>
            <para>
            Using the default constructor will require the Source property to be specified. 
            As an alternative to the default constructor, consider using the overloaded constructor 
            <see cref="M:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.#ctor(System.String)"/> which accepts a path to an ArcGIS map package.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.#ctor(System.String)">
            <summary>
            Initializes a new instance of the ArcGISLocalDynamicMapServiceLayer class with the specified path to an ArcGIS map package.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the map package path or Url.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.#ctor(ESRI.ArcGIS.Client.Local.LocalMapService)">
            <summary>
            Initializes a new instance of the ArcGISLocalDynamicMapServiceLayer class with the specified LocalMapService
            </summary>
            <param name="service">The <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> with which the ArcGISLocalDynamicMapServiceLayer will be initialized.</param>
            <remarks>
            <para>
            You should use this overloaded constructor if you have already created an instance of a <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/>. 
            The LocalMapService will become the <see cref="P:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.Service"/> property of the ArcGISLocalDynamicMapServiceLayer.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.Initialize">
            <summary>
            Initializes the ArcGISLocalDynamicMapServiceLayer by creating the underlying LocalMapService.
            </summary>
            <remarks>
            <para>
            The ArcGISLocalDynamicMapServiceLayer Initialize method overrides the base Initialize method to handle the creation 
            of the underlying LocalMapService or reuse of an existing LocalMapService if one exists with the same properties. 
            Once the LocalMapService creation has completed the Url property is set and the base class Initialise method is called to 
            continue the initialization process. 
            </para>
            <para>
            It is not necessary to call the Initialize method explicitly since this process will be initiated by the Map when the layer is added 
            to the <see cref="P:ESRI.ArcGIS.Client.Map.Layers"/> collection.
            </para>
            <para>
            If the LocalMapService creation encountered an error the InitializationFailure property will contain the exception.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.HandlePropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Track attempts to set the Url property, and disallow them (only place Url can be set is when the local Service has initialized)
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.Service">
            <summary>
            The local map service used by this layer.
            </summary>
            <remarks>
            <para>
            For example usage see <see cref="T:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer"/>
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.Path">
            <summary>
            Gets or sets the path or URL to the map package from which the ArcGISLocalDynamicMapServiceLayer is created.
            </summary>
            <remarks>
            <para>
            This property cannot be set once the ArcGISLocalDynamicMapServiceLayer is initialized. Prior to attempting to set this property the 
            <see cref="P:ESRI.ArcGIS.Client.Layer.IsInitialized"/> property should be checked. If an attempt is 
            made to set the Path property whilst the ArcGISLocalDynamicMapServiceLayer is initialized a 
            <see cref="T:System.InvalidOperationException"/> will be thrown.    
            </para>
            </remarks>
            <value>A  <see cref="T:System.String"/> representing the map package path or URL.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.EnableDynamicLayers">
            <summary>
            Gets or sets a value indicating whether the dynamic layers capability is enabled on this ArcGISLocalDynamicMapServiceLayer.
            </summary>
            <value>
              <see langword="true"/> if the dynamic layers capability is enabled; otherwise, <see langword="false"/>.
            </value>
            <remarks>
            <para>
            This property must be set prior to the layer being initialized or being added to the Map.
            </para>
            <para>
            The dynamic layers capability allows you to dynamically change layer appearance and behavior in your local map service through both XAML and code. 
            Determining which layers will appear in a map, layer symbology, layer order and position, labelling, and more, can be achieved on the 
            runtime local server through the use of dynamic layers.
            </para>
            </remarks>
            <seealso cref="T:ESRI.ArcGIS.Client.ArcGISDynamicMapServiceLayer"/>
            <seealso cref="T:ESRI.ArcGIS.Client.DataSource"/>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer">
            <summary>
            ArcGISLocalFeatureLayers are a special type of Graphics layer that allow you to display graphic features which consist of 
            geometry and attributes. 
            </summary>
            <remarks>
            <para>
            ArcGISLocalFeatureLayers require both a path to an ArcGIS map package and the name or numeric ID of a layer within that map package. 
            </para>
            <para>
            FeatureLayers provide a greater degree of interaction with features in the map and provide the basis for feature editing functionality.
            </para>
            </remarks>
            <example>
            The following example demonstrates how to define an ArcGISLocalFeatureLayer in XAML:
            <code title="ArcGISLocalFeatureLayer in XAML" description="" lang="XAML">
              &lt;esri:Map x:Name="_map"&gt;              
                &lt;esri:ArcGISLocalFeatureLayer ID="arcGISLocalFeatureLayer" LayerName="Cities" 
                         Path="C:\Program Files (x86)\ArcGIS SDKs\WPF1.0\SDK\Samples\Data\MPKs\USCitiesStates.mpk"/&gt;            
              &lt;/esri:Map&gt;  
            </code>
            </example>
            <example>
            The following examples demonstrate how to define an ArcGISLocalFeatureLayer in code using the static GetServiceAsync:
            <code title="ArcGISLocalFeatureLayer in Code" description="" lang="CS">
            // Create a new local feature service instance and supply an ArcGIS Map Package path as string.
            LocalFeatureService.GetServiceAsync(@"C:\Program Files (x86)\ArcGIS SDKs\WPF1.0\SDK\Samples\Data\MPKs\USCitiesStates.mpk",
                localFeatureService =>
                {
                    // Create a new local feature layer instance and provide the local feature service and a layer name.
                    ArcGISLocalFeatureLayer arcGISLocalFeatureLayer
                        = new ArcGISLocalFeatureLayer(localFeatureService, "Cities");
                        
                    // Set the ID.
                    arcGISLocalFeatureLayer.ID = "Cities";
                    
                    // Add the local feature layer to the map. The map will handle the initialization of the layer.
                    _map.Layers.Add(arcGISLocalFeatureLayer);
                });
            </code>
            <code title="ArcGISLocalFeatureLayer in Code" description="" lang="VB.NET">
            ' Create a new local feature service instance and supply an ArcGIS Map Package path as string.
            LocalFeatureService.GetServiceAsync("C:\Program Files (x86)\ArcGIS SDKs\WPF1.0\SDK\Samples\Data\MPKs\USCitiesStates.mpk",
                Function(localServiceDelegate)
                    ' Create a new local feature layer instance and provide the local feature service and a layer name.
                    Dim arcGISLocalFeatureLayer As New ArcGISLocalFeatureLayer(localServiceDelegate, "Cities")
                    
                    ' Set the ID.
                    arcGISLocalFeatureLayer.ID = "Cities"
                    
                    ' Add the local feature layer to the map. The map will handle the initialization of the layer which 
                    ' in turn will start the local feature service if required.
                    _map.Layers.Add(arcGISLocalFeatureLayer)
                    
                End Function)
            </code>
            </example>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.#ctor">
            <summary>
            Initializes a new instance of the ArcGISLocalFeatureLayer class.
            </summary>
            <remarks>
            <para>
            Instead of using the default constructor to create a ArcGISLocalFeatureLayer, consider using one of the overridden constructors 
            which accept various combinations of a LocalMapService, a map package path or Url, a layer name or a numeric layer ID.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the ArcGISLocalFeatureLayer class using the provided path to a map package and the name of a layer within 
            the map.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path or URL of an ArcGIS map package.</param>
            <param name="layerName">A <see cref="T:System.String"/> representing the name of a layer within the .</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the ArcGISLocalFeatureLayer class using the provided path to an ArcGIS map package and the numeric index of a layer 
            within the map.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path or URL of an ArcGIS map package.</param>
            <param name="layerId">A <see cref="T:System.Int32"/> representing the zero-based index position of a layer within the map.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.#ctor(ESRI.ArcGIS.Client.Local.LocalMapService,System.Int32)">
            <summary>
            Initializes a new instance of the ArcGISLocalFeatureLayerclass using the provided <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> and the numeric index of a layer 
            within the map.
            </summary>
            <param name="service">The <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> on which to base the ArcGISLocalFeatureLayer.</param>
            <param name="layerId">A <see cref="T:System.Int32"/> representing the index position of the layer within the LocalMapService.</param>
            <remarks>
            <para>
            If the LocalMapService is not started prior to the initialization phase of the ArcGISLocalFeatureLayer the service will be automatically started.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.#ctor(ESRI.ArcGIS.Client.Local.LocalMapService,System.String)">
            <summary>
            Initializes a new instance of the ArcGISLocalFeatureLayer class using the provided <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> and the name of a layer 
            within the map.
            </summary>
            <param name="service">The <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> on which to base the ArcGISLocalFeatureLayer.</param>
            <param name="layerName">A <see cref="T:System.String"/> representing the name of the layer within the map.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.Initialize">
            <summary>
            Initializes the ArcGISLocalFeatureLayer.        
            </summary>
            <remarks>
            <para>
            It is not usually necessary to call the Initialize method explicitly, rather the layer initialization is typically triggered by adding the layer to the map. 
            If however you are working with an ArcGISLocalFeatureLayer instance without adding it to the map then you will need to call the Initialize method.
            </para>
            <para>
            After the layer initialization phase completes the <see cref="E:ESRI.ArcGIS.Client.Layer.Initialized"/> event will always be raised, 
            regardless of whether the layer initialization completed successfully.
            To appropriately handle a layer initialization failure you should register an event handler for the 
            <see cref="E:ESRI.ArcGIS.Client.Layer.Initialized"/> event and check the <see cref="P:ESRI.ArcGIS.Client.Layer.InitializationFailure"/> 
            property to determine the cause of the failure. Alternatively, if you do not intend to implement any functionality in the 
            layer initialized event there is an <see cref="E:ESRI.ArcGIS.Client.Layer.InitializationFailed"/> event available for directly handling the 
            failure scenario. Again, when implementing the logic for responding to this case you should check the 
            <see cref="P:ESRI.ArcGIS.Client.Layer.InitializationFailure"/> property to determine the cause of the failure. 
            </para>
            <para>
            ArcGISLocalFeatureLayers are not shown at design time. 
            </para>
            </remarks>
            <seealso cref="E:ESRI.ArcGIS.Client.Layer.Initialized"/>   
            <seealso cref="P:ESRI.ArcGIS.Client.Layer.InitializationFailure"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.HandlePropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Track attempts to set the URL property, and disallow them (only place URL can be set is when the local Service has initialized).
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.Service">
            <summary>
            The local service used by this layer. This can be a LocalMapService or LocalFeatureService which derives from LocalMapService.
            </summary>
            <remarks>
            <para>
            To support editing the Service property must have an editable LocalFeatureService set. LocalMapServices and LocalFeatureServices 
            also support definition queries, time definitions and selections. 
            </para>
            </remarks>
            <exception cref="T:System.InvalidOperationException">The Service property cannot be set if the ArcGISLocalFeatureLayer is initialized.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.Path">
            <summary>
            Gets or sets the path or URL of the ArcGIS map package for ArcGISLocalFeatureLayer.
            </summary>
            <remarks>
            <para>
            This property cannot be set once the ArcGISLocalFeatureLayer is initialized. Prior to attempting to set this property the 
            <see cref="P:ESRI.ArcGIS.Client.Layer.IsInitialized"/> property should be checked. If an attempt is 
            made to set the Path property whilst the ArcGISLocalFeatureLayer is initialized a 
            <see cref="T:System.InvalidOperationException"/> will be thrown.
            </para>
            </remarks>
            <value>A <see cref="T:System.String"/> representing the path or URL of an ArcGIS map package.</value>
            <exception cref="T:System.InvalidOperationException">The Path property cannot be set if the ArcGISLocalFeatureLayer is initialized.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.Editable">
            <summary>
            Gets or sets a value that determines whether this ArcGISLocalFeatureLayer is editable.
            </summary>
            <exception cref="T:System.InvalidOperationException">The Editable property cannot be set if the ArcGISLocalFeatureLayer is initialized.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.LayerName">
            <summary>
            Gets or sets the name of the layer within the [MAP] which the ArcGISLocalFeatureLayer is connected to.
            </summary>
            <value>A <see cref="T:System.String"/> representing the name of the layer.</value>
            <exception cref="T:System.InvalidOperationException">The LayerName property cannot be set if the ArcGISLocalFeatureLayer is initialized.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.LayerId">
            <summary>
            Gets or sets the ID of the layer within the map which the ArcGISLocalFeatureLayer is connected to.
            </summary>
            <value>A <see cref="T:System.Int32"/> representing the zero-based index position of a layer within the [MAP].</value>
            <exception cref="T:System.InvalidOperationException">The LayerId property cannot be set if the ArcGISLocalFeatureLayer is initialized.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer.LayerNameIsSet">
            <summary>
            Gets a value indicating whether the LayerName is set.
            </summary>
            <value><c>true</c> if the LayerName is set; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalServer">
            <summary>
            The static LocalServer class enables a number of LocalService types to be created based on local geographic content.
            </summary>
            <remarks>
            <para>
            Before using the the LocalServer the runtime deployment must be licensed with a valid license string. For more information please see the 
            <see cref="T:ESRI.ArcGIS.Client.ArcGISRuntime">ArcGISRuntime class</see>. Once the license string has been set and the ArcGISRuntime initialized,  
            the recommended approach to start the LocalServer is by calling the asynchronous InitializeAsync method and listening for the 
            InitializeCompleted event or by using the overloaded <see cref="M:ESRI.ArcGIS.Client.Local.LocalServer.InitializeAsync(System.Action)">
            Initialize(Action) method</see> and passing in an action callback delegate.
            </para>
            <para>
            Once initialized the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.InitializationError">
            InitializationError property</see> should be checked to confirm there were no problems encountered starting the LocalServer. 
            To determine the current state of the LocalServer the Status property will provide a <see cref="T:ESRI.ArcGIS.Client.Local.LocalServerStatus">
            LocalServerStatus</see> value or alternatively the property IsRunning can be used to quickly determine whether the 
            LocalServer is currently running. 
            </para>
            <para>
            Finally, when the LocalServer is no longer required the asynchronous <see cref="M:ESRI.ArcGIS.Client.Local.LocalServer.ShutdownAsync">
            ShutdownAsync method</see> should be called to shutdown the LocalServer and recover any resources being used. Once the LocalServer 
            has shutdown it will trigger the ShutdownCompleted event, or alternatively you can use the overloaded 
            <see cref="M:ESRI.ArcGIS.Client.Local.LocalServer.ShutdownAsync(System.Action)">ShutdownAsync(Action) method</see> to provide 
            an action callback delegate.
            </para>
            <para>
            It is not necessary to work with the LocalServer directly. Instead, creating and starting the individual LocalService classes (
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService">LocalMapService</see>, <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService">
            LocalFeatureService</see>, <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeocodeService">LocalGeocodeService</see>, 
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeometryService">LocalGeometryService</see>, and 
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService">LocalGeoprocessingService</see>) 
            will start the LocalServer if it is not already running. 
            </para>
            </remarks>
            <example>This example shows the one approach to starting the LocalServer by calling InitializeAsync and 
            using a lambda expression to handle the callback inline. The code which is executed in the callback first checks the 
            status of the LocalServer via the InitializationError property before proceeding.
            <code title="LocalServer InitializeAsync Example with Lambda Expression" description="" lang="CS">
            // Initialize the LocalServer using a lambda expression to handle the callback
            LocalServer.InitializeAsync(() =&gt;
            {
                // Check the LocalServer InitializationError property
                if (LocalServer.InitializationError != null)
                {
                    // The LocalServer encountered an error or did not start correctly
                    // ...
                    return;
                }
            });
            </code>
            <code title="LocalServer InitializeAsync Example with Lambda Expression" description="" lang="VB.NET">
            ' Initialize the LocalServer using a lambda expression to handle the callback
            LocalServer.InitializeAsync(Function()
                ' Check the LocalServer InitializationError property
                If LocalServer.InitializationError IsNot Nothing Then
                    ' The LocalServer encountered an error or did not start correctly
                    ' ...
                    Exit Function
                End If
            End Function)
            </code>
            </example>
            <example>This example shows recommended approach to initializing the LocalServer by first setting the license via the 
            SetLicense method then registering an event handler for the InitializationCompleted event and calling the default InitializeAsync 
            method. The code which is executed in the event handler first checks checks the status of the LocalServer via the 
            InitializationError property and the LicenseStatus of the LocalServer before proceeding.
            </example>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.#cctor">
            <summary>
            Static Constructor for the LocalServer class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.AddService(ESRI.ArcGIS.Client.Local.LocalService)">
            <summary>
            Adds a service to the list of running services. Before calling this make sure the service is not already on the list.
            Also only add services that are stopped.
            </summary>
            <param name="localService"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.RemoveService(ESRI.ArcGIS.Client.Local.LocalService)">
            <summary>
            Remove a service from the list of running services.
            </summary>
            <param name="localService"></param>
            <returns>Boolean indicating if the service was removed from the list</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.InitializeAsync">
            <summary>
            Asynchronously initializes the LocalServer.
            </summary>
            <returns>Returns a Task representing the asynchronous Initialize operation.</returns>
            <remarks>The InitializeAsync call should be wrapped in a try/catch block to handle any exceptions raised.</remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.InitializeAsync(System.Action)">
            <summary>
            Asynchronously initializes the LocalServer. The callback action is called when the server has started or if the server is already 
            running the callback is called immediately.
            </summary>
            <param name="callback"></param>
            <remarks>
            <para>
            The <see cref="E:ESRI.ArcGIS.Client.Local.LocalServer.InitializeCompleted"/> event should be handled to confirm 
            that no errors were encountered during initialization of the LocalServer. 
            The <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.InitializationError"/> property will contain an exception if an error occurred. 
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.Initialize">
            <summary>
            Synchronously starts the runtime local server.
            </summary>
            <remarks>
            <para>
            The <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Status"/> property should be checked prior to calling the Initialize method. 
            If Initialize is called whilst the LocalServer is <see cref="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.Initializing"/> or 
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.Running"/> the method will return immediately. If the LocalServer is 
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.ShuttingDown"/> a <see cref="T:ESRI.ArcGIS.Client.Local.LocalServerException"/> will be raised. 
            </para>
            </remarks>
            <exception cref="T:ESRI.ArcGIS.Client.Local.LocalServerException">Initialize should not be called if the LocalServer is currently 
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.ShuttingDown"/>.</exception>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalServer.InitializeAsync"/>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalServer.InitializeAsync(System.Action)"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.PostAction(System.Action)">
            <summary>
            Uses the SynchronizationContext to call back the action. The SynchronizationContext is captured when
            starting the LocalServer or stopping the local server. This will ensure callbacks occur on the UI thread
            </summary>
            <param name="a"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.ShutdownAsync">
            <summary>
            Asynchronously shuts down the LocalServer.
            </summary>	
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.DisconnectAllCallbacks">
            <summary>
            *FOR INTERNAL USE ONLY* Disconnects all callbacks.
            </summary>
            <exclude/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.ShutdownAsync(System.Action)">
            <summary>
            Shuts down the LocalServer, the callback action is called when the server has stopped.
            </summary>
            <param name="callback">The Action delegate called when the asynchronous operation completes.</param>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalServer.ShutdownAsync"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.Shutdown">
            <summary>
            Shutsdown the LocalServer synchronously if it is running.
            </summary>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalServer.ShutdownAsync"/>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalServer.ShutdownAsync(System.Action)"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.AbortServices">
            <summary>
            Use this method when shutting a server down, or clearing state.
            Sets services to be stopped without telling the server process and clears them from the LocalServer.Services
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.HandleMessageFromServer(System.Object,System.Diagnostics.DataReceivedEventArgs)">
            <summary>
            Handles messages sent to the console from the server process. Note runs in threadpool not UI thread.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.Diagnostics.DataReceivedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.HandleServerProcessExited(System.Diagnostics.Process)">
            <summary>
            Event called when server process exits. Note Runs on threadpool thread.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.HandleStartupMessage(System.Diagnostics.Process,System.String)">
            <summary>
             Handle server process messages. Runs on main thread.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.InitializeDone(System.Exception)">
            <summary>
            Called when completing Initialization, if error is set then it is used as the failure, otherwise _startupFailureReason is checked
            </summary>
            <param name="error"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServer.GetServerProcesses">
            <summary>
            *FOR INTERNAL USE ONLY* Gets a list of the processes which are dependent on the current LocalServer process.
            </summary>
            <returns>A <see cref="T:System.Collections.Generic.IList"/> of <see cref="T:System.Diagnostics.Process"/> objects.</returns>
            <exclude/>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.LocalServer.PropertyChanged">
            <summary>
            Occurs when a property value changes. 
            </summary>
            <remarks>
            
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">
            <summary>
            Returns the collection of active local services. These services can be in a Starting, Running, or Stopping state.
            </summary>
            <returns>A read only collection of LocalService objects.</returns>
            <remarks>
            <para>
            The collection may contain any of the following types of LocalService:
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService">LocalMapService</see>; 
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService">LocalFeatureService</see>;
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeocodeService">LocalGeocodeService</see>; 
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeometryService">LocalGeometryService</see>; or
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService">LocalGeoprocessingService</see>.
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.Url">
            <summary>
            Returns the URL to the services directory on the LocalServer when the LocalServer is running.
            </summary>
            <returns>
            Returns a string containing URL for the REST services directory on the LocalServer.
            </returns>
            <remarks>
            <para>
            The URL property can be useful for determining the contents of local services. The services directory URL is specific to a 
            running instance of the LocalServer because part of the URL comprises a GUID generated upon LocalServer initialization. 
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.Status">
            <summary>
            Gets the current status of the LocalServer.
            </summary>
            <value>The current <see cref="T:ESRI.ArcGIS.Client.Local.LocalServerStatus"/>.</value>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.LocalServer.InitializeCompleted">
            <summary>
            Occurs when the local server is initialized.
            </summary>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.LocalServer.ShutdownCompleted">
            <summary>
            Occurs when the local server shuts down.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.IsRunning">
            <summary>
            Gets a value indicating whether the LocalServer is initialized.
            </summary>
            <value>
            <see langword='true' /> if the LocalServer is initialized; otherwise, <see langword='false' />.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.InitializationError">
            <summary>
            Gets an Exception containing the LocalServer InitializationError.
            </summary>
            <value>The initialization error. Null if no error was encountered during initialization.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.LocalServerExePath">
            <summary>
            *FOR INTERNAL USE ONLY* Gets the runtime local server executable path.
            </summary>
            <value>
            A <see cref="T:System.String"/> representing the runtime local server executable path.
            </value>
            <exclude/>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.AdminUrl">
            <summary>
            *FOR INTERNAL USE ONLY* Gets the admin URL.
            </summary>
            <exclude/>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.CrashDumpPath">
            <summary>
            *FOR INTERNAL USE ONLY* Gets the crash dump path.
            </summary>
            <exclude/>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalServer.LogPath">
            <summary>
            *FOR INTERNAL USE ONLY* Gets the log path.
            </summary>
            <exclude/>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalServerException">
            <summary>
            Represents an error returned from the LocalServer.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServerException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServerException"/> class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServerException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServerException"/> class.
            </summary>
            <param name="error">A <see cref="T:System.String"/> representing the error.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServerException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServerException"/> class.
            </summary>
            <param name="error">A <see cref="T:System.String"/> representing the error.</param>
            <param name="inner">A <see cref="T:System.Exception"/> representing the inner exception message.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServerException.#ctor(ESRI.ArcGIS.Client.Local.ErrorResponse)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServerException"/> class.
            </summary>
            <param name="error">The <see cref="T:ESRI.ArcGIS.Client.Local.ErrorResponse"/> representing the error.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServerException.#ctor(ESRI.ArcGIS.Client.Local.ErrorResponse,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServerException"/> class.
            </summary>
            <param name="error">The <see cref="T:ESRI.ArcGIS.Client.Local.ErrorResponse"/> representing the error.</param>
            <param name="inner">A <see cref="T:System.Exception"/> representing the inner exception message.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalServerException.ExceptionIndicatesThatServerIsDown(System.Exception)">
            <summary>
            This is a probably brittle test for whether an exception returned from a request indicates
            that the ArcGIS Runtime server is currently down.
            Checks the exception returned from a request to the runtime, and if it looks like the kind of
            exception you get back when the server is down, return true.
            </summary>
            <param name="error"></param>
            <returns></returns>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalServerStatus">
            <summary>
            Indicates the current status of the LocalServer.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.NotInitialized">
            <summary>
            The LocalServer is not initialized.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.Initializing">
            <summary>
            The LocalServer is initializing.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.Running">
            <summary>
            The LocalServer is initialized.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.InitializeError">
            <summary>
            The LocalServer failed to initialize correctly.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.ShuttingDown">
            <summary>
            The LocalServer is shutting down.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureLayer_BothProperties01NotSet">
            <summary>
              Looks up a localized string similar to Either &apos;{0}&apos; or &apos;{1}&apos; must be set, in order to provide enough information to connect to a feature service..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureLayer_BothProperties01Set">
            <summary>
              Looks up a localized string similar to Only one of either &apos;{0}&apos; or &apos;{1}&apos; may be set..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureLayer_CannotCreateEditable012">
            <summary>
              Looks up a localized string similar to Cannot create an editable &apos;{0}&apos; because an instance of &apos;{1}&apos; is set in the &apos;{2}&apos; property and this does not support editing..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureLayer_NoLayerWithID0">
            <summary>
              Looks up a localized string similar to Unable to find layer with a ID &apos;{0}&apos; in the service..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureLayer_NoLayerWithName0">
            <summary>
              Looks up a localized string similar to Unable to find layer with a name &apos;{0}&apos; in the service..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureServ_CantRetrieveInformationFeatureService">
            <summary>
              Looks up a localized string similar to Could not retrieve information on the feature service layers..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureServ_CantRetrieveInformationMapService">
            <summary>
              Looks up a localized string similar to Could not retrieve information on the map service layers..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureServ_CantRetrieveInformationOnLayers">
            <summary>
              Looks up a localized string similar to Could not retrieve information on the layers in a Feature Service..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureServ_FailedToCreate0DuplicateServicePath1">
            <summary>
              Looks up a localized string similar to Failed to create a &apos;{0}&apos; for editing as the service with the same path is already running as an instance of &apos;{1}&apos; which does not support editing..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureServ_FailedToCreate0SamePathInstance1">
            <summary>
              Looks up a localized string similar to Failed to create a &apos;{0}&apos; as the service with the same path is already running as an instance of &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.FeatureServ_ServiceNotImplement01Endpoints">
            <summary>
              Looks up a localized string similar to Service does not implement &apos;{0}&apos; and &apos;{1}&apos; endpoints..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_0CantBenegative">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; number can&apos;t be negative.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_0IsAlreadyinUse">
            <summary>
              Looks up a localized string similar to &apos;{0}&apos; is already in use.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_0PropertyNotSetCantStartService">
            <summary>
              Looks up a localized string similar to {0} property is not set, cannot create a service..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_CantInitialize01NotSet">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; or &apos;{1}&apos; is not set. Cannot initialize this layer..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_CantInitializeServiceStopping">
            <summary>
              Looks up a localized string similar to Service is stopping and cannot be used to initialize the layer..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_CantRetrieveInfoOn0">
            <summary>
              Looks up a localized string similar to Could not retrieve information on the &apos;{0}&apos; service..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_CantStartService01NotSet">
            <summary>
              Looks up a localized string similar to Both &apos;{0}&apos; and &apos;{1}&apos; are not set, one of these properties must be set to start a service..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_CantWait0SamePathStarting">
            <summary>
              Looks up a localized string similar to Cannot wait on a &apos;{0}&apos; as the service with the same path is starting asynchronously..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_ClientDllFolderPathNotFound">
            <summary>
              Looks up a localized string similar to Client dll folder path not found..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_CouldNotLoad0FunctionFromDll">
            <summary>
              Looks up a localized string similar to Could not load &apos;{0}&apos; function from DLL..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_CouldNotLoadDLL0">
            <summary>
              Looks up a localized string similar to Could not load the DLL &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_DLLCouldNotBeFound">
            <summary>
              Looks up a localized string similar to DLL could not be found..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_FailedToCreate0SamePathStopping">
            <summary>
              Looks up a localized string similar to Failed to create a &apos;{0}&apos; as the service with the same path is already stopping..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_InServiceCantBeStarted">
            <summary>
              Looks up a localized string similar to The service is currently in progress and cannot be started..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_Property0CantChangeWhenLayerInitialized">
            <summary>
              Looks up a localized string similar to The property: &apos;{0}&apos; cannot be changed when the layer is initialized..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_Property0CantChangeWhenServiceRunning">
            <summary>
              Looks up a localized string similar to {0} property cannot be changed when the service is running..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_Property0NotUserSettable">
            <summary>
              Looks up a localized string similar to The property: &apos;{0}&apos; is not user settable..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_ServiceNotImplement0Endpoint">
            <summary>
              Looks up a localized string similar to Service does not implement &apos;{0}&apos; endpoint..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_SpecifiedPathNotFound">
            <summary>
              Looks up a localized string similar to The specified Path does not exist..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Generic_TheFile0DoesNotExist">
            <summary>
              Looks up a localized string similar to The file &apos;{0}&apos; does not exist..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.GP_CantRetrieve0InfoOn1">
            <summary>
              Looks up a localized string similar to Could not retrieve &apos;{0}&apos; information on the &apos;{1}&apos; service..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.GPS_CantChangeValueWhenStarted">
            <summary>
              Looks up a localized string similar to Cannot change value when started..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.GPS_COMPortIsInvalid">
            <summary>
              Looks up a localized string similar to COM port is invalid..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.GPS_CouldntLoadGPSFunctionality">
            <summary>
              Looks up a localized string similar to Couldn’t load GPS functionality - ArcGISRuntime could not be found..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.GPS_InvalidNMEAFilePath">
            <summary>
              Looks up a localized string similar to Invalid NMEA file path..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.GPS_NMEAFileDoesntExists">
            <summary>
              Looks up a localized string similar to NMEA file doesn&apos;t exists..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.GPS_SecurityException">
            <summary>
              Looks up a localized string similar to SecurityException.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.GPS_ValueShouldBeGreaterThanZero">
            <summary>
              Looks up a localized string similar to Value should be greater than zero..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.MapService_0in1MustHaveUniqueIds">
            <summary>
              Looks up a localized string similar to &apos;{0}&apos; in the &apos;{1}&apos; collection must have unique Ids..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.MapService_CantRetrieveInformationInA0">
            <summary>
              Looks up a localized string similar to Could not retrieve information on the layers in a &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.QueryTask_CantFindLayer0In1">
            <summary>
              Looks up a localized string similar to Unable to find the layer &apos;{0}&apos; in the &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_AlreadyShuttingDown">
            <summary>
              Looks up a localized string similar to Local server is already shutting down..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_ARequestToTheLocalServerWasCancelled">
            <summary>
              Looks up a localized string similar to A request to the LocalServer was cancelled..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_AUnexpectedEmptyResponseWasReturnedFromTheLocalServer">
            <summary>
              Looks up a localized string similar to A unexpected empty response was returned from the LocalServer..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_BasicLicenseError">
            <summary>
              Looks up a localized string similar to LocalServer cannot start with a basic license..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_CannotInitializeItsShuttingDown">
            <summary>
              Looks up a localized string similar to Cannot initialize the Localserver as it is shutting down..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_CrashDump0">
            <summary>
              Looks up a localized string similar to \nCrash dump file saved to: &apos;{0}&apos;.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_FailedToFind">
            <summary>
              Looks up a localized string similar to Filed to find local server executable..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_InitializingCantShutdown">
            <summary>
              Looks up a localized string similar to Local server is currently initializing, cannot shutdown..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_InnerException">
            <summary>
              Looks up a localized string similar to See the inner exception for details..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_LicenseOnlySetUninitialized">
            <summary>
              Looks up a localized string similar to The LocalServer license can only be set when its not initialized..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_NotResponding">
            <summary>
              Looks up a localized string similar to The LocalServer is not responding to requests and may have crashed..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_ServiceAlreadyRunning">
            <summary>
              Looks up a localized string similar to Cannot start this service as it is already running on the LocalServer..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_ServiceStopped0">
            <summary>
              Looks up a localized string similar to Service stopped unexpectedly. &apos;{0}&apos;.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_ServiceWithName0AlreadyRunning">
            <summary>
              Looks up a localized string similar to Cannot start service because a local service with the same Name &apos;{0}&apos; is already running..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_Warning_Content">
            <summary>
              Looks up a localized string similar to This application is running using a developer license. This license is valid for development and testing only..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_Warning_Title">
            <summary>
              Looks up a localized string similar to ArcGIS Runtime.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Server_WrongInstallDirectory0">
            <summary>
              Looks up a localized string similar to LocalServer initialization error. Failed to find an ArcGISRuntime install to start the LocalServer. Ensure in your exe location there is a sub-folder called &apos;{0}&apos;. Alternatively you can set the InstallDirectory property to point to a different location..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Service_CantSetName">
            <summary>
              Looks up a localized string similar to Cannot set the services name when the service is in progress. The name can only be set when the service is stopped..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Service_CantStart0InProcessOfStarting">
            <summary>
              Looks up a localized string similar to &apos;{0}&apos; is in the process of starting, cannot start a service synchronously while the server is starting asynchronously..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Service_CantStart0IsShuttingDown">
            <summary>
              Looks up a localized string similar to &apos;{0}&apos; is shutting down, cannot create services..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Service_FailToCreate0">
            <summary>
              Looks up a localized string similar to Failed to create a &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Service_NoRunningServiceToStop">
            <summary>
              Looks up a localized string similar to LocalServer is not initialized, so there are no running services to stop..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Service_NotStartedCantStopped">
            <summary>
              Looks up a localized string similar to The Service is not started, so it cannot be stopped..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.Service_ServerServiceNotEqualServiceName">
            <summary>
              Looks up a localized string similar to Server service name does not equal client service name..
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.Properties.Resources.WorkspaceInfo_0CantChangeWhenServiceRunning">
            <summary>
              Looks up a localized string similar to {0} cannot be changed when the service is running..
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfo">
             <summary>
             The GeoprocessingTaskInfo class provides information on each task inside a Geoprocessing Service.
             </summary>
             <remarks>
             <para>
             This class provides a convenient way to quickly obtain task information that is required for the execution of geoprocessing tasks such as the 
             Name property and importantly provides access to the Parameter list.
             </para> 
             </remarks> 
             <example>This example shows how to access the GeoprocessingTaskInfo for the first task within a Local Geoprocessing Service.
             <code title="GeoprocessingTaskInfo Example" description="" lang="CS">
             // Create a new LocalGeoprocessing service of synchronous execution type.
             LocalGeoprocessingService gpService
                 = new LocalGeoprocessingService("Path to geoprocessing package", GPServiceType.Execute);
                 
             gpService.StartAsync(delegateService =>
                 {
                     // The name of the task.
                     string taskName = gpService.Tasks[0].Name;
                     
                     // The display name of task.
                     string taskDisplayName = gpService.Tasks[0].DisplayName;
            
                     // The category of the task.
                     string taskCategory = gpService.Tasks[0].Category;
                     
                     // The execution type (synchronous / asynchronous) of the task.
                     GPServiceType taskGpServiceType = gpService.Tasks[0].ExecutionType;
                     
                     // The Url of the task. This is used in conjunction with the Geoprocessor task.
                     string taskUrl = gpService.Tasks[0].Url;
                     
                     // The Url of the help for the task.
                     string taskHelpUrl = gpService.Tasks[0].HelpUrl;
                     
                     // Instantiate a new Geoprocessor task with the Url.
                     ESRI.ArcGIS.Client.Tasks.Geoprocessor geoprocessorTask
                         = new ESRI.ArcGIS.Client.Tasks.Geoprocessor(gpService.Tasks[0].Url);
                 });
             </code>
             <code title="GeoprocessingTaskInfo Example" description="" lang="VB.NET">
             ' Create a new LocalGeoprocessing service of synchronous execution type.
             Dim gpService As New LocalGeoprocessingService("Path to geoprocessing package", GPServiceType.Execute)
             gpService.StartAsync(Function(delegateService)
                 ' The name of the task.
                 Dim taskName As String = gpService.Tasks(0).Name
                 
                 ' The display name of the task.
                 Dim taskDisplayName As String = gpService.Tasks(0).DisplayName
                 
                 ' The category of the task.
                 Dim taskCategory As String = gpService.Tasks(0).Category
                 
                 ' The execution type (synchronous / asynchronous) of the task.
                 Dim taskGpServiceType As GPServiceType = gpService.Tasks(0).ExecutionType
                    
                 ' The Url of the task. 
                 ' This is used in conjunction with the Geoprocessor task.
                 Dim taskUrl As String = gpService.Tasks(0).Url
                 
                 ' The Url of the help for this task.
                 Dim taskHelpUrl As String = gpService.Tasks(0).HelpUrl
                 
                 ' Instantiate a new Geoprocessor task with the Url.
                 Dim geoprocessorTask As New ESRI.ArcGIS.Client.Tasks.Geoprocessor(gpService.Tasks(0).Url)
                 
             End Function)
             </code>
             </example>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfo.Name">
            <summary>
            Gets the name of the geoprocessing task.
            </summary>        
            <value>A <see cref="T:System.String"/> representing the name of the geoprocessing task.</value>
            <remarks>
            <para>
            The Name property gets the name of the geoprocessing task. The Name property corresponds with the name of the geoprocessing model or script 
            created in ArcGIS for Desktop.</para> 
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfo.DisplayName">
            <summary>
            Gets the display name of the geoprocessing task.
            </summary>
            <value>A <see cref="T:System.String"/> representing the display name of the geoprocessing task.</value>
            <remarks>
            <para>
            The DisplayName property gets the display name of the GeoprocessingTask. The DisplayName property corresponds with the label of the 
            model or script created in ArcGIS for Desktop.</para> 
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfo.Category">
             <summary>
            Gets the Category of the geoprocessing task.
            </summary>
            <value>A <see cref="T:System.String"/> representing the category of the geoprocessing task.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfo.HelpUrl">
            <summary>
            Gets the URL of the help for the geoprocessing task.
            </summary>
            <value>A <see cref="T:System.String"/> representing the URL to the help for the geoprocessing task.</value>
            <remarks>
            <para>
            The help is an HTML document accessed via the HelpUrl property.</para> 
             </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfo.Url">
            <summary>
            Gets the URL of the geoprocessing task.
            </summary>
            <value>A <see cref="T:System.String"/> representing the URL of the geoprocessing task.</value>
            <remarks>
            <para>
            The Url property is required by the <see cref="T:ESRI.ArcGIS.Client.Tasks.Geoprocessor"/> task. The URL can be useful for viewing the 
            tasks and parameters of the geoprocessing service in the runtime local server REST Services Directory within a browser. 
            This will help to execute geoprocessing tasks successfully by confirming that the tools and parameters specified are correct.
            </para> 
             </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfo.ExecutionType">
            <summary>
            Gets the execution type of the geoprocessing task.
            </summary>
            <value>A <see cref="T:ESRI.ArcGIS.Client.Local.GPServiceType"/> representing the execution type of the geoprocessing task.</value>
            <remarks>
            <para>
            The ExecutionType shows whether the task is synchronous or asynchronous. 
            When a GeoprocessingTask is set to synchronous the results of each operation are returned to the client as soon as the operation is complete. 
            An asynchronous task typically takes longer to execute and a job ID is returned to the client. 
            The client can then use this ID to retrieve the results at a convenient time. 
            An asynchronous service also gives you the opportunity to get feedback on the job status of the asynchronous operation. 
            Synchronous services are intended for geoprocessing operations that take a short amount of time to execute, 
            while asynchronous services should be used for longer-running operations.</para> 
             </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfo.Parameters">
            <summary>
            A collection of GeoprocessingTaskParameters.
            </summary>  
            <value>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1"/> representing a collection of GeoprocessingTaskParameters.</value>
            <remarks>
            <para>
            This will return a collection of GeoprocessingTaskParameters that are describe each parameter exposed by the 
            geoprocessing task.
             </para> 
            </remarks>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParameters">
            <summary>
            GeoprocessingTaskParameters provide the information available for each parameter in the GeoprocessingTask.
            </summary>
            <remarks>
            <para>
            This class provides a convenient way to quickly obtain parameter information and to confirm that parameters that you specify for executing a geoprocessing task are correct. 
            </para> 
            </remarks>  
            <example>This example shows how to access the GeoprocessingTaskParameters within a GeoprocessingTask.
            <code title="GeoprocessingTaskInfo parameters example" description="" lang="CS">
            // Create a new LocalGeoprocessing service of synchronous execution type.
            LocalGeoprocessingService gpService 
                = new LocalGeoprocessingService("Path to geoprocessing package", GPServiceType.Execute);
            // Asynchronously start the service then check the parameter information.
            gpService.StartAsync(delegateService =>
            {  
                // Check the number of parameters of the first task (tool) in the service.
                int numberOfServiceParameters = gpService.Tasks[0].Parameters.Count;
                
                // Check the data type of the first parameter e.g. GPString.
                GPParameterDataType dataTypeOfFirstParameter = gpService.Tasks[0].Parameters[0].DataType;
                
                // Check the direction of the first parameter: input; or output.
                GPParameterDirection directionofFirstParameter = gpService.Tasks[0].Parameters[0].Direction;
                
                // Check the parameter type of the first parameter: required; optional; or derived (output).
                GPParameterType parameterTypeOfFirstParameter = gpService.Tasks[0].Parameters[0].ParameterType;
                });
            </code>
            <code title="GeoprocessingTaskInfo parameters example" description="" lang="VB.NET">
            ' Create a new LocalGeoprocessing service of synchronous execution type.
            Dim gpService As New LocalGeoprocessingService("Path to geoprocessing package", GPServiceType.Execute)
            
            ' Asynchronously start the service then check the parameter information.
            gpService.StartAsync(Function(delegateService)
                ' Check the number of parameters of the first task (tool) in the service.
                Dim numberOfServiceParameters As Integer = gpService.Tasks(0).Parameters.Count
                
                ' Check the data type of the first parameter e.g. GPString.
                Dim dataTypeOfFirstParameter As GPParameterDataType = gpService.Tasks(0).Parameters(0).DataType
                
                ' Check the direction of the first parameter: input; or output.
                Dim directionofFirstParameter As GPParameterDirection = gpService.Tasks(0).Parameters(0).Direction
                
                ' Check the parameter type of the first parameter: required; optional; or derived (output).
                Dim parameterTypeOfFirstParameter As GPParameterType = gpService.Tasks(0).Parameters(0).ParameterType
            End Function)
            </code>
            </example>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParameters.Name">
            <summary>
            Gets the Name of the GeoprocessingTaskParameter.
            </summary>
            <value name="Name">A string that represents the name of the GeoprocessingTaskParameter.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParameters.DisplayName">
            <summary>
            Gets the DisplayName of the GeoprocessingTaskParameter.
            </summary>
            <value name="DisplayName">A string that represents the display name of the GeoprocessingTaskParameter.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParameters.Category">
            <summary>
            Gets the Category of the GeoprocessingTaskParameter.
            </summary>
            <value name="Category">A string that represents the category of the GeoprocessingTaskParameter.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParameters.DataType">
            <summary>
            Gets the DataType of the GeoprocessingTaskParameter.
            </summary>
            <value name="DataType">A GPParameterDataType that represents the data type of the GeoprocessingTaskParameter.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParameters.Direction">
            <summary>
            Gets the Direction of the GeoprocessingTaskParameter.
            </summary>
            <value name="Direction">A GPParameterDirection that represents the direction of the GeoprocessingTaskParameter.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParameters.ParameterType">
            <summary>
            Gets the ParameterType of the GeoprocessingTaskParameter.
            </summary>
            <value name="ParameterType">A GPParameterType that represents the type of the GeoprocessingTaskParameter.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeoprocessingTaskParameters.Choices">
            <summary>
            Gets the available choices for the GeoprocessingTaskParameter.
            </summary>
            <value name="Choices">A collection that represents the choices of the GeoprocessingTaskParameter. 
            The choices correspond with the choices specified in the model or script created in ArcGIS for Desktop. </value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GPParameterDataType">
            <summary>
            Indicates the data type of the parameter.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.String">
            <summary>
            Data type is a String.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.Double">
            <summary>
            Data type is a Double.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.Boolean">
            <summary>
            Data type is a Boolean.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.Long">
            <summary>
            Data type is a Long.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.LinearUnit">
            <summary>
            Data type is a LinearUnit.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.RecordSet">
            <summary>
            Data type is a RecordSet.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.FeatureRecordSetLayer">
            <summary>
            Data type is a FeatureRecordSetLayer.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.DataFile">
            <summary>
            Data type is a DataFile(The DataFile data type is not supported as an input).
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.Date">
            <summary>
            Data type is a Date.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.RasterData">
            <summary>
            Data type is a RasterData(The RasterData data type is not supported as an input).
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.RasterDataLayer">
            <summary>
            Data type is a RasterDataLayer.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDataType.MultiValue">
            <summary>
            Data type is a Multivalue.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GPParameterDirection">
            <summary>
            Indicates the direction of the parameter.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDirection.Input">
            <summary>
            Parameter is an input.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterDirection.Output">
            <summary>
            Parameter is an output.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GPParameterType">
            <summary>
            Indicates if the parameter is required, optional or derived (output).
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterType.Required">
            <summary>
            The parameter is required.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterType.Optional">
            <summary>
            The parameter is optional.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPParameterType.Derived">
            <summary>
            The parameter is derived.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GeocodeField">
            <summary>
            A class representing information for each Field in a Geocode Service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeField.Name">
            <summary>
            The name of the GeocodeField.
            </summary>
            <value>A <see cref="T:System.String"/> representing the Name of the GeocodeField</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeField.Alias">
            <summary>
            The alias of the GeocodeField.
            </summary>
            <value>A <see cref="T:System.String"/> representing the Alias of the GeocodeField</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeField.IsRequired">
            <summary>
            Whether this Geocode Field is required for the locator task to run on the Geocode Service.
            </summary>
            <value>A <see cref="T:System.Boolean"/> indicating whether this geocode field is required.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.GeocodeField.Type">
            <summary>
            The type of the GeocodeField.
            </summary>
            <value>A <see cref="T:ESRI.ArcGIS.Client.Field.FieldType"/> representing the datatype of the geocode field.</value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.GPServiceType">
            <summary>
            The GPServiceType enumeration contains values which are used to determine how a LocalGeoprocessingService will run.
            </summary>
            <remarks>
              <para>
            When creating LocalGeoprocessingServices to run Geoprocessing tools and models you must choose whether that service will run synchronously (<see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.Execute"/>), 
            asynchronously (<see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJob"/>) or asynchronously with an dedicated LocalMapService instance to draw the result (<see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJobWithMapServerResult"/>). 
            The synchronous Execute option should be chosen if the tool will take less than 15 seconds to complete but for larger and more complex tools which take longer than 15 seconds it is recommended 
            that the asynchronous SubmitJob option is used. The synchronous Execute option will result in a single RuntimeLocalServer.exe worker process being created to perform the work 
            (in addition to the primary RuntimeLocalServer process) whereas the
            asynchronous SubmitJob option will result in two RuntimeLocalServer.exe processes, one to undertake the actual processing and one to monitor the processing. Lastly, the asynchronous option with a 
            map server result produces three RuntimeLocalServer.exe processes where the third one is a LocalMapService instance employed to render the results of the Geoprocessing operation. 
            Once the LocalGeoprocessingService has started the setting becomes read-only for the lifetime of that service and cannot be changed once the service is running.
              </para>
              <para>
            The <see cref="T:ESRI.ArcGIS.Client.Tasks.Geoprocessor">Geoprocessor Task</see> has methods which correspond to the execution type selected when creating the LocalGeoprocessingService:
              <list type="bullet">
              <item>For <see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.Execute"/> use the <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.Execute(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter})">Geoprocessor.Execute</see> or
              <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.ExecuteAsync(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter})">Geoprocessor.ExecuteAsync</see> method.</item>
              <item>For <see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJob"/> use the <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.SubmitJob(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter})">Geoprocessor.SubmitJob</see> or
              <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.SubmitJobAsync(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter},System.Object)">Geoprocessor.SubmitJobAsync</see> method.</item>
              <item>For <see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJob"/> use the <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.SubmitJob(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter})">Geoprocessor.SubmitJob</see> or
              <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.SubmitJobAsync(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter},System.Object)">Geoprocessor.SubmitJobAsync</see> method in conjunction with the
              <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.GetResultImageLayer(System.String,System.String)"/> or <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.GetResultImageLayerAsync(System.String,System.String,System.Object)"/> method.</item>
              </list>
              </para>
            </remarks>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPServiceType.Execute">
            <summary>
            The service will execute tasks synchronously on the LocalServer. 
            </summary>
            <remarks>
            Use this for tasks that run quickly (approx 15 seconds). 
            The Geoprocessor.ExecuteAsync method is typically used to initiate tasks in this service.
            </remarks>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJob">
            <summary>
            The service will run tasks asynchronously on the LocalServer.
            </summary>
            <remarks>
            A job id is used to poll for progress of asynchronous tasks. 
            The Geoprocessor.SubmitJobAsync method is typically used to initiate tasks in this service.
            </remarks>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJobWithMapServerResult">
            <summary>
            The service will run tasks asynchronously, a job id is used to poll for progress. 
            </summary>
            <remarks>
            The Geoprocessor.SubmitJobAsync method is typically used to initiate tasks in this service. 
            A result of a task can be displayed as a layer in a map using the GPResultImageLayer class.
            </remarks>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService">
            <summary>
            A class representing a local geoprocessing service hosted by the runtime local server.
            </summary>
            <remarks>
            <para>A single local geoprocessing service instance corresponds directly to a geoprocessing package shared from ArcGIS for Desktop. 
            One or more geoprocessing tasks may be exposed by the local geoprocessing service depending on the number of individual tool or script results 
            contained within the geoprocessing package. When starting local geoprocessing services there are a number of properties you must choose values for 
            which are usually determined by the server administrator for online geoprocessing services hosted by ArcGIS for Server. 
            </para>
            <para>
            These properties are:
            <list type="buller">
            <item>
            Service execution type: The <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.ServiceType"/> property determines whether the service will run the task asynchronously or 
            synchronously. This affects how the client application you are developing interacts with the runtime local server and gets the result from the task. 
            When a service is set to <see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.Execute"/> (synchronous) operation, the client waits for the task to finish. Typically, a service that 
            will run tasks synchronously should execute quickly (five seconds or less). A service should be set to use the <see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJob"/> 
            (asynchronous) if one or more of that tasks within that service will takes longer to run. When using the SubmitJob mode the client application 
            must periodically ask the server if the task has finished and, if it has finished, get the result. The API can perform this periodic check for you 
            on a frequency determined by the <see cref="P:ESRI.ArcGIS.Client.Tasks.Geoprocessor.UpdateDelay">Geoprocessor task UpdateDelay property</see>, the default value being 1000 milliseconds. 
            The GPServiceType enumeration values which determine the synchronous or asynchronous service operation are called "Execute" and "SubmitJob" to align 
            them with the corresponding methods on the Geoprocessor task API and to differentiate them from the synchronous or asynchronous usage of those methods: 
            <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.ExecuteAsync(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter})">Geoprocessor.ExecuteAsync</see>, 
            <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.Execute(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter})">Geoprocessor.Execute</see>, 
            <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.SubmitJobAsync(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter},System.Object)">Geoprocessor.SubmitJobAsync</see> and 
            <see cref="M:ESRI.ArcGIS.Client.Tasks.Geoprocessor.SubmitJob(System.Collections.Generic.List{ESRI.ArcGIS.Client.Tasks.GPParameter})">Geoprocessor.SubmitJob</see>. 
            You must use the method which corresponds to the GPServiceType of the local geoprocessing service you are working with. 
            The service mode <see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJobWithMapServerResult"/> will enable you to display the results of the task within a local map service.
            </item>
            <item>
            Maximum number of records returned: The maximum number of results the service can return to a client. 
            Setting this value to a large number means your runtime local server can handle sending a lot of individual records or features to the client 
            application. Whether you choose this pattern depends on the number and complexity of the feature geometries. To display large numbers of graphic features 
            you should consider using the accelerated display mode enabled via <see cref="P:ESRI.ArcGIS.Client.Map.UseAcceleratedDisplay"/> property or the <see cref="T:ESRI.ArcGIS.Client.AcceleratedDisplayLayers"/> 
            group layer. If you do not want to return any features, set this value to 0 (zero). Typically, you set this value to zero only when you create the 
            service as <see cref="F:ESRI.ArcGIS.Client.Local.GPServiceType.SubmitJobWithMapServerResult"/>. To determine whether the <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.MaxRecords"/> limits has been exceeded you should 
            check the exceeded transfer limit property of the feature set or record set returned by the task. The exceeded transfer limit property will be set to true 
            when the number of records and features of the output parameter exceed the maximum number of records specified by the service. this information is also included 
            in the <see cref="T:ESRI.ArcGIS.Client.Tasks.GPMessage">GPMessages</see> returned by the service.
            </item>
            </list>
            </para>
            <para>
            Once you have created a new LocalGeoprocessingService and specified the <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Path"/>, <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.ServiceType"/> and <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.MaxRecords"/> properties, 
            starting the service via the <see cref="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.StartAsync"/> or <see cref="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Start"/> methods will populate the <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.UrlGeoprocessingService"/> property. It is this 
            last property, the URL of the local geoprocessing service that you provide a <see cref="T:ESRI.ArcGIS.Client.Tasks.Geoprocessor"/> task with in order to interact 
            with the tasks exposed by the service. Additionally you can use the static 
            <see cref="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.GetServiceAsync(System.String,ESRI.ArcGIS.Client.Local.GPServiceType,System.Action{ESRI.ArcGIS.Client.Local.LocalGeoprocessingService})"/> and <see cref="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.GetService(System.String,ESRI.ArcGIS.Client.Local.GPServiceType)"/> methods to instantiate 
            and start a new local geoprocessing service or alternatively the task extension methods 
            <see cref="M:ESRI.ArcGIS.Client.Tasks.GeoprocessorLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.Geoprocessor,System.String,System.String,ESRI.ArcGIS.Client.Local.GPServiceType,System.Action{ESRI.ArcGIS.Client.Local.LocalGeoprocessingService})">InitializeWithLocalServiceAsync</see> and 
            <see cref="M:ESRI.ArcGIS.Client.Tasks.GeoprocessorLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.Geoprocessor,System.String,System.String,ESRI.ArcGIS.Client.Local.GPServiceType)">InitializeWithLocalService</see>.
            </para>
            </remarks>
            <example>This example shows how to create a new LocalGeoprocessingService and use the first task within that service to provide the Url property 
            for the Geoprocessor task:
            <code title="LocalGeoprocessingService Example" description="" lang="CS">
            // Create a new LocalGeoprocessing service of synchronous execution type.
            LocalGeoprocessingService gpService
                = new LocalGeoprocessingService("Path to geoprocessing package", GPServiceType.Execute, 1000000);
                
            gpService.StartAsync(delegateService =&gt;
                {
                    // Instantiate a new Geoprocessor task with the Url.
                    ESRI.ArcGIS.Client.Tasks.Geoprocessor geoprocessorTask
                        = new ESRI.ArcGIS.Client.Tasks.Geoprocessor(gpService.Tasks[0].Url);
                });
            </code>
            <code title="GeoprocessingTaskInfo Example" description="" lang="VB.NET">
            ' Create a new LocalGeoprocessing service of synchronous execution type.
            Dim gpService As New LocalGeoprocessingService("Path to geoprocessing package", GPServiceType.Execute, 100000)
            
            gpService.StartAsync(Function(delegateService)
            
                ' Instantiate a new Geoprocessor task with the Url.
                Dim geoprocessorTask As New ESRI.ArcGIS.Client.Tasks.Geoprocessor(gpService.Tasks(0).Url)
                
            End Function)
            </code>
            </example>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalService">
            <summary>
            Provides a base class for services which can be initialized on the runtime LocalServer.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalService"/> class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.OnIsActiveChanged">
            <summary>
            *FOR INTERNAL USE ONLY* Raises the IsActiveChanged event.
            </summary>
            <exclude/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.OnStartCompleted">
            <summary>
            *FOR INTERNAL USE ONLY* Raises StartCompleted event
            </summary>
            <exclude/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.OnStopCompleted">
            <summary>
            *FOR INTERNAL USE ONLY* Raises StopCompleted event
            </summary>
            <exclude/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.OnPropertyChanged(System.String)">
            <summary>
            *FOR INTERNAL USE ONLY* Triggers the PropertyChanged event.
            </summary>
            <exclude/>
            <param name="name">A <see cref="T:System.String"/> representing the property name.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.StartAsyncImpl">
            <summary>
            Asynchronously start the service creation process. The StartCompleted event is called when the start process is complete.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.StartAsync">
            <summary>
            Asynchronously starts this local service instance and returns a Task object on which the Result property contains LocalService instance.
            </summary>
            <returns>A Task object on which the Result property contains the LocalService instance.</returns>
            <remarks>
            The StartAsync call should be enclosed in a try-catch block to handle any exceptions raised.
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.Start">
            <summary>
            Synchronously starts this local service instance.
            </summary>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalService.StartAsync"/>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalService.StartAsync(System.Action{ESRI.ArcGIS.Client.Local.LocalService})"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.StartAsync(System.Action{ESRI.ArcGIS.Client.Local.LocalService})">
            <summary>
            Start the local service creation process. The callback delegate will be called when the start process is complete.
            </summary>
            <param name="callback">An <see cref="T:System.Action`1"/> representing the local service that has been started.</param>
            <remarks>
            <para>
            The callback action will always be called even if an error was encountered starting the local service. It is recommended that you check the 
            <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Error"/> property to confirm the start was successful.
            </para>
            </remarks>        
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.FailStart(System.Exception)">
            <summary>
            This method is called if the start of a service needs to be fail with an error. It puts the services into a StartError state.
            </summary>
            <param name="error"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.StartServiceInternal(ESRI.ArcGIS.Client.Local.Internal.QueryParameters)">
            <summary>
            This method is called form implementation of StartAsync or Start, it will ensure the LocalServer is running, when it is
            ContinueStartService is called
            </summary>
            <param name="serviceSpecificParams"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.ContinueStartService(ESRI.ArcGIS.Client.Local.Internal.QueryParameters)">
            <summary>
            This is called form StartService and will send a REST request to start a service. If it comes back without an error
            then CompleteStartService is called. This can be overriden by derived implementations of LocalService.
            </summary>
            <param name="serviceSpecificParams"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.CompleteStartService(ESRI.ArcGIS.Client.Local.CreateServiceResponse)">
            <summary>
            This provides the default implementation of completing a service, it looks for an endpoint matching the ServiceType
            (e.g. GeocodeServer) and then initializes the Url and status and fires events. This can be overriden is more steps are required to 
            complete the starting of a service (e.g. getting service metadata).
            </summary>
            <param name="createServiceResponse"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.Abort">
            <summary>
            This method is called when the LocalServer is shutting down - it sets the service to stopped without sending
            rest calls to the local server.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.StopAsync">
            <summary>
            Asynchronously stops this local service instance, and returns a Task object.
            </summary>
            <remarks>The StopAsync call should be enclosed in a try-catch block to handle any exceptions raised.</remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.StopAsync(System.Action{ESRI.ArcGIS.Client.Local.LocalService})">
            <summary>
            This will initiate stopping the local service. The callback action will be called when the service has stopped.
            </summary>
            <param name="callback">The Action delegate to be called when the StopAsync operation has completed.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.Stop">
            <summary>
            Synchronously stops this local service instance.
            </summary>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalService.StopAsync"/>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalService.StopAsync(System.Action{ESRI.ArcGIS.Client.Local.LocalService})"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalService.CompleteStopService(System.Exception)">
            <summary>
            This is called to complete the stopping of the service and reset internal variables and fire event. 
            The service goes into Stopped state. Override this in derived classes to reset variables first before calling this implementation.
            </summary>
            <param name="error"></param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalService.Name">
            <summary>
            Unique name for the service. Auto-assigned if no name is provided before the service is started.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalService.AutoName">
            <summary>
            Returns an automatically generated name for the service based on the Paths etc. This is used
            to set the Name property when the service is started and the Name is null.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalService.Mode">
            <summary>
            Is starting or stopping a service it indicates if thats sync or async
            </summary>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.LocalService.StartCompleted">
            <summary>
            Occurs when the local service creation process completes.
            </summary>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.LocalService.StopCompleted">
            <summary>
            Occurs when the local service has stopped.
            </summary>
        </member>
        <member name="E:ESRI.ArcGIS.Client.Local.LocalService.PropertyChanged">
            <summary>
            Occurs when a LocalService property value changes.
            </summary>
            <remarks>
            The properties that raise the PropertyChanged event are:
            <list type="bullet">
            		<item>
            			<see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Name"/>
            		</item>
            		<item>
            			<see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Error"/>
            		</item>
            		<item>
            			<see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Status"/>
            		</item>
            	</list>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalService.Error">
            <summary>
            Gets the local service error.
            </summary>
            <value>A <see cref="T:System.Exception"/> representing the error.</value>
            <remarks>
            <para>
            If an error is encountered when starting the local service the <see cref="E:ESRI.ArcGIS.Client.Local.LocalService.StartCompleted"/> event will be raised 
            and the exception will be available in the Error property. It is therefore recommended that the Error property is always checked 
            when starting local services to confirm the service started successfully.
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalService.Status">
            <summary>
            Gets the LocalService status.
            </summary>
            <value>
            A <see cref="T:ESRI.ArcGIS.Client.Local.LocalServiceStatus"/> value indicating the current status of the local service.
            </value>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.#ctor">
            <summary>
            Initializes a new instance of the LocalGeoprocessingService class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService"/> class based on the provided path to a geoprocessing package.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path to a geoprocessing package.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.#ctor(System.String,ESRI.ArcGIS.Client.Local.GPServiceType)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService"/> class based on the provided path to a geoprocessing package and 
            service type.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path to a geoprocessing package.</param>
            <param name="serviceType">A <see cref="T:ESRI.ArcGIS.Client.Local.GPServiceType"/> representing the service type.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.#ctor(System.String,ESRI.ArcGIS.Client.Local.GPServiceType,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService"/> class based on the provided path to a geoprocessing package and 
            service type.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path to a geoprocessing package.</param>
            <param name="serviceType">A <see cref="T:ESRI.ArcGIS.Client.Local.GPServiceType"/> representing the service type.</param>
            <param name="maxRecords">A <see cref="T:System.Int32"/> representing the maximum number of records the service can return.</param>
            <remarks>
            <para>
            The <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.MaxRecords"/>
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.GetServiceAsync(System.String,ESRI.ArcGIS.Client.Local.GPServiceType,System.Action{ESRI.ArcGIS.Client.Local.LocalGeoprocessingService})">
            <summary>
            This will either reuse an existing service for the supplied path, or make a new service if its not already running.
            </summary>
            <param name="path"></param>
            <param name="serviceType"></param>
            <param name="callback"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.GetServiceAsync(System.String,ESRI.ArcGIS.Client.Local.GPServiceType)">
            <summary>
            This will either reuse an existing service for the supplied path, or make a new service if its not already running.
            </summary>
            <param name="path"></param>
            <param name="serviceType"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.GetServiceAsync(System.String,ESRI.ArcGIS.Client.Local.GPServiceType,System.Int32,System.Action{ESRI.ArcGIS.Client.Local.LocalGeoprocessingService})">
            <summary>
            This will either reuse an existing service for the supplied path, or make a new service if its not already running.
            </summary>
            <param name="path"></param>
            <param name="serviceType"></param>
            <param name="maxRecords"></param>
            <param name="callback"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.GetServiceAsync(System.String,ESRI.ArcGIS.Client.Local.GPServiceType,System.Int32)">
            <summary>
            This will either reuse an existing service for the supplied path, or make a new service if its not already running.
            </summary>
            <param name="path"></param>
            <param name="serviceType"></param>
            <param name="maxRecords"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.GetService(System.String,ESRI.ArcGIS.Client.Local.GPServiceType)">
            <summary>
            This method will either reuse an existing service for the supplied path  and execution type or create a new service if one is not already running.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path to the geoprocessing package.</param>
            <param name="serviceType">Type of the service.</param>
            <returns>An existing LocalGeoprocessingService instance if one exists with the same properties in the 
            <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">LocalServer Services collection</see>, otherwise a new LocalGeoprocessingService instance.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.GetService(System.String,ESRI.ArcGIS.Client.Local.GPServiceType,System.Int32)">
            <summary>
            This method will either reuse an existing service for the supplied path  and execution type or create a new service if one is not already running.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path to the geoprocessing package.</param>
            <param name="serviceType">Type of the service.</param>
            <param name="maxRecords">A <see cref="T:System.Int32"/> representing the <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.MaxRecords">maximum number of records</see> that can be returned.</param>
            <returns>An existing LocalGeoprocessingService instance if one exists with the same properties in the 
            <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">LocalServer Services collection</see>, otherwise a new LocalGeoprocessingService instance.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.StartAsync">
            <summary>
            Asynchronously starts the local geoprocessing service.
            </summary>
            <returns>
            A Task object on which the Result property contains the LocalService instance.
            </returns>
            <exception cref="T:System.InvalidOperationException">StartAsync should can not be called if the local service is already in the
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state.</exception>
            <exception cref="T:System.InvalidOperationException">The <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Path"/> property must specify a valid geoprocessing package.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Start">
            <summary>
            Synchronously starts the local service instance.
            </summary>
            <remarks>
            <para>
            The Start method will check whether this local service is already in the <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state and 
            raise an <see cref="T:System.InvalidOperationException"/> if necessary. The <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Path"/> property 
            will also be checked for a valid geoprocessing package.
            </para>
            <para>
            The StartAsync method call should be enclosed in a try-catch block to handle any exceptions that might be raised.
            </para>
            </remarks>
            <exception cref="T:System.InvalidOperationException">StartAsync should can not be called if the local service is already in the 
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state.</exception>
            <exception cref="T:System.InvalidOperationException">An exception is raised if the <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Path"/> property 
            does not reference a valid geoprocessing package file.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.CompleteStartService(ESRI.ArcGIS.Client.Local.CreateServiceResponse)">
            <summary>
             This provides the default implementation of completing a service, it looks for an endpoint matching the ServiceType of GeoprocessingServer and then initializes the Url and changes the LocalServer status and fires the OnStartCompleted event. This can be overriden if more steps are required to 
            complete the starting of a service (e.g. getting service metadata).
            </summary>
            <param name="createServiceResponse"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.CompleteStopService(System.Exception)">
            <summary>
             This is called to complete the stopping of the service and reset internal variables and fire event. 
            The service goes into Stopped state. Override this in derived classes to reset variables first before calling this implementation.
            </summary>
            <param name="error"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.PopulateGeoprocessingTaskInfo(ESRI.ArcGIS.Client.Local.GeoprocessingTaskInfoInternal,System.String)">
            <summary>
            This will get the informations needed from the internal GeoprocessingServiceInfo and expose the one that are relevant for the user work.
            </summary>
            <param name="geoprocessingTaskInfo">The class GeoprocessingTaskInfoInternal that contains all the field describing the Geoprocessing Task.</param>
            <param name="urlTask">A string that represant the path to the Task on the local service.</param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.AutoName">
            <summary>
             Gets the PreferredName 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Path">
            <summary>
            Gets or sets the path or Url to the geoprocessing package.
            </summary>
            <value>
            A <see cref="T:System.String"/> representing the path to a geoprocessing package or disk or Url of the geoprocessing package on ArcGIS.com.
            </value>
            <exception cref="T:System.InvalidOperationException">The Path property cannot be changed if the local service is in the 
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.ServiceType">
            <summary>
            Specifies the execution type of the local geoprocessing service. 
            </summary>
            <remarks>
            <para>
            This can use the synchronous ExecuteAsync/Execute type or asynchronous SubmitJobAsync/SubmitJob type. 
            Additionally the SubmitJobAsync/SubmitJob operation can also optionally return results via a map service.
            </para>
            </remarks>
            <seealso cref="T:ESRI.ArcGIS.Client.Local.GPServiceType"/>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.MaxRecords">
            <summary>
            Specifies the maximum number of records that can be returned by the Geoprocessing service. 
            </summary>
            <remarks>
            <para>
            The MaxRecords property must be set before the Geoprocessing service has started. This property has a default value of 1000. 
            </para>
            <para>
            The maximum number of records property determines the number of output features or records returned by the service. 
            If the number of output records or features created by a geoprocessing task exceeded the maximum number of records, 
            the service will not return any features or records. The clients can identify such cases by examining the exceeded transfer limit 
            property of the feature set or record set returned by the task. The exceeded transfer limit property will be set to true when the 
            number of records and features of the output parameter exceed the maximum number of records specified by the service.
            </para> 
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.UrlGeoprocessingService">
            <summary>
             Gets the Url of the local geoprocessing service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Tasks">
            <summary>
            This will return a collection of GeoprocessingTaskInfo that are describing each Geoprocessing Task available with the Geoprocessing Service.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalFeatureService">
            <summary>
            The LocalFeatureService class represents a Feature Service hosted by the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>.
            </summary>  
            <remarks>
            <para>
            LocalFeatureServices form the basis for feature editing in conjunction with an instance of the <see cref="T:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer"/> class.
            The feature service is just a map service with the Feature Access capability enabled. This capability allows the map service to expose feature 
            geometries and their symbols in a way that is easy for the API to use and update. Unlike with map services, which have an ExportMap function 
            to draw the map, visualization of feature services works completely from queries. The server returns the queried features to the client, then 
            the client does the work of drawing the features in the feature layer. 
            </para>
            </remarks>
            <example>
            The following examples demonstrate how to define an ArcGISLocalFeatureLayer in code using the static GetServiceAsync:
            <code title="ArcGISLocalFeatureLayer in Code" description="" lang="CS">
            // Create a new local feature service instance and supply an ArcGIS Map Package path as string.
            LocalFeatureService.GetServiceAsync(@"Path to map package",
                localFeatureService =&gt;
                {
                    // Create a new local feature layer instance and provide the local feature service and a layer name.
                    ArcGISLocalFeatureLayer arcGISLocalFeatureLayer
                        = new ArcGISLocalFeatureLayer(localFeatureService, "Cities");
                        
                    // Set the ID.
                    arcGISLocalFeatureLayer.ID = "Cities";
                    
                    // Add the local feature layer to the map. The map will handle the initialization of the layer.
                    _map.Layers.Add(arcGISLocalFeatureLayer);
                });
            </code>
            <code title="ArcGISLocalFeatureLayer in Code" description="" lang="VB.NET">
            ' Create a new local feature service instance and supply an ArcGIS Map Package path as string.
            LocalFeatureService.GetServiceAsync("Path to map package",
                Function(localServiceDelegate)
                    ' Create a new local feature layer instance and provide the local feature service and a layer name.
                    Dim arcGISLocalFeatureLayer As New ArcGISLocalFeatureLayer(localServiceDelegate, "Cities")
                    
                    ' Set the ID.
                    arcGISLocalFeatureLayer.ID = "Cities"
                    
                    ' Add the local feature layer to the map. The map will handle the initialization of the layer which 
                    ' in turn will start the local feature service if required.
                    _map.Layers.Add(arcGISLocalFeatureLayer)
                    
                End Function)
            </code>
            </example>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalMapService">
            <summary>
            The LocalMapService class represents a map service hosted by the LocalServer.
            </summary>
            <remarks>
            <para>
            A local map service provides access to maps, features, and attribute data contained within a Map Package. Local map services are typically 
            used to display operational or business data on top of a tiled basemap layer.
            Map images from local map services are dynamically rendered from the data with each request (e.g. when the user pans the map). Local map services 
            do not support caching in the same way as other server-based map services. To use local cached maps in your application you should use the 
            <see cref="T:ESRI.ArcGIS.Client.ArcGISLocalTiledLayer">ArcGISLocalTiledLayer class</see>.
            Local map services have built-in capabilities that allow each layer's behavior and appearance to be changed dynamically. 
            These dynamic layers can increase the amount of interaction that users are able to have with maps within your application. For more information see 
            the <see cref="T:ESRI.ArcGIS.Client.ArcGISDynamicMapServiceLayer">ArcGISDynamicMapServiceLayer class</see> documentation.
            </para>
            <para>
            Local map services don't always need to display images. You can create a local map service for the purpose of returning a set of features to work within your 
            application. You retrieve these features through tasks that you add to your application. For example, you might want to query a map service and display 
            the resulting features as graphics in the map. Local map services always have their Query and Data operations enabled in contrast to map services hosted 
            by ArcGIS for Server where these operations can be explicitly enabled or disabled by the server administrator.
            </para>
            <para>
            To start a LocalMapService you can either create a new instance of a LocalMapService object via the default constructor then 
            set the path property or use the <see cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.#ctor(System.String)">overloaded constructor</see>. 
            Once the path property has been set by either approach above, the <see cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.StartAsync">
            StartAsync</see> method should be called to start the LocalMapService which will be hosted by the 
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>. When the LocalMapService creation completes the 
            <see cref="E:ESRI.ArcGIS.Client.Local.LocalService.StartCompleted">StartCompleted</see> event will be raised. The 
            StartCompleted even will always fire regardless of whether the service creation was successful or unsuccessful. Therefore you 
            should check the <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Error">Error</see> property to confirm the service 
            creation was successful. Additionally the <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Status">Status</see> property 
            can be checked to determine the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServiceStatus">LocalServiceStatus</see> state. 
            </para>
            <para>
            Alternatively the <see cref="M:ESRI.ArcGIS.Client.Local.LocalService.StartAsync(System.Action{ESRI.ArcGIS.Client.Local.LocalService})">overloaded StartAsync</see> 
            method can be called with an Action callback delegate. In the same pattern as the StartAsync method, the callback will always happen 
            whether the LocalMapService creation was successful or not. 
            </para>
            <para>
            Finally, there is one other convenience method for obtaining a LocalMapService object. 
            Based on the supplied path to a Map Package the <see cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            GetServiceAsync</see> method will either start a new LocalMapService or return a reference to an existing 
            LocalMapService if one has previously been created for that particular Map Package resource with the same service properties. 
            The second parameter of this method is an Action callback delegate which will return a reference to a LocalMapService. 
            As with the other approaches to starting LocalMapServices, the Error property should be checked to confirm a successful or unsuccessful service creation.
            </para>
            <para>
            It is not necessary to work with the LocalMapService class directly. Instead the 
            <see cref="M:ESRI.ArcGIS.Client.Local.ArcGISLocalDynamicMapServiceLayer.#ctor(System.String)">overloaded ArcGISLocalDynamicMapServiceLayer 
            constructor</see> takes a <see cref="T:System.String"/> representing the path to a Map Package and will handle the creation 
            of the underlying LocalMapService. The path string can either refer to a Map Package on disk or on ArcGIS.com. The 
            ArcGISLocalDynamicMapServiceLayer can be added to a <see cref="T:ESRI.ArcGIS.Client.Map"/> in this state, without waiting 
            for the LocalMapService creation to complete. Once the LocalMapService initialization is complete, the Url property of the ArcGISLocalDynamicMapServiceLayer 
            will be set and the ArcGISLocalDynamicMapServiceLayer will begin to draw. If using this approach the 
            <see cref="E:ESRI.ArcGIS.Client.Layer.InitializationFailed">InitializationFailed</see> event 
            should be listened for to determine whether the layer initialization failed. In the case of a failure the 
            <see cref="P:ESRI.ArcGIS.Client.Layer.InitializationFailure">InitializationFailure</see> 
            property will contain the Exception. Another more granular approach is to create a new ArcGISLocalDynamicMapServiceLayer 
            object and explicitly set the path property then call the Initialize method which again will handle the creation of the supporting 
            LocalMapService. 
            </para>
            </remarks>
            <example>
            The following code shows the recommended approach to explicitly starting a LocalMapService using the StartAsync method with an Action delegate.
            <code title="LocalMapService StartAsync Example" description="" lang="CS">
            string mpkPath = @"Path to Map Package (.mpk)";
            LocalMapService localMapService = new LocalMapService()
            {
                Path=mpkPath,
                MaxRecords=100000
            };
            localMapService.StartAsync(localService =&gt;
            {
                if (localService.Error != null)
                    return;
                ArcGISLocalDynamicMapServiceLayer layer = new ArcGISLocalDynamicMapServiceLayer(localMapService);
                _map.Layers.Add(layer);
            }); 
            </code>
            <code title="LocalMapService StartAsync Example" description="" lang="VB.NET">
            Dim mpkPath As String = "Path to Map Package (.mpk)"
            Dim _localMapService As New LocalMapService()
            _localMapService.Path = mpkPath#
            _localMapService.MaxRecords = 100000
            
            _localMapService.StartAsync(Function(localService)
                If localService.Error IsNot Nothing Then
                    Exit Function
                End If
                Dim layer As New ArcGISLocalDynamicMapServiceLayer(_localMapService)
                _map.Layers.Add(layer)
            End Function)
            </code>
            </example>
            <example>
            The following example shows the static convenience method GetServiceAsync on the LocalMapService class. 
            This is an alternative and in many cases simpler approach to using the StartAsync method.
            <code title="LocalMapService GetServiceAsync Example" description="" lang="CS">
             LocalMapService.GetServiceAsync(@"Path to Map Package (.mpk)", (serviceDelegate) =&gt;
             {
                // GetServiceAsync returns once the service has started.
                // Declare a new ArcGISLocalDynamicMapServiceLayer.
                ArcGISLocalDynamicMapServiceLayer localArcGISDynamicMapServiceLayer = new ArcGISLocalDynamicMapServiceLayer()
                {
                    // Set the service property (must be done before layer is initialized).
                    Service = serviceDelegate, 
                    // Give the layer an identifier.
                    ID = "operationalLayer", 
                    // Set the image format to PNG 32 for highest quality.
                    ImageFormat = ArcGISDynamicMapServiceLayer.RestImageFormat.PNG32, 
                };
                // Add the layer to the map.
                _map.Layers.Add(localArcGISDynamicMapServiceLayer); 
                });
            </code>
            <code title="LocalMapService GetServiceAsync Example" description="" lang="VB.NET">
            LocalMapService.GetServiceAsync("Path to Map Package (.mpk)", _
                Function(serviceDelegate)
                    ' Declare a new ArcGISLocalDynamicMapServiceLayer
                    Dim localArcGISDynamicMapServiceLayer As New ArcGISLocalDynamicMapServiceLayer()
                    ' Set the service property (must be done before layer is initialized)
                    localArcGISDynamicMapServiceLayer.Service = serviceDelegate
                    ' Give the layer an identifier
                    localArcGISDynamicMapServiceLayer.ID = "operationalLayer"
                    ' Set the image format to PNG 32 for highest quality
                    localArcGISDynamicMapServiceLayer.ImageFormat = ArcGISDynamicMapServiceLayer.RestImageFormat.PNG32
                    ' Add the layer to the map
                    _map.Layers.Add(localArcGISDynamicMapServiceLayer)
                End Function)
            </code>
            </example>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.OnIsActiveChanged">
            <summary>
            *FOR INTERNAL USE ONLY* Raises the IsActiveChanged event.
            </summary>      
            <exclude/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> class.
            </summary>
            <remarks>
            <para>
            This is the default constructor for creating a new LocalMapService instance. If creating an instance via the default 
            constructor it is necessary to set the <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.Path">path</see> property 
            prior to starting the service. 
            </para>
            <para>
            Alternatively you can use the <see cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.#ctor(System.String)">overloaded constructor</see> 
            to create a new LocalMapService instance and set the path property.
            </para>    
            <para>
            Once the path property has been set by either approach above, the <see cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.StartAsync">
            StartAsync</see> method can be called to start the LocalMapService.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.#ctor(System.String)">
            <summary>
            Initializes a new instance of the LocalMapService class based on the provided path property.
            </summary>
            <param name="path">A string representing the path to an ArcGIS Map Package (.MPK) either located on disk on on 
            ArcGIS.com.</param>
            <remarks>
            <para>
            This overloaded constructor provides a more convenient option for instantiating a new LocalMapService object and setting 
            the path property in one call. 
            </para>
            <para>
            Once the path property has been set, the <see cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.StartAsync">
            StartAsync</see> method can be called to start the LocalMapService.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">The path or Url of the Map Package.</param>
            <param name="callback">An Action delegate.</param>
            <remarks>
            <para>
            This is a convenience method for obtaining a reference to a LocalMapService object 
            Based on the supplied path to a Map Package the <see cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            GetServiceAsync</see> method will either start a new LocalMapService or return a reference to an existing 
            LocalMapService if one has previously been created for that particular Map Package resource. The second parameter of this 
            method is an Action delegate which will return a reference to a LocalMapService. As with the other approaches to 
            starting LocalMapServices, the Error property should be checked to confirm a successful or unsuccessful service creation.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String)">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">The path or Url of the Map Package.</param>
            <remarks>
            <para>
            This is a convenience method for obtaining a reference to a LocalMapService object 
            Based on the supplied path to a Map Package the <see cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            GetServiceAsync</see> method will either start a new LocalMapService or return a reference to an existing 
            LocalMapService if one has previously been created for that particular Map Package resource. The second parameter of this 
            method is an Action delegate which will return a reference to a LocalMapService. As with the other approaches to 
            starting LocalMapServices, the Error property should be checked to confirm a successful or unsuccessful service creation.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Int32,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">The or Url of the Map Package.</param>
            <param name="maxRecords">The maximum number of records to return in response to a query.</param>
            <param name="callback">An Action delegate.</param>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Int32)">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">The or Url of the Map Package.</param>
            <param name="maxRecords">The maximum number of records to return in response to a query.</param>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Int32,System.Boolean,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">The or Url of the Map Package.</param>
            <param name="maxRecords">The maximum number of records to return in response to a query.</param>
            <param name="enableDynamicLayers"><see langword="true"/> if the Dynamic Layers capability is to be enabled; otherwise <see langword="false"/></param>
            <param name="callback">An Action delegate.</param>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Int32,System.Boolean)">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">The or Url of the Map Package.</param>
            <param name="maxRecords">The maximum number of records to return in response to a query.</param>
            <param name="enableDynamicLayers"><see langword="true"/> if the Dynamic Layers capability is to be enabled; otherwise <see langword="false"/></param>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetService(System.String)">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path or Url to the Map Package.</param>
            <returns>An existing LocalMapService instance if one exists with the same properties in the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">LocalServer Services collection</see>, 
            otherwise a new LocalMapService instance.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetService(System.String,System.Int32)">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path or Url of the Map Package.</param>
            <param name="maxRecords">A <see cref="T:System.Int32"/> representing the maximum number of records to return in response to a query.</param>
            <returns>An existing LocalMapService instance if one exists with the same properties in the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">LocalServer Services collection</see>, 
            otherwise a new LocalMapService instance.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.GetService(System.String,System.Int32,System.Boolean)">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">The path or Url of the Map Package.</param>
            <param name="maxRecords">The maximum number of records to return in response to a query.</param>
            <param name="enableDynamicLayers"><see langword="true"/> if the Dynamic Layers capability is to be enabled; otherwise <see langword="false"/></param>
            <returns>An existing LocalMapService instance if one exists with the same properties in the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">LocalServer Services collection</see>, 
            otherwise a new LocalMapService instance.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.StartAsync">
            <summary>
            Asynchronously creates a LocalMapService based on the Map Package referenced by the Path property. 
            Returns a Task object on which the Result property contains the LocalService instance.
            </summary>
            <returns>A Task object on which the Result property contains the LocalService instance.</returns>
            <remarks>
              <para>
            The StartAsync method will perform various checks to ascertain the status of both the LocalMapService instance
            and the underlying LocalServer:
              <list type="bullet">
              <item>
            First the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServiceStatus"/> of the
            LocalMapService instance will be ascertained. In the cases that it is already <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> or
              <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Starting"/> the method will exit.
            If the LocalMapService creation failed, the current status will be <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.StartError"/>,
            and an InvalidOperationException will be thrown.
              </item>
              <item>
            The status of the LocalServer will then be confirmed. If the status is <see cref="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.Running"/> the
            LocalMapService creation will continue. In the cases that the LocalServer is <see cref="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.NotInitialized"/> or
              <see cref="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.Initializing"/> an attempt will be made to start the LocalServer and execution will wait until
            the status is <see cref="F:ESRI.ArcGIS.Client.Local.LocalServerStatus.Running"/> at which point the attempt to create the LocalMapService will be remade.
            The status will also be <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.StartError"/> if the StartAsync method is called whilst the LocalServer
            is shutting down. If the underlying LocalServer encountered an error and failed to initialize correctly
            an exception will be raised, supplemented by the information contained within the
              <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.InitializationError"/>.
              </item>
              </list>
              </para>
              <para>
            The StartAsync operation should be enclosed in a try-catch block to handle any exceptions raised.
              </para>
            </remarks>
            <exception cref="T:System.InvalidOperationException">StartAsync should can not be called if the local service is already in the
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state.</exception>
            <exception cref="T:System.InvalidOperationException">The <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.Path"/> property must specify a valid Map Package.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.Start">
            <summary>
            Synchronously starts the LocalMapService instance.
            </summary>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalMapService.StartAsync"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.CompleteStartService(ESRI.ArcGIS.Client.Local.CreateServiceResponse)">
            <summary>
            *FOR INTERNAL USE ONLY* This provides the default implementation of completing a service, it looks for an endpoint matching the ServiceType
            (e.g. GeocodeServer) and then initializes the Url and status and fires events. This can be overriden is more steps are required to
            complete the starting of a service (e.g. getting service metadata).
            </summary>
            <param name="createServiceResponse"></param>
            <exclude/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.CompleteStopService(System.Exception)">
            <summary>
            This is called to complete the stopping of the service and reset internal variables and fire event.
            The service goes into Stopped state. Override this in derived classes to reset variables first before calling this implementation.
            </summary>
            <param name="error"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.FindMapLayerDetails(System.String)">
            <summary>
            Gets the LayerDetails for a given map layer within the local map service.
            </summary>
            <param name="layerName">The name of the layer within the map service.</param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.LayerDetails"/> object.</returns>
            <seealso cref="T:ESRI.ArcGIS.Client.Local.LayerDetails"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.FindMapLayerDetails(System.Int32)">
            <summary>
            Gets the LayerDetails for a given map layer within the local map service.
            </summary>
            <param name="id">The ID of the layer within the map service.</param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.LayerDetails"/> object.</returns>
            <seealso cref="T:ESRI.ArcGIS.Client.Local.LayerDetails"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalMapService.ToString">
            <summary>
            Gets the path to the Map Package on which the LocalMapService is based.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that contains the map path.
            </returns>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.Path">
            <summary>
            Gets or sets the path of the Map Package on which the LocalMapService is based.
            </summary>
            <remarks>
            <para>
            The Path property may only be set once prior to the LocalMapService being started.
            </para>
            </remarks>
            <value>A String representing the physical path to the Map Package.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.EnableDynamicLayers">
            <summary>
            Gets or sets a value indicating whether the dynamic layers capability is enabled.
            </summary>
            <value>
            <see langword='true' /> if the Dynamic Layers capability is to be enabled; otherwise <see langword='false' />.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.DynamicWorkspaces">
            <summary>
            Gets or sets the dynamic workspaces to be used in conjunction with the dynamic layers functionality.
            </summary>
            <value>
            A <see cref="T:System.Collections.ObjectModel.Collection`1">Collection</see> of <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> objects.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService">
            <summary>
            Gets the Url of the local map service.
            </summary>
            <remarks>
            <para>
            The Url property is used when creating tasks which operate against the service or specific layers within the service:
            <list type="bullet">
            <item>
            <see cref="T:ESRI.ArcGIS.Client.Tasks.FindTask"/>
            </item>
            <item>
            <see cref="T:ESRI.ArcGIS.Client.Tasks.GenerateRendererTask"/>
            </item>
            <item>
            <see cref="T:ESRI.ArcGIS.Client.Tasks.IdentifyTask"/>
            </item>
            <item>
            <see cref="T:ESRI.ArcGIS.Client.Tasks.QueryTask"/>
            </item>
            </list>
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.MapLayers">
            <summary>
            Gets the collection of map layers exposed by the local map service.
            </summary>
            <value>
            A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1">ReadOnlyCollection</see> of <see cref="T:ESRI.ArcGIS.Client.Local.LayerDetails"/>.
            </value>
            <seealso cref="T:ESRI.ArcGIS.Client.Local.LayerDetails"/>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.SpatialReference">
            <summary>
            Gets the spatial reference of the LocalMapService. Note: this Property is ReadOnly.
            </summary>
            <value>The <see cref="T:ESRI.ArcGIS.Client.Geometry.SpatialReference"/> of the local map service.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.Units">
            <summary>
            Gets the map units the local map service uses for its default spatial reference. Note: this Property is ReadOnly.
            </summary>
            <value>A <see cref="T:System.String"/> representing the map units.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.InitialExtent">
            <summary>
            Gets the initial extent of the local map service. Note: this Property is ReadOnly.
            </summary>
            <value>An <see cref="T:ESRI.ArcGIS.Client.Geometry.Envelope"/> representing the initial extent of the local map service.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.FullExtent">
            <summary>
            Gets the full extent of the local map service. Note: this Property is ReadOnly.
            </summary>
            <value>An <see cref="T:ESRI.ArcGIS.Client.Geometry.Envelope"/> representing the full extent of the local map service.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.CopyrightText">
            <summary>
            Gets the copyright text included with the local map service. Note: this Property is ReadOnly.
            </summary>
            <value>A <see cref="T:System.String"/> containing the copyright text from the source Map Package.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.Description">
            <summary>
            Gets the description of the local map service. Note: this Property is ReadOnly.
            </summary>
            <value>A <see cref="T:System.String"/> containing the description of the source Map Package.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.MapName">
            <summary>
            Gets the map name of the map on which the local map service is based. Note: this Property is ReadOnly.
            </summary>
            <value>A <see cref="T:System.String"/> containing the map name.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalMapService.MaxRecords">
            <summary>
            Gets or Sets the maximum number of records that can be returned by the LocalMapService in response to a query, find, or identify operation.
            </summary>
            <remarks>
            <para>
            The MaxRecords property should be set before the LocalMapService has started.
            This property has a default value of 1000. If the property is not set explicitly the service will return the first 1000 records. 
            The service can still exceed the MaxRecords property set.
            For both these scenarios more information can be found in the results of an operation to find out whether the service has exceeded the MaxRecords property set.
            To improve performance it is recommended that the MaxRecords property is set in order to limit an operation that may return a large number of results.
            </para> 
            </remarks>
            <value>A <see cref="T:System.Int32"/> representing the maximum number of records that can be returned by query, find, and identify operations on a local map service.</value> 
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalFeatureService._urlFeatureService">
            <summary>
            The LocalFeatureService Url
            </summary> 
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalFeatureService._featureLayerDetails">
            <summary>
            The list of layer metadata
            </summary> 
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalFeatureService._featureLayerDetailsCollection">
            <summary>
            The read-only collection of layer metadata
            </summary> 
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalFeatureService._enableZDefaults">
            <summary>
            Enable Z Default values
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalFeatureService._zDefaultValue">
            <summary>
            Default Z value
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.#ctor">
            <summary>
            Initializes a new instance of the LocalFeatureService class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.#ctor(System.String)">
            <summary>
            Initializes a new instance of the LocalFeatureService class based on the provided ArcGIS map package path property.
            </summary>
            <param name="path">A string representing the path or URL of an ArcGIS map package.</param>
            <remarks>
            <para>
            This overloaded constructor provides a more convenient option for instantiating a new LocalFeatureService object and setting 
            the path property in one call. 
            </para>
            <para>
            Once the path property has been set, the <see cref="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.StartAsync">
            StartAsync</see> method can be called to start the LocalFeatureService.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalFeatureService})">
            <summary>
            Creates and starts a new LocalFeatureService or reuses an existing
            LocalFeatureService if one exists with the same properties in the runtime <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>.
            </summary>
            <param name="path">A string representing the path or URL of a ArcGIS map package.</param>
            <param name="callback">An action delegate.</param>
            <remarks>
            <para>
            This method, particularly when used in conjunction with a Lambda Expression, provides a convenient way
            to quickly obtain a reference to a LocalFeatureService without having to specifically check for existing
            LocalFeatureService instances already running on the LocalServer or directly instantiate a new
            LocalFeatureService and explicitly call the <see cref="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.StartAsync">
            StartAsync method</see>.
            </para>
            <para>
            The Action delegate will always be called regardless of the success or failure of the LocalServer to
            create or return a LocalFeatureService. For this reason the <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Error">
            Error property</see> should always be checked to confirm a successful execution. The Error property will be
            will be null if the if the LocalFeatureService was started successfully.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetServiceAsync(System.String)">
            <summary>
            Creates and starts a new LocalFeatureService or reuses an existing
            LocalFeatureService if one exists with the same properties in the runtime <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>.
            </summary>
            <param name="path">A string representing the path or URL of a ArcGIS map package.</param>
            <remarks>
            <para>
            This method, particularly when used in conjunction with a Lambda Expression, provides a convenient way
            to quickly obtain a reference to a LocalFeatureService without having to specifically check for existing
            LocalFeatureService instances already running on the LocalServer or directly instantiate a new
            LocalFeatureService and explicitly call the <see cref="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.StartAsync">
            StartAsync method</see>.
            </para>
            <para>
            The Action delegate will always be called regardless of the success or failure of the LocalServer to
            create or return a LocalFeatureService. For this reason the <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Error">
            Error property</see> should always be checked to confirm a successful execution. The Error property will be
            will be null if the if the LocalFeatureService was started successfully.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetServiceAsync(System.String,System.Int32,System.Action{ESRI.ArcGIS.Client.Local.LocalFeatureService})">
            <summary>Creates and starts a new LocalFeatureService or reuses an existing 
            LocalFeatureService if one is already running on the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>. 
            </summary>
            <param name="path">A string representing the path to a Map Package.</param>
            <param name="maxRecords">An integer representing the maximum number of records that can be returned by the service in response to a query. 
            This also determines how many features will be visible in the map as requested from the service.</param>
            <param name="callback">An action delegate either in the form of a lambda expression 
            or a named method (event handler).</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetServiceAsync(System.String,System.Int32)">
            <summary>Creates and starts a new LocalFeatureService or reuses an existing 
            LocalFeatureService if one is already running on the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>. 
            </summary>
            <param name="path">A string representing the path to a Map Package.</param>
            <param name="maxRecords">An integer representing the maximum number of records that can be returned by the service in response to a query. 
            This also determines how many features will be visible in the map as requested from the service.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetServiceAsyncInternal(System.String,System.Nullable{System.Int32},System.Action{ESRI.ArcGIS.Client.Local.LocalFeatureService})">
            <summary>
            Asynchronously gets the service.
            </summary>
            <param name="path">The path.</param>
            <param name="maxRecords">The max records.</param>
            <param name="callback">The callback.</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetService(System.String)">
            <summary>
            Creates and attempts to start a new LocalFeatureService instance based on the provided path to a Map Package.
            </summary>
            <param name="path">A string representing the path to a Map Package.</param>
            <returns>A new LocalFeatureService instance.</returns>
            <remarks>
            <para>
            The GetService method is a synchronous static convenience method on the LocalFeatureService class which takes a path to an Map Package. The method creates a new LocalFeatureService instance 
            and attempts to start that local service instance. When the LocalFeatureService is returned, the <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Status"/> property should be checked to determine the 
            state of the service and in the case of a <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.StartError"/> the <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Error">Error property</see> should checked to determine the cause.
            </para>
            <para>
            If a LocalFeatureService based on the same Map Package is already available in the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services"/> collection the existing instance will be returned rather than creating a new 
            instance.
            </para>
            </remarks>
            <seealso cref="T:System.IO.Path"/>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalFeatureService})"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetService(System.String,System.Int32)">
            <summary>
            Creates and attempts to start a new LocalFeatureService instance based on the provided path to a map package and the maximum records value.
            </summary>
            <param name="path">A string representing the path to a Map Package.</param>
            <param name="maxRecords">An integer representing the maximum number of records that can be returned by the service in response to a query. 
            This also determines how many features will be visible in the map as requested from the service.</param>
            <returns>A new LocalFeatureService instance.</returns>
            <remarks>
            <para>
            If a LocalFeatureService based on the same Map Package and with the same setting for maximum records is already available in the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services"/> collection the existing 
            instance will be returned rather than creating a new instance.
            </para>
            </remarks>
            <seealso cref="T:System.IO.Path"/>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalFeatureService})"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.GetServiceInternal(System.String,System.Nullable{System.Int32})">
            <summary>
            Gets the service.
            </summary>
            <param name="path">The path.</param>
            <param name="maxRecords">The max records.</param>
            <returns></returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.StartAsync">
            <summary>
            Starts this LocalFeatureService instance asynchronously. Returns a Task object on which the Result property contains the LocalService instance.
            </summary>
            <returns>
            A Task object on which the Result property contains the LocalService instance.
            </returns>
            <remarks>
            The StartAsync method call should be enclosed in a try-catch block to handle any exceptions that might be raised.
            </remarks>
            <exception cref="T:System.InvalidOperationException">StartAsync should can not be called if the local service is already in the
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state.</exception>
            <exception cref="T:System.InvalidOperationException">The <see cref="T:System.IO.Path"/> property must specify a valid Map Package.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.Start">
            <summary>
            Synchronously starts this LocalFeatureService instance on the LocalServer.
            </summary>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.StartAsync"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.CompleteStartService(ESRI.ArcGIS.Client.Local.CreateServiceResponse)">
            <summary>
             This provides the default implementation of completing a service, it looks for an endpoint matching the ServiceType of FeatureServer and then initializes the Url and changes the LocalServer status and fires the OnStartCompleted event. This can be overriden if more steps are required to 
            complete the starting of a service (e.g. getting service metadata).
            </summary>
            <param name="createServiceResponse"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.UpdateBasicFeatureServiceProperties(System.String,ESRI.ArcGIS.Client.Local.FeatureServiceInfo)">
            <summary>
             This provides the details of the LocalFeatureService layers
            </summary>
            <param name="url">A string representing the url to the LocalFeatureService</param>
            <param name="featureService">FeatureServiceInfo</param>
            <returns>A list representing the layer details of the LocalFeatureService.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.CompleteStopService(System.Exception)">
            <summary>
             This is called to complete the stopping of the service and reset internal variables and fire event. 
            The service goes into Stopped state. Override this in derived classes to reset variables first before calling this implementation.
            </summary>
            <param name="error"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.FindFeatureLayerDetails(System.String)">
            <summary>
             Finds the metadata about a layer specified by its layer name in the LocalFeatureService
            </summary>  
            <returns>A list representing the layer details of the specified layer in the LocalFeatureService.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.FindFeatureLayerDetails(System.Int32)">
            <summary>
             Finds the metadata about a layer specified by its numeric layer ID in the LocalFeatureService
            </summary>
            <returns>A list representing the layer details of the specified layer in the LocalFeatureService.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalFeatureService.ToString">
            <summary>
            Returns a string representing the path of the map package on which the LocalFeatureService is based.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that contains the LocalFeatureService path.
            </returns>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalFeatureService.UrlFeatureService">
            <summary>
             The Url of this LocalFeatureService instance.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalFeatureService.EnableZDefaults">
            <summary>
            TODO
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalFeatureService.ZDefaultValue">
            <summary>
            TODO
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalFeatureService.FeatureLayers">
            <summary>
            Gets metadata for the individual layers within the LocalFeatureService.
            </summary>
            <value>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1"/> of LayerDetails objects.</value>
            <seealso cref="T:ESRI.ArcGIS.Client.Local.LayerDetails"/>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LayerDetails">
            <summary>
            The LayerDetails class provides information on the sub layers within instances of <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> and 
            <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService"/>.
            </summary>
            <remarks>
            <para>
            LayerDetails are typically accessed via the <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.MapLayers"/> and <seealso cref="P:ESRI.ArcGIS.Client.Local.LocalFeatureService.FeatureLayers"/> properties, both of which return a 
            <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1"/> of LayerDetails objects.
            </para>
            </remarks>
            <seealso cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.MapLayers"/>
            <seealso cref="P:ESRI.ArcGIS.Client.Local.LocalFeatureService.FeatureLayers"/>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.Id">
            <summary>
            Gets the index position of the layer within the <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> or <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService"/>.
            </summary>
            <value>An integer indicating the index position of the layer within the local service. Layer Ids are a zero based index and are always unique.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.Url">
            <summary>
            Gets the URL of this specific layer within the <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> or <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService"/>.
            </summary>
            <value>A string representing the layer within the service as a Url resource. 
            This is useful when working with the tasks classes, such as the QueryTask, which operate on a specific layer within a service.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.Name">
            <summary>
            Gets the name of the layer within the <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> or <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService"/>.
            </summary>
            <value>A string representing the name of the layer within the local service. 
            The name may not be unique, depending on how the source map document was designed.</value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.IsQueryable">
            <summary>
            Indicates whether the layer is queryable.
            </summary>
            <value>
            <see langword='true' /> if this layer can be queried; otherwise, <see langword='false' />.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.IsEditable">
            <summary>
            Gets a value indicating whether this layer can be edited.
            </summary>
            <value>
            <see langword='true' /> if this layer can be edited; otherwise, <see langword='false' />.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.HasAttachments">
            <summary>
            Gets a value indicating whether this layer has attachments associated with features.
            </summary>
            <value>
            <see langword='true' /> if features within this layer have attachments; otherwise, <see langword='false' />.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.Extent">
            <summary>
            Gets the extent of this layer.
            </summary>
            <value>
            An <see cref="T:ESRI.ArcGIS.Client.Geometry.Envelope"/> representing the spatial extent of the layer.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.Description">
            <summary>
            Gets the description of this layer.
            </summary>
            <value>
            A string representing the textual description of the layer.
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.GeometryType">
            <summary>
            Gets the description of this layer.
            </summary>
            <value>
            A <see cref="T:ESRI.ArcGIS.Client.Tasks.GeometryType"/> value representing geometry type of this layer.
            </value>
            <remarks>
            <para>
            The GeometryType can be one of: Envelope; Multipoint; Point; Polygon; or Polyline.
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.IsTable">
            <summary>
            Gets the description of this layer.
            </summary>
            <value>
            <see langword="true"/> if this layer is a table; otherwise, <see langword="false"/>.
            </value>
            <remarks>
            <para>
            Tables within a <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> or <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService"/> can form the basis for a <see cref="T:ESRI.ArcGIS.Client.Local.ArcGISLocalFeatureLayer"/> to enable editing workflows 
            or they can be used in conjunction with tasks such as the <see cref="T:ESRI.ArcGIS.Client.Tasks.QueryTask"/> for retrieving records via queries.
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LayerDetails.Service">
            <summary>
            Gets the <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> or <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService"/> which contains this layer.
            </summary>
            <value>
            A <see cref="T:ESRI.ArcGIS.Client.Local.LocalMapService"/> or <see cref="T:ESRI.ArcGIS.Client.Local.LocalFeatureService"/> in which the layer participates.
            </value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalGeocodeService">
            <summary>
            A class representing a geocode (locator) service managed by the runtime LocalServer.
            </summary>
            <remarks>
            <para>
            Geocoding is the process of assigning a location, usually in the form of coordinate values, to an address by comparing the descriptive location elements in the address to those present 
            in the reference material. Addresses come in many forms, ranging from the common address format of a house number followed by the street name and succeeding information to other location descriptions 
            such as postal zone or census tract. An address includes any type of information that distinguishes a place. 
            </para>
            <para>
            The address locator is the major component in the geocoding process. An address locator is created based on a specific address locator style. Once created, an address locator contains the 
            geocoding properties and parameters, a snapshot of the address attributes in the reference data, and the queries for performing a geocoding search. 
            The address locator also contains a set of address parsing and matching rules that directs the geocoding engine to perform address standardization and matching.
            </para>
            <para>
            When creating a local geocode service the <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.Path"/> and <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.LocatorName"/> properties should be set prior to starting the service. The <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.Path"/> can represent an absolute or relative path to 
            an Locator Package (.gcpk), a Geodatabase or folder. The LocatorName is the name of the AddressLocator within the Address Locator Package, Geodatabase or folder workspace 
            (which may contain one or more Address Locators). If the LocatorName is not specified the first Address Locator found in the workspace Path will be used. 
            </para>
            <para>
            Once the Path and optionally the LocatorName properties are set the local geocode service must be started prior to using it in conjunction with the 
            <see cref="T:ESRI.ArcGIS.Client.Tasks.Locator">Locator Task</see>. There are several options available when managing the lifetime of the service. The simplest option is to use the static convenience methods 
            provided as class extensions on the Locator task class <see cref="M:ESRI.ArcGIS.Client.Tasks.LocatorLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.Locator,System.String,System.String)">
            InitializeWithLocalService</see> and <see cref="M:ESRI.ArcGIS.Client.Tasks.LocatorLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.Locator,System.String,System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalGeocodeService})">
            InitializeWithLocalServiceAsync</see>.
            Alternatively to start the service directly there is an asynchronous <see cref="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.StartAsync"/> method and a synchronous <see cref="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.Start"/> method.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.#ctor">
            <summary>
            Initializes a new instance of the LocalGeocodeService class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.#ctor(System.String)">
            <summary>
            Initializes a new instance of the LocalGeocodeService class.
            </summary>
            <param name="path">Path to the Locator</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the LocalGeocodeService class.
            </summary>
            <param name="path">Path to the Locator</param>
            <param name="locator">Locator name</param>  
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.GetServiceAsync(System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalGeocodeService})">
            <summary>
            This will either reuse an existing service for the supplied path, or make a new service if 
            its not already running.
            </summary>
            <param name="path"></param>
            <param name="callback"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.GetServiceAsync(System.String)">
            <summary>
            This will either reuse an existing service for the supplied path, or make a new service if 
            its not already running.
            </summary>
            <param name="path"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.GetServiceAsync(System.String,System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalGeocodeService})">
            <summary>
            This will either reuse an existing service for the supplied path, or make a new service 
            if its not already running.
            </summary>
            <param name="path"></param>
            <param name="locator"></param>
            <param name="callback"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.GetServiceAsync(System.String,System.String)">
            <summary>
            This will either reuse an existing service for the supplied path, or make a new service 
            if its not already running.
            </summary>
            <param name="path"></param>
            <param name="locator"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.GetService(System.String)">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">The path or Url to the locator package.</param>
            <returns>An existing LocalGeocodeService instance if one exists with the same properties in the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">LocalServer Services collection</see>, 
            otherwise a new LocalGeocodeService instance.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.GetService(System.String,System.String)">
            <summary>
            This method will either reuse an existing service for the supplied path or create a new service if one is not already running.
            </summary>
            <param name="path">A <see cref="T:System.String"/> representing the path or Url to the locator package.</param>
            <param name="locator">A <see cref="T:System.String"/> representing the name of the locator.</param>
            <returns>An existing LocalGeocodeService instance if one exists with the same properties in the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">LocalServer Services collection</see>, 
            otherwise a new LocalGeocodeService instance.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.StartAsync">
            <summary>
            Asynchronously starts the local geocode service instance. Returns a Task object on which the Result property contains LocalService instance.
            </summary>
            <returns>
            A Task object on which the Result property contains the LocalService instance.
            </returns>
            <exception cref="T:System.InvalidOperationException">StartAsync should can not be called if the local service is already in the
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state.</exception>
            <exception cref="T:System.InvalidOperationException">The <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.Path"/> property must specify a valid ArcGIS Locator package (.gcpk).</exception>
            <remarks>
            The StartAsync method call should be enclosed in a try-catch block to handle any exceptions that might be raised.
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.Start">
            <summary>
            Synchronously starts the local geocode service instance.
            </summary>
            <remarks>
            <para>
            The Start method will check whether this local service is already in the <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state and 
            raise an <see cref="T:System.InvalidOperationException"/> if necessary. The <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Path"/> property 
            will also be checked for a valid locator package.
            </para>
            </remarks>
            <exception cref="T:System.InvalidOperationException">An exception is raised if the <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.Path"/> property 
            does not reference a valid locator package file.</exception>
            <seealso cref="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.StartAsync"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.CompleteStartService(ESRI.ArcGIS.Client.Local.CreateServiceResponse)">
            <summary>
             This provides the default implementation of completing a service, it looks for an endpoint matching the ServiceType of GeocodeServer and then initializes the Url and changes the LocalServer status and fires the OnStartCompleted event. This can be overriden if more steps are required to 
            complete the starting of a service (e.g. getting service metadata).
            </summary>
            <param name="createServiceResponse"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.FindServiceByPathAndName(System.String,System.String)">
            <summary>
             This will find a LocalGeocodeService by the path to the locator and the locator name
            </summary>
            <param name="path">Path to locator</param>
            <param name="locator">Locator name, this can be null</param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.CompleteStopService(System.Exception)">
            <summary>
             This is called to complete the stopping of the service and reset internal variables and fire event. 
            The service goes into Stopped state. Override this in derived classes to reset variables first before calling this implementation.
            </summary>
            <param name="error"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeocodeService.PopulateGeocodeServiceDetails(ESRI.ArcGIS.Client.Local.GeocodeServiceInfo)">
            <summary>
            This will get the informations needed from the internal GeocodeServiceInfo and expose the one that are relevant for the user work.
            </summary>
            <param name="geoService">The class GeocodeServiceInfo that contains all the field describing the Geocode Service.</param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.AutoName">
            <summary>
             Gets the PreferredName 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.Path">
            <summary>
             Gets or sets the path or Url to the locator package from which the LocalGeocodeService is created.
            </summary>
            <exception cref="T:System.InvalidOperationException">The <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.Path"/> property cannot be changed once the 
            local geocode service has started.</exception>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.LocatorName">
            <summary>
             Gets or sets the name of the locator from which the LocalGeocodeService is created.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.UrlGeocodeService">
            <summary>
             Gets the Url to the geocoding service.
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.AddressesFields">
            <summary>
            This will return a collection of GeocodeFields that are describing the AddressesFields that the Geocode Service accepts as inputs
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.CandidatesFields">
            <summary>
            This will return a collection of GeocodeFields that are describing the CandidatesFields that can be added as ouputs when sending a request to the Geocode Service
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.IntersectionCandidatesFields">
            <summary>
            This will return a collection of GeocodeFields that are describing the IntersectionCandidatesFields that can be added as ouputs when sending an intersection request to the Geocode Service
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.SingleLineAddressField">
            <summary>
            This will return a GeocodeField that describes the SingleLineAddressField that can be used when sending a single line addresses request to the Geocode Service
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.SpatialReference">
            <summary>
            This will return the SpatialReference of the Geocode Service
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.IntersectionConnectors">
            <summary>
            This will return a collection of string that can be used as Connectors when sending an Intersection request to the Geocode Service
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.MaxRecords">
            <summary>
            Gets or Sets the Maximum number of candidates returned by the LocalGeocodeService.
            </summary>
            <remarks>
            <para>
            The MaxRecords property must be set before the LocalGeocodeService has started.
            This property determines the number of candidates that will be returned when you find addresses interactively (as opposed to batch). 
            The response time is faster when there are fewer candidates to return.
            This property has a default value of 1000. If the property is not set explicitly the service will return the first 1000 records. 
            To improve performance it is recommended that the MaxRecords property is set in order to limit an operation that may return a large number of results, although in a LocalServer scenario 
            the performance gain will be marginal as compared to the transmission of results from a remote ArcGIS for Server instance.
            </para> 
            </remarks>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalGeometryService">
            <summary>
            The LocalGeometryService class represents a Geometry Service hosted by the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>.
            </summary>
            <remarks>
            <para>
            Geometry Services are a special type of service not based on any specific geographic resource 
            such as a Map Package but instead provide access to sophisticated and 
            frequently used geometric operations which can be perform on individually specified geometries.
            To access these operations, the LocalGeometryService should be used in conjunction with the 
            use of a <see cref="T:ESRI.ArcGIS.Client.Tasks.GeometryService">GeometryService Task</see> 
            which references the underlying LocalGeometryService via the <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeometryService.UrlGeometryService"/>.
            </para>
            </remarks>
            <example>
            The following example demonstrates starting a LocalGeometryService by using the static method GetServiceAsync. 
            Once the service has started the code creates a new GeometryService task instance, sets the Url property and registers 
            event handlers for the BufferCompleted event and the task Failed event.
            <code title="LocalGeometryService GetServiceAsync Example" description="" lang="CS">
            LocalGeometryService.GetServiceAsync(localGeometryService =&gt;
                {
                    // Create a new GeometryService task instance.
                    GeometryService geometryTask;
                    geometryTask = new GeometryService();
                    
                    // Set the Url property to the Url of the LocalGeometryService.
                    geometryTask.Url = localGeometryService.UrlGeometryService;
                    
                    // Register event handlers for the BufferCompleted and Failed events.
                    geometryTask.BufferCompleted += GeometryService_BufferCompleted;
                    geometryTask.Failed += GeometryService_Failed;
                });
            </code>
            <code title="LocalGeometryService GetServiceAsync Example" description="" lang="VB.NET">
            LocalGeometryService.GetServiceAsync(Function(localServiceDelegate)
                ' Create a new GeometryService task instance.
                Dim geometryTask As GeometryService
                geometryTask = New GeometryService()
                
                ' Set the Url property to the Url of the LocalGeometryService.
                geometryTask.Url = localServiceDelegate.UrlGeometryService
                
                ' Register event handlers for the BufferCompleted and Failed events.
                geometryTask.BufferCompleted += GeometryService_BufferCompleted
                geometryTask.Failed += GeometryService_Failed
                
            End Function)
            </code>
            </example>
            <example>
            The following example demonstrates using the task extension GeometryService.InitializeWithLocalServiceAsync:
            <code title="GeometryService InitializeWithLocalServiceAsync Example" description="" lang="CS">
            GeometryService geometryTask = new GeometryService();
            geometryTask.InitializeWithLocalServiceAsync(delegateService =&gt; 
            {
                if (string.IsNullOrEmpty(geometryTask.Url))
                {
                    // Check delegateService.Error property.
                    return;
                }
                
                // Continue to use geometryTask...
            });
            </code>
            <code title="GeometryService InitializeWithLocalServiceAsync Example" description="" lang="VB.NET">
            Dim geometryTask As New GeometryService()
            geometryTask.InitializeWithLocalServiceAsync(Function(delegateService) 
                If String.IsNullOrEmpty(geometryTask.Url) Then
                    ' Check delegateService.Error property.
                    Exit Function
                End If
                
                ' Continue to use geometryTask...
                
            End Function)
            </code>
            </example>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.#ctor">
            <summary>
            Initializes a new instance of the LocalGeometryService class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.GetServiceAsync(System.Action{ESRI.ArcGIS.Client.Local.LocalGeometryService})">
            <summary>Creates and starts a new LocalGeometryService or reuses an existing 
            LocalGeometryService if one is already running on the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>. 
            </summary>
            <param name="callback">An action delegate either in the form of a lambda expression 
            or a named method.</param>
            <remarks>
            <para>
            This method, particularly when used in conjunction with a Lambda Expression, provides a convenient way 
            to quickly obtain a reference to a LocalGeometryService without having to specifically check for existing 
            LocalGeometryService instances already running on the LocalServer or directly instantiate a new 
            LocalGeometryService and explicitly call the <see cref="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.StartAsync">
            StartAsync method</see>.
            </para>
            <para>
            The Action delegate will always be called regardless of the success or failure of the LocalServer to 
            create or return a LocalGeometryService. For this reason the <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Error">
            Error property</see> should always be checked to confirm a successful execution. The Error property will be 
            will be null if the if the LocalGeometryService was started successfully. 
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.GetServiceAsync">
            <summary>Creates and starts a new LocalGeometryService or reuses an existing 
            LocalGeometryService if one is already running on the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>. 
            </summary>
            <remarks>
            <para>
            This method, particularly when used in conjunction with a Lambda Expression, provides a convenient way 
            to quickly obtain a reference to a LocalGeometryService without having to specifically check for existing 
            LocalGeometryService instances already running on the LocalServer or directly instantiate a new 
            LocalGeometryService and explicitly call the <see cref="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.StartAsync">
            StartAsync method</see>.
            </para>
            <para>
            The Action delegate will always be called regardless of the success or failure of the LocalServer to 
            create or return a LocalGeometryService. For this reason the <see cref="P:ESRI.ArcGIS.Client.Local.LocalService.Error">
            Error property</see> should always be checked to confirm a successful execution. The Error property will be 
            will be null if the if the LocalGeometryService was started successfully. 
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.GetService">
            <summary>
            This method will either reuse an existing service or create a new service if one is not already running.
            </summary>
            <returns>An existing LocalGeometryService instance if one exists with the same properties in the <see cref="P:ESRI.ArcGIS.Client.Local.LocalServer.Services">LocalServer Services collection</see>, 
            otherwise a new LocalGeometryService instance.
            </returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.StartAsync">
            <summary>
            Asynchronously starts the LocalGeometryService on the LocalServer. Returns a Task object representing the LocalGeometryService instance.
            </summary>
            <returns>
            A Task object on which the Result property contains the LocalService instance.
            </returns>
            <remarks>
            The StartAsync method call should be enclosed in a try-catch block to handle any exceptions that might be raised.
            </remarks>
            <exception cref="T:System.InvalidOperationException">StartAsync should can not be called if the local service is already in the
            <see cref="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running"/> state.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.Start">
            <summary>
            Synchronously starts this local geometry service instance.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.CompleteStartService(ESRI.ArcGIS.Client.Local.CreateServiceResponse)">
            <summary>
             This provides the default implementation of completing a service, it looks for an endpoint matching the ServiceType of GeocodeServer and then initializes the Url and changes the LocalServer status and fires the OnStartCompleted event. This can be overriden if more steps are required to 
            complete the starting of a service (e.g. getting service metadata).
            </summary>
            <param name="createServiceResponse"></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.LocalGeometryService.CompleteStopService(System.Exception)">
            <summary>
             This is called to complete the stopping of the service and reset internal variables and fire event. 
            The service goes into Stopped state. Override this in derived classes to reset variables first before calling this implementation.
            </summary>
            <param name="error"></param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeometryService.AutoName">
            <summary>
             Gets the PreferredName 
            </summary>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.LocalGeometryService.UrlGeometryService">
            <summary>
             Gets the Url of this LocalGeometryService instance.
            </summary>
            <returns>A string representing the Url of the LocalGeometryService.
            </returns>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.LocalServiceStatus">
            <summary>
            Indicates the status of a service during its lifecycle on the <see cref="T:ESRI.ArcGIS.Client.Local.LocalServer"/>.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.None">
            <summary>
            No status.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Starting">
            <summary>
            The LocalServer is starting the service.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Running">
            <summary>
            The service has started successfully.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.StartError">
            <summary>
            The service failed to start. 
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Stopping">
            <summary>
            The service is stopping.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.LocalServiceStatus.Stopped">
            <summary>
            The service has stopped running.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType">
            <summary>
            An enumeration of workspace types for working with the dynamic layers capability of the local map service.
            </summary>
            <remarks>
            <para>
            The user executing the application will the same user under which the runtime local server process is started and therefore in the case of 
            <see cref="F:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType.Shapefile"/>, <see cref="F:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType.FileGDB"/>, or <see cref="F:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType.Raster"/> this 
            user must have access to the workspace being referenced. For <see cref="F:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType.SDE"/> valid credentials must be provided in 
            order that the runtime local server process can establish a database connection.
            </para>
            </remarks>
            <seealso cref="T:ESRI.ArcGIS.Client.ArcGISDynamicMapServiceLayer"/>
            <seealso cref="T:ESRI.ArcGIS.Client.DataSource"/>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType.SDE">
            <summary>
            Workspace factory type SDE should be used when the workspace being referenced is an Enterprise Geodatabase.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType.Shapefile">
            <summary>
            Workspace factory type Shapefile should be used when the workspace being referenced is a folder on disk containing one or more Shapefiles.
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType.FileGDB">
            <summary>
            Workspace factory type FileGDB should be used when the workspace being referenced is a file geodatabase. 
            </summary>
        </member>
        <member name="F:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType.Raster">
            <summary>
            Workspace factory type Raster should be used when the workspace being referenced is folder containing one or more raster datasets.
            </summary>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo">
            <summary>
            A class representing information about a workspace to which the local map service will connect in order to allow the dynamic switching of layer 
            datasources to datasets within this workspace.
            </summary>
            <seealso cref="T:ESRI.ArcGIS.Client.ArcGISDynamicMapServiceLayer"/>
            <seealso cref="T:ESRI.ArcGIS.Client.DataSource"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.CreateFileGeoDatabaseConnection(System.String,System.String)">
            <summary>
            Creates a WorkspaceInfo object of WorkspaceFactoryType FileGDB based on the provided unique identifier and path to the file geodatabase folder (.gdb).
            </summary>
            <param name="workspaceID">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="pathToFolder">A <see cref="T:System.String"/> representing the path to the file geodatabase folder (.gdb).</param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> of WorkspaceFactoryType FileGDB.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.CreateShapefileFolderConnection(System.String,System.String)">
            <summary>
            Creates a WorkspaceInfo object of WorkspaceFactoryType Shapefile based on the provided unique identifier and path to a folder on disk.
            </summary>
            <param name="workspaceID">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="pathToFolder">A <see cref="T:System.String"/> representing the path to the folder.</param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> of WorkspaceFactoryType Shapefile.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.CreateRasterFolderConnection(System.String,System.String)">
            <summary>
            Creates a WorkspaceInfo object of WorkspaceFactoryType Raster based on the provided unique identifier and path to a folder on disk.
            </summary>
            <param name="workspaceID">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="pathToFolder">A <see cref="T:System.String"/> representing the path to the folder.</param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> of WorkspaceFactoryType Raster.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.CreateSDEConnectionFromFilePath(System.String,System.String)">
            <summary>
            Creates a WorkspaceInfo object of WorkspaceFactoryType SDE based on the provided unique identifier and path to an ArcSDE connection file.
            </summary>
            <param name="workspaceID">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="pathToSdeConnectionFile">The path to the ArcSDEconnection file (.sde).</param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> of WorkspaceFactoryType SDE.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.CreateSDEConnectionFromFilePath(System.String,System.String,System.Boolean)">
            <summary>
            Creates a WorkspaceInfo object of WorkspaceFactoryType SDE based on the provided unique identifier and path to an ArcSDE connection file.
            </summary>
            <param name="workspaceID">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="pathToSdeConnectionFile">The path to the ArcSDEconnection file (.sde).</param>
            <param name="lockVersion"><see langword="true"/> if the connection is locked to this database version; otherwise <see langword="false"/></param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> of WorkspaceFactoryType SDE.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.CreateSDEConnection(System.String,System.String)">
            <summary>
            Creates a WorkspaceInfo object of WorkspaceFactoryType SDE based on the provided unique identifier and connection string.
            </summary>
            <param name="workspaceID">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="connectionString">The ArcSDE connection string.</param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> of WorkspaceFactoryType SDE.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.CreateSDEConnection(System.String,System.String,System.Boolean)">
            <summary>
            Creates a WorkspaceInfo object of WorkspaceFactoryType SDE based on the provided unique identifier and connection string.
            </summary>
            <param name="workspaceID">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="connectionString">The ArcSDE connection string.</param>
            <param name="lockVersion"><see langword="true"/> if the connection is locked to this database version; otherwise <see langword="false"/></param>
            <returns>A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> of WorkspaceFactoryType SDE.</returns>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.#ctor(System.String,ESRI.ArcGIS.Client.Local.WorkspaceFactoryType,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> class.
            </summary>
            <param name="workspaceId">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="factoryType">A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType"/> representing the type of the workspace (SDE, Shapefile, FileGDB, or Raster).</param>
            <param name="connectionString">A <see cref="T:System.String"/> representing the connection string (folder, ArcSDE connection file or ArcSDE connection string).</param>
            <param name="lockVersion"><see langword="true"/> if the connection is locked to this database version; otherwise <see langword="false"/></param>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfo.#ctor(System.String,ESRI.ArcGIS.Client.Local.WorkspaceFactoryType,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> class.
            </summary>
            <param name="workspaceId">A <see cref="T:System.String"/> representing a unique identifier for this workspace.</param>
            <param name="factoryType">A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType"/> representing the type of the workspace (SDE, Shapefile, FileGDB, or Raster).</param>
            <param name="connectionString">A <see cref="T:System.String"/> representing the connection string (folder, ArcSDE connection file or ArcSDE connection string).</param>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.WorkspaceInfo.Id">
            <summary>
            Gets or sets the identifier for this workspace
            </summary>
            <value>
            A <see cref="T:System.String"/> representing an identifier for this workspace.
            </value>
            <remarks>
            <para>
            The Id is a user defined string used to uniquely refer to a workspace which has been registered with the local map service. 
            The Id should be set as the WorkspaceID property when working with <see cref="T:ESRI.ArcGIS.Client.QueryDataSource">QueryDataSource</see>, 
            <see cref="T:ESRI.ArcGIS.Client.TableDataSource">TableDataSource</see>, or <see cref="T:ESRI.ArcGIS.Client.RasterDataSource">RasterDataSource</see> 
            objects.
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.WorkspaceInfo.ConnectionString">
            <summary>
            Gets or sets the connection string for the workspace.
            </summary>
            <value>
            A <see cref="T:System.String"/> representing the connection path or information.
            </value>
            <remarks>
            <para>
            Connection strings should be specified as follows:
            <list type="bullet">
            <item>ArcSDE: For ArcSDE connections you should use the <see cref="P:ESRI.ArcGIS.Client.Local.WorkspaceInfo.SDEConnectionFilePath"/> to specify the path to an ArcSDE connection file (.sde). 
            </item>
            <item>File Geodatabase: The connection string is a full or relative path to the .gdb folder, 
            for example "C:\MapsAndData\OperationalData.gdb". Relative paths are relative to the executing application.
            </item>
            <item>Shapefile: The connection string is a full or relative path to a folder containing one or more Shapefiles, 
            for example "C:\MapaAndData\Shapefiles". Relative paths are relative to the executing application.
            </item>
            <item>Raster: The connection string is a full or relative path to a folder containing one or more raster datasets, 
            for example "C:\MapaAndData\Rasters". Relative paths are relative to the executing application.
            </item>
            </list>
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.WorkspaceInfo.SDEConnectionFilePath">
            <summary>
            Gets or sets the SDE connection file path.
            </summary>
            <value>
            The SDE connection file path.
            </value>
            <remarks>
            <para>
            When you add a database connection under the Database Connections node in the Catalog tree in ArcGIS for Desktop, 
            a file is created on the client computer that contains the connection information to a database that you provide. 
            The default location for the file is \\&lt;computer_name&gt;\Users\&lt;user_name&gt;\AppData\Roaming\ESRI\Desktop&lt;release#&gt;\ArcCatalog, 
            but you can move the connection to another location.  
            For more information see http://resourcesbeta.arcgis.com/en/help/main/10.1/index.html#/Preparing_database_connection_files_to_share_with_other_users/00290000008v000000/.
            </para>
            </remarks>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.WorkspaceInfo.FactoryType">
            <summary>
            Gets or sets the type of the workspace.
            </summary>
            <value>
            A <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceFactoryType"/> representing the type of the workspace (SDE, Shapefile, FileGDB, or Raster).
            </value>
        </member>
        <member name="P:ESRI.ArcGIS.Client.Local.WorkspaceInfo.LockVersion">
            <summary>
            Gets or sets a value indicating whether the connection should be locked to this database version.
            </summary>
            <value>
              <see langword='true' /> if the connection is locked to this database version; otherwise <see langword='false' />
            </value>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Local.WorkspaceInfoCollection">
            <summary>
            A class representing a collection of items that contain information about workspaces which will be registered with the local map service 
            to enable the dynamic switching of layer datasources whilst the map service is running.
            </summary>
            <seealso cref="T:ESRI.ArcGIS.Client.ArcGISDynamicMapServiceLayer"/>
            <seealso cref="T:ESRI.ArcGIS.Client.DataSource"/>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfoCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfoCollection"/> class.
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfoCollection.InsertItem(System.Int32,ESRI.ArcGIS.Client.Local.WorkspaceInfo)">
            <summary>
            Inserts an element into the <see cref="T:System.Collections.ObjectModel.Collection`1"/> at the specified index.
            </summary>
            <param name="index">The zero-based index at which the <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> item should be inserted.</param>
            <param name="item">The <see cref="T:ESRI.ArcGIS.Client.Local.WorkspaceInfo"/> item to insert.</param>
            <exception cref="T:System.InvalidOperationException">DynamicWorkspaces property cannot be modified when the service is running.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfoCollection.ClearItems">
            <summary>
            Removes all WorkspaceInfo items from the <see cref="T:System.Collections.ObjectModel.Collection`1"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">DynamicWorkspaces property cannot be modified when the service is running.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfoCollection.RemoveItem(System.Int32)">
            <summary>
            Removes the WorkspaceInfo item at the specified index of the collection.
            </summary>
            <param name="index">The zero-based index of the element to remove.</param>
            <exception cref="T:System.InvalidOperationException">DynamicWorkspaces property cannot be modified when the service is running.</exception>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Local.WorkspaceInfoCollection.SetItem(System.Int32,ESRI.ArcGIS.Client.Local.WorkspaceInfo)">
            <summary>
            Replaces the WorkspaceItem at the specified index.
            </summary>
            <param name="index">The zero-based index of the WorkspaceInfo item to replace.</param>
            <param name="item">The WorkspaceInfo item with which to update an existing item at the specified index.</param>
            <exception cref="T:System.InvalidOperationException">DynamicWorkspaces property cannot be modified when the service is running.</exception>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Tasks.IdentifyTaskLocalExtensions">
            <summary>
            Extension methods for Identify Task
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.IdentifyTaskLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.IdentifyTask,System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            <summary>
            Asynchronously initializes the supplied IdentifyTask instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService"/> 
            based on the provided path to a Map Package.
            </summary>
            <param name="identifyTask">The <see cref="T:ESRI.ArcGIS.Client.Tasks.IdentifyTask"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Map Package from which the local map service will be started.</param>
            <param name="callback">An <see cref="T:System.Action`1"/> delegate to support inline event handling.</param>
            <remarks>
            <para>
            This method will reuse an existing local map service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.IdentifyTaskLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.IdentifyTask,System.String)">
            <summary>
            Synchronously initializes the supplied IdentifyTask instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService"/> 
            based on the provided path to a Map Package.
            </summary>
            <param name="identifyTask">The <see cref="T:ESRI.ArcGIS.Client.Tasks.IdentifyTask"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Map Package from which the local map service will be started.</param>
            <remarks>
            <para>
            This method will reuse an existing local map service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Tasks.LocatorLocalExtensions">
            <summary>
            Extension methods for Locator Task
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.LocatorLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.Locator,System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalGeocodeService})">
            <summary>
            Asynchronously initializes a Locator task instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.UrlGeocodeService"/> based on the provided path 
            to a Geocode Package (.gcpk) or alternatively a folder or Geodatabase containing a locator.
            </summary>
            <param name="locator">A <see cref="T:ESRI.ArcGIS.Client.Tasks.Locator"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Locator Package, folder or Geodatabase.</param>
            <param name="callback"></param>
            <remarks>
            <para>
            This method accepts only a <see cref="T:ESRI.ArcGIS.Client.Tasks.Locator"/> task and the path to a package or workspace. 
            If the package or workspace contains more than one locator the API will automatically use the first locator found.
            </para>
            <para>
            This method will reuse an existing local geocode service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.LocatorLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.Locator,System.String,System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalGeocodeService})">
            <summary>
            Asynchronously initializes a Locator task instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.UrlGeocodeService"/> based on the provided path 
            to a Geocode Package (.gcpk) or alternatively a folder or Geodatabase containing a locator.
            </summary>
            <param name="locator">A <see cref="T:ESRI.ArcGIS.Client.Tasks.Locator"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Locator Package, folder or Geodatabase.</param>
            <param name="locatorName">A <see cref="T:System.String"/> representing the name of the Locator within the package or workspace.</param>
            <param name="callback">An <see cref="T:System.Action`1"/> delegate to support inline event handling.</param>
            <para>
            This method will reuse an existing local geocode service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.LocatorLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.Locator,System.String)">
            <summary>
            Synchronously initializes a Locator task instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.UrlGeocodeService"/> based on the provided path 
            to a Geocode Package (.gcpk) or alternatively a folder or Geodatabase containing a locator.
            </summary>
            <param name="locator">A <see cref="T:ESRI.ArcGIS.Client.Tasks.Locator"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Locator Package, folder or Geodatabase.</param>
            <remarks>
            <para>
            This method accepts only a <see cref="T:ESRI.ArcGIS.Client.Tasks.Locator"/> task and the path to a package or workspace. 
            If the package or workspace contains more than one locator the API will automatically use the first locator found.
            </para>
            <para>
            This method will reuse an existing local geocode service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.LocatorLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.Locator,System.String,System.String)">
            <summary>
            Synchronously initializes a Locator task instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeocodeService.UrlGeocodeService"/> based on the provided path 
            to a Geocode Package (.gcpk) or alternatively a folder or Geodatabase containing a locator.
            </summary>
            <param name="locator">A <see cref="T:ESRI.ArcGIS.Client.Tasks.Locator"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Locator Package, folder or Geodatabase.</param>
            <param name="locatorName"> a <see cref="T:System.String"/> representing the name of the specific locator within the package, geodatabase or folder.</param>
            <para>
            This method will reuse an existing local geocode service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Tasks.GeometryServiceLocalExtensions">
            <summary>
            Extension methods for Geometry Service Task
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.GeometryServiceLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.GeometryService,System.Action{ESRI.ArcGIS.Client.Local.LocalGeometryService})">
            <summary>
            Asynchronously initializes a GeometryService task instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeometryService.UrlGeometryService"/>.
            </summary>
            <param name="geometryService">The <see cref="T:ESRI.ArcGIS.Client.Tasks.GeometryService"/> task to initialize.</param>
            <param name="callback">An <see cref="T:System.Action`1"/> delegate to support inline event handling.</param>
            <remarks>
            <para>
            This method will reuse an existing local geometry service if one exists or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.GeometryServiceLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.GeometryService)">
            <summary>
            Synchronously initializes a GeometryService task instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeometryService.UrlGeometryService"/>.
            </summary>
            <param name="geometryService">The <see cref="T:ESRI.ArcGIS.Client.Tasks.GeometryService"/> task to initialize.</param>
            <remarks>
            <para>
            This method will reuse an existing local geometry service if one exists or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Tasks.GeoprocessorLocalExtensions">
            <summary>
            Extension methods for Geoprocessing Task
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.GeoprocessorLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.Geoprocessor,System.String,System.String,ESRI.ArcGIS.Client.Local.GPServiceType,System.Action{ESRI.ArcGIS.Client.Local.LocalGeoprocessingService})">
            <summary>
            Asynchronously initializes a Geoprocessor task instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.UrlGeoprocessingService"/> 
            based on the provided path to a geoprocessing package (.gpk).
            </summary>
            <param name="geoprocessor">The <see cref="T:ESRI.ArcGIS.Client.Tasks.Geoprocessor"/> task to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a geoprocessing package (.gpk).</param>
            <param name="toolname">A <see cref="T:System.String"/> representing the name of the tool within the package.</param>
            <param name="gpServiceType">A <see cref="T:ESRI.ArcGIS.Client.Local.GPServiceType"/> representing the required type of 
            the local geoprocessing service: synchronous, asynchronous or asynchronous with map server result.</param>
            <param name="callback">An <see cref="T:System.Action`1"/> delegate to support inline event handling.</param>
            <remarks>
            <para>
            This method will reuse an existing local geoprocessing service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.GeoprocessorLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.Geoprocessor,System.String,System.String,ESRI.ArcGIS.Client.Local.GPServiceType)">
            <summary>
            Synchronously initializes a Geoprocessor task instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalGeoprocessingService.UrlGeoprocessingService"/> 
            based on the provided path to a geoprocessing package (.gpk).
            </summary>
            <param name="geoprocessor">The <see cref="T:ESRI.ArcGIS.Client.Tasks.Geoprocessor"/> task to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a geoprocessing package (.gpk).</param>
            <param name="toolname">A <see cref="T:System.String"/> representing the name of the tool within the package.</param>
            <param name="gpServiceType">A <see cref="T:ESRI.ArcGIS.Client.Local.GPServiceType"/> representing the required type of 
            the local geoprocessing service: synchronous, asynchronous or asynchronous with map server result.</param>
            <remarks>
            <para>
            This method will reuse an existing local geoprocessing service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Tasks.QueryTaskLocalExtensions">
            <summary>
            Extension methods for Query Task
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.QueryTaskLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.QueryTask,System.String,System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            <summary>
            Asynchronously initializes the supplied QueryTask instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService"/> 
            based on the provided path to a Map Package.
            </summary>
            <param name="queryTask">The <see cref="T:ESRI.ArcGIS.Client.Tasks.QueryTask"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Map Package from which the local map service will be started.</param>
            <param name="layerName">The name of a layer within the Map.</param>
            <param name="callback">An <see cref="T:System.Action`1"/> delegate to support inline event handling.</param>
            <remarks>
            <para>
            This method will reuse an existing local map service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.QueryTaskLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.QueryTask,System.String,System.Int32,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            <summary>
            Asynchronously initializes the supplied QueryTask instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService"/> 
            based on the provided path to a Map Package.
            </summary>
            <param name="queryTask">The <see cref="T:ESRI.ArcGIS.Client.Tasks.QueryTask"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Map Package from which the local map service will be started.</param>
            <param name="layerId">An integer representing the zero-based index position of a layer within the local map service.</param>
            <param name="callback">An <see cref="T:System.Action`1"/> delegate to support inline event handling.</param>
            <remarks>
            <para>
            This method will reuse an existing local map service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.QueryTaskLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.QueryTask,System.String,System.Int32)">
            <summary>
            Synchronously initializes the supplied QueryTask instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService"/> 
            based on the provided path to a Map Package.
            </summary>
            <param name="queryTask">The <see cref="T:ESRI.ArcGIS.Client.Tasks.QueryTask"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Map Package from which the local map service will be started.</param>
            <param name="layerId">An integer representing the zero-based index position of a layer within the Map.</param>
            <remarks>
            <para>
            This method will reuse an existing local map service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.QueryTaskLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.QueryTask,System.String,System.String)">
            <summary>
            Synchronously initializes the supplied QueryTask instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService"/> 
            based on the provided path to a Map Package.
            </summary>
            <param name="queryTask">The <see cref="T:ESRI.ArcGIS.Client.Tasks.QueryTask"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Map Package from which the local map service will be started.</param>
            <param name="layerName">A <see cref="T:System.String"/> representing the name of the layer within the local map service.</param>
            <remarks>
            <para>
            This method will reuse an existing local map service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="T:ESRI.ArcGIS.Client.Tasks.FindTaskLocalExtensions">
            <summary>
            Extension methods for Find Task
            </summary>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.FindTaskLocalExtensions.InitializeWithLocalServiceAsync(ESRI.ArcGIS.Client.Tasks.FindTask,System.String,System.Action{ESRI.ArcGIS.Client.Local.LocalMapService})">
            <summary>
            Asynchronously initializes the supplied FindTask instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService"/> 
            based on the provided path to a Map Package.
            </summary>
            <param name="findTask">The Find Task</param>
            <param name="path">A string representing the path to an ArcGIS Map Package (.MPK) or a Map Service Definition (.MSD) either located on disk or on ArcGIS.com</param>
            <param name="callback">An <see cref="T:System.Action`1"/> delegate to support inline event handling.</param>
            <remarks>
            <para>
            This method will reuse an existing local map service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
        <member name="M:ESRI.ArcGIS.Client.Tasks.FindTaskLocalExtensions.InitializeWithLocalService(ESRI.ArcGIS.Client.Tasks.FindTask,System.String)">
            <summary>
            Synchronously initializes the supplied FindTask instance with a <see cref="P:ESRI.ArcGIS.Client.Local.LocalMapService.UrlMapService"/> 
            based on the provided path to a Map Package.
            </summary>
            <param name="findTask">The <see cref="T:ESRI.ArcGIS.Client.Tasks.FindTask"/> to initialize.</param>
            <param name="path">A <see cref="T:System.String"/> representing the path to a Map Package from which the local map service will be started.</param>
            <remarks>
            <para>
            This method will reuse an existing local map service if one exists with the same properties or start a new one if none are found on the runtime local server.
            </para>
            </remarks>
        </member>
    </members>
</doc>
