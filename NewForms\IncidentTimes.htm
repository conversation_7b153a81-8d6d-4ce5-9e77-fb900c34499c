<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Incident Times</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>

	<body>

		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Times</H4>
						<form action="IncidentTimesResults.aspx?queryfile=IncidentTimes.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td colspan="2">Unit Incident Times for 7 Day Period
									</td>
								</tr>
								<tr>
									<td align="center" colspan="2"><span style="font-size:12px;">(ENTER UNIT NAME and START TIME)</span><br><br>
									</td>
								</tr>

								<tr>
									<td valign="top">Unit:</td>

									<td>
										<XML id="radiostyle" src="configurationunits.xsl"></XML>
										<SPAN type="selectlist" id="radiovals" name="radiovals">
											<XML id="radiosource" src="../Configuration_Units.xml"></XML>
										</SPAN>
									</td>


								</tr>


								<tr>
									<td>Start Date:</td>
									<td>


										<input type="text" name="startdate" size="10" maxlength=10 ID="startdate" >
										
										<a href="javascript:void(0)" onclick='calendar(document.Form.startdate, "date");' HIDEFOCUS>
										  <img name="popcal1" id="popcal1" src="calbtn.gif" width="34" height="22" border="0" alt="">
										</a> 
										<input type="text" name="starttime" size="5" maxlength=5 ID="starttime" >

						 			</td>
								</tr>

							</table>
							<br>

		<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
		<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">

		<input type="hidden" name="enddate" size="10" maxlength=10 ID="enddate" style="border:none;"> 
		<input type="hidden" name="endtime" size="5" maxlength=5 ID="endtime" style="border:none;" >
		<input type="hidden" readonly name="radioname" id="radioname">
		<input type="hidden" name="selectedunit" id="selectedunit">
		

						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
		<!--  PopCalendar(tag name and id must match) Tags should sit at the page bottom -->
		<iframe width="168" height="190" name="gToday:normal:agenda.js" id="gToday:normal:agenda.js" src="ipopeng.htm" scrolling="no" frameborder="0" style="border:2px ridge; visibility:visible; z-index:999; position:absolute; left:-500px; top:0px;"></iframe>

	</body>
	<script src="clientutilities.js"></script>
	<script LANGUAGE="VBScript" src="clientutilities.vbs"></script>
	<SCRIPT language="javascript">


		setTimeout(function(){ // C4G anonymous function to capture variable after window loads
 			var unitlist = Form.unit.options;
			var unitCount = unitlist.length;
			for (i = 0; i < unitCount; i++) {
				if (unitlist(i).text == Form.radioname.value) {
					unitlist(i).selected = true;
					break; 
				}
			}
		 }, 150);

		function window.onload()
		{

			var yesterday = new Date();
			var today = new Date();
			var hourstr;
			var minutestr;
			Date.addDate(yesterday, Date.DAY, -7);

			Form.startdate.value = FormatDate(yesterday);
			Form.enddate.value = FormatDate(today);

			if (today.getHours().toString().length == 1)
				hourstr = '0' + today.getHours().toString();
			else
				hourstr = today.getHours().toString();

			if (today.getMinutes().toString().length == 1)
				minutestr = '0' + today.getMinutes().toString();
			else
				minutestr = today.getMinutes().toString();

			Form.starttime.value = hourstr + ':' + minutestr;
			Form.endtime.value = hourstr + ':' + minutestr;

			// set the default user to current user
			radiovals.innerHTML = GenerateSelectBox("unit", radiosource, radiostyle, true, false, true, 1, false, false);

			Form.unit.focus();
		}

		function calendar(ctrl, formattype)
		{
			gfPop.fPopCalendar(ctrl);
			return false;
		}

		function checkdates()
		{
			if (!isDate(Form.startdate.value))
			{
				Form.startdate.focus();
				return false;
			}
			return true;
		}

		function checkTime()
		{
			if (!isTime(Form.starttime.value))
			{
				Form.starttime.focus();
				return false;
			}
			return true;
		}
		function window.validatepage()
		{
			Form.selectedunit.value = Form.unit.options[Form.unit.selectedIndex].text;

			if (checkdates() && checkTime())
			{
				updateEndDate();
				Form.startdate.value = FormatToStandardDate(Form.startdate.value);
				Form.enddate.value = FormatToStandardDate(Form.enddate.value);
				Form.Submit.click();
			}

		}


		function updateEndDate()
		{
			var nextday = new Date( Form.startdate.value);
			Date.addDate(nextday, Date.DAY, +7);

			Form.enddate.value = FormatDate(nextday);
			Form.endtime.value = Form.starttime.value;
		}

	</SCRIPT>
</HTML>
