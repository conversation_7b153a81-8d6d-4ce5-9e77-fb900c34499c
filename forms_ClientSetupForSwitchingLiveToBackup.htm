<HTML>
<HEAD>
<META NAME="GENERATOR" Content="Microsoft Visual Studio 6.0">
<TITLE></TITLE>
</HEAD>
<!-- Script that generates the data for the XML island.  --/>
  <!-- For a 2nd island, add the line: xslTarget2.innerHTML = --/>
  <!-- source2.transformNode(style2.XMLDocument), etc --/>
  <!-- SCRIPT FOR="window" EVENT="onload" -->
  <!--  xslTarget.innerHTML = source.transformNode(style.XMLDocument);-->
  <!-- SCRIPT -->

  <!-- Rules for field attributes for consumption by Voyager Mobile --/>
  <!-- id="voyager_mobile" : VM looks at all elements with this id --/>
  <!-- name="fieldname" :  the name of the form field that is submitted --/>
  <!-- type= Do NOT USE                                           --/>
  <!-- class="InputData" makes the font for input very large and Courier (easier for input) --/>
  <!-- title="text" :  for text input --/>
  <!-- title="number,min=1900,max=2100" :  for numeric fields.  min, max are optional
       but must be in the order shown if both are present --/>
  <!-- value="val" :  the data value for radio buttons, checkboxes and options --/>
<!-- ***********************************************************************/>  </!-->
  <SCRIPT FOR="window" EVENT="onload">
   document.voyager_mobile.RegistrationID.focus();
  </SCRIPT>
<!-- ***********************************************************************/>  </!-->

<BODY>
<LINK REL="stylesheet" TYPE="text/css" HREF="template.css">

<!-- ***********************************************************************/>  </!-->
<form id="voyager_mobile" name="Client Setup" class="2">
<!-- ***********************************************************************/>  </!-->

<table width=100% cellpadding=0 cellspacing=0 align=center border=0>
	<tr>
		<td width=14>&nbsp</td>
		<td>
			<table  width=100% cellpadding=0 cellspacing=0 align=center border=0>
				<tr>
					<td align=left colspan=2><img src=forms_logo.gif border=0></td>
				</tr>
				<tr>
					<td width=7><img src=forms_spacer.gif width=6 height=1></td><td width=100% height=1 bgcolor=#48A6B0 align=left></td>
				</tr>
				<tr>
				
<!-- ***********************************************************************/>  </!-->
<td class=title align=right colspan=2>Client Setup</td>
<!-- ***********************************************************************/>  </!-->

				</tr>
				<tr>
					<td height=38 colspan=2></td>
				</tr>
				<tr>
					<td colspan=2>
						<table width=90% cellpadding=0 cellspacing=0 align=center border=0>
							<tr>
								<td colspan=100%>
									<table width=100% cellpadding=0 cellspacing=0 align=center border=0>
										<tr>
											<td><img src=forms_cornerleft.gif></td>
											<td width=100%>
												<table width=100% cellpadding=0 cellspacing=0 align=center border=0>
													<tr>
														<td height=1 bgcolor=#286369></td>
													</tr>
													<tr>
													
<!-- ***********************************************************************/>  </!-->
<td class=SubTitle height=1>Client Setup</td>
<!-- ***********************************************************************/>  </!-->

													</tr>
													<tr>
														<td height=1 bgcolor=#286369></td>
													</tr>
												</table>
											</td>
											<td><img src=forms_cornerright.gif></td>
										</tr>
									</table>
								</td>
							</tr>								
							<tr>
								<td bgcolor=#286369 width=1></td>
								<td>
									<table width=100% cellpadding=0 cellspacing=0 align=center border=0  height=60>
										<tr>
											<td height=1>
											</td>
										</tr>
										<tr>
											<td align=center valign=top>
												<table align=center border=0 cellpadding=0 cellspacing=0>
<tr>
	<TD height=40 colspan=3 class=InputNames><input type="button" value=" Use Live System " onclick="SwitchToLive()" class="InputData"></TD>
</tr>	
<tr>
	<TD height=40 colspan=3 class=InputNames><input type="button" value="Use Backup System" onclick="SwitchToBackup()" class="InputData"></TD>
</tr>	
													<tr>
														<td colspan=2>&nbsp</td>
													</tr>
	<tr>
													
<!-- ***********************************************************************/>  </!-->
<td height=40 class=InputNames>Registration ID:</td><td>&nbsp</td>
<td>
<input type=text name="RegistrationID" lang="RegistrationID,none" id="voyager_mobile" class="InputData" type ="text" maxlength=15 size=15> 
</td>
<!-- ***********************************************************************/>  </!-->

													</tr>		

	<tr>
													
<!-- ***********************************************************************/>  </!-->
<td height=40 class=InputNames>Mobile Server IP or name:</td><td>&nbsp</td>
<td>
<input type=text id="voyager_mobile" lang="MobileServerIP,none" class="InputData" name="Server" type ="text"> 
</td>
<!-- ***********************************************************************/>  </!-->

													</tr>	
	<tr>
													
<td height=40 class=InputNames>S/W Server IP:</td><td>&nbsp</td>
<td>
<input type=text id="voyager_mobile" lang="SWServerIP,none" class="InputData" name="SWServer" type ="text"> 
</td>

													</tr>	


	<tr>
													
<!-- ***********************************************************************/>  </!-->
<td height=40 class=InputNames>Software Update Folder Name:</td><td>&nbsp</td>
<td>
<input type=text id="voyager_mobile" lang="SWServerAgencyName,none" class="InputData" type ="text"> 
</td>
<!-- ***********************************************************************/>  </!-->

													</tr>	
	<tr>
													
<!-- ***********************************************************************/>  </!-->
<td height=40 class=InputNames>WLAN mask:</td><td>&nbsp</td>
<td>
<input type=text id="voyager_mobile" lang="WLANMask,none" class="InputData" type ="text"> 
</td>
<!-- ***********************************************************************/>  </!-->

													</tr>	
											</table>
											</td>
										</tr>
									</table>
									
								</td>
								<td bgcolor=#286369 width=1></td>
							</tr>
							<tr>
								<td colspan=100%>
									<table width=100% cellpadding=0 cellspacing=0 align=center border=0>
										<tr>
											<td><img src=forms_bottomright.gif></td>
											<td width=100%>
												<table width=100% cellpadding=0 cellspacing=0 align=center border=0>
													<tr>
														<td height=16></td>
													</tr>
													<tr>
														<td height=1 bgcolor=#286369></td>
													</tr>
												</table>
											</td>
											<td><img src=forms_bottomleft.gif></td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</td>
		<td width=14>&nbsp</td>
	</tr>
</table>
</form>
</BODY>
	<SCRIPT language="javascript">
		function SwitchToLive()
		{
			voyager_mobile.Server.value = "LiveServerNameOrIP";
			voyager_mobile.SWServer.value = "LiveServerNameOrIP";
		}
		function SwitchToBackup()
		{
			voyager_mobile.Server.value = "BackupServerNameOrIP";
			voyager_mobile.SWServer.value = "BackupServerNameOrIP";
		}
	</SCRIPT>
</HTML>
