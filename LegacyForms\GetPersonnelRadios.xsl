<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet version="1.0"
    xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:variable name="RadioType1" select="'1'"/>
  <!-- permanent vehicle radios -->
  <xsl:variable name="RadioType2" select="'2'"/>
  <!-- temporary vehicle radios -->
  <xsl:variable name="RadioType3" select="'3'"/>
  <!-- temporary personnel radios -->
  <xsl:variable name="RadioType4" select="'4'"/>
  <!-- permanent personnel radios ON DUTY -->

  <xsl:variable name="PermVehicleRadios" select="'Vehicle Assigned Portable Radio'"/>
  <xsl:variable name="TempVehicleRadios" select="'Temporary Vehicle Radio'"/>
  <xsl:variable name="TempPersonnelRadios" select="'Temporary Personnel Radio'"/>
  <xsl:variable name="PermPersonnelRadios" select="'Permanent Personnel Radio'"/>
  <xsl:variable name="BlankSpace" select="' '"/>

  <xsl:template match="/">
    <html>
      <head>
        <link href="bigstyle.css" type="text/css" rel="stylesheet" />
        <script type="text/javascript">
          <![CDATA[
          
          function resetselectedoption()
          {
            if (empradiosupdate.personnelradio1 != null) { empradiosupdate.personnelradio1.text = ''; }
            if (empradiosupdate.personnelradio2 != null) { empradiosupdate.personnelradio2.text = ''; }
            if (empradiosupdate.personnelradio3 != null) { empradiosupdate.personnelradio3.text = ''; }
            if (empradiosupdate.personnelradio4 != null) { empradiosupdate.personnelradio4.text = ''; }
            if (empradiosupdate.personnelradio5 != null) { empradiosupdate.personnelradio5.text = ''; }
            if (empradiosupdate.personnelradio6 != null) { empradiosupdate.personnelradio6.text = ''; }
            if (empradiosupdate.personnelradio7 != null) { empradiosupdate.personnelradio7.text = ''; }
            if (empradiosupdate.personnelradio8 != null) { empradiosupdate.personnelradio8.text = ''; }
            
            if(empradiosupdate.radioid1 != null) 
            { 
              if(empradiosupdate.radioid1.value.length == 0) { empradiosupdate.radioid1.value = '-1'; }
            }
            
            if(empradiosupdate.radioid2 != null) 
            { 
              if(empradiosupdate.radioid2.value.length == 0) { empradiosupdate.radioid1.value = '-1'; }
            }
            
            if(empradiosupdate.radioid3 != null) 
            { 
              if(empradiosupdate.radioid3.value.length == 0) { empradiosupdate.radioid1.value = '-1'; }
            }
            
            if(empradiosupdate.radioid4 != null) 
            { 
              if(empradiosupdate.radioid4.value.length == 0) { empradiosupdate.radioid1.value = '-1'; }
            }
            
            if(empradiosupdate.radioid5 != null) 
            { 
              if(empradiosupdate.radioid5.value.length == 0) { empradiosupdate.radioid1.value = '-1'; }
            }
            
            if(empradiosupdate.radioid6 != null) 
            { 
              if(empradiosupdate.radioid6.value.length == 0) { empradiosupdate.radioid1.value = '-1'; }
            }
            
            if(empradiosupdate.radioid7 != null) 
            { 
              if(empradiosupdate.radioid7.value.length == 0) { empradiosupdate.radioid1.value = '-1'; }
            }
            
            if(empradiosupdate.radioid8 != null) 
            { 
              if(empradiosupdate.radioid8.value.length == 0) { empradiosupdate.empscheduleid8.value = ''; }
            }
          }
          
          function updatehiddenfields(position, selectedoption)
          {            
              if(position != 0)
              {
              
                var str = selectedoption.options[selectedoption.selectedIndex].value;
                
                if(position == 1)
                {
                  if(str.length != 0)
                  {
                    empradiosupdate.radioid1.value = str;
	                  empradiosupdate.radiodescription1.value = selectedoption.options[selectedoption.selectedIndex].radiodescription;
	                  empradiosupdate.radiocode1.value = selectedoption.options[selectedoption.selectedIndex].radiocode;
                  }	                
                }
                else if(position == 2)
                {
	                if(str.length != 0)
                  {
                    empradiosupdate.radioid2.value = str;
	                  empradiosupdate.radiodescription2.value = selectedoption.options[selectedoption.selectedIndex].radiodescription;
	                  empradiosupdate.radiocode2.value = selectedoption.options[selectedoption.selectedIndex].radiocode;
                  }
                }
                else if(position == 3)
                {
	                if(str.length != 0)
                  {
                    empradiosupdate.radioid3.value = str;
	                  empradiosupdate.radiodescription3.value = selectedoption.options[selectedoption.selectedIndex].radiodescription;
	                  empradiosupdate.radiocode3.value = selectedoption.options[selectedoption.selectedIndex].radiocode;
                  }
                }
                else if(position == 4)
                {
	                if(str.length != 0)
                  {
                    empradiosupdate.radioid4.value = str;
	                  empradiosupdate.radiodescription4.value = selectedoption.options[selectedoption.selectedIndex].radiodescription;
	                  empradiosupdate.radiocode4.value = selectedoption.options[selectedoption.selectedIndex].radiocode;
                  }
                }
                else if(position == 5)
                {
	                if(str.length != 0)
                  {
                    empradiosupdate.radioid5.value = str;
	                  empradiosupdate.radiodescription5.value = selectedoption.options[selectedoption.selectedIndex].radiodescription;
	                  empradiosupdate.radiocode5.value = selectedoption.options[selectedoption.selectedIndex].radiocode;
                  }
                }
                else if(position == 6)
                {
	                if(str.length != 0)
                  {
                    empradiosupdate.radioid6.value = str;
	                  empradiosupdate.radiodescription6.value = selectedoption.options[selectedoption.selectedIndex].radiodescription;
	                  empradiosupdate.radiocode6.value = selectedoption.options[selectedoption.selectedIndex].radiocode;
                  }
                }
                else if(position == 7)
                {
	                if(str.length != 0)
                  {
                    empradiosupdate.radioid7.value = str;
	                  empradiosupdate.radiodescription7.value = selectedoption.options[selectedoption.selectedIndex].radiodescription;
	                  empradiosupdate.radiocode7.value = selectedoption.options[selectedoption.selectedIndex].radiocode;
                  }
                }
                else if(position == 8)
                {
	                if(str.length != 0)
                  {
                    empradiosupdate.radioid8.value = str;
	                  empradiosupdate.radiodescription8.value = selectedoption.options[selectedoption.selectedIndex].radiodescription;
	                  empradiosupdate.radiocode8.value = selectedoption.options[selectedoption.selectedIndex].radiocode;
                  }
                }
              }            
          }

          ]]>
        </script>
      </head>

      <body>
        <!-- Report Header -->
        <table width="100%" border="0">
          <tr>
            <TD align="center"><H4>Personnel Radios</H4></TD>
          </tr>
        </table>
        <form name="empradiosupdate" action="assignpersonnelscheduleradios.aspx?queryfile=assignpersonnelscheduleradios.qry">
          <br/>
          <xsl:apply-templates select="/results/exemel/NewDataSet"/>
          <br/>
          <div align="right">
            <input type="button" name="Query" id="Query" value="Submit" onkeypress="resetselectedoption();empradiosupdate.Submit.click();" onclick="resetselectedoption();empradiosupdate.Submit.click();"/>
            <input style="width:0px;" TABINDEX="-1" type="submit" name="Submit" id="Submit" value="Submit"/>
          </div>
        </form>
      </body>
    </html>
  </xsl:template>

  <xsl:template match="/results/exemel/NewDataSet">
    <table border="1" cellspacing="10">
      <tr>
        <td valign="top" align="center" width="200px">Employee ID</td>
        <td valign="top" align="center" width="200px">Employee Name</td>
        <td valign="top" align="center" width="500px">Radios</td>
        <td valign="top" align="center" width="200px">Temporary Radio</td>
      </tr>
      <xsl:for-each select="/results/exemel/NewDataSet/Table">
        <tr>
          <td valign="top" width="200px">
            <xsl:value-of select="EmployeeID"/>
            <xsl:element name ="input">
              <xsl:attribute name="id">empscheduleid<xsl:number value="position()"/></xsl:attribute>
              <xsl:attribute name="type">hidden</xsl:attribute>
              <xsl:attribute name="value"><xsl:value-of select="EmployeeScheduleID"/></xsl:attribute>
            </xsl:element>
          </td>
          <td valign="top" width="200px">
            <xsl:value-of select="EmployeeName"/>
          </td>
          <td valign="top" width="500px">
            <br/>
            <ul>
              <xsl:call-template name="Radios">
                <xsl:with-param name="EmpID" select="EmployeeID"/>
              </xsl:call-template>

              <xsl:element name ="input">
                <xsl:attribute name="id">radioid<xsl:number value="position()"/></xsl:attribute>
                <xsl:attribute name="type">hidden</xsl:attribute>
                <xsl:attribute name="value"></xsl:attribute>
              </xsl:element>
              <xsl:element name ="input">
                <xsl:attribute name="id">radiocode<xsl:number value="position()"/></xsl:attribute>
                <xsl:attribute name="type">hidden</xsl:attribute>
                <xsl:attribute name="value"></xsl:attribute>
              </xsl:element>
              <xsl:element name ="input">
                <xsl:attribute name="id">radiodescription<xsl:number value="position()"/></xsl:attribute>
                <xsl:attribute name="type">hidden</xsl:attribute>
                <xsl:attribute name="value"></xsl:attribute>
              </xsl:element>
            </ul>
          </td>
          <td valign="top">
            <xsl:element name="select">
              <xsl:attribute name="id">personnelradio<xsl:number value="position()"/></xsl:attribute>
              <xsl:attribute name="name">personnelradio<xsl:number value="position()"/></xsl:attribute>
              <xsl:attribute name="width">200px</xsl:attribute>
              <xsl:attribute name="style">width:200px</xsl:attribute>
              <xsl:attribute name="OnChange">updatehiddenfields(<xsl:number value="position()"/>,this);</xsl:attribute>

              <xsl:element name="option">
                <xsl:attribute name="id">Radio0</xsl:attribute>
                <xsl:attribute name="value"><xsl:value-of select="BlankSpace"/></xsl:attribute>
                <xsl:attribute name="radiodescription"><xsl:value-of select="BlankSpace"/></xsl:attribute>
                <xsl:attribute name="radioname"><xsl:value-of select="BlankSpace"/></xsl:attribute>
              </xsl:element>

              <xsl:for-each select="/results/exemel/NewDataSet/Table2">
                <xsl:element name="option">
                  <xsl:attribute name="value"><xsl:value-of select="RadioID"/></xsl:attribute>
                  <xsl:attribute name="radiodescription"><xsl:value-of select="RadioName"/></xsl:attribute>
                  <xsl:attribute name="radiocode"><xsl:value-of select="RadioCode"/></xsl:attribute>
                  <xsl:value-of select="RadioID"/>
                </xsl:element>
              </xsl:for-each>
            </xsl:element>
          </td>
        </tr>
      </xsl:for-each>
    </table>
  </xsl:template>

  <xsl:template name="Radios">
    <xsl:param name="EmpID"/>

    <xsl:for-each select="/results/exemel/NewDataSet/Table1">
      <xsl:if test="EmployeeID = $EmpID">
        <li>
          <xsl:choose>
            <xsl:when test="RadioType = $RadioType1">
              <xsl:value-of select="$BlankSpace"/>
              <xsl:value-of select="$PermVehicleRadios"/>
            </xsl:when>
            <xsl:when test="RadioType = $RadioType2">
              <xsl:value-of select="$BlankSpace"/>
              <xsl:value-of select="$TempVehicleRadios"/>
            </xsl:when>
            <xsl:when test="RadioType = $RadioType3">
              <xsl:value-of select="$BlankSpace"/>
              <xsl:value-of select="$TempPersonnelRadios"/>
            </xsl:when>
            <xsl:when test="RadioType = $RadioType4">
              <xsl:value-of select="$BlankSpace"/>
              <xsl:value-of select="$PermPersonnelRadios"/>
            </xsl:when>
          </xsl:choose>
          <br/>
          <xsl:variable name="RadioString">
            <xsl:value-of select="RadioID"/>:<xsl:value-of select="$BlankSpace"/><xsl:value-of select ="RadioCode"/>,<xsl:value-of select="$BlankSpace"/><xsl:value-of select="$BlankSpace"/><xsl:value-of select ="RadioName"/>
          </xsl:variable>
          <xsl:value-of select="substring($RadioString,0,50)"/>
        </li>
      </xsl:if>
    </xsl:for-each>
  </xsl:template>
</xsl:stylesheet>