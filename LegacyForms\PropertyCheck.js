//start share
var m_proSerialName = "SerialNo";
var m_proArticleName = "Article";
var m_proBrandName = "Brand";
//end share
//start of supplement
var m_proSuppCheckboxName = "doSupplement";
var m_proSuppHiddenFieldSerialName = "SupplementSerialNumber";
function HandlePropertySupplementFieldsBeforeSubmit()
{
    //this method is call before submit; that is in window.validatepage.
    //how to use supplement:
    //To enable:
    //  labelSupplement, doSupplement,SerialNo, and SupplementSerialNumber  are needed.
    //To disable:
    //  labelSupplement and doSupplement must be commented out.  We do not remove the nodes, just commented them out.

    var isValid = false;
    var elementPropDoSupplement = document.getElementById(m_proSuppCheckboxName);
    var elementPropSerialNo = document.getElementById(m_proSerialName);
    var elementPropSupplementSerialNumber = document.getElementById(m_proSuppHiddenFieldSerialName);
    if ((elementPropDoSupplement != null) && (elementPropDoSupplement.checked))
    {
        if (CheckPropertySupplementRequiredFields())
        {
            if (elementPropSerialNo.value != "")
            {
                elementPropSupplementSerialNumber.value = elementPropSerialNo.value;
                isValid = true;
            }
            else
            {
                //required field is not met.
                alert("Serial number is needed to query supplement.");
                isValid = false;
            }
        }
    }
    else
    {
        isValid = true;
    }
    return isValid;
}
function CheckPropertySupplementRequiredFields()
{
    //this method is called by CheckPropertySupplementRequiredFields and HandlePropertySupplementFieldsBeforeSubmit.
    //check to see if the checkbox and required input fields exist
    var elementPropDoSupplement = document.getElementById(m_proSuppCheckboxName);
    var elementPropSerialNo = document.getElementById(m_proSerialName);
    if (elementPropDoSupplement != null)
    {
        //check to see if the serial input is there.
        if (elementPropSerialNo == null)
        {
            alert("Configuration error: " + m_proSuppCheckboxName + " requires " + m_proSerialName + " field.");
            return false;
        }
        else
        {
            //the checkbox and serial field are there
            return true;

        }
    }
    else
    {
        return false;
    }
}
function GeneratePropertySupplementHiddenFields()
{
    //this method is called as part of the window.onload.

    //check to see if the checkbox exist
    if (CheckPropertySupplementRequiredFields())
    {

        //add the hidden field
        return "<input type=\"hidden\" name=\"" + m_proSuppHiddenFieldSerialName + "\" id=\"" + m_proSuppHiddenFieldSerialName + "\" />"

    }
    else
    {
        return "";
    }
}
//end of supplement
//start RMS
var m_proDoRMSCheckboxName = "doRMS";
var m_proRMSHiddenFieldSerialName = "RMSSerial";
var m_proRMSHiddenFieldArticleName = "RMSArticle";
var m_proRMSHiddenFieldBrandName = "RMSBrand";

function HandlePropertyRMSFieldsBeforeSubmit() {
    //this method is call before submit; that is in window.validatepage.
    //how to use RMS:
    //To enable:
    //  labelRMS, doRMS, SerialNo/Article/Brand) are needed.
    //To disable:
    //  labelRMS and doRMS must be commented out.  We do not remove the nodes, just commented them out.

    //Do checkboxes
    var elementPropDoRMS = document.getElementById(m_proDoRMSCheckboxName);

    //Values on form
    var elementPropSerialNo = document.getElementById(m_proSerialName);
    var elementPropArticle = document.getElementById(m_proArticleName);
    var elementPropBrand = document.getElementById(m_proBrandName);

    //Hidden fields
    var elementPropRMSSerialNumber = document.getElementById(m_proRMSHiddenFieldSerialName);
    var elementPropRMSArticle = document.getElementById(m_proRMSHiddenFieldArticleName);
    var elementPropRMSBrand = document.getElementById(m_proRMSHiddenFieldBrandName);

    if (elementPropDoRMS != null && elementPropDoRMS.checked)
    {
        if (CheckPropertyRMSRequiredFields()) {
            if (IsElementNullOrValueWhitespace(elementPropSerialNo) && IsElementNullOrValueWhitespace(elementPropArticle) && IsElementNullOrValueWhitespace(elementPropBrand))
            {
                alert("Serial number, article, or brand is needed to query RMS.");
                return false;
            }
        
            //copy values to hidden fields if available
            if (!IsElementNullOrValueWhitespace(elementPropSerialNo)) {
                elementPropRMSSerialNumber.value = elementPropSerialNo.value;
            }

            if (!IsElementNullOrValueWhitespace(elementPropArticle)) {
                elementPropRMSArticle.value = elementPropArticle.value;
            }
            
            if (!IsElementNullOrValueWhitespace(elementPropBrand)) {
                elementPropRMSBrand.value = elementPropBrand.value;
            }
        }
    }

    return true;
}
function CheckPropertyRMSRequiredFields() {
    //this method is called by GeneratePropertyRMSHiddenFields and HandlePropertyRMSFieldsBeforeSubmit.
    //check to see if the checkbox and required input fields exist
    var elementProDoRMS = document.getElementById(m_proDoRMSCheckboxName);
    var elementPropSerialNo = document.getElementById(m_proSerialName);
    var elementPropArticle = document.getElementById(m_proArticleName);
    var elementPropBrand = document.getElementById(m_proBrandName);
    
    if (elementProDoRMS != null) {
        //make sure there is something that can query rms on the form
        if (elementPropSerialNo == null && elementPropArticle == null && elementPropBrand == null) {
            alert("Configuration error: " + elementProDoRMS + " requires " + m_proSerialName + ", " + m_proArticleName + ", " + " or " + m_proBrandName + " field.");
            return false;
        }
        return true;
    }
    else {
        return false;
    }
}
function GeneratePropertyRMSHiddenFields()
{
    //this method is called as part of the window.onload.

    //check to see if the checkbox exist
    if (CheckPropertyRMSRequiredFields())
    {
        //add the hidden field
        return "<input type=\"hidden\" name=\"" + m_proRMSHiddenFieldSerialName + "\" id=\"" + m_proRMSHiddenFieldSerialName + "\" />"
                + "<input type=\"hidden\" name=\"" + m_proRMSHiddenFieldArticleName + "\" id=\"" + m_proRMSHiddenFieldArticleName + "\" />"
                + "<input type=\"hidden\" name=\"" + m_proRMSHiddenFieldBrandName + "\" id=\"" + m_proRMSHiddenFieldBrandName + "\" />";
    }
    else
    {
        return "";
    }
}

function IsElementNullOrValueWhitespace(input)
{
    if (input == null) return true;

    return input.value == "";
}