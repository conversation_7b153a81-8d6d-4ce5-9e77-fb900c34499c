{"ActiveIncidentsQueueConfiguration": {"Name": "ACTIVE CALLS", "EmptyLabelText": "No Active Calls", "Columns": [{"Type": 1, "HeaderText": "ID", "SourcePropertyName": "IncidentIdShortened", "Width": "Auto", "MinWidth": 50.0}, {"Type": 1, "HeaderText": "Call Number", "SourcePropertyName": "IncidentNumber", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Agency", "SourcePropertyName": "Agency", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Division", "SourcePropertyName": "Division", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Battalion", "SourcePropertyName": "Battalion", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Sector", "SourcePropertyName": "Sector", "Width": "*", "MinWidth": 50.0}, {"Type": 1, "HeaderText": "Priority", "SourcePropertyName": "Priority", "Width": "Auto", "MinWidth": 50.0}, {"Type": 6, "HeaderText": "Elapsed Time", "SourcePropertyName": "ElapsedTime", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Time", "SourcePropertyName": "TimestampDisplay", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 1, "HeaderText": "Problem", "SourcePropertyName": "ProblemName", "Width": "*", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 1, "HeaderText": "Units", "SourcePropertyName": "AssignedUnitSummary", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 2, "HeaderText": "Address", "SourcePropertyName": "Address", "Width": "2*", "MinWidth": 50.0}, {"HeaderText": "Zip", "SourcePropertyName": "Zip", "Width": "Auto", "MinWidth": 50.0}, {"Type": 2, "HeaderText": "Address", "SourcePropertyName": "AddressExtended", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 2, "HeaderText": "Location", "SourcePropertyName": "LocationName", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Juris<PERSON>", "SourcePropertyName": "Juris<PERSON>", "Width": "*", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Response Area", "SourcePropertyName": "ResponseArea", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "City", "SourcePropertyName": "City", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "County", "SourcePropertyName": "County", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "State", "SourcePropertyName": "State", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Incident Type", "SourcePropertyName": "IncidentType", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Case Number", "SourcePropertyName": "CaseNumber", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Alarm Level", "SourcePropertyName": "AlarmLevel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Alternate TAC Channel", "SourcePropertyName": "AlternateTACChannel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Base Response Number", "SourcePropertyName": "BaseResponseNumber", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Building", "SourcePropertyName": "Building", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Call Back Phone Ext", "SourcePropertyName": "CallBackPhoneExt", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Caller Building", "SourcePropertyName": "CallerBuilding", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Caller Name", "SourcePropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Caller Type", "SourcePropertyName": "CallerType", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Call Taking Performed By", "SourcePropertyName": "CallTakingPerformedBy", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Command Channel", "SourcePropertyName": "CommandChannel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Determinant", "SourcePropertyName": "Determinant", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Fire Box", "SourcePropertyName": "FireBox", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Map Reference", "SourcePropertyName": "MapReference", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Method Call Received", "SourcePropertyName": "MethodCallReceived", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Patient First Name", "SourcePropertyName": "PatientFirstName", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Patient Last Name", "SourcePropertyName": "PatientLastName", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Pick Up Address Info", "SourcePropertyName": "PickUpAddressInfo", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Primary TAC Channel", "SourcePropertyName": "PrimaryTACChannel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Problem Code", "SourcePropertyName": "ProblemCode", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time Call Assigned", "SourcePropertyName": "TimeCalledAssigned", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time Call Entered Queue", "SourcePropertyName": "TimeCallEnteredQueue", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time First Unit Arrived", "SourcePropertyName": "TimeFirstUnitArrived", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time First Unit Assigned", "SourcePropertyName": "TimeFirstUnitAssigned", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time First Unit Enroute", "SourcePropertyName": "TimeFirstUnitEnroute", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time First Call Taking Keystroke", "SourcePropertyName": "TimeFirstCallTakingKeystroke", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport Protocol", "SourcePropertyName": "TransportProtocol", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Address", "SourcePropertyName": "TransportToAddress", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Apartment", "SourcePropertyName": "TransportToApartment", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Building", "SourcePropertyName": "TransportToBuilding", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To City", "SourcePropertyName": "TransportToCity", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Location", "SourcePropertyName": "TransportToLocation", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To State", "SourcePropertyName": "TransportToState", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Zip", "SourcePropertyName": "TransportToZip", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 1", "SourcePropertyName": "UDF1", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 2", "SourcePropertyName": "UDF2", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 3", "SourcePropertyName": "UDF3", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 4", "SourcePropertyName": "UDF4", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 5", "SourcePropertyName": "UDF5", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 6", "SourcePropertyName": "UDF6", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 7", "SourcePropertyName": "UDF7", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 8", "SourcePropertyName": "UDF8", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 9", "SourcePropertyName": "UDF9", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 10", "SourcePropertyName": "UDF10", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 11", "SourcePropertyName": "UDF11", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 12", "SourcePropertyName": "UDF12", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 13", "SourcePropertyName": "UDF13", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 14", "SourcePropertyName": "UDF14", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 15", "SourcePropertyName": "UDF15", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 16", "SourcePropertyName": "UDF16", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 17", "SourcePropertyName": "UDF17", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 18", "SourcePropertyName": "UDF18", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 19", "SourcePropertyName": "UDF19", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 20", "SourcePropertyName": "UDF20", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 21", "SourcePropertyName": "UDF21", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 22", "SourcePropertyName": "UDF22", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Personnel Count", "SourcePropertyName": "PersonnelCount", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Duplicate Incident Count", "SourcePropertyName": "DuplicateIncidentCount", "Width": "Auto", "MinWidth": 50.0}], "IsEnabled": true}, "PendingIncidentsQueueConfiguration": {"Name": "PENDING CALLS", "EmptyLabelText": "No Pending Calls", "Columns": [{"Type": 1, "HeaderText": "ID", "SourcePropertyName": "IncidentIdShortened", "Width": "Auto", "MinWidth": 50.0}, {"Type": 1, "HeaderText": "Call Number", "SourcePropertyName": "IncidentNumber", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Agency", "SourcePropertyName": "Agency", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Division", "SourcePropertyName": "Division", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Battalion", "SourcePropertyName": "Battalion", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Sector", "SourcePropertyName": "Sector", "Width": "*", "MinWidth": 50.0}, {"Type": 1, "HeaderText": "Priority", "SourcePropertyName": "Priority", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Time", "SourcePropertyName": "TimestampDisplay", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 6, "HeaderText": "Elapsed Time", "SourcePropertyName": "ElapsedTime", "Width": "Auto", "MinWidth": 50.0}, {"Type": 1, "HeaderText": "Problem", "SourcePropertyName": "ProblemName", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 2, "HeaderText": "Address", "SourcePropertyName": "AddressExtended", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"Type": 2, "HeaderText": "Address", "SourcePropertyName": "Address", "Width": "2*", "MinWidth": 50.0}, {"Type": 2, "HeaderText": "Location", "SourcePropertyName": "LocationName", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Zip", "SourcePropertyName": "Zip", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Stacked Unit", "SourcePropertyName": "StackedUnit", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Response Area", "SourcePropertyName": "ResponseArea", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "City", "SourcePropertyName": "City", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "County", "SourcePropertyName": "County", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "State", "SourcePropertyName": "State", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Incident Type", "SourcePropertyName": "IncidentType", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Alarm Level", "SourcePropertyName": "AlarmLevel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Alternate TAC Channel", "SourcePropertyName": "AlternateTACChannel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Base Response Number", "SourcePropertyName": "BaseResponseNumber", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Building", "SourcePropertyName": "Building", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Call Back Phone Ext", "SourcePropertyName": "CallBackPhoneExt", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Caller Building", "SourcePropertyName": "CallerBuilding", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Caller Name", "SourcePropertyName": "<PERSON><PERSON><PERSON><PERSON>", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Caller Type", "SourcePropertyName": "CallerType", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Call Taking Performed By", "SourcePropertyName": "CallTakingPerformedBy", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Command Channel", "SourcePropertyName": "CommandChannel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Determinant", "SourcePropertyName": "Determinant", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Fire Box", "SourcePropertyName": "FireBox", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Map Reference", "SourcePropertyName": "MapReference", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Method Call Received", "SourcePropertyName": "MethodCallReceived", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Patient First Name", "SourcePropertyName": "PatientFirstName", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Patient Last Name", "SourcePropertyName": "PatientLastName", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Pick Up Address Info", "SourcePropertyName": "PickUpAddressInfo", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Primary TAC Channel", "SourcePropertyName": "PrimaryTACChannel", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Problem Code", "SourcePropertyName": "ProblemCode", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time Call Assigned", "SourcePropertyName": "TimeCalledAssigned", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time Call Entered Queue", "SourcePropertyName": "TimeCallEnteredQueue", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time First Unit Arrived", "SourcePropertyName": "TimeFirstUnitArrived", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time First Unit Assigned", "SourcePropertyName": "TimeFirstUnitAssigned", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time First Unit Enroute", "SourcePropertyName": "TimeFirstUnitEnroute", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Time First Call Taking Keystroke", "SourcePropertyName": "TimeFirstCallTakingKeystroke", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport Protocol", "SourcePropertyName": "TransportProtocol", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Address", "SourcePropertyName": "TransportToAddress", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Apartment", "SourcePropertyName": "TransportToApartment", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Building", "SourcePropertyName": "TransportToBuilding", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To City", "SourcePropertyName": "TransportToCity", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Location", "SourcePropertyName": "TransportToLocation", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To State", "SourcePropertyName": "TransportToState", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Transport To Zip", "SourcePropertyName": "TransportToZip", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 1", "SourcePropertyName": "UDF1", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 2", "SourcePropertyName": "UDF2", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 3", "SourcePropertyName": "UDF3", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 4", "SourcePropertyName": "UDF4", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 5", "SourcePropertyName": "UDF5", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 6", "SourcePropertyName": "UDF6", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 7", "SourcePropertyName": "UDF7", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 8", "SourcePropertyName": "UDF8", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 9", "SourcePropertyName": "UDF9", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 10", "SourcePropertyName": "UDF10", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 11", "SourcePropertyName": "UDF11", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 12", "SourcePropertyName": "UDF12", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 13", "SourcePropertyName": "UDF13", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 14", "SourcePropertyName": "UDF14", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 15", "SourcePropertyName": "UDF15", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 16", "SourcePropertyName": "UDF16", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 17", "SourcePropertyName": "UDF17", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 18", "SourcePropertyName": "UDF18", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 19", "SourcePropertyName": "UDF19", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 20", "SourcePropertyName": "UDF20", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 21", "SourcePropertyName": "UDF21", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "UDF 22", "SourcePropertyName": "UDF22", "Width": "*", "MinWidth": 50.0}, {"HeaderText": "Juris<PERSON>", "SourcePropertyName": "Juris<PERSON>", "Width": "*", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Duplicate Incident Count", "SourcePropertyName": "DuplicateIncidentCount", "Width": "Auto", "MinWidth": 50.0}], "IsEnabled": true}, "Options": [{"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 221, "MobileAction": 3}], "Buttons": [{"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 84, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 83, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 80, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 79, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 85, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 82, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 78, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 77, "MobileAction": 3}], "IsEnabled": true, "VersionNumber": 7}