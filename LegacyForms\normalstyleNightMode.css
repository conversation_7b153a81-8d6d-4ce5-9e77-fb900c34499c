INPUT
{
	font-size: 12pt;
	font-family: <PERSON><PERSON>e;
}
TEXTAREA
{
	font-size: 12pt;
    font-family: <PERSON><PERSON> Console;
}
SELECT
{
	font-size: 12pt;
	font-family: Verdana;
}
OPTION
{
	font-size: 12pt;
	font-family: Verdana;
}
A:link
{
	font-weight: bold;
	font-size: 12pt;
	font-family: Verdana;
	text-decoration: underline;
}
A:visited
{
	font-weight: bold;
	font-size: 12pt;
	font-family: Verdana;
	text-decoration: underline;
}
A:hover
{
	font-weight: bold;
	font-size: 12pt;

	font-family: Verdana;
	text-decoration: underline;
}
B
{
	font-weight: bold;
    font-size: 12pt;
    color: #BFC3C9;
}
H2 {
    FONT-SIZE: 24px; 
    FONT-FAMILY: Verdana
}
H3 {
    PADDING-RIGHT: 2px; 
    PADDING-LEFT: 2px; 
    FONT-SIZE: 16px; 
    PADDING-BOTTOM: 3px; 
    TEXT-TRANSFORM: capitalize; 
    PADDING-TOP: 3px; 
    FONT-FAMILY: Verdana; 
    TEXT-ALIGN: left
}
H4 {
    FONT-SIZE: 14pt; 
    FONT-FAMILY: Verdana
}
H5 {
    FONT-SIZE: 10px; 
    COLOR: #BFC3C9;
    FONT-FAMILY: Verdana
}
xml{
	border-style:none !important;
	border:0 !important;
}
BODY {
    FONT-SIZE: 12pt; 
    BACKGROUND-COLOR:black; 
	COLOR:  #BFC3C9;
    FONT-FAMILY: Verdana; 
}
LI {
	LIST-STYLE-TYPE: square
}
TABLE {
    FONT-SIZE: 12pt; 
    FONT-FAMILY: Verdana
}
HR {
    FONT-SIZE: 1px; 
    FLOAT: left; 
    POSITION: relative; 
    HEIGHT: 1px
}
input[type=button]{
    Display:inline;
    border-radius: 0px;
    border-style: hidden;
	COLOR: #FFFFFF;
    BACKGROUND-COLOR:#3B90EE;
}
input[type=submit] {
    Display:inline;
    border-radius: 2px;
    border-style: hidden;
    COLOR: #FFFFFF;
    BACKGROUND-COLOR:#3B90EE;
}
input[type=radio]{
	Display:inline;
	border: 2px #3B90EE;
}
input[type=text]{
	background-color:#111B2B;
	border-style: hidden;
	COLOR:  #BFC3C9;
}
input[type=password]{
	background-color:#111B2B;
	COLOR:  #BFC3C9;
	border-style:none !important;
	border:0 !important;
}
textarea{
	border-color:0px solid transparent;
	background-color:#111B2B;
	border-style: hidden;
	COLOR:  #BFC3C9;
}
select{
	border-color:10px solid transparent;
	background-color:#111B2B;
	border-style: hidden;
	COLOR:  #BFC3C9;
}
select empty{
	background-color:#111B2B;
}
select option{
	background-color:#111B2B;
	COLOR:  #BFC3C9;
}
select option:not(:checked) { 
    background-color: #111B2B; 
}
.base {
    PADDING-RIGHT: 10px; 
    PADDING-LEFT: 10px; 
    PADDING-BOTTOM: 10px;
    MARGIN: 10px;
    PADDING-TOP: 10px; 
}
.rightaligncell {
    PADDING-RIGHT: 2px; 
    PADDING-LEFT: 2px; 
    FONT-WEIGHT: bold; 
    PADDING-BOTTOM: 2px; 
    PADDING-TOP: 2px; 
    TEXT-ALIGN: right
}
.topbottombordercell {
    PADDING-RIGHT: 2px; 
    PADDING-LEFT: 2px; 
    PADDING-BOTTOM: 2px; 
    PADDING-TOP: 2px; 
    TEXT-ALIGN: right;
    background-color:#E8EAED; 
	color:#212E40;
}
.bottombordercell {
    PADDING-RIGHT: 2px; 
    PADDING-LEFT: 2px; 
    PADDING-BOTTOM: 2px; 
    PADDING-TOP: 2px; 
}
.bottomborderpanel {
    PADDING-RIGHT: 5px; 
    PADDING-LEFT: 5px; 
    PADDING-BOTTOM: 10px; 
    PADDING-TOP: 5px; 
    TEXT-ALIGN: left
}
.topbordercell {
    PADDING-RIGHT: 10px; 
    PADDING-LEFT: 10px; 
    PADDING-BOTTOM: 10px; 
    PADDING-TOP: 10px; 
    TEXT-ALIGN: left
}
.message {
    FONT-WEIGHT: bolder; 
    FONT-SIZE: 12pt; 
    TEXT-ALIGN: center
}
.xslsubheading {
	font-size: 8pt;
}
BODY
{
    FONT-SIZE: 12pt;
    FONT-FAMILY: Arial
}
BODY TD
{
    FONT-SIZE: 12pt;
    BACKGROUND-COLOR:#071021;
}
BODY TD .title
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    COLOR:  #BFC3C9;
    FONT-FAMILY: Arial;
    TEXT-ALIGN: right;
    TEXT-DECORATION: none
}
BODY TD .subtitle
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    FONT-FAMILY: Arial;
    TEXT-ALIGN: center;
    TEXT-DECORATION: none
}
BODY TD .InputNames
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    FONT-FAMILY: Arial
}
BODY TD .InputData
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    FONT-FAMILY: Courier
}
.bigcheckbox
{
	WIDTH: 65px; 
	HEIGHT: 33px;
}
.bigradio
{
	WIDTH: 65px; 
	HEIGHT: 33px;
}
TABLE .radiotable
{
    background-color:#111B2B;
	COLOR:  #BFC3C9;
}
TD .RadioTableRowHeader
{
	width:120px; 
    text-align:right;
	COLOR:  #BFC3C9;
}
TD .RadioTableRadioIDEntry
{	
	width:120; 
    text-align:left;
    background-color:#111B2B;
	COLOR:  #BFC3C9;
}
TD .RadioTableRadioIDEntry INPUT
{	
    width:100%; 
    background-color:#111B2B;
	COLOR:  #BFC3C9;
}
TD .RadioTableRadioCodeEntry
{	
	width:160; 
    text-align:left;
    background-color:#111B2B;
	COLOR:  #BFC3C9;
}
TD .RadioTableRadioCodeEntry INPUT
{	
    width:100%; 
    background-color:#111B2B;
	COLOR:  #BFC3C9;
}
TD .RadioTableRadioDescEntry
{	
    text-align:left;
    background-color:#111B2B;
	COLOR:  #BFC3C9;
}
TD .RadioTableRadioDescEntry INPUT
{	
    width:100%; 
    background-color:#111B2B;
	COLOR:  #BFC3C9;
}

