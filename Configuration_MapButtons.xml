<?xml version="1.0" encoding="utf-8"?>
<MapButtonPanel xmlns="http://www.voyagersystemsinc.com/VoyagerMobile/SchemaMapButtons.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Button>
		<Type>ZoomIn</Type>
		<Caption>Zoom In</Caption>
		<IconID>0</IconID>
		<FKeyNumber>9</FKeyNumber>
		<HoldAltKey>true</HoldAltKey>
		<HoldCtrlKey>false</HoldCtrlKey>
		<HoldShiftKey>false</HoldShiftKey>
		<BackColorRed>255</BackColorRed>
		<BackColorGreen>255</BackColorGreen>
		<BackColorBlue>255</BackColorBlue>
		<ForeColorRed>0</ForeColorRed>
		<ForeColorGreen>0</ForeColorGreen>
		<ForeColorBlue>0</ForeColorBlue>
		<IsHidden>false</IsHidden>
	</Button>
	<Button>
		<Type>ZoomOut</Type>
		<Caption>Zoom Out</Caption>
		<IconID>1</IconID>
		<FKeyNumber>10</FKeyNumber>
		<HoldAltKey>true</HoldAltKey>
		<HoldCtrlKey>false</HoldCtrlKey>
		<HoldShiftKey>false</HoldShiftKey>
		<BackColorRed>255</BackColorRed>
		<BackColorGreen>255</BackColorGreen>
		<BackColorBlue>255</BackColorBlue>
		<ForeColorRed>0</ForeColorRed>
		<ForeColorGreen>0</ForeColorGreen>
		<ForeColorBlue>0</ForeColorBlue>
		<IsHidden>false</IsHidden>
	</Button>
	<Button>
		<Type>Squeegee</Type>
		<Caption>Clear</Caption>
		<IconID>9</IconID>
		<FKeyNumber>8</FKeyNumber>
		<HoldAltKey>true</HoldAltKey>
		<HoldCtrlKey>false</HoldCtrlKey>
		<HoldShiftKey>false</HoldShiftKey>
		<BackColorRed>255</BackColorRed>
		<BackColorGreen>255</BackColorGreen>
		<BackColorBlue>255</BackColorBlue>
		<ForeColorRed>0</ForeColorRed>
		<ForeColorGreen>0</ForeColorGreen>
		<ForeColorBlue>0</ForeColorBlue>
		<IsHidden>false</IsHidden>
	</Button>
	<Button>
		<Type>RecenterTool</Type>
		<Caption>Center</Caption>
		<IconID>2</IconID>
		<FKeyNumber>11</FKeyNumber>
		<HoldAltKey>true</HoldAltKey>
		<HoldCtrlKey>false</HoldCtrlKey>
		<HoldShiftKey>false</HoldShiftKey>
		<BackColorRed>255</BackColorRed>
		<BackColorGreen>255</BackColorGreen>
		<BackColorBlue>255</BackColorBlue>
		<ForeColorRed>0</ForeColorRed>
		<ForeColorGreen>0</ForeColorGreen>
		<ForeColorBlue>0</ForeColorBlue>
		<IsHidden>false</IsHidden>
	</Button>
	<Button>
		<Type>MapItemInfoTool</Type>
		<Caption>Info</Caption>
		<IconID>3</IconID>
		<FKeyNumber>11</FKeyNumber>
		<HoldAltKey>true</HoldAltKey>
		<HoldCtrlKey>false</HoldCtrlKey>
		<HoldShiftKey>false</HoldShiftKey>
		<BackColorRed>255</BackColorRed>
		<BackColorGreen>255</BackColorGreen>
		<BackColorBlue>255</BackColorBlue>
		<ForeColorRed>0</ForeColorRed>
		<ForeColorGreen>0</ForeColorGreen>
		<ForeColorBlue>0</ForeColorBlue>
		<IsHidden>false</IsHidden>
	</Button>
<!--
	<Button>
		<Type>Separator</Type>
	</Button>
-->
	<Button>
		<Type>Toolbar</Type>
		<Caption>Map Tracking</Caption>
		<MapButtonPanel>
			<Button>
				<Type>NormalView</Type>
				<Caption>Normal</Caption>
				<IconID>4</IconID>
				<FKeyNumber>10</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
			</Button>
			<Button>
				<Type>AutoCenter</Type>
				<Caption>Vehicle</Caption>
				<IconID>5</IconID>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
			</Button>
			<Button>
				<Type>HeadingUp</Type>
				<Caption>Heading</Caption>
				<IconID>6</IconID>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
			</Button>
			<Button>
				<Type>AutoZoom</Type>
				<Caption>AutoZoom</Caption>
				<IconID>7</IconID>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
			</Button>
			<Button>
				<Type>IncidentZoom</Type>
				<Caption>CallZoom</Caption>
				<IconID>8</IconID>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
			</Button>
		</MapButtonPanel>
	</Button>
	<Button>
		<Type>Toolbar</Type>
		<Caption>Views</Caption>
		<MapButtonPanel>
			<ButtonZoomToPreset>
				<Caption>Dakota County</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>60</HeightMiles>
					<WidthMiles>60</WidthMiles>
				</Dimensions>
				<NewCenter>
					<Latitude>44.676810</Latitude>
					<Longitude>-93.035020</Longitude>
				</NewCenter>
			</ButtonZoomToPreset>
			<ButtonZoomToPreset>
				<Caption>North</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>28</HeightMiles>
					<WidthMiles>28</WidthMiles>
				</Dimensions>
				<NewCenter>
					<Latitude>44.827762</Latitude>
					<Longitude>-93.093861</Longitude>
				</NewCenter>
			</ButtonZoomToPreset>
			<ButtonZoomToPreset>
				<Caption>South</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>28</HeightMiles>
					<WidthMiles>28</WidthMiles>
				</Dimensions>
				<NewCenter>
					<Latitude>44.581710</Latitude>
					<Longitude>-93.034909</Longitude>
				</NewCenter>
			</ButtonZoomToPreset>
			<ButtonZoomToPreset>
				<Caption>East</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>28</HeightMiles>
					<WidthMiles>28</WidthMiles>
				</Dimensions>
				<NewCenter>
					<Latitude>44.693200</Latitude>
					<Longitude>-92.912964</Longitude>
				</NewCenter>
			</ButtonZoomToPreset>
			<ButtonZoomToPreset>
				<Caption>West</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>28</HeightMiles>
					<WidthMiles>28</WidthMiles>
				</Dimensions>
				<NewCenter>
					<Latitude>44.719986</Latitude>
					<Longitude>-93.161130</Longitude>
				</NewCenter>
			</ButtonZoomToPreset>
			<ButtonZoomToPreset>
				<Caption>10 mi</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>10</HeightMiles>
					<WidthMiles>10</WidthMiles>
				</Dimensions>
			</ButtonZoomToPreset>
			<ButtonZoomToPreset>
				<Caption>5 mi</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>5</HeightMiles>
					<WidthMiles>5</WidthMiles>
				</Dimensions>
			</ButtonZoomToPreset>
			<ButtonZoomToPreset>
				<Caption>2 mi</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>2</HeightMiles>
					<WidthMiles>2</WidthMiles>
				</Dimensions>
			</ButtonZoomToPreset>
			<ButtonZoomToPreset>
				<Caption>Hydrants</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<IsHidden>false</IsHidden>
				<Dimensions>
					<HeightMiles>.75</HeightMiles>
					<WidthMiles>.75</WidthMiles>
				</Dimensions>
			</ButtonZoomToPreset>
		</MapButtonPanel>
	</Button>
	<Button>
		<Type>Toolbar</Type>
		<Caption>Layers</Caption>
		<MapButtonPanel>
			<!-- <ButtonLayerToggle>
				<Caption>Airport</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<LayerName>AIRPORT</LayerName>
			</ButtonLayerToggle> -->
			<ButtonLayerToggle>
				<Caption>Aerial</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<LayerName>Aerial</LayerName>
			</ButtonLayerToggle>
			<ButtonLayerToggle>
				<Caption>Lakes + Rivers</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<LayerName>Lakes/Rivers</LayerName>
			</ButtonLayerToggle>
			<ButtonLayerToggle>
				<Caption>Overlay</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<LayerName>Boundary</LayerName>
			</ButtonLayerToggle>
			<ButtonLayerToggle>
				<Caption>Parks</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<LayerName>Parks</LayerName>
			</ButtonLayerToggle>
			<ButtonLayerToggle>
				<Caption>Trails</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<LayerName>Trails</LayerName>
			</ButtonLayerToggle>
			<ButtonLayerToggle>
				<Caption>Transit</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<LayerName>Transit</LayerName>
			</ButtonLayerToggle>
			<!-- <ButtonLayerToggle>
				<Caption>Address Points</Caption>
				<FKeyNumber>11</FKeyNumber>
				<HoldAltKey>true</HoldAltKey>
				<HoldCtrlKey>false</HoldCtrlKey>
				<HoldShiftKey>false</HoldShiftKey>
				<BackColorRed>255</BackColorRed>
				<BackColorGreen>255</BackColorGreen>
				<BackColorBlue>255</BackColorBlue>
				<ForeColorRed>0</ForeColorRed>
				<ForeColorGreen>0</ForeColorGreen>
				<ForeColorBlue>0</ForeColorBlue>
				<LayerName>ADDRESSPOINTS</LayerName>				
			</ButtonLayerToggle> -->					
		</MapButtonPanel>
	</Button>
</MapButtonPanel>
