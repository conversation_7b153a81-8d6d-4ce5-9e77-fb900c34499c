﻿var CefSharpEnabled = false;

(async function () {
	if (typeof CefSharp != "undefined") {
		CefSharpEnabled = true;
		await CefSharp.BindObjectAsync('ClientJsObject');
	}
})();


//check if query string contains theme=night then add nightmode css
var url = window.location.href;
if (url.indexOf('?theme=night') != -1) {
	ToggleNightMode(true);
}

async function GenerateSelectBox(listname, source, style, mandatory, parent, savelast, size, multiple, print, problemnature) {
	if (CefSharpEnabled == true) {//WPF implementation
		var ret = await ClientJsObject.generateSelectInput(listname, source, style, mandatory, parent, savelast, size, multiple, print, problemnature);
		if (ret) {
			ret = ret.split('  ').join('&nbsp;&nbsp;');
		}
		return ret;
	}
	else {//Xamarin implementation
		var param = {
			_method: 'generateSelectInput',
			_callback: 'GenerateSelectBox_callback',
			_listname: listname,
			_source: source,
			_style: style,
			_mandatory: mandatory,
			_parent: parent,
			_savelast: savelast,
			_size: size,
			_multiple: multiple,
			_print: print,
			_problemnature: problemnature
		}
		GenerateSelectBox_callback.df = $.Deferred();
		GenerateSelectBox_callback.parameters = param;
		invokeCSharp(JSON.stringify(param));
		return $.when(GenerateSelectBox_callback.df).done().promise();
	}
}

function GenerateSelectBox_callback(result) {

	if (result == '__fail__') {
		GenerateSelectBox_callback.df.fail(result);
	}
	else {

		result = result.split('  ').join('&nbsp;&nbsp;');

		var $selectBox = $(result);
		var param = GenerateSelectBox_callback.parameters;
		if (param != undefined) {
			//Need to account for multiple and all the other fields.
			$selectBox.attr("id", param._listname);
			$selectBox.attr("name", param._listname);
			$selectBox.attr("mandatory", param._mandatory);
			$selectBox.attr("savelast", param._savelast);
			$selectBox.attr("parent", param._parent);
			$selectBox.attr("size", param._size);
			$selectBox.attr("multiple", param._multiple);
			$selectBox.attr("print", param._print);
		} 

		if (param._multiple && $selectBox.find('option:first').val().length == 0) {
			$selectBox.find('option:first').remove();
		}

		GenerateSelectBox_callback.df.resolve($selectBox);
	}
}

async function GenerateListBox(listname, size, source, style, problemnature) {
	if (CefSharpEnabled == true) {//WPF implementation
		var ret = await ClientJsObject.generateListBox(listname, source, style, size, problemnature);
		if (ret) {
			ret = ret.split('  ').join('&nbsp;&nbsp;');
		}
		return ret;
	}
	else {//Xamarin implementation
		var param = {
			_method: 'generateListInput',
			_callback: 'GenerateListBox_callback',
			_listname: listname,
			_source: source,
			_style: style,
			_size: size,
			_problemnature: problemnature
		}
		GenerateListBox_callback.df = $.Deferred();
		GenerateListBox_callback.parameters = param;
		invokeCSharp(JSON.stringify(param));
		return $.when(GenerateListBox_callback.df).done().promise();
	}
}

function GenerateListBox_callback(result) {

	if (result == '__fail__') {
		GenerateListBox_callback.df.fail(result);
	}
	else {

		result = result.split('  ').join('&nbsp;&nbsp;');

		var $listBox = $(result);
		var param = GenerateListBox_callback.parameters;
		if (param != undefined) {
			//some other parameters may need to be added here, just not sure what yet. 
			$listBox.attr("id", param._listname);
			$listBox.attr("name", param._listname);
			$listBox.attr("savelast", param._savelast);
		}
		GenerateListBox_callback.df.resolve($listBox);
	}
}

//This isn't used, should it be removed?
async function GenerateComboBox(listname, source, style) {

	var ret = await ClientJsObject.generateComboBox(listname, source, style);

	return ret;
}

//This also isn't used; remove?
async function GenerateTable(source, element) {

	var element = element.attr('id');

	ClientJsObject.generateTable(source, element);
}

async function GetIncidentNumber() {
	if (CefSharpEnabled == true) { //WPF Implementation
		var ret = await ClientJsObject.getIncidentNumber();
		return ret;
	}
	else { //Xamarin implementation
		var param = {
			_method: 'getIncidentNumber',
			_callback: 'GetIncidentNumber_callback'
		}
		GetIncidentNumber_callback.df = $.Deferred();
		GetIncidentNumber_callback.parameters = param;
		invokeCSharp(JSON.stringify(param));
		return $.when(GetIncidentNumber_callback.df).done().promise();
	}
}

function GetIncidentNumber_callback(result) {

	if (result == '__fail__') {
		GetIncidentNumber_callback.df.fail(result);
	}
	else {
		GetIncidentNumber_callback.df.resolve(result);
	}
}

async function GetProblemNature() {
	if (CefSharpEnabled == true) { //WPF Implementation
		var ret = await ClientJsObject.getProblemNature();
		return ret;
	}
	else { //Xamarin implementation
		var param = {
			_method: 'getProblemNature',
			_callback: 'GetProblemNature_callback'
		}
		GetProblemNature_callback.df = $.Deferred();
		GetProblemNature_callback.parameters = param;
		invokeCSharp(JSON.stringify(param));
		return $.when(GetProblemNature_callback.df).done().promise();
	}
}

function GetProblemNature_callback(result) {

	if (result == '__fail__') {
		GetProblemNature_callback.df.fail(result);
	}
	else {
		GetProblemNature_callback.df.resolve(result);
	}
}

async function GetLoggedinUser() {
	if (CefSharpEnabled == true) { //WPF Implementation
		var ret = await ClientJsObject.getLoggedinUser();
		return ret;
	}
	else { //Xamarin implementation
		var param = {
			_method: 'getLoggedinUser',
			_callback: 'GetLoggedinUser_callback'
		}
		GetLoggedinUser_callback.df = $.Deferred();
		GetLoggedinUser_callback.parameters = param;
		invokeCSharp(JSON.stringify(param));
		return $.when(GetLoggedinUser_callback.df).done().promise();
	}
}

function GetLoggedinUser_callback(result) {

	if (result == '__fail__') {
		GetLoggedinUser_callback.df.fail(result);
	}
	else {
		GetLoggedinUser_callback.df.resolve(result);
	}
}

async function GetIncidentId() {
	if (CefSharpEnabled == true) { //WPF Implementation
		var ret = await ClientJsObject.getIncidentId();
		return ret;
	}
	else { //Xamarin implementation
		var param = {
			_method: 'getIncidentId',
			_callback: 'GetIncidentId_callback'
		}
		GetIncidentId_callback.df = $.Deferred();
		GetIncidentId_callback.parameters = param;
		invokeCSharp(JSON.stringify(param));
		return $.when(GetIncidentId_callback.df).done().promise();
	}
}

function GetIncidentId_callback(result) {

	if (result == '__fail__') {
		GetIncidentId_callback.df.fail(result);
	}
	else {
		GetIncidentId_callback.df.resolve(result);
	}
}

async function DisableResetSubmit() {
	ClientJsObject.disableResetSubmit();
}

async function CanShowRecordsCheck() {

	var ret = await ClientJsObject.canShowRecordsCheck();
	return ret;
}

function SubmitQuery(jsonDataString, query) {

	if (CefSharpEnabled == true) {
		var saveLastFields = {};
		$("[savelast]").each(function () {
			if ($(this).attr("savelast").toUpperCase() == "TRUE")
				saveLastFields[$(this).attr("name")] = $(this).val();
		});
		ClientJsObject.submitQuery(jsonDataString, query, JSON.stringify(saveLastFields));
	}
	else {
		var saveLastFields = {};
		$("[savelast]").each(function () {
			if ($(this).attr("savelast").toUpperCase() == "TRUE")
				saveLastFields[$(this).attr("name")] = $(this).val();
		});

		var param = {
			_method: 'submitQuery',
			_jsonData: jsonDataString,
			_query: query,
			_saveLastFields: JSON.stringify(saveLastFields)
		};

		invokeCSharp(JSON.stringify(param));
	}
}

function ToggleNightMode(isNightMode) {

	if (isNightMode) {
		$('head').append('<link rel="stylesheet" id="darkModeStyle" href="darkMode.css"/>');
	}
	else {
		$("#darkModeStyle").remove();

	}
}

var paramObjs;
function FillForm(parameters) {

	var autosubmit = false;
	if (parameters != undefined && parameters != "") {

		paramObjs = JSON.parse(parameters);
		if (Array.isArray(paramObjs)) {

			for (var i = 0; i < paramObjs.length; i++) {
				if (paramObjs[i].Key == "AutoSubmit" && paramObjs[i].Value == "true") {
					autosubmit = true;
				}
				var $element = $("#" + paramObjs[i].Key);

				if ($element.length) {
					switch ($element.prop('tagName').toUpperCase()) {
						case 'INPUT':
							switch ($element.attr('type').toUpperCase()) {
								case 'PASSWORD':
								case 'HIDDEN':
								case 'TEXT':
								case 'NUMBER':
								case 'TIME':
									$element.val(paramObjs[i].Value);
									if (paramObjs[i].Key == "location" || paramObjs[i].Key == "locationtype") {
										if (typeof Materialize != "undefined" && typeof (Materialize.updateTextFields) == typeof (Function)) {
											$('ul.tabs').tabs('select_tab', paramObjs[i].Value);
										}

										if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
											$('.tabs').tabs();
											var instance = M.Tabs.getInstance($('.tabs'));
											instance.select(paramObjs[i].Value);
										}
									}
									break;
								case 'CHECKBOX':
									var currentlyChecked = $element.is(':checked');
									var shouldCheck = false;
									value = paramObjs[i].Value.toLowerCase();

									if (value == "true" || value == "on" ||
										value == "1" || value == "y") {
										shouldCheck = true;
									}
									if (currentlyChecked != shouldCheck) {
										$element.prop('checked', shouldCheck);
									}
									break;
								case 'RADIO':
									var $radioEl = $("#" + paramObjs[i].Value.toLowerCase());
									if ($radioEl.length == 0) {
										var newline = '\n';
										alert("Warning! Field '" + param.Value + "' in the form is configured incorrectly." + newline +
											"Please contact your system administrator." + newline +
											"Press OK to continue.");
									}
									else {
										$radioEl.prop('checked', true);
									}
									break;

								default:
									break;

							}
							break;

						case 'SELECT':
							//this should work for single select
							var $option = $element.find("option[value='" + paramObjs[i].Value + "']");
							$option.prop("selected", true);
							$element.material_select();
							//TODO handle multiselect
							break;

						case 'TEXTAREA':
							$element.val(paramObjs[i].Value);
							break;

						case 'SPAN':
							var formvalue = $element.attr("formvalue");
							if (formvalue != undefined && formvalue.toLowerCase() == 'true') {
								$element.text(paramObjs[i].Value);
							}
							break;

						default:
							break;

					}
				}
			}
		}
	}
	//for v1.0.0
	if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
		M.updateTextFields();
	}
	//for beta version
	if (typeof Materialize != "undefined" && typeof (Materialize.updateTextFields) == typeof (Function)) {
		Materialize.updateTextFields();
	}

	if (typeof (AfterFillForm) == typeof (Function)) {
		AfterFillForm();
	}

	UpdateAutoCompleteFields();

	if (autosubmit) {
		//run this outside the thread of execution
		setTimeout(SubmitForm);
	}
}

function UpdateAutoCompleteFields() {

	$('input.autocomplete').each(function (index) {
		var instance = M.Autocomplete.getInstance(this);
		instance.options.onAutocomplete = function () {
			this.$el[0].focus();
		};
	});
}

function AfterAllControlsInitialized() {

	// must wire up after all select controls have been initialized
	$("#addresstab input").on("input", HandleAddressControlFieldChanged);
	$("#addresstab select").on("change", HandleAddressControlFieldChanged);
}

function HandleAddressControlFieldChanged() {

	// clear geo valid address if address fields change
	if ($("#geovalid_address_selected").css("display") != "none") {

		GeoValidateAddressReturn();
		setTab('addresstab');

		if (typeof (LocationCleared) == typeof (Function)) {
			LocationCleared();
		}
	}
}

function SetSelectBoxFromParameters($select) {
	if (paramObjs != undefined && $select.length > 0) {
		for (var i = 0; i < paramObjs.length; i++) {
			if (paramObjs[i].Key.toUpperCase() == $select.attr("name").toUpperCase()) {
				var $option = $select.find("option[value='" + paramObjs[i].Value + "']");
				$option.prop("selected", true);
			}
		}
	}
	if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
		$select.formSelect();
	}
	else {
		$select.material_select();
	}
}

function SetMultiSelectBoxFromParameters($select) {
	if (paramObjs != undefined && $select.length > 0) {
		for (var i = 0; i < paramObjs.length; i++) {
			if (paramObjs[i].Key.toUpperCase() == $select.attr("name").toUpperCase()) {
				var array = paramObjs[i].Value.split(",");
				for (var p = 1; p < array.length; p++) {
					var $option = $select.find("option[value='" + array[p] + "']");
					$option.prop("selected", true);					
				}
			}
		}
	}
	if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
		$select.formSelect();
	}
	else {
		$select.material_select();
	}
}

//called from context menu in inform mobile
var setFocusTimeout;
function SubmitForm(parameters) {
	var shouldSubmit = true;
	var $form = $('#Form');

	var isSubmitClick;
	if (parameters != undefined && parameters != "") {
		var submitFormParameters = JSON.parse(parameters);
		if (Array.isArray(submitFormParameters)) {
			for (var i = 0; i < submitFormParameters.length; i++) {
				if (submitFormParameters[i].Key == "isSubmitClick") {
					isSubmitClick = submitFormParameters[i].Value;
				}
			}
		}
	}

	//if focused element is textarea and it is not Comments field then shouldsubmit is false
	if (document.activeElement.tagName.toLowerCase() === 'textarea' && document.activeElement.name.toLowerCase() != "comment"
		&& document.activeElement.name.toLowerCase() != "comments") {
		shouldSubmit = false;
	}

	$('.dropdown-trigger').each(function (e) {
		var instance = M.Dropdown.getInstance($(this));
		if (instance != undefined && instance.isOpen && (isSubmitClick === undefined || isSubmitClick === "False")) {
			shouldSubmit = false;
		}
	});
	$('input.autocomplete').each(function (e) {
		var instance = M.Autocomplete.getInstance($(this));
		if (instance != undefined && instance.dropdown.isOpen) {
			shouldSubmit = false;
		}
	});

	if (shouldSubmit) {
		$form.submit();
	}
}

function OpenDropDown() {
	$('.dropdown-trigger').each(function (e) {
		var instance = M.Dropdown.getInstance($(this));
		var x = instance.el;
		if (document.activeElement === x) {
			instance.open();
		}
	});
}

function ResetDayNight(isNightMode) {
	if (isNightMode) {
		$('head').append('<link rel="stylesheet" id="darkModeStyle" href="darkMode.css"/>');
	}
	else {
		$("#darkModeStyle").remove();
	}
}

function GetFormData() {
	$(':disabled').each(function (e) {
		$(this).removeAttr('disabled');
	})
	var formValues = $('#Form').serialize();
	return formValues;
}

//**FORM VALIDATION**//
$(document).ready(function () {

	$(".input-validate-year").focusout(ValidateYear);
	$(".input-validate-month").focusout(ValidateMonth);
	$(':input[required]').addClass("input-validate-required");

	//Patients seen must be greater than or equal to Patients transported
	$('#PatientsTransported').on("input", function () {
		var PT = parseInt($(this).val());
		var PS = parseInt($('#PatientsSeen').val());
		if (PT > PS) {
			$(this)[0].setCustomValidity("invalid");
		}
		else {
			$(this)[0].setCustomValidity("");
		}
	});

	//Patients seen must be greater than or equal to Patients transported
	$('#PatientsSeen').on("input", function () {
		var PT = parseInt($('#PatientsTransported').val());
		var PS = parseInt($(this).val());
		if (PT > PS) {
			$('#PatientsTransported')[0].setCustomValidity("invalid");
		}
		else {
			$('#PatientsTransported')[0].setCustomValidity("");
		}
	});

	//validates the Date of birth field
	$('#dob').on("input", function () {
		$(this).removeClass("input-validate-date");
		$(this).removeClass("input-validate-future-date");

		var values = $(this).val().split("-");
		if (GetCurrentDate() < $(this).val()) {
			$(this).addClass("input-validate-future-date");
			$(this)[0].setCustomValidity("invalid");
		}
		else if (!isValidYear(values[0])) {
			$(this).addClass("input-validate-date");
			$(this)[0].setCustomValidity("invalid");
		}
		else {
			$(this)[0].setCustomValidity("");
		}
	});

	$("#Address").on("input", function () {
		address = $("#Address")
		address[0].setCustomValidity("");
		if ($("#Address").val().length < 1) {
			address[0].setCustomValidity("Required Field");
			address.addClass("input-validate-required");
		}
	});

	$(".toggle-disabled").change(function () {
		var $checkbox = $(this);
		var $controls = $($checkbox.attr("target"));
		if ($checkbox.is(':checked')) {
			$checkbox.val("true");
			enableControls($controls);	
			UpdateAutoCompleteFields();
		}
		else {
			$checkbox.val("false");
			disableControls($controls);
		}
	});

	$(".toggle-disabled-reverse").change(function () {
		var $checkbox = $(this);
		var $controls = $($checkbox.attr("target"));
		if ($checkbox.is(':checked')) {
			$checkbox.val("true");
			disableControls($controls);
		}
		else {
			$checkbox.val("false");
			enableControls($controls);
		}
	});
});

function enableControls($controls) {

	$controls.each(function () {
		var $control = $(this);
		if ($control.hasClass("permanently-disabled")) {
			return;
		}
		if ($control.hasClass("autocomplete")) {
			$control.prop('disabled', false);
			$control.autocomplete();
		}
		else if ($control[0].tagName == "A") {
			$control.removeClass("disabled");
		}
		else if ($control[0].tagName == "SELECT") {
			$control.prop('disabled', false);
			$control.formSelect();
		}
		else if ($control[0].tagName == "INPUT") {
			$control.prop('disabled', false);
		}
	});
}
function disableControls($controls) {
	$controls.each(function () {
		var $control = $(this);
		if ($control.hasClass("autocomplete")) {
			$control.prop('disabled', true);
			$control.autocomplete();
		}
		else if ($control[0].tagName == "A") {
			$control.addClass("disabled");
		}
		else if ($control[0].tagName == "SELECT") {
			$control.prop('disabled', true);
			$control.formSelect();
		}
		else if ($control[0].tagName == "INPUT") {
			$control.prop('checked', false);
			$control.prop('disabled', true);		
		}
	});
}

var date = new Date();
var CurrentYear = parseInt(date.getFullYear());
var NextAllowedYear = CurrentYear + 1;
var StartingYear = CurrentYear - 150;

function ValidateYear() {
	var vehicleyear = $(this).val();
	if (vehicleyear != "" && !isValidYear(vehicleyear)) {
		$(this)[0].setCustomValidity("invalid");
	}
	else {
		$(this)[0].setCustomValidity("");
	}
}

function isValidYear(year) {
	if (year.length == 4) {
		year = parseInt(year);
		if (year < StartingYear || year > NextAllowedYear) {
			return false;
		}
		else {
			return true;
		}
	}
	else {
		return false;
	}
}

function ValidateMonth() {
	if ($(this).val() != "") {
		var month = parseInt($(this).val());
		if (month != null && month > 0 && month < 13) {
			$(this)[0].setCustomValidity("");
		}
		else {
			$(this)[0].setCustomValidity("invalid");
		}
	}
}

function ValidateRequiredSelect() {
	var value = $(this).val();
	var $label = $("label[for='" + $(this).attr('id') + "']");
	if (Array.isArray(value) && value.length > 0) {
		$label.removeClass("select-invalid");
	}
	else {
		$label.addClass("select-invalid");
	}
}

function ValidateRequiredSingleSelect(Select) {
	var $label = $("label[for='" + Select.attr('id') + "']");
	var $input = $("input.select-dropdown");
	if (Select.val().length > 0) {
		$label.removeClass("select-invalid");
		if ($input != null)
			$input.removeClass("select-invalid-underline");
	}
	else {
		$label.addClass("select-invalid");
		if ($input != null)
			$input.addClass("select-invalid-underline");
	}
}

//This needs to change, very similar to the ValidateRequiredSingleSelect
function ValidateRequiredSingleSelectOnly(Select) {
	var $label = $("label[for='" + Select.attr('id') + "']");
	if (Select.val().length > 0) {
		$label.removeClass("select-invalid");
		if (Select != null)
			Select.removeClass("select-invalid-underline");
	}
	else {
		$label.addClass("select-invalid");
		if (Select != null)
			Select.addClass("select-invalid-underline");
	}
}

function GetCurrentDate() {
	var dateToFormat = new Date();
	return dateToFormat.getFullYear() + '-' + _pad(dateToFormat.getMonth() + 1) + '-' + _pad(dateToFormat.getDate()) + ' '
		+ _pad(dateToFormat.getHours()) + ':' + _pad(dateToFormat.getMinutes()) + ':' + _pad(dateToFormat.getSeconds());
}

function _pad(n) {
	return (n < 10 ? "0" : "") + n;
}


var getTableDataTimeOut;
function GetTableData() {
	clearTimeout(getTableDataTimeOut);
	var $input = $(this);
	var searchText = $input.val();
	var source = $input.attr("source");
	var tbody = $input.attr("tbody");
	var searchField = $input.attr("searchField");
	var $table = $("#" + tbody);

	if (typeof (LocationCleared) == typeof (Function)) {
		// notify form that location has been cleared
		LocationCleared();
	}

	if (searchText != "" && searchText.length > 1) {
		getTableDataTimeOut = setTimeout(function () {
			if (CefSharpEnabled == true) { //WPF Implementation
				ClientJsObject.getDataByFilter(source, searchText, "", "", false, false).then(function (ret) {
					var items = JSON.parse(ret);
					$table.empty();
					$("#location").val("");
					if (items != null && items.length > 0) {
						for (var i = 0; i < items.length; i++) {
							var value = items[i];
							$row = $("<tr><td>" + value + "</td></tr>");
							$row.data("value", value);
							$row.data("index", i);
							$row.click(LocationItemClicked);
							$table.append($row);
						}
					}
				});
			}
			else {
				var param = {
					_method: 'getDataByFilter',
					_callback: 'GetDataByFilter_callback',
					_source: source,
					_filter: searchText,
					_searchField: searchField
				}
				GetDataByFilter_callback.df = $.Deferred();
				GetDataByFilter_callback.parameters = param;
				invokeCSharp(JSON.stringify(param));
				$.when(GetDataByFilter_callback.df).done(function (result) {
					if (result != "") {
						var items = JSON.parse(result);
						$table.empty();
						$("#location").val("");
						if (items != null && items.length > 0) {
							for (var i = 0; i < items.length; i++) {
								var value = items[i];
								$row = $("<tr><td>" + value + "</td></tr>");
								$row.data("value", value);
								$row.data("index", i);
								$row.click(LocationItemClicked);
								$table.append($row);
							}
						}
					}
				});
			}
		}, 300);
	}
	else {
		//clear tbody
		$table.empty();
	}

}

function setDefaultLocation() {
	if ($("#location").val() != "") {
		var value = $("#location").val();
		$row = $("<tr class='selected'><td>" + value + "</td></tr>");
		$row.data("value", value);
		$row.data("index", 0);
		$row.click(LocationItemClicked);
		$("#locationbody").append($row);
	}
}

function LocationItemClicked() {
	var value = $(this).data("value");
	if (value != undefined) {
		$("#locationtab").find("tr").removeClass("selected");
		$(this).addClass("selected");
		$("#location").val(value);
		$(".table-validate-required").hide();

		if (typeof (LocationSelected) == typeof (Function)) {
			// notify form that location has been selected
			LocationSelected(value);
		}
	}
}


var getDataListTimeOut;
//used for autocomplete control
function GetDataList($input) {

	clearTimeout(getDataListTimeOut);

	var filter = $input.val();
	var source = $input.attr("source");
	var searchField = $input.attr("searchField");

	if (filter == "" || filter.length < 2) {
		if ($input.data("autocomplete") != undefined) {
			$input.data("autocomplete").updateData();
		}
		return;
	}

	getDataListTimeOut = setTimeout(function () {

		if (CefSharpEnabled == true) { //wpf implementation
			ClientJsObject.getDataByFilter(source, filter, searchField, "", false, false).then(function (result) {
				var items;
				if (result != "") {
					items = JSON.parse(result);
				}
				if ($input.data("autocomplete") != undefined) {
					$input.data("autocomplete").updateData(items);
				}
				else if (items != undefined && items != null && items.length > 0) {
					var data = {};
					for (var i = 0; i < items.length; i++) {
						data[items[i]] = null;
					}
					$input.autocomplete('updateData', data);
					$input.autocomplete('open');
				}
			});
		}
		else { //xamarin 
			var param = {
				_method: 'getDataByFilter',
				_callback: 'GetDataByFilter_callback',
				_source: source,
				_filter: filter,
				_searchField: searchField
				//_idField:
				//_descriptionField:
			}
			GetDataByFilter_callback.df = $.Deferred();
			GetDataByFilter_callback.parameters = param;
			invokeCSharp(JSON.stringify(param));
			$.when(GetDataByFilter_callback.df).done(function (result) {
				var items;
				if (result != "") {
					items = JSON.parse(result);
				}
				if ($input.data("autocomplete") != undefined) {
					$input.data("autocomplete").updateData(items);
				}
				else if (items != undefined && items != null && items.length > 0) {
					var data = {};
					for (var i = 0; i < items.length; i++) {
						data[items[i]] = null;
					}
					$input.autocomplete('updateData', data);
					$input.autocomplete('open');
				}
			});
		}
	}, 300);

}

function GetDataByFilter(source, filter, searchField, searchAttribute, exactMatch, returnasjsonXML) {
	var deferred = $.Deferred();
	if (CefSharpEnabled == true) { //wpf implementation
		ClientJsObject.getDataByFilter(source, filter, searchField, searchAttribute, exactMatch, returnasjsonXML).then(function (result) {
			deferred.resolve(result);
		});
	}
	else { //xamarin 
		var param = {
			_method: 'getDataByFilter',
			_callback: 'GetDataByFilter_callback',
			_source: source,
			_filter: filter,
			_searchField: searchField,
			_searchAttribute: searchAttribute,
			_exactMath: exactMatch,
			_retunasjsonXml: returnasjsonXML
			//_idField:
			//_descriptionField:
		}
		GetDataByFilter_callback.df = $.Deferred();
		GetDataByFilter_callback.parameters = param;
		invokeCSharp(JSON.stringify(param));
		$.when(GetDataByFilter_callback.df).done(function (result) {
			deferred.resolve(result);
		});
	}
	return deferred;
}

function GetDataByFilter_callback(result) {

	if (result == '__fail__') {
		GetDataByFilter_callback.df.fail(result);
	}
	else {
		GetDataByFilter_callback.df.resolve(result);
	}
}



function InsertIncidentTagsSelect() {
	GetIncidentTags().then(function (result) {
		var $incidentTagsSelect = $("#IncidentTags");
		var promptTags = [];
		if (result != "") {
			var items = JSON.parse(result);
			if (items.length > 0) {
				for (var i = 0; i < items.length; i++) {
					var item = items[i];
					$incidentTagsSelect.append('<option value=' + item.TagID + '>' + item.Caption + '</option>');
					if (item.PromptUserEnabled)
						promptTags.push(item);
				}
				$incidentTagsSelect.formSelect();
				$incidentTagsSelect.data("promptTags", promptTags);
				$("#incidentTagsVals").show();
			}
		}
	});
}

async function GetIncidentTags() {

	var param = {
		_method: 'getIncidentTags',
		_callback: 'GetIncidentTags_callback'
	}
	GetIncidentTags_callback.df = $.Deferred();
	GetIncidentTags_callback.parameters = param;
	invokeCSharp(JSON.stringify(param));
	return $.when(GetIncidentTags_callback.df).done().promise();
}

function GetIncidentTags_callback(result) {

	if (result == '__fail__') {
		GetIncidentTags_callback.df.fail(result);
	}
	else {
		GetIncidentTags_callback.df.resolve(result);
	}
}

async function PromptIncidentTags() {
	var selectedTags = $("#IncidentTags").val();
	var promptTags = $("#IncidentTags").data("promptTags");
	if (promptTags != undefined && promptTags.length > 0) {
		for (var i = 0; i < promptTags.length; i++) {
			var promptTag = promptTags[i];
			if (selectedTags.includes(promptTag.TagID.toString()) == false) {
				var ret = await openModal('TAG PROMPT - ' + promptTag.Caption, promptTag.PromptText);
				if (ret == true) {
					$("#IncidentTags").find('option[value=' + promptTag.TagID + ']').prop('selected', true);
				}
			}
		}
	}
}

function openModal(title, content) {

	var deferred = $.Deferred();
	$('.modal-header').empty();
	$('.modal-header').append('<h5>' + title + '</h5>');
	if (content) {
		$('.modal-content').empty();
		$('.modal-content').append('<p>' + content + '</p>');
	}
	$("#addbtn").one("click", function () {
		deferred.resolve(true);
	});
	$("#closebtn").one("click", function () {
		deferred.resolve(false);
	});
	$('.modal').modal('open');
	return deferred;
}

// override of jquery serilize to serlize unchecked checkboxes
(function ($) {

	$.fn.serialize = function (options) {
		return $.param(this.serializeArray(options));
	};

	$.fn.serializeArray = function (options) {
		var o = $.extend({
			checkboxesAsBools: true
		}, options || {});

		var rselectTextarea = /input|select|textarea/i;
		var theseTypes = /submit|button|image|reset|file/i;

		return this.map(function () {
			return this.elements ? $.makeArray(this.elements) : this;
		})
			.filter(function () {
				return this.name && !this.disabled &&
					(this.checked
						|| (o.checkboxesAsBools && this.type === 'checkbox')
						|| (rselectTextarea.test(this.nodeName) && !theseTypes.test(this.type)));
			})
			.map(function (i, elem) {
				var val = $(this).val();
				return val == null ?
					null :
					$.isArray(val) ?
						$.map(val, function (val, i) {
							return { name: elem.name, value: val };
						}) :
						{
							name: elem.name,
							value: (o.checkboxesAsBools && this.type === 'checkbox') ? //moar ternaries!
								(this.checked ? 'true' : 'false') :
								val
						};
			}).get();
	};

})(jQuery);