<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Modify Logon</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Modify Logon</H4>
						<form action="ModifyLogonQuery.aspx?queryfile=ModifyLogon.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
							  <tr >
									<td valign="top"><input type=checkbox ID=modifyCapabilities NAME=modifyCapabilities onclick="UpdateCapabilitiesView();">&nbsp;Capabilities:</td>
								</tr>
								<tr>
									<td>
										<XML id="capstyle" src="genericselect.xsl"></XML>										
										<SPAN type="selectlist" id="capvals" name="capvals">
											<XML id="capsource" src="capabilities.xml"></XML>
										</SPAN>

									</td>
								</tr>

								<tr><td>&nbsp;</td></tr>
								<tr>
									<td valign="top" colspan=3><input type=checkbox ID=modifyCrewmember NAME=modifyCrewmember onclick="UpdateCrewmembersView();">&nbsp;Additional Crew Members:</td>
								</tr>
								<tr style="display:none">
									<td>
										<SELECT ID="crewmember" NAME="crewmember" MULTIPLE size=4>
										</SELECT>
									</td>
								</tr>
								<tr>
									<table>
									<tr>
									<td valign="top">Unselected
										<br>
										<XML id="crewstyle" src="configurationusers.xsl"></XML>										
										<SPAN type="selectlist" id="crewvals" name="crewvals">
											<XML id="crewsource" src="../Configuration_Users.xml"></XML>
										</SPAN>
									</td>
									<td>
										<input type="button" value="  ->  " onclick="MoveItems( Form.Unselected, Form.Selected, Form.LoggedinUser.value)">
										<br/>
										<input type="button" value="  <-  " onclick="MoveItems( Form.Selected, Form.Unselected, Form.LoggedinUser.value)">
									</td>
									<td valign="top">Selected
										<br>
										<input type="text" readonly name="LoggedinUser" id="LoggedinUser">
										<br>
										<SELECT ID="Selected" NAME="Selected" SIZE="3" style="width:320">
										</SELECT>
									</td>
									</tr>
									</table>
								</tr>
<!-- Temp Radios Block (Uncomment this section if want to have temp unit radios be modified for the unit shift.
<tr>
									<td valign="top"><input type=checkbox ID=modifyradios NAME=modifyradios onclick="UpdateTempRadiosView();">&nbsp;Radios:</td>
								</tr>
								<tr valign="top">
									<td colspan="2"><div>Temporary Radios:</div>
									    <div>
										    <table cellpadding="2" cellspacing="0" width="100%" class="radiotable">
											    <tr>
												    <td>&nbsp;</td>
												    <td>Radio ID</td>
												    <td>Code</td>
												    <td>Description</td>
											    </tr>
											    <tr>
											        <td class="RadioTableRowHeader">Radio 1:</td>
											        <td class="RadioTableRadioIDEntry"><input type="text" value='' id="radioID1" name="radioID1" /></td>
											        <td class="RadioTableRadioCodeEntry"><input maxlength="10" type="text" value='' id="radioCode1" name="radioCode1" /></td>
											        <td class="RadioTableRadioDescEntry"><input maxlength="30" type="text" value='' id="radioDescription1" name="radioDescription1" /></td>
											    </tr>
											    <tr>
												    <td>&nbsp;</td>
												    <td>Radio ID</td>
												    <td>Code</td>
												    <td>Description</td>
											    </tr>
											    <tr>
											        <td class="RadioTableRowHeader">Radio 2:</td>
											        <td class="RadioTableRadioIDEntry"><input type="text" value='' id="radioID2" name="radioID2" /></td>
											        <td class="RadioTableRadioCodeEntry"><input maxlength="10" type="text" value='' id="radioCode2" name="radioCode2" /></td>
											        <td class="RadioTableRadioDescEntry"><input maxlength="30" type="text" value='' id="radioDescription2" name="radioDescription2" /></td>
											    </tr>
											    <tr>
												    <td>&nbsp;</td>
												    <td>Radio ID</td>
												    <td>Code</td>
												    <td>Description</td>
											    </tr>
											    <tr>
											        <td class="RadioTableRowHeader">Radio 3:</td>
											        <td class="RadioTableRadioIDEntry"><input type="text" value='' id="radioID3" name="radioID3" /></td>
											        <td class="RadioTableRadioCodeEntry"><input maxlength="10" type="text" value='' id="radioCode3" name="radioCode3" /></td>
											        <td class="RadioTableRadioDescEntry"><input maxlength="30" type="text" value='' id="radioDescription3" name="radioDescription3" /></td>
											    </tr>
											    <tr>
												    <td>&nbsp;</td>
												    <td>Radio ID</td>
												    <td>Code</td>
												    <td>Description</td>
											    </tr>
											    <tr>
											        <td class="RadioTableRowHeader">Radio 4:</td>
											        <td class="RadioTableRadioIDEntry"><input type="text" value='' id="radioID4" name="radioID4" /></td>
											        <td class="RadioTableRadioCodeEntry"><input maxlength="10" type="text" value='' id="radioCode4" name="radioCode4" /></td>
											        <td class="RadioTableRadioDescEntry"><input maxlength="30" type="text" value='' id="radioDescription4" name="radioDescription4" /></td>
											    </tr>
											    <tr>
												    <td>&nbsp;</td>
												    <td>Radio ID</td>
												    <td>Code</td>
												    <td>Description</td>
											    </tr>
											    <tr>
											        <td class="RadioTableRowHeader">Radio 5:</td>
											        <td class="RadioTableRadioIDEntry"><input type="text" value='' id="radioID5" name="radioID5" /></td>
											        <td class="RadioTableRadioCodeEntry"><input maxlength="10" type="text" value='' id="radioCode5" name="radioCode5" /></td>
											        <td class="RadioTableRadioDescEntry"><input maxlength="30" type="text" value='' id="radioDescription5" name="radioDescription5" /></td>
											    </tr>
											    <tr>
												    <td>&nbsp;</td>
												    <td>Radio ID</td>
												    <td>Code</td>
												    <td>Description</td>
											    </tr>
											    <tr>
											        <td class="RadioTableRowHeader">Radio 6:</td>
											        <td class="RadioTableRadioIDEntry"><input type="text" value='' id="radioID6" name="radioID6" /></td>
											        <td class="RadioTableRadioCodeEntry"><input maxlength="10" type="text" value='' id="radioCode6" name="radioCode6" /></td>
											        <td class="RadioTableRadioDescEntry"><input maxlength="30" type="text" value='' id="radioDescription6" name="radioDescription6" /></td>
											    </tr>
											    <tr>
												    <td>&nbsp;</td>
												    <td>Radio ID</td>
												    <td>Code</td>
												    <td>Description</td>
											    </tr>
											    <tr>
											        <td class="RadioTableRowHeader">Radio 7:</td>
											        <td class="RadioTableRadioIDEntry"><input type="text" value='' id="radioID7" name="radioID7" /></td>
											        <td class="RadioTableRadioCodeEntry"><input maxlength="10" type="text" value='' id="radioCode7" name="radioCode7" /></td>
											        <td class="RadioTableRadioDescEntry"><input maxlength="30" type="text" value='' id="radioDescription7" name="radioDescription7" /></td>
											    </tr>
											    <tr>
												    <td>&nbsp;</td>
												    <td>Radio ID</td>
												    <td>Code</td>
												    <td>Description</td>
											    </tr>
											    <tr>
											        <td class="RadioTableRowHeader">Radio 8:</td>
											        <td class="RadioTableRadioIDEntry"><input type="text" value='' id="radioID8" name="radioID8" /></td>
											        <td class="RadioTableRadioCodeEntry"><input maxlength="10" type="text" value='' id="radioCode8" name="radioCode8" /></td>
											        <td class="RadioTableRadioDescEntry"><input maxlength="30" type="text" value='' id="radioDescription8" name="radioDescription8" /></td>
											    </tr>
										    </table>
										</div>
									</td>
								</tr>
End of Temp Radios Block -->
								<tr><td>&nbsp;</td></tr>
							</table>
							<br>
		<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
		<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
        <script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
		
		// Move items between the selected and unselected lists
		function MoveItems( From, To, dontMove)
		{
			// move items from listbox to listbox
			var i = From.selectedIndex;
			if (i == -1) return;
			var sItem = From.options[i].text;
			From.remove(i);
			var oOption = document.createElement("OPTION");
			oOption.text = sItem;
			// add the item into the right place (to keep the list sorted)
			var oOptions = To.options;
			var iLength = oOptions.length;
			for (i = 0; i < iLength; i++)
			{
				if (oOptions[i].text > sItem) break;
			}
			// make sure it's not the loggedin user
			if (oOption.text != dontMove)
			{
				To.add( oOption, i);
			}
		}
		// select all items in selected crewmembers list
		function SelectOptions()
		{
			var oOptions = Form.crewmember.options;
			// clear the hidden listbox
			var i;
			for ( i=oOptions.length-1; i>=0; i--)
			{
				oOptions.remove(i);
			}
			// copy options from Selected to hidden, and select them all
			var oSelectedOptions = Form.Selected.options;
			var iSelectedLength = oSelectedOptions.length;
			for ( i=0; i<iSelectedLength; i++)
			{
				var oOption = document.createElement("OPTION");
				oOption.text = oSelectedOptions[i].text;
				oOption.selected = true;
				oOptions.add(oOption);
			}
		}

		function window.validatepage()
		{
			if (Form.Selected.options.length > 7)
			{
				Form.Selected.focus();
				alert('Too many crew members selected.');
			}
//	Radios Block (Uncomment this call to ValidateRadios() if radio controls are to be redisplayed.)
//	        else if(ValidateRadios())
	        else 
			{
				// temporarily, add the loggedin user (if the MObile is configured to log in via Users
				var oOption = document.createElement("OPTION");
				oOption.text = Form.LoggedinUser.value;
				Form.Selected.add( oOption, 0);
				
				// Use this block of code if the Mobile is configured to log in via Units
				//var oOption = document.createElement("OPTION");
				//oOption.text = Form.LoggedinUser.value;
				//Form.Selected.add( oOption, 0);
					
				// select everything
				SelectOptions();
				Form.Submit.click();
			}
		}	
		function window.onload()
		{
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.

			PrepareValidation(Form);
			capvals.innerHTML = GenerateSelectBox("Capabilities", capsource, capstyle, false, false, true, 3, true, false);
			Form.Capabilities.focus();
			crewvals.innerHTML = GenerateSelectBox("Unselected", crewsource, crewstyle, false, false, false, 6, true, false);
			Form.Unselected.multiple = false;
			Form.Unselected.style.width = 300;
			Form.LoggedinUser.style.width = Form.Selected.style.width;

			UpdateCapabilitiesView();
			UpdateCrewmembersView();
			//Uncoment the following block of code if you want the customers having the ability to modify temp radios.
			//UpdateTempRadiosView();	
		}
		function UpdateCapabilitiesView()
		{
			if (Form.modifyCapabilities.checked)
			{
				Form.Capabilities.disabled = false;
			}
			else
			{
				Form.Capabilities.disabled = true;
			}
		}
		function UpdateCrewmembersView()
		{
			if (Form.modifyCrewmember.checked)
			{
				Form.Unselected.disabled = false;
			}
			else
			{
				Form.Unselected.disabled = true;
			}
		}
<!-- Temp Radios Block (Uncomment this section if want to have temp unit radios be modified for the unit shift.
		function UpdateTempRadiosView()
		{
			if (Form.modifyradios.checked)
			{
				Form.radioID1.disabled = false;
				Form.radioID2.disabled = false;
				Form.radioID3.disabled = false;
				Form.radioID4.disabled = false;
				Form.radioID5.disabled = false;
				Form.radioID6.disabled = false;
				Form.radioID7.disabled = false;
				Form.radioID8.disabled = false;
				Form.radioCode1.disabled = false;
				Form.radioCode2.disabled = false;
				Form.radioCode3.disabled = false;
				Form.radioCode4.disabled = false;
				Form.radioCode5.disabled = false;
				Form.radioCode6.disabled = false;
				Form.radioCode7.disabled = false;
				Form.radioCode8.disabled = false;
				Form.radioDescription1.disabled = false;
				Form.radioDescription2.disabled = false;
				Form.radioDescription3.disabled = false;
				Form.radioDescription4.disabled = false;
				Form.radioDescription5.disabled = false;
				Form.radioDescription6.disabled = false;
				Form.radioDescription7.disabled = false;
				Form.radioDescription8.disabled = false;
			}
			else
			{
				Form.radioID1.disabled = true;
				Form.radioID2.disabled = true;
				Form.radioID3.disabled = true;
				Form.radioID4.disabled = true;
				Form.radioID5.disabled = true;
				Form.radioID6.disabled = true;
				Form.radioID7.disabled = true;
				Form.radioID8.disabled = true;
				Form.radioCode1.disabled = true;
				Form.radioCode2.disabled = true;
				Form.radioCode3.disabled = true;
				Form.radioCode4.disabled = true;
				Form.radioCode5.disabled = true;
				Form.radioCode6.disabled = true;
				Form.radioCode7.disabled = true;
				Form.radioCode8.disabled = true;
				Form.radioDescription1.disabled = true;
				Form.radioDescription2.disabled = true;
				Form.radioDescription3.disabled = true;
				Form.radioDescription4.disabled = true;
				Form.radioDescription5.disabled = true;
				Form.radioDescription6.disabled = true;
				Form.radioDescription7.disabled = true;
				Form.radioDescription8.disabled = true;
			}
		}
End of Temp Radios Block -->	
    </SCRIPT>
</HTML>
