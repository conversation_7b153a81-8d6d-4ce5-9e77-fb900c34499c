<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Capability Search</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Capability Search</H4>
						<form action="CapabilitySearch.aspx?queryfile=CapabilitySearch.qry" method="post" id="CapabilitySearch" name="CapSearch">
							<table ID="Table2">
								<tr>
									<td valign="top"><b>Capability:</b></td>
									<td>
										<XML id="capstyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="capvals" name="capvals">
											<XML id="capsource" src="capabilities_ALL.xml"></XML>
										</SPAN>
									</td>
								</tr>
							</table>
							<br>
							<input type="submit" name="Query" id="Query" value="Query">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
		function window.onload()
		{
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			capvals.innerHTML = GenerateSelectBox("capability", capsource, capstyle, true, false, false, 1, false, false);
		}
	</SCRIPT>
</HTML>
