<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - OTHER STATUS</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">OTHER STATUS</H4>
						<form action="OutOfServiceQuery.aspx?queryfile=outofservice.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr style="display:none">
									<td valign="top">Reason:&nbsp;&nbsp;</td>
									<td width="300">
										<!-- Root item requires a SPAN element to contain it. -->
										<XML id="reasonstyle" src="genericselect.xsl"></XML>										
										<SPAN type="selectlist" id="reasonvals" name="reasonvals">
											<XML id="reasonsource" src="Reason.xml"></XML>
										</SPAN>
									</td>
								</tr>
								<tr>
									<td valign="top">Unselected:
										<br>
										<SELECT ID="Unselected" NAME="Unselected" SIZE="9" style="width:385px" onKeyPress="SearchListBox(this, window.event)">
										</SELECT>
									</td>

									<td align=center>
										<input type="button" value=" -> " onclick="MoveReason( Form.Unselected, Form.Selected)" ID="Button1" NAME="Button1">
										<br>
										<input type="button" value=" <- " onclick="MoveReason( Form.Selected, Form.Unselected)" ID="Button2" NAME="Button2">
									</td>

									<td valign="top">Selected:
										<br>
										<SELECT ID="Selected" NAME="Selected" SIZE="9" style="width:385px">
										</SELECT>
									</td>
								</tr>

							</table>
							<br>
							<input type="submit" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<br>
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
        <script src="clientutilities.js"></script>

	<script language="javascript">

	function window.onload()
	{
		// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
		reasonvals.innerHTML = GenerateSelectBox("Reason", reasonsource, reasonstyle, false, false, true, 9, true, false);
	}

	function OnAfterFormFill()
	{
		var oReasonOptions = Form.Reason.options;
		var oUnselectedOptions = Form.Unselected.options;
		var oSelectedOptions = Form.Selected.options;
		var iLength = oReasonOptions .length;
		// clear the Reason hidden listbox
		var i;
		for ( i=0; i<iLength; i++)
		{
			var oOption = document.createElement("OPTION");
			oOption.text = oReasonOptions[i].text;
			if (oReasonOptions[i].selected)
			{
				oSelectedOptions.add(oOption);
			}
			else
			{
				oUnselectedOptions.add(oOption);
			}
		}
		
		CheckMaximumSelected();
	}

	// Move items between the selected and unselected lists
	function MoveReason( From, To)
	{
		// move items from listbox to listbox
		var i = From.selectedIndex;
		if (i == -1) return;
		var sItem = From.options[i].text;
		From.remove(i);
		var oOption = document.createElement("OPTION");
		oOption.text = sItem;
		// add the item into the right place (to keep the list sorted)
		var oOptions = To.options;
		var iLength = oOptions.length;
		for (i = 0; i < iLength; i++)
		{
			if (oOptions[i].text > sItem) break;
		}
		To.add( oOption, i);
		
		CheckMaximumSelected();
	}

	function validatepage()
	{
		var oReasonOptions = Form.Reason.options;
		var iReasonLength = oReasonOptions .length;
		// clear the Reason hidden listbox
		var i;
		for ( i=iReasonLength-1; i>=0; i--)
		{
			oReasonOptions.remove(i);
		}
		// copy options from Selected to Reason, and select them all
		var oSelectedOptions = Form.Selected.options;
		var iSelectedLength = oSelectedOptions.length;
		for ( i=0; i<iSelectedLength; i++)
		{
			var oOption = document.createElement("OPTION");
			oOption.text = oSelectedOptions[i].text;
			oOption.selected = true;
			oReasonOptions.add(oOption);
		}
	}

	// temporary function to make sure only one item can be entered into te selected listbox.
	// Note: this functionality can be removed, once we implement the OOS entirly as in CAD.
	function CheckMaximumSelected()
	{
		var oSelectedOptions = Form.Selected.options;
		var iSelectedLength = oSelectedOptions.length;
		Form.Button1.disabled = (iSelectedLength > 9);
		if (Form.Button1.disabled)
		{
			alert("Maximum of 10 OS Reasons only can be selected");
		}
	}

	// Added functionality to double click item to move to other list. Marvin C4G
	document.getElementById('Unselected').ondblclick = function(e){
		 MoveReason (document.getElementById('Unselected'), document.getElementById('Selected') );
	}

	document.getElementById('Selected').ondblclick = function(e){
		 MoveReason (document.getElementById('Selected'), document.getElementById('Unselected') );
	}

	</script>
</HTML>
