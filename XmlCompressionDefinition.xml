<?xml version="1.0" encoding="utf-8" ?>
<XmlCompressionDefinition xmlns="http://tempuri.org/SchemaXmlCompression.xsd">
	<ReplacedStrings>
		<Name>header</Name>
		<Code>a0</Code>
		<ReplaceFullStartTags>false</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>unique_transaction_id</Name>
		<Code>a1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>timestamp</Name>
		<Code>a2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>true</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>ack_required</Name>
		<Code>a3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>expiration_minutes</Name>
		<Code>a4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>acked_unique_id</Name>
		<Code>a5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>protocol_version</Name>
		<Code>a6</Code>
		<ReplaceFullStartTags>false</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>false</ReplaceEndTags>
		<ReplaceAttributes>true</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>unit_update</Name>
		<Code>a7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>unit</Name>
		<Code>a8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>unit_name</Name>
		<Code>a9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>address</Name>
		<Code>b0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>current_sector</Name>
		<Code>b1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>home_sector</Name>
		<Code>b2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>current_status</Name>
		<Code>b3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>incidentid</Name>
		<Code>b4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>true</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>incident_number</Name>
		<Code>b5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>true</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>user</Name>
		<Code>b6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>avl</Name>
		<Code>b7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>latitude</Name>
		<Code>b8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>longitude</Name>
		<Code>b9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
  <ReplacedStrings>
    <Name>city</Name>
    <Code>b10</Code>
    <ReplaceFullStartTags>true</ReplaceFullStartTags>
    <ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
    <ReplaceEndTags>true</ReplaceEndTags>
    <ReplaceAttributes>false</ReplaceAttributes>
  </ReplacedStrings>
	<ReplacedStrings>
		<Name>field</Name>
		<Code>c0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>field_name</Name>
		<Code>c1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>field_value</Name>
		<Code>c2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>driving_directions_request</Name>
		<Code>c3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>from_lat</Name>
		<Code>c4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>from_lon</Name>
		<Code>c5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>to_lat</Name>
		<Code>c6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>to_lon</Name>
		<Code>c7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>driving_directions</Name>
		<Code>c8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>node</Name>
		<Code>c9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>lat</Name>
		<Code>d0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>lon</Name>
		<Code>d1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>direction</Name>
		<Code>d2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>type</Name>
		<Code>d3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>true</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>text</Name>
		<Code>d4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>priority</Name>
		<Code>d5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>incident_status</Name>
		<Code>d6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>hierarchy_level</Name>
		<Code>d7</Code>
		<ReplaceFullStartTags>false</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>name</Name>
		<Code>d8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>true</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>sector</Name>
		<Code>d9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>case_number</Name>
		<Code>e0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>incident_type</Name>
		<Code>e1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>premise_info</Name>
		<Code>e2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>preplan</Name>
		<Code>e3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>prem_hist</Name>
		<Code>e4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>incident</Name>
		<Code>e5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>problem_nature</Name>
		<Code>e6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>call_disp</Name>
		<Code>e7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>partial_list</Name>
		<Code>e8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>location_name</Name>
		<Code>e9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>crossing_street</Name>
		<Code>f0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>apartment</Name>
		<Code>f1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>city</Name>
		<Code>f2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>state</Name>
		<Code>f3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>zip</Name>
		<Code>f4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>hazards</Name>
		<Code>f5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>caution_notes</Name>
		<Code>f6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>caller_name</Name>
		<Code>f7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>caller_address</Name>
		<Code>f8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>caller_phone</Name>
		<Code>f9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>assigned_units</Name>
		<Code>g0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>response_priority</Name>
		<Code>g1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>true</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>assigned_other</Name>
		<Code>g2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>comment</Name>
		<Code>g3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>author</Name>
		<Code>g4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>append_comments</Name>
		<Code>g5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>success</Name>
		<Code>g6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>unassign_incident</Name>
		<Code>g7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>message</Name>
		<Code>g8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>request_update</Name>
		<Code>g9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>include_home_sector</Name>
		<Code>h0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>exclude_status</Name>
		<Code>h1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>ack_confirm</Name>
		<Code>h2</Code>
		<ReplaceFullStartTags>false</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>false</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>incident_update</Name>
		<Code>h3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>remove_mail</Name>
		<Code>h4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>mail</Name>
		<Code>h5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>mail_type</Name>
		<Code>h6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>mail_id</Name>
		<Code>h7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>sent_time</Name>
		<Code>h8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>from_user</Name>
		<Code>h9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>folder_id</Name>
		<Code>h10</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>subject</Name>
		<Code>i0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>mail_body</Name>
		<Code>i1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>to_user</Name>
		<Code>i2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>to_cad_user</Name>
		<Code>i3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>to_unit</Name>
		<Code>i4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>to_sector</Name>
		<Code>i5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>close_incident</Name>
		<Code>i6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>form_response</Name>
		<Code>i7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>form_name</Name>
		<Code>i8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>form_location</Name>
		<Code>i9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>form_data</Name>
		<Code>j0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>request_parameter</Name>
		<Code>j1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>reference_id</Name>
		<Code>j2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>form_request</Name>
		<Code>j3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>source_form</Name>
		<Code>j4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>destination_form</Name>
		<Code>j5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>queryfile</Name>
		<Code>j6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>fields</Name>
		<Code>j7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>field_id</Name>
		<Code>j8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>images</Name>
		<Code>j9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>image</Name>
		<Code>k0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>request_incident</Name>
		<Code>k1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>watch_list</Name>
		<Code>k2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>change_status</Name>
		<Code>k3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>change_status_request</Name>
		<Code>k4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>status</Name>
		<Code>k5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>out_of_vehicle</Name>
		<Code>k6</Code>
		<ReplaceFullStartTags>false</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>false</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>in_vehicle</Name>
		<Code>k7</Code>
		<ReplaceFullStartTags>false</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>true</ReplaceOpenedStartTags>
		<ReplaceEndTags>false</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>response_number</Name>
		<Code>k8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>true</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>attachment</Name>
		<Code>m9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>size</Name>
		<Code>n0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>attachment_request</Name>
		<Code>n1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>position</Name>
		<Code>n2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>new_password</Name>
		<Code>n3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>change_password_reason</Name>
		<Code>n4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>change_password</Name>
		<Code>n5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>failure_reason</Name>
		<Code>n6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>reset_password</Name>
		<Code>n7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>reset_login_name</Name>
		<Code>n8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>datasource</Name>
		<Code>n9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>mag_stripe_parse_failure</Name>
		<Code>o0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>SupplementWeapon</Name>
		<Code>o1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>SupplementVehicle</Name>
		<Code>o2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>SupplementProperty</Name>
		<Code>o3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>SupplementPerson</Name>
		<Code>o4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>unit_route_deviation</Name>
		<Code>o5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>password_length</Name>
		<Code>o6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>new_password_length</Name>
		<Code>o7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>UnitAVLconnection</Name>
		<Code>o8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>connected</Name>
		<Code>o9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>FilesReadyForDownload</Name>
		<Code>p0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>folderName</Name>
		<Code>p1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>RequestToDownloadFiles</Name>
		<Code>p2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>Notification</Name>
		<Code>p3</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>destinationLatitude</Name>
		<Code>p4</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
    <ReplacedStrings>
		<Name>destinationLongitude</Name>
		<Code>p5</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>Crew</Name>
		<Code>p6</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>IsMobile</Name>
		<Code>p7</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>AssignedUnits</Name>
		<Code>p8</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>PasswordExpirationWarning</Name>
		<Code>p9</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>TelephonyCallID</Name>
		<Code>q0</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>RingTime</Name>
		<Code>q1</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
	<ReplacedStrings>
		<Name>IsCleared</Name>
		<Code>q2</Code>
		<ReplaceFullStartTags>true</ReplaceFullStartTags>
		<ReplaceOpenedStartTags>false</ReplaceOpenedStartTags>
		<ReplaceEndTags>true</ReplaceEndTags>
		<ReplaceAttributes>false</ReplaceAttributes>
	</ReplacedStrings>
</XmlCompressionDefinition>