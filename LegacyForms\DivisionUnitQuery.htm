<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Division Unit Query</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Division Unit Query</H4>
						<form action="DivisionUnitQuery.aspx?queryfile=DivisionUnitQuery.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td>Jurisdiction:</td>
									<td>
										<XML id="sectorstyle" src="sectors.xsl"></XML>
										<SPAN type="selectlist" id="sectorvals" name="sectorvals">
											<XML id="sectorsource" src="sectors.xml"></XML>
										</SPAN>
									</td>
								</tr>
								<tr>
									<td>Division:</td>
									<td>
										<span id="divisionvals" type="selectlist" parent="sectorvals">
											<XML id="divisionsource" src="divisions.xml"></XML>
											<select id="divisions" name="divisions" size=5 multiple mandatory="true" style="background-color:lightblue;">
											</select>
										</span>
									</td>
								</tr>
							</table>
							<br>
							<input type="hidden" name="level" id="level">
							<input type="hidden" name="itemid" id="itemid">
							<input type="submit" name="Query" id="Query" value="Query" onclick="setvalue()">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
	    
		function window.onload()
		{
			Form.Query.focus();
			PrepareValidation(Form);

			// Note: setting savelast to true causes problems because the onchanged event doesn't get fired
			// and the divisions are out of synch with the secotors.
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			sectorvals.innerHTML = GenerateSelectBox("sector", sectorsource, sectorstyle, true, false, false, 1, false, false);

			// The root level .XSL file contains an onchange event defined for the 
			// root level select object.  It must be called on window loading.
			sectorchanged();
		}

		function setvalue()
		{
			// if no item is selected, then select the first one
			if (Form.divisions.selectedIndex == -1) Form.divisions.selectedIndex = 0;

			if (Form.divisions.selectedIndex == 0)
			{
				Form.itemid.value = Form.sector.options[Form.sector.selectedIndex].value;
				Form.level.value = 'jurisdiction';
				//Form.level.value = 'sector';
			}
			else
			{
				Form.itemid.value = '';
				var i;
				var values = '';
				for (i=1; i<Form.divisions.length; i++) // not including the first "include all" item
				{
					if (Form.divisions.options[i].selected)
					{
						if (values != '') values += ',';
						values += Form.divisions.options[i].value;
					}
				}
				Form.itemid.value = values;
				Form.level.value = "division";
			}
		}

		function sectorchanged()
		{
			// Non-leaf items will have a 'Changed' event.  
			// These events will contain the following code:
			
			// This gets the newly selected value from the changed select control.
			var sectorID = Form.sector.options[Form.sector.selectedIndex].value;

			// This line gets all elements that have a 'foreign key' of the newly
			// selected value.
			var nodes = divisionsource.XMLDocument.documentElement.selectNodes("//divisions/item[parentlistid=" + sectorID + "]");
			
			// Clear the select combo that is to be repopulated.
			Form.divisions.options.length = 0;

			// Repopulate the select combo with the desired data from the retrieve nodes..
			var oOption = document.createElement("OPTION");
			Form.divisions.options.add(oOption, 0);
			oOption.innerText = "Include All";
			oOption.value = '0';
			oOption.selected = true;
			for(var i=0; i<nodes.length; i++)
			{
				var oOption = document.createElement("OPTION");
				Form.divisions.options.add(oOption, 1);
				oOption.value = nodes[i].selectSingleNode("id").text;
				oOption.innerText = nodes[i].selectSingleNode("description").text;
			}
		}

	</script>
</HTML>
