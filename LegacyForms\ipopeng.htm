<html>
<head>
<title>PopCalendar eXtremely Perfect 6.1 DHTML Engine - www.calendarxp.net</title>
<script type="text/javascript">
function FormatShortDate(date)
{
    return FormatShortDateVB(date.getYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate());
}

function GetDatePosBasedOnRegionalSettings()
{
    var dateString = FormatShortDate(new Date(2000, 0, 31));
    dateString = dateString.replace(".", "/");
    dateString = dateString.replace("-", "/");
    var dateElement = dateString.split("/");
    var element0 = parseInt(dateElement[0]);
    if (element0 == 31)
    {
        return 0;
    }
    else if (element0 == 2000)
    {
        return 2;
    }
    else
    {
        return 1;
    }
}
</script>

<script type="text/vbscript">
Function FormatShortDateVB(date)
    FormatShortDateVB = FormatDateTime(date, vbShortDate)
End function
</script>

<script type='text/javascript'>
// Making any modification to the following scripts is a breach to the license agreement and will get you into a lawsuit eventually!
//== PopCalendarXP 6.1.120 Lite Edition, Copyright 2001-2002 CalendarXP.net, LIMING WENG
//== Release date: 2003.02.08         website - http://www.calendarxp.net
// Lite Edition is only allowed in use with non-commercial and non-profit websites. Monetary penalties will be incurred for misuse.
// Please purchase the commercial edition if you plan to use it within a commercial or profitable website, any commercial firm's intranet and/or any products.
var ua=navigator.userAgent.toLowerCase();
var MAC=ua.indexOf('mac')!=-1;
var NN4=IE4=OP6=KO3=false, OP=!!self.opera;
var IE=ua.indexOf("msie")!=-1&&!OP&&ua.indexOf("webtv")==-1;
var gd=new Date();
var gToday=[gd.getFullYear(),gd.getMonth()+1,gd.getDate()];
var gTheme=self.name.split(":");
var gCurMonth=eval(gTheme[0]);
var gContainer=parent;
var fHoliday,fOnChange,fAfterSelected;
var _agenda=[], popkey=["Lite"], flatkey=["Lite"];
var gbInvertBold=false;

var gDateFormat="default";


if (!gTheme[3]) gTheme[3]="gfPop";
eval("parent."+gTheme[3]+"=parent.frames[self.name]");

function fCalibrate(y,m) {
	if (m<1) { y--; m+=12; }
	else if (m>12) { y++; m-=12; }
	return [y,m];
}

function fGetById(doc, id) {
	return doc.getElementById(id);
}

function fAddEvent(y,m,d,message,action,bgcolor,fgcolor,bgimg,boxit,html) {
	_agenda[y+"-"+m+"-"+d]=[message,action,bgcolor,fgcolor,bgimg,boxit,html];
}

function fGetEvent(y,m,d) {
	var ag=_agenda[y+"-"+m+"-"+d];
	if (ag) return ag.slice(0);
	return null;
}

function fRemoveEvent(y,m,d) {
	_agenda[y+"-"+m+"-"+d]=null;
}

var gfSelf=fGetById(parent.document,self.name);
with (document) {
	write("<scr"+"ipt type='text/javascript' src='"+gTheme[1]+".js'></scr"+"ipt>");
	write("<lin"+"k rel='stylesheet' type='text/css' href='"+gTheme[1]+".css'>");
	if (gTheme[2]) write("<scr"+"ipt type='text/javascript' src='"+gTheme[2]+"'></scr"+"ipt>");
	write("<scr"+"ipt type='text/javascript' src='"+(gTheme[4]?gTheme[4]:"plugins.js")+"'></scr"+"ipt>");
}
</script>
</head>
<body leftmargin=0 topmargin=0 marginwidth=0 marginheight=0 hspace=0 vspace=0 onselectstart="return false" ondragstart="return false" onmouseup="/*self.status='PopCalendarXP 6.1 Lite Edition, Copyright 2001-2002 CalendarXP.net';*/return true;">
<script type='text/javascript'>
gCurMonth=fCalibrate(gCurMonth[0],gCurMonth[1]);
var gdBegin,gdEnd,gRange,gcbMon,gcbYear,gdCtrl,gbMouse=true;
var gcTemp=gcCellBG;
var giSat=gbEuroCal?5:6;
var giSun=gbEuroCal?6:0;
if (gbEuroCal) gWeekDay=gWeekDay.slice(1).concat(gWeekDay[0]);
var _cal=[];
for (var i=0;i<6;i++) { _cal[i]=[]; for (var j=0;j<7;j++) _cal[i][j]=[]; }
var gDays=[31,31,28,31,30,31,30,31,31,30,31,30,31];
var gob=parseInt(gfSelf.style.borderWidth,10);
gob=isNaN(gob)?0:gob+gob;

function fPopCalendar(dateCtrl,range,posLayerId,posCtrl) {
	var dc=dateCtrl;
	var pc=posCtrl?posCtrl:dc;
	if (gdCtrl!=dc)
		gdCtrl=dc;
	else if (gfSelf.style.visibility=="visible") {
		fHideCal();
		return;
	}
	var s=fParseDate(gdCtrl.value);
	if (s==null) {
		if (gdSelect[2]==0||gdCtrl.value=="") {
			s=eval(gTheme[0]);
			gdCtrl.value="";
		} else {
			s=gdSelect;
			gdCtrl.value=fFormatDate(s[0],s[1],s[2]);
		}
	} else {
		gdCtrl.value=fFormatDate(s[0],s[1],s[2]);
		gdSelect=s;
	}
	fInitRange(range);
	if (gRange[2]&&fIsOutRange(s[0],s[1])) s=gRange[2];
	if (!fSetCal(s[0],s[1],0,true)) {
		gdCtrl.value="";
		fHideCal();
		return;
	}
	var p,oh;
	if (gbFixedPos) {
		p=gPosOffset;
		oh=-1;
	} else {
		p=fGetXY(pc,gPosOffset);
		if (posLayerId) {
			var lyr=fGetById(parent.document,posLayerId);
			if (lyr&&lyr.tagName.toUpperCase()=="IFRAME") {
				var pl=fGetXY(lyr);
				var p2=fGetWinSize(parent.frames[posLayerId]).slice(2);
				p[0]+=pl[0]-p2[0]+gob/2;
				p[1]+=pl[1]-p2[1]+gob/2;
			}
		}
		var oh=pc.offsetHeight;
		var ptb=fGetById(document,"outerTable");
		var h=ptb.offsetHeight;
		var w=ptb.offsetWidth;
		h=(h?h:gfSelf.height)+oh+gob;
		if (gbAutoPos) {
			var ws=fGetWinSize(parent);
			var tmp=ws[0]+ws[2]-(w?w:gfSelf.width)-gob;
			p[0]=p[0]<ws[2]?ws[2]+2:p[0]>tmp?tmp:p[0];
			tmp=ws[1]+ws[3]-h;
			if (p[1]>tmp&&(!gbPopDown||p[1]-ws[3]+oh>=h)) 
				p[1]-=oh>0?h+2:h+25;
		} else if (!gbPopDown) p[1]-=oh>0?h+2:h+25;
	}
	with (gfSelf.style) {
		left=p[0]+"px";
		top =p[1]+oh+1+"px";
		visibility="visible";
	}
}

function fGetWinSize(w) {
	if (parent.innerWidth)
		return [w.innerWidth-16,w.innerHeight,w.pageXOffset,w.pageYOffset];
	else if (w.document.compatMode=='CSS1Compat')
		with (w.document.documentElement) return [clientWidth,clientHeight,scrollLeft,scrollTop];
	else
		with (w.document.body) return [clientWidth,clientHeight,scrollLeft,scrollTop];
}

function fHideCal() {
	with (gfSelf.style) {
		visibility="hidden";
		top=parseInt(top,10)-10+"px";
	}
}

function fGetXY(a,offset) {
	var p=offset?offset.slice(0):[0,0],tn;
	while(a) {
		tn=a.tagName.toUpperCase();
		p[0]+=a.offsetLeft-(!KO3&&tn=="DIV"&&a.scrollLeft?a.scrollLeft:0);
		p[1]+=a.offsetTop-(!KO3&&tn=="DIV"&&a.scrollTop?a.scrollTop:0);
		if (tn=="BODY") break;
		a=a.offsetParent;
	}
	return p;
}

function fInitRange(r) {
	gRange=r?r:[];
	var rb=gRange[0]?r[0]:gBegin;
	gdBegin=new Date(rb[0],rb[1]-1,rb[2]);
	gRange[0]=rb;
	var re=gRange[1]?r[1]:gEnd;
	gdEnd=new Date(re[0],re[1]-1,re[2]);
	gRange[1]=re;
}

function fParseDate(ds) {
	var i,r=null;
	if (ds!=null) {
		var pd=ds.split(gsSplit);
		if (pd.length==3) {
			giDatePos=GetDatePosBasedOnRegionalSettings();
			var m=pd[giDatePos==1?0:1];
			for (i=0; (i<12)&&(gMonths[i].substring(0,3).toLowerCase()!=m.substring(0,3).toLowerCase())&&(i+1!=m); i++);
			if (i<12) {
				var y=parseInt(pd[giDatePos==2?0:2].substring(0,4),10);
				var pf=Math.floor(gEnd[0]/100)*100;
				r=[y<100?y>gEnd[0]%100?pf-100+y:pf+y:y,i+1,parseInt(pd[giDatePos],10)];
			} else return null;
		} else return null;
		var td=new Date(r[0],r[1]-1,r[2]);
		if (isNaN(td)||td.getMonth()!=r[1]-1) return null;
	}
	return r;
}

function fFormatDate(y,m,d){
	var M=giMonthMode==0?gbPadZero&&m<10?"0"+m:m:giMonthMode==1?gMonths[m-1]:gMonths[m-1].substring(0,giMonthMode);
	var D=gbPadZero&&d<10?"0"+d:d;
	var sy=y%100;
	var Y=gbShortYear?sy<10?"0"+sy:sy:y;
	if (gDateFormat == "nosplit")
	{
		switch (giDatePos) {
			case 0: return ""+D+M+Y;//// D+gsSplit+M+gsSplit+Y;
			case 1: return ""+M+D+Y;//// M+gsSplit+D+gsSplit+Y;
			case 2: return ""+Y+M+D;//// Y+gsSplit+M+gsSplit+D;
		}
	}
	else if (gDateFormat == "default")
	{
		switch (giDatePos) {
			case 0: return D+gsSplit+M+gsSplit+Y;
			case 1: return M+gsSplit+D+gsSplit+Y;
			case 2: return Y+gsSplit+M+gsSplit+D;
		}
	}
}

function fGetAgenda(y,m,d,taint) {
	var s=fCalibrate(y,m),t=gToday,cm=gCurMonth,oor=false;
	var def=["",gsAction,gcCellBG,null,guCellBGImg,false,gsCellHTML];
	if (taint) if ((giShowOther&4)&&(s[0]<cm[0]||s[0]==cm[0]&&s[1]<cm[1])||(giShowOther&8)&&(s[0]>cm[0]||s[0]==cm[0]&&s[1]>cm[1]))
		return null;
	var ag=fHoliday?fHoliday(s[0],s[1],d):fGetEvent(y,m,d);
	if (ag==null) ag=def;
	else {
		for (var i=0;i<7;i++) {
			if (gAgendaMask[i]!=-1) ag[i]=gAgendaMask[i];
			if (ag[i]==null&&i!=1) ag[i]=def[i];
		}
		if (taint&&s[1]!=cm[1]&&!(giShowOther&1)) {
			def[0]=ag[0]; def[1]=ag[1]; ag=def;
		}
	}
	if (taint&&s[1]!=cm[1]) ag[3]=gcOtherDay;
	for (var i=3; i<gRange.length; i++)
		if (gRange[i][2]==d&&gRange[i][1]==s[1]&&gRange[i][0]==s[0])
			{ oor=true; break; }
	if (oor||!fValidRange(s[0],s[1],d)) {
		ag[0]=gsOutOfRange; ag[1]=null;
		if (guOutOfRange) ag[4]=guOutOfRange;
	}
	return ag;
}

function fGetDOW(y,m,d) {
	var dow=new Date(y,m-1,d).getDay();
	if (gbEuroCal)
		if (--dow<0) dow=6;
	return dow;
}

function fValidRange(y,m,d) {
	var dt=new Date(y,m-1,d);
	return (dt>=gdBegin)&&(dt<=gdEnd);
}

function fGetDays(y) {
	gDays[2]=y%4==0&&y%100!=0||y%400==0?29:28;
	return gDays;
}

function fBuildCal(y,m) {
	var days=fGetDays(y),iDay1=fGetDOW(y,m,1);
	var iLast=days[m-1]-iDay1+1,iDate=1,iNext=1;
	for (var d=0;d<7;d++) {
		_cal[0][d][0]=d<iDay1?m-1:m;
		_cal[0][d][1]=d<iDay1?iLast+d:iDate++;
	}
	for (var w=1;w<6;w++)
		for (var d=0;d<7;d++) {
			_cal[w][d][0]=iDate<=days[m]?m:m+1;
			_cal[w][d][1]=iDate<=days[m]?iDate++:iNext++;
		}
}

function fIsOutRange(y,m) {
	return (y>gRange[1][0]||y<gRange[0][0]||y==gRange[0][0]&&m<gRange[0][1]||y==gRange[1][0]&&m>gRange[1][1]);
}

function fCheckRange(y,m) {
	if (fIsOutRange(y,m)) {
		if (gsOutOfRange!="") alert(gsOutOfRange);
		return false;
	}
	return true;
}

function fSetCal(y,m,d,bTriggerOnChg) {
	var t=fCalibrate(parseInt(y,10),parseInt(m,10)), ptb,ow,oh;
	y=t[0];
	m=t[1];
	if (!fCheckRange(y,m)||bTriggerOnChg&&fOnChange&&fOnChange(y,m,d)) {
		if (gcbMon) gcbMon.options[gCurMonth[1]-1].selected=true;
		if (gcbYear) gcbYear.options[gCurMonth[0]-gBegin[0]].selected=true;
		return false;
	}
	if (d>0) gdSelect=[y,m,d];
	fGetById(document,"middleDiv").innerHTML=fDrawCal(y,m)+"\n";
	if (gcbMon) gcbMon.options[m-1].selected=true;
	if (gcbYear) gcbYear.options[y-gBegin[0]].selected=true;
	if (!gbHideTop&&giDCStyle>0) fGetById(document,"calTitle").innerHTML=eval(gsCalTitle)+"\n";
	ptb=fGetById(document,"outerTable");
	if (ptb) {
		ow=ptb.offsetWidth;
		oh=ptb.offsetHeight;
		if (ow) gfSelf.width=ow;
		if (oh) gfSelf.height=oh;
	}
	return true;
}

function fSetDate(y,m,d,taint) {
	var ag=fGetAgenda(y,m,d,taint);
	if (ag==null||ag[1]==null) return false;
	if (!fSetCal(y,m,d,true)) return false;
	gdCtrl.value=fFormatDate(y,m,d);
	if (gbAutoClose) fHideCal();
	gbMouse=true;
	eval(ag[1]);
	if (fAfterSelected) fAfterSelected(y,m,d);
	return true;
}

function fMouseOver(t) {
	if (!gbFocus) return;
	gcTemp=t.style.backgroundColor;
	t.style.backgroundColor=gcToggle;
	self.status=t.title;
	gbMouse=false;
}

function fMouseOut(t) {
	if (!gbFocus) return;
	t.style.backgroundColor=gcTemp?gcTemp:"transparent";
	gbMouse=true;
}

function fDrawCal(y,m) {
	var sDIV=" style='position:relative;height:";
	var td,ti,htm,bo,ag,i,c,c1,dayNo,dc,cbg,isT,isS,weekNo,cd,ex;
	var ms=giMarkSelected,ht=giMarkToday;
	gCurMonth[0]=y; gCurMonth[1]=m;
	fBuildCal(y,m);
	var html="<TABLE width='100%' "+gsInnerTable+" ><tr>";
	for (var wd=0,i=0; i<7; i++)
		html+="<td class='CalHead'><div "+sDIV+giHeadHeight+"px;width:"+giCellWidth+"px;top:"+giHeadTop+"px;'>"+gWeekDay[wd++]+"</div></td>";
	html+="</tr>";
	for (var week=0; week<6; week++) {
		ex=week>3&&_cal[week][0][1]<20;
		if (gbShrink2fit&&ex) continue;
		html+="<tr>";
		for (var day=-1,i=0; i<7; i++) {
			day++;
			dayNo=_cal[week][day][1];
			cd=fCalibrate(y,_cal[week][day][0]);
			isS=gdSelect[2]==dayNo&&gdSelect[1]==cd[1]&&gdSelect[0]==cd[0];
			isT=gToday[2]==dayNo&&gToday[1]==cd[1]&&gToday[0]==cd[0];
			ag=fGetAgenda(cd[0],cd[1],dayNo,true);
			if (ag==null) {
				c=c1=dc=gcCellBG; cbg=null; bo=false; td=ti=htm="";
			} else {
				dc=ag[3]==null?day==giSun?gcSun:day==giSat?gcSat:gcWorkday:ag[3];
				cbg=ag[4];
				if (cd[1]==m||(giShowOther&2)) {
					c=isS&&(ms&2)?gcBGSelected:isT&&(ht&2)?gcBGToday:ag[2];
					c1=isS&&(ms&1)?gcBGSelected:isT&&(ht&1)?gcBGToday:ag[5]==true?gcCellBG:ag[2];
					bo=isS&&(ms&4)||isT&&(ht&4);
					dc=isS&&(ms&8)?gcFGSelected:isT&&(ht&8)?gcFGToday:dc;
					cbg=isS&&(ms&16)?guSelectedBGImg:isT&&(ht&16)?guTodayBGImg:cbg;
				} else {
					c=ag[2];
					c1=ag[5]==true?gcCellBG:c;
					bo=false;
				}
				bo=gbBoldAgenda&&ag[0]&&ag[0]!=gsOutOfRange||bo;
				if (gbInvertBold) bo=!bo;
				htm=ag[6]?"<BR>"+ag[6]:"";
				td=ag[1]==null?";text-decoration:line-through":"";
				ti=ag[0].replace(/\"/g,"&quot;");
				if (gcSunBG&&day==giSun) { c1=c1==gcCellBG?gcSunBG:c1; c=c==gcCellBG?gcSunBG:c; }
				if (gcSatBG&&day==giSat) { c1=c1==gcCellBG?gcSatBG:c1; c=c==gcCellBG?gcSatBG:c; }
			}
			html+="<td "+(c?" bgcolor='"+c+"' ":"")+"><div class='CalCell' "+sDIV+giCellHeight+"px;width:"+giCellWidth+"px;"+("background:"+(c1?c1:"")+(cbg?" url("+cbg+")":""))+"' title=\""+ti+"\" onmouseover='fMouseOver(this);return true;' onmouseout='fMouseOut(this)' onclick='fSetDate("+cd[0]+","+cd[1]+","+dayNo+",true)'><A href='javascript:void(0)' class='CellAnchor' style='color:"+dc+(bo?";font-weight:bold":"")+td+"' onfocus='if(this.blur)this.blur();'>"+eval(gsDays)+"</A>"+htm+"</div></td>";
			ag=null;
		}
		html+="</tr>";
	}
	return html+"</TABLE>";
}

function fPrevMonth() {
	return fSetCal(gCurMonth[0],gCurMonth[1]-1,0,true);
}

function fNextMonth() {
	return fSetCal(gCurMonth[0],gCurMonth[1]+1,0,true);
}

with (document) {
	body.bgColor=gcCalBG;
	write("<TABLE id='outerTable' "+gsOuterTable+"><FORM name='topForm'>");
	if (!gbHideTop)
	if (giDCStyle==2)
		write("<TR><TD class='CalTop' nowrap><SPAN id='calTitle' class='CalTitle'>"+eval(gsCalTitle)+"</SPAN></TD></TR>");
	else if (giDCStyle==1){
		write("<TR><TD class='CalTop' nowrap><table border=0 cellspacing=0 cellpadding=0 width='100%'><tr><TD align='left' nowrap><A href='javascript:void(0)' class='MonthNav' onclick='fPrevMonth();if(this.blur)this.blur();' onmouseover='return true;'>"+gsNavPrev+"</A></TD><TD id='calTitle' class='CalTitle' nowrap>");
		write(eval(gsCalTitle));
		write("</TD><TD align='right' nowrap><A href='javascript:void(0)' class='MonthNav' onclick='fNextMonth();if(this.blur)this.blur();' onmouseover='return true;'>"+gsNavNext+"</A></TD></tr></table></TD></TR>");
	} else {
		write("<TR><TD class='CalTop' nowrap>"+gsNavPrev+" ");
		var mstr="<SELECT id='MonSelect' class='CalTitle' onchange='fSetCal(gcbYear.value, gcbMon.value,0,true)'>";
		for (var i=0; i<12; i++)
			mstr+="<OPTION value='"+(i+1)+"'>"+gMonths[i]+"</OPTION>";
		mstr+="</SELECT>";		
		var ystr="<SELECT id='YearSelect' class='CalTitle' onchange='fSetCal(gcbYear.value, gcbMon.value,0,true)'>";
		for(var i=gBegin[0];i<=gEnd[0];i++)
			ystr+="<OPTION value='"+i+"'>"+i+"</OPTION>";
		ystr+="</SELECT>";
		if (gbDCSeq) write(mstr+ystr);
		else write(ystr+mstr);
		gcbMon=fGetById(document,"MonSelect");
		gcbYear=fGetById(document,"YearSelect");
		write(" "+gsNavNext+"</TD></TR>");
	}
	write("</FORM><TR><TD class='CalMiddle'><DIV id='middleDiv' style='background:"+gcCalFrame+(guCalBG?" url("+guCalBG+") ":"")+";'></DIV></TD></TR>");
	if (!gbHideBottom) write("<FORM name='bottomForm'><TR><TD class='CalBottom' nowrap>"+gsBottom+"</TD></TR></FORM>");
	write("</TABLE>");
	for (var i=0;i<giFreeDiv;i++)
		write("<DIV class='FreeDiv' id='freeDiv"+i+"' style='position:absolute;visibility:hidden;z-index:500'></DIV>");
}
gfSelf.style.visibility='hidden';
</script>
</body>
</html>
