<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Responding (Second Location)</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Responding - Second Location</H4>
						<form action="SecondLocationQuery.aspx?queryfile=secondlocation.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td align="right" vAlign="top">Location<input type="radio" class="bigradio" name="chk" id="loc" value="loc"></td>
									<td>
										<!-- Root item requires a SPAN element to contain it. -->
										<XML id="locationstyle" src="genericlist.xsl"></XML>
										<SPAN type="selectlist" id="locationvals" name="locationvals">
											<XML id="locationsource" src="Respond2ndLocation.xml"></XML>
										</SPAN>
									</td>
								</tr>
								<tr><td>&nbsp;</td></tr>
								<tr>
									<td align="left" valign="top">Street Address<input type="radio" class="bigradio" name="chk" id="addr" value="addr"></td>
									<td>
<table id="innertable">
    <tr><td>Address:</td><td><input type="text" name="address" id="address"></td></tr>
    <tr><td>City:</td><td><input type="text" name="city" id="city"></td></tr>
    <tr><td>State:</td><td><SPAN type="selectlist" id="statevals" name="statevals"><XML id="statesource" src="state.xml"></XML></SPAN></td></tr>
    <tr><td>Zip:</td><td><input type="text" name="postalcode" id="postalcode"></td></tr>
    <tr><td>Room/Apt:</td><td><input type="text" name="roomapt" id="roomapt"></td></tr>
    <tr><td>Building:</td><td><input type="text" name="building" id="building"></td></tr>
</table>
									</td>

								</tr>								<tr><td>&nbsp;</td><td><XML id="statestyle" src="genericcombo.xsl"></XML>
							</table>
							<hr>
							<table>
								<tr>
									<td valign="top"><b>Comment:</b></td>
									<td width="300"><textarea name="comments" id="comments" rows="8" onkeyup="CheckTextAreaLength(this.form.comments,200);"></textarea></td>
								</tr>
							</table>
							<!-- Modified by Harshad on 6/30/05 -->
							<!--<input type="submit" name="Query" id="Query" value="Submit"> -->
							<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
							<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
							<!-- End modifications by Harshad -->
							<!-- Modified by Harshad on 08/08/05 -->
							<input type="hidden" name="unitname1" id="unitname1">
							<input type="hidden" name="location1" id="location1">
							<input type="hidden" name="address1" id="address1">
							<input type="hidden" name="city1" id="city1">
							<input type="hidden" name="state1" id="state1">
							<input type="hidden" name="postalcode1" id="postalcode1">
							<input type="hidden" name="roomapt1" id="roomapt1">
							<input type="hidden" name="building1" id="building1">

							<input type="hidden" name="unitname2" id="unitname2">
							<input type="hidden" name="location2" id="location2">
							<input type="hidden" name="address2" id="address2">
							<input type="hidden" name="city2" id="city2">
							<input type="hidden" name="state2" id="state2">
							<input type="hidden" name="postalcode2" id="postalcode2">
							<input type="hidden" name="roomapt2" id="roomapt2">
							<input type="hidden" name="building2" id="building2">

							<input type="hidden" name="unitname3" id="unitname3">
							<input type="hidden" name="location3" id="location3">
							<input type="hidden" name="address3" id="address3">
							<input type="hidden" name="city3" id="city3">
							<input type="hidden" name="state3" id="state3">
							<input type="hidden" name="postalcode3" id="postalcode3">
							<input type="hidden" name="roomapt3" id="roomapt3">
							<input type="hidden" name="building3" id="building3">

							<input type="hidden" name="unitname4" id="unitname4">
							<input type="hidden" name="location4" id="location4">
							<input type="hidden" name="address4" id="address4">
							<input type="hidden" name="city4" id="city4">
							<input type="hidden" name="state4" id="state4">
							<input type="hidden" name="postalcode4" id="postalcode4">
							<input type="hidden" name="roomapt4" id="roomapt4">
							<input type="hidden" name="building4" id="building4">

							<input type="hidden" name="unitname" id="unitname">

							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<!-- End modifications b Harshad -->
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<script language="javascript">

	function window.onload()
	{
		// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.

		PrepareValidation(Form);
		locationvals.innerHTML = GenerateSelectBox("location", locationsource, locationstyle, false, false, false, 8, false, false);
		statevals.innerHTML = GenerateComboBox("state", statesource, statestyle);
		Form.addr.checked = true;
		Form.address.focus();
	}

	function clearaddress()
	{
		Form.address.value = '';
		Form.city.value = '';
		Form.state.selectedIndex = -1;
		Form.postalcode.value = '';
		Form.roomapt.value = '';
		Form.building.value = '';
	}

	function clearlocation()
	{
		Form.location.selectedIndex = -1;
	}

	function window.validatepage()
	{
		<!-- Determine the selection. If location radio is selected user must select location value	-->
		<!-- If Address radio is selected, user must enter the Address -->
		var sLoc = "";
		if (Form.loc.checked)
		{
			if (Form.location.selectedIndex != -1)
			{
				sLoc = Form.location.options[Form.location.selectedIndex].text;
			}

			if (sLoc == "")
			{
				Form.location.focus();
				alert ("Please select a location");
			}
			else
			{
				clearaddress();
				Form.Submit.click();
                return;
			}

		}

		var sAddress = "";
		if (Form.addr.checked)
		{
			sAddress = Form.address.value;

			if (sAddress == "")
			{
				Form.address.focus();
				alert ("Please enter an address");
			}
			else
			{
				clearlocation();
				Form.Submit.click();
			}

		}
	}

	<!--Modified by Harshad on 8/8/05 for enahncement request -->
	<!--Function call to determine pre selection of Location/Address radio -->
	function OnAfterFormFill()
	{
		CheckUnitsForPreFillFields(Form.unitname1.value ,Form.location1.value,Form.address1.value,Form.city1.value,Form.postalcode1.value,Form.roomapt1.value,Form.building1.value,Form.state1.value);
		CheckUnitsForPreFillFields(Form.unitname2.value ,Form.location2.value,Form.address2.value,Form.city2.value,Form.postalcode2.value,Form.roomapt2.value,Form.building2.value,Form.state2.value);
		CheckUnitsForPreFillFields(Form.unitname3.value ,Form.location3.value,Form.address3.value,Form.city3.value,Form.postalcode3.value,Form.roomapt3.value,Form.building3.value,Form.state3.value);
		CheckUnitsForPreFillFields(Form.unitname4.value ,Form.location4.value,Form.address4.value,Form.city4.value,Form.postalcode4.value,Form.roomapt4.value,Form.building4.value,Form.state4.value);
	}

	<!--Function to check the unit names and pre fill the Form fields for relevant unit -->
	function CheckUnitsForPreFillFields(unitname,location,address,city,postalcode,roomapt,building,state)
	{
		//Check for the unit name and prefill the location/ Address details
		//with the hidden fields value. If location is present, location value should be
		//pre selected with location radio checked.
		if (Form.unitname.value == unitname )
		{
			//Fill Address fields
			Form.address.value = address;
			Form.city.value = city;
			Form.postalcode.value = postalcode;
			Form.roomapt.value = roomapt;
			Form.building.value = building;
			//Check for State
			if (state.length > 0)
			{
				Form.state.value = state;
			}
			//Check relevant radio
			//if Location has value, check the location radio, else check Address radio
			if (location.length > 0)
			{
				Form.location.selectedIndex = ReturnSelectedLocationIndex(location);
				Form.loc.checked = true;
			}
			else
			{
				Form.addr.checked = true;
			}
		}
	}

	<!--Function to return index of option in Select box for given location text -->
	function ReturnSelectedLocationIndex(OptionText)
	{
		var x;
		for (x=0;x < Form.location.length;x++){
			if (Form.location.options(x).text == OptionText)
			{
				return x;
			}
		}
		return -1;
	}




<!--End Modifications by Harshad -->

	</script>
</HTML>
