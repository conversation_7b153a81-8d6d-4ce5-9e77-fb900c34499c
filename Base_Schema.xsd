<?xml version="1.0" encoding="utf-8" ?>
<xs:schema id="Mobile_Schema" targetNamespace="Mobile_Schema" xmlns="Mobile_Schema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:mstns="Mobile_Schema" elementFormDefault="qualified">
  <xs:complexType name="header_Type">
    <xs:sequence>
      <xs:element name="unique_transaction_id" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="timestamp" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
      <xs:element name="ack_required" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="expiration_minutes" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="acked_unique_id" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
    <xs:attribute name="protocol_version" use="required">
      <xs:simpleType>
        <xs:restriction base="xs:string">
          <xs:enumeration value="23.3.15" />
        </xs:restriction>
      </xs:simpleType>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="avl_Type">
    <xs:sequence>
      <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="1" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="1" maxOccurs="1" />
      <xs:element name="speed" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="heading" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="altitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="cross_street" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="snapped_latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="snapped_longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="snapped_distance" type="xs:double" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ClientStateType">
    <xs:sequence>
      <xs:element name="IgnoreRecordsCheckResponses" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="NoPendingIncidentsToMobile" type="xs:boolean" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="None">
    <xs:restriction base="xs:string">
      <xs:maxLength value="0" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="response_Type">
    <xs:sequence>
      <xs:element name="success" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="sender_unique_id" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="error_message" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="facilityDivert_Type">
    <xs:sequence>
      <xs:element name="LocationName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="StartDate" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="EndDate" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Comments" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Reason" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" />
            <xs:element name="Protocols" type="xs:string" minOccurs="1" maxOccurs="1" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="mailrecipient_SubType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="CC" />
      <xs:enumeration value="BCC" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="priority_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="NORMAL" />
      <xs:enumeration value="LOW" />
      <xs:enumeration value="HIGH" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="mail_Type">
    <xs:sequence>
      <xs:element name="mail_type" minOccurs="1" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="INSTANT" />
            <xs:enumeration value="MESSAGEBOX" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="folder_id" type="xs:int" minOccurs="0" maxOccurs="1"/>
      <xs:element name="mail_id" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="sent_time" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
      <xs:element name="from_user" minOccurs="1" maxOccurs="1">
        <xs:complexType>
          <xs:simpleContent>
            <xs:extension base="xs:string">
              <xs:attribute name="cad_user" type="xs:boolean" use="optional" />
              <xs:attribute name="mailBoxCode" type="xs:string" use="optional" />
            </xs:extension>
          </xs:simpleContent>
        </xs:complexType>
      </xs:element>
      <xs:element name="priority" type="priority_Type" minOccurs="1" maxOccurs="1" />
      <xs:element name="subject" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="mail_body" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="attachment" type="FileType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="to_user" type="mailrecipient_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="to_cad_user" type="mailrecipient_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="to_unit" type="mailrecipient_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="to_group" type="mailrecipient_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="to_sector" type="mailrecipient_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="to_incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="isRead" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="include_sectors_controlling_dispatcher" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="send_to_controlling_dispatcher" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="controller_dispatcher_type" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="send_as_user" type="xs:boolean" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="NonEmptyString">
    <xs:restriction base="xs:string">
      <xs:minLength value="1" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="status_Type">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="incidentid" type="xs:string" use="optional" />
        <xs:attribute name="incident_number" type="xs:string" use="optional" />
        <xs:attribute name="incident_priority" type="xs:string" use="optional" />
        <xs:attribute name="timestamp" type="xs:dateTime" use="optional" />
        <xs:attribute name="destinationLatitude" type="xs:double" use="optional" />
        <xs:attribute name="destinationLongitude" type="xs:double" use="optional" />
        <xs:attribute name="destination" type="xs:string" use="optional" />
        <xs:attribute name="isRelatedIncident" type="xs:string" use="optional" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="assignedunit_Type">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="response_priority" type="xs:string" use="optional" />
        <xs:attribute name="response_number" type="xs:string" use="optional" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="CrewType">
    <xs:sequence>
      <xs:element name="user" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PersonName">
    <xs:sequence>
      <xs:element name="firstName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="middleName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="lastName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="suffix" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="AssignedUnitType">
    <xs:sequence>
      <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="code" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Crew" type="CrewType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="response_priority" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="response_number" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="IsMobile" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <!--this is the status ID-->
      <xs:element name="current_status" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="agency" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="radio_name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="vehicle_id" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_preassigned" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_assigned" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_acknowledged" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_enroute" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_arrivedAtScene" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_staged" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_contact" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_departedScene" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_delayedAvail" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_callCleared" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_backInQrtrs" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_availAtScene" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="unit_Alias" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="vehicle_Name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="personnel_Count" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="current_ETA" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="time_Status_Changed" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="location" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="locationLat" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="locationLong" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="destinationLat" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="destinationLong" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="destination" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="meets_Criteria" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="unit_Responsibility" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="mailrecipient_Type">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="recipient_level" type="mailrecipient_SubType" use="optional" />
        <xs:attribute name="recipient_MailCode" type="xs:string" use="optional" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:simpleType name="action_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="ADD" />
      <xs:enumeration value="REMOVE" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="error_Type">
    <xs:sequence>
      <xs:element name="message_received" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="sender_unique_id" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="error_message" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="incident_Type">
    <xs:sequence>
      <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="agency" type="xs:string" minOccurs="0" maxOccurs="1"  />
      <xs:element name="timestamp" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
      <xs:element name="priority" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="isAnonymousCaller" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="incident_status" minOccurs="0" maxOccurs="1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="ACTIVE" />
            <xs:enumeration value="WAITING" />
            <xs:enumeration value="CLOSED" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="hierarchy_level" type="hierarchylevel_Type" minOccurs="0" maxOccurs="1" />
      <xs:element name="sector" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="incident_number" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="case_number" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="incident_type" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="premise_info" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="preplan" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="prem_hist" minOccurs="0" maxOccurs="1">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="incident" minOccurs="0" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
                  <xs:element name="incident_number" type="xs:string" minOccurs="0" maxOccurs="1" />
                  <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
                  <xs:element name="problem_nature" type="xs:string" minOccurs="0" maxOccurs="1" />
                  <xs:element name="call_disp" type="xs:string" minOccurs="0" maxOccurs="1" />
                  <xs:element name="additional_info" type="xs:string" minOccurs="0" maxOccurs="1" />
                  <xs:element name="match_type" type="xs:string" minOccurs="0" maxOccurs="1" />
                  <xs:element name="agency" type="xs:string" minOccurs="0" maxOccurs="1"/>
                  <xs:element name="location" type="xs:string" minOccurs="0" maxOccurs="1"/>
                  <xs:element name="distance" type="xs:double" minOccurs="0" maxOccurs="1"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="partial_list" type="xs:boolean" minOccurs="0" maxOccurs="1" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="problem_nature" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="location_name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="crossing_street" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="apartment" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="state" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="zip" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="address_occurrence" type="address_Type" minOccurs="0" maxOccurs="1" />
      <xs:element name="tagIds" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="permits" type="PermitType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="permits_provided" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="hazards" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="caution_notes" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="CautionNotesProvided" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="CautionNotes" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="priority" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="type" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="source" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="category" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="location" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="distance" type="xs:string" minOccurs="0" maxOccurs="1" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="caller_name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <!-- caller_address: including city, state and zip-->
      <xs:element name="caller_address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="caller_phone" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="caller_phone2" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="assigned_units" type="assignedunit_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="assigned_other" type="assignedunit_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="primary_unit" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="stacked_unit" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="comment" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
            <xs:element name="author" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="text" type="xs:string" minOccurs="1" maxOccurs="1" />
            <xs:element name="IsConfidential" type="xs:boolean" minOccurs="0" maxOccurs="1" />
            <xs:element name="sequenceNumber" type="xs:int" minOccurs="0" maxOccurs="1" />
            <xs:element name="IsHighPriority" type="xs:boolean" minOccurs="0" maxOccurs="1" />
            <xs:element name="CommentCategory" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="IsPinned" type="xs:boolean" minOccurs="0" maxOccurs="1" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="append_comments" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="update_comment" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="field" type="field_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name ="AttachmentsProvided" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="attachment" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="ID" type="xs:int" minOccurs="1" maxOccurs="1" />
            <xs:element name="description" type="xs:string" minOccurs="1" maxOccurs="1" />
            <!-- the file type (this is not the file extension) -->
            <xs:element name="type" type="xs:string" minOccurs="1" maxOccurs="1" />
            <!-- the file size in bytes -->
            <xs:element name="size" type="xs:int" minOccurs="1" maxOccurs="1" />
            <!-- the file creation time -->
            <xs:element name="timestamp" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
            <xs:element name="fileName" type="xs:string" minOccurs="1" maxOccurs="1" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="SupplementPerson" type="SupplementType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name ="SupplementPersonProvided" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="SupplementVehicle" type="SupplementType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name ="SupplementVehicleProvided" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="SupplementProperty" type="SupplementType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name ="SupplementPropertyProvided" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="SupplementWeapon" type="SupplementType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name ="SupplementWeaponProvided" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="StreetID" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="CommentConfidentialityConfiguration" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="AssignedUnits" type="AssignedUnitType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="hydrants" type="hydrant_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="hydrants_provided" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
      <xs:element name="RemovedUnits" type="AssignedUnitType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="AddedOrUpdatedUnits" type="AssignedUnitType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="ActivityLog" type="activityLog_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="SummaryLog" type="summaryLog_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="LinkedIncidents" type="incident_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="modify_closed" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="MultiLocationIncidents" type="MultiLocationIncident" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="Cameras" type="camera_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="PersonnelCount" type="xs:int" minOccurs="0" maxOccurs="1"/>
      <xs:element name="Transports" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="IncidentTransportID" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="TransportProtocol" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportToLocation" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportToAddress" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportToCity" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportToState" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportToZip" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="PickUpAddressInfo" type="address_Type" minOccurs="0" maxOccurs="1" />
            <xs:element name="PatientLastName" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="PatientFirstName" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportToApartment" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportToBuilding" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="OdometerAtScene" type="xs:decimal" minOccurs="0" maxOccurs="1" />
            <xs:element name="OdometerAtDestination" type="xs:decimal" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportMileage" type="xs:decimal" minOccurs="0" maxOccurs="1" />
            <xs:element name="TransportPriority" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="Unit" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="DepartSceneTime" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
            <xs:element name="AtDestinationTime" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
            <xs:element name="AvailableTime" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="DuplicateIncidentCount" type="xs:int" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="MultiLocationIncident">
    <xs:sequence>
      <xs:element name="multiLocationIncidentID" type="xs:int" minOccurs="1" maxOccurs="1" />
      <xs:element name="incidentID" type="xs:int" minOccurs="1" maxOccurs="1" />
      <xs:element name="jurisdiction" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="division" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="locationName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="crossStreet" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="building" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="apartment" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="responseArea" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="incidentType" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="problem" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="state" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="county" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="streetID" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="premiseID" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="locationIndex" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="assignedUnits" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="camera_Type">
    <xs:sequence>
      <xs:element name="IncidentCameraID" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="IncidentID" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="CameraIdentifier" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="CameraName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="CameraAccessURL" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="DistanceInMeters" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="Latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="Longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="address_Type">
    <xs:sequence>
      <xs:element name="locationName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="crossStreet" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="building" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="apartment" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="state" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="zip" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="responseArea" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="streetID" type="xs:int" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <!--<xs:complexType name="IncidentTagType">
    <xs:sequence>
      <xs:element name="Description" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="IsHighPriority" type="xs:boolean" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>-->
  <xs:complexType name="PermitType">
    <xs:sequence>
      <xs:element name="PermitID" type="xs:int" minOccurs="1" maxOccurs="1" />
      <xs:element name="Type" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="PermitNumber" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="PrimaryContact" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="StartDate" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="EndDate" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="PhoneNumber" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="PermitStatus" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="LocationName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Apartment" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Building" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="City" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="State" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="ZipCode" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="County" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="PhoneNumber2" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="AlternateContact" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="AlternatePhoneNumber" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="AlternatePhoneNumber2" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="AlarmCompany" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="AlarmPhoneNumber" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Comments" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="Distance" type="xs:double" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SupplementType">
    <xs:sequence>
      <xs:element name="ID" type="xs:int" minOccurs="1" maxOccurs="1" />
      <xs:element name="field" type="field_Type" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="closeincident_Type">
    <xs:sequence>
      <xs:element name="incidentid" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="timestamp" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
      <xs:element name="hierarchy_level" type="hierarchylevel_Type" minOccurs="0" maxOccurs="1" />
      <xs:element name="sector" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="message" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="unitupdate_Type">
    <xs:sequence>
      <!-- unit_id is the CAD mdt_id -->
      <xs:element name="unit_id" type="xs:string" minOccurs="0" maxOccurs="1" />
      <!-- unit_name is the CAD radio name -->
      <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="current_sector" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="home_sector" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="agency" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="agencyID" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="current_status" type="status_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="off_duty" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="user" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="avl" type="avl_Type" minOccurs="0" maxOccurs="1" />
      <xs:element name="field" type="field_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="RadioLocation" type="RadioLocationType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="incident_fields" type="SupplementType" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="primaryResourceID" type="xs:int" minOccurs="0" maxOccurs="1" />
      <xs:element name="primaryResourceName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="unit_alias" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="home_station" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="avlEnabled" type="xs:boolean" minOccurs="0" maxOccurs="1" />

      <xs:element name="certificationType" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="homeBattalion" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="mdtEnabled" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="outOfVehicle" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="primaryRadioChannel" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="shiftStart" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="shiftEnd" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="station" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="supervisorRadioName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="unitCode" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="RadioLocationType">
    <xs:sequence>
      <xs:element name="RadioID" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="RadioName" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="PersonName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="1" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="1" maxOccurs="1" />
      <xs:element name="Requestor" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="field_Type">
    <xs:sequence>
      <xs:element name="field_name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="field_value" type="xs:string" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="hierarchylevel_Type">
    <xs:sequence>
      <xs:element name="hierarchy_level" type="hierarchylevel_Type" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="optional" />
    <xs:attribute name="name" type="xs:string" use="required" />
  </xs:complexType>
  <xs:complexType name="hierarchy_Type">
    <xs:sequence>
      <xs:element name="hierarchy_level" type="hierarchy_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="unit" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="optional" />
    <xs:attribute name="name" type="xs:string" use="required" />
  </xs:complexType>
  <xs:complexType name="user_Type">
    <xs:sequence>
      <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="agency" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="requestincident_Type">
    <xs:sequence>
      <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="form_request_Type">
    <xs:sequence>
      <xs:element name="source_form" type="xs:string" />
      <xs:element name="destination_form" type="xs:string" />
      <xs:element name="formfill" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="local" />
            <xs:enumeration value="remote" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="queryfile" type="xs:string" />
      <xs:element name="fields" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="field" minOccurs="0" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="field_id" type="xs:string" />
                  <xs:element name="field_value" type="xs:string" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="request_parameter" type="xs:string" minOccurs="0" />
      <xs:element name="reference_id" type="xs:string" minOccurs="0" />
      <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="form_response_Type">
    <xs:sequence>
      <xs:element name="form_name" type="xs:string" />
      <xs:element name="form_location" type="xs:string" />
      <xs:element name="priority" type="priority_Type" minOccurs="0" />
      <xs:element name="datasource" type="xs:string" minOccurs="0" />
      <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="form_data" type="xs:string" />
      <xs:element name="formfill" minOccurs="0">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="local" />
            <xs:enumeration value="remote" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="form_parent" type="xs:string" minOccurs="0" />
      <xs:element name="request_parameter" type="xs:string" minOccurs="0" />
      <xs:element name="images" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="image" minOccurs="1" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="imagename" type="xs:string" />
                  <xs:element name="imagelink" type="xs:string" />
                  <xs:element name="imagedata" type="xs:base64Binary" />
                  <xs:element name="datasource" type="xs:string" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="reference_id" type="xs:string" minOccurs="0" />
      <xs:element name="unit_name" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="radio_Type">
    <xs:sequence>
      <xs:element name="ID" type=" xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="code" type=" xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="pager_Type">
    <xs:sequence>
      <xs:element name="provider" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="ID" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="number" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="shift_info_Type">
    <xs:sequence>
      <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="unit_temp_capabilities" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="unit_temp_radio" type="radio_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="unit_temp_pager" type="pager_Type" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="personnel_shift_info" minOccurs="0" maxOccurs="unbounded" >
        <xs:complexType>
          <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="unbounded" />
            <xs:element name="temp_radio" type="radio_Type" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="temp_pager" type="pager_Type" minOccurs="0" maxOccurs="unbounded" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="modify_personnel" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="modify_unit_capabilities" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="modify_unit_pager" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="modify_unit_radio" type="xs:boolean" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="FileType">
    <xs:sequence>
      <xs:element name="file" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
      <xs:element name="file_name" type="xs:string" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="EmployeeRadioType">
    <xs:sequence>
      <xs:element name="empRecID" type="xs:integer" minOccurs="0" maxOccurs="1" />
      <xs:element name="radioID" type="xs:integer" minOccurs="0" maxOccurs="1" />
      <xs:element name="radioCode" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="radioDescription" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="TelephonyCallType">
    <xs:sequence>
      <xs:element name="TelephonyCallID" type="xs:int" minOccurs="1" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="1" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="1" maxOccurs="1" />
      <xs:element name="caller_phone" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="caller_name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="RingTime" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="IsCleared" type="xs:boolean" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="hydrant_Type">
    <xs:sequence>
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="state" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="zip" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="activityLog_Type" >
    <xs:sequence>
      <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="activity" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="location" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="comment" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="radio_name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="dispatcher_init" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="summaryLog_Type" >
    <xs:sequence>
      <xs:element name="incidentsummarylogID" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="agencyID" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="incidentID" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="incidentvehicleassignedID" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="logtime" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="createdBy" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="fields" type="field_Type" minOccurs="0" maxOccurs="30" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="unit_details_Type">
    <xs:sequence>
      <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="agency" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="all_capabilities" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="beat" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="current_city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="current_division" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="current_jurisdiction" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="current_latitude" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="current_longitude" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="current_location" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="current_sector" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="destination_location" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="destination_city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="home_jurisdiction" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="home_division" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="home_battalion" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="home_station" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="primary_radio_channel" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="resource_type" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="radio_channels" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="secondary_resource_type" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="staffing" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="status" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="vehicle_capabilities" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
      <xs:element name="vehicle_certification_type" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="vehicle_id" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="vehicle_name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="unit_alias" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="personnelCount" type="xs:int" minOccurs="1" maxOccurs="1" />
      <xs:element name="personnel_assigned" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
            <xs:element name="position" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="capabilities" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="call_history" minOccurs="0" maxOccurs="unbounded" >
        <xs:complexType>
          <xs:sequence>
            <xs:element name="problemnature" type="xs:string" minOccurs="1" maxOccurs="1" />
            <xs:element name="currentstatus" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="incidentnumber" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="unitsassigned" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="datetimereceived" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="currentsector" type="xs:string" minOccurs="0" maxOccurs="1" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="avlEnabled" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="mdtEnabled" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="outOfVehicle" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="shiftStart" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="shiftEnd" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="station" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="supervisorRadioName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="unitCode" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="code_Info">
    <xs:sequence>
      <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="id" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="camera_settings">
    <xs:sequence>
      <xs:element name="camera_access" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="auto_close_timer" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="enable_auto_close" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="enable_session_extension" type="xs:boolean" minOccurs="0" maxOccurs="1" />
      <xs:element name="camera_access_url_endpoint" type="xs:string" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="premiseLocation_Type">
    <xs:sequence>
      <xs:element name="id" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="code" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="apartment" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="state" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="postalcode" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="building" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="crossstreet" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="locationtype" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="phone" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="preplanreference" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="billcode" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="facilitydiversionreason" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="start" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="end" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="comments" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="priorHistory_Type">
    <xs:sequence>
      <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="incident_number" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
      <xs:element name="problem_nature" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="call_disp" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="additional_info" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="match_type" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="agency" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="location" type="xs:string" minOccurs="0" maxOccurs="1"/>
      <xs:element name="distance" type="xs:double" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="HydrantStationType">
    <xs:sequence>
      <xs:element name="distance" type="xs:double" minOccurs="0" maxOccurs="1"/>
      <xs:element name="code" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="state" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="zip" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="crossStreet" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="detailed_Info" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PremiseLocationType">
    <xs:sequence>
      <xs:element name="distance" type="xs:double" minOccurs="0" maxOccurs="1"/>
      <xs:element name="premiseCode" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="premiseName" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="state" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="zip" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="crossStreet" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="phone" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="prePlan" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="billingCode" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="locationType" type="xs:string" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <!--each element must be added to C:\vss\Command_x\VisiNet Mobile\Installation and Upgrade\Mobile Server\Files\MobileServer\LatestVersion\XmlCompressionDefinition.xml-->
  <!--element name should be Pascal, unless the name is already used somewhere in this doc, then we have to use existing one.-->

</xs:schema>
