<?xml version="1.0" encoding="utf-8" ?>
<xs:schema id="SchemaMailbox"
                  targetNamespace="http://tempuri.org/SchemaMailbox.xsd"
                  elementFormDefault="qualified"
                  xmlns="http://tempuri.org/SchemaMailbox.xsd"
                  xmlns:mstns="http://tempuri.org/SchemaMailbox.xsd"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:complexType name="ExporterType">
    <xs:sequence>
      <xs:element name="Enabled" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="DestinationFolder" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="FileNamePattern" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="AutoExport" type="xs:boolean" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SpeakerType">
    <xs:sequence>
      <xs:element name="Enabled" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="SpeechPattern" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="AutoSpeakHighPriority" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="AutoSpeakNonHighPriority" type="xs:boolean" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="ParserFormatType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="RawData" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="ParserDefinitionType">
    <xs:sequence>
      <xs:element name="Enabled" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="ParserDescription" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="ParserRegex" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="ParserRegexForMultipleMatches" type="xs:string" minOccurs="0" maxOccurs="1" />
      <xs:element name="ParserFormat" type="ParserFormatType" minOccurs="1" maxOccurs="1" />
      <xs:element name="ParserPlugin" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="Exporter" type="ExporterType" minOccurs="0" maxOccurs="1" />
      <xs:element name="Speaker" type="SpeakerType" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="RecordCheckParserType">
    <xs:sequence>
      <xs:element name="Enabled" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="ParserDefinition" type="ParserDefinitionType" minOccurs="0" maxOccurs="unbounded" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="InboxType">
    <xs:sequence>
      <!-- in the future, add here buttons configuration for inbox -->
      <xs:element name="AutoSpeakNonSystemHighPriorityMailMessages" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="AutoSpeakSystemHighPriorityMailMessages" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="AutoSpeakNonSystemNonHighPriorityMailMessages" type="xs:boolean" minOccurs="1" maxOccurs="1" />
      <xs:element name="AutoSpeakSystemNonHighPriorityMailMessages" type="xs:boolean" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MailboxConfiguration">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="RecordCheckParser" type="RecordCheckParserType" minOccurs="1" maxOccurs="1" />
        <xs:element name="Inbox" type="InboxType" minOccurs="1" maxOccurs="1" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
