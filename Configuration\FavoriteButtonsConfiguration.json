{"Buttons": [{"$type": "VSI.Mobile.Client.Controls.Models.NavigationButton, InformMobile", "Identifier": 45, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.NavigationButton, InformMobile", "Identifier": 38, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.NavigationButton, InformMobile", "Identifier": 39, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.NavigationButton, InformMobile", "Identifier": 47, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.NavigationButton, InformMobile", "Identifier": 21, "MobileAction": 3}], "StateType": "State.xml", "QuickPlateCheckLicensePlateCode": "LIC", "QuickPlateCheckLicenseStateCode": "LIS", "AssociateRecordCheckWithIncidentByDefault": true, "IsEnabled": true, "VersionNumber": 5}