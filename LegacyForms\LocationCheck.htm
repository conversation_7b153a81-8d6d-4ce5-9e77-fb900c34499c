<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Location Check</TITLE><LINK href="normalstyle.css" type="text/css" rel="stylesheet">
			<script language="javascript" id="clientEventHandlersJS">
			</script>
	</HEAD>
	<body>
        <table class="base" id="Table1" cellpadding="10" align="center" border="0">
            <tbody>
                <tr>
                    <td valign="top">
                        <h4 align="center">Location Check</h4>
                        <form id="Form" name="Form" action="LocationCheck.aspx?queryfile=LocationCheck.qry" method="post">
                            <p>&nbsp;</p>
                            <p>
                                <table>
                                    <tr>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td width="25%">Street Number: </td>
                                                    <td><input id="StreetNumber" type="text" maxlength="20" size="20" name="StreetNumber"></td>
                                                </tr>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td></td>
                                                </tr>
                                                <tr>
                                                    <td width="25%">Street Name: </td>
                                                    <td><input id="StreetName" type="text" maxlength="50" size="50" name="StreetName"></td>
                                                </tr>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td></td>
                                                </tr>
                                                <tr>
                                                    <td width="25%">City: </td>
                                                    <td><input id="City" type="text" maxlength="30" size="30" name="City"></td>
                                                </tr>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
		                    </p>
                            <!--when click on this button, it call the validation.-->
                            <input type="button" name="Query" id="Button" value="Submit" onkeypress="validatepage()" onclick="validatepage()" />
	                        <!--when validation is ok, it will call this button to submit the page.  This button is not visible because of it width is zero.-->
                            <input style="width: 0px;" tabindex="-1" type="submit" name="Submit" id="Submit" value="Submit" />
                        </form>
                    </TD>
                </TR>
            </TBODY>
        </TABLE>
		<script src="clientutilities.js"></script>
		<SCRIPT language="javascript">
		function window.onload()
		{
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.StreetNumber.focus();
			PrepareValidation(Form);
		}

		function window.validatepage()
		{
		    var sStreetNumber = "";
		    var sStreetName = "";
		    var sCity="";

		    sStreetNumber = Form.StreetNumber.value;
		    sStreetName = Form.StreetName.value;
		    sCity = Form.City.value;
		    if(sStreetNumber =="")
		    {
		        Form.StreetNumber.focus();
		        alert ("Please enter street number");
		    }
		    else if(sStreetName =="")
		    {
		        Form.StreetName.focus();
		        alert ("Please enter street name");
		    }
		    else if(sCity =="")
		    {
		        Form.City.focus();
		        alert ("Please enter city");
		    }
		    else
		    {
		        Form.Submit.click();
		    }	
		}

		</SCRIPT>
	</body>
</HTML>

