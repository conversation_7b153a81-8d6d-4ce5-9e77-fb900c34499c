<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Reverse Phone Search</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center"><input type="button" value="Submit" onclick='Form.Query.click()'>&nbsp;&nbsp;&nbsp;Reverse Phone Search</H4>
						<form action="ReversePhoneSearch.aspx?queryfile=ReversePhoneSearch.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td>Area Code:</td>
									<td><input type="text" SIZE="3" maxlength="3" name="AreaCode" id="AreaCode" numeric="true"></td>
								</tr>
								<tr>
									<td>Phone #:</td>
									<td><input type="text" SIZE="7" maxlength="7" name="Phone" id="Phone" numeric="true"></td>
								</tr>
								<tr>
									<td>Address:</td>
									<td><input type="text" name="Address" id="Address"></td>
								</tr>
								<tr>
									<td>City:</td>
									<td><input type="text" name="City" id="City"></td>
								</tr>
								<tr>
									<td>State:</td>
									<td><input type="text" SIZE="2" maxlength="2" name="State" id="State"></td>
								</tr>
								<tr>
									<td>Zip:</td>
									<td><input type="text" SIZE="5" maxlength="5" name="Zip" id="Zip" numeric="true"></td>
								</tr>
								<tr>
									<td colspan=2>Note: The use of partial is acceptable</td>
								</tr>
							</table>
							
							<br>
							<input type="button" name="Query" id="Query" value="Submit" onkeypress="ProcessAndSubmit()" onclick="ProcessAndSubmit()">
							<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
							<input type="hidden" name="Comment" id="Comment">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<SCRIPT language="javascript">
		function window.onload()
		{
			Form.AreaCode.focus();
		}
	
		function window.ProcessAndSubmit()
		{
			// set the activity log
			Form.Comment.value = 'Reverse Phone Search query was submitted with parameters:';
			if (Form.AreaCode.value != '')
			{
				Form.Comment.value =  Form.Comment.value +  ' Area Code=' + Form.AreaCode.value;
			}
			if (Form.Phone.value != '')
			{
				Form.Comment.value =  Form.Comment.value +  ' Phone=' + Form.Phone.value;
			}
			if (Form.Address.value != '')
			{
				Form.Comment.value =  Form.Comment.value +  ' Address=' + Form.Address.value;
			}
			if (Form.City.value != '')
			{
				Form.Comment.value =  Form.Comment.value +  ' City=' + Form.City.value;
			}
			if (Form.State.value != '')
			{
				Form.Comment.value =  Form.Comment.value +  ' State=' + Form.State.value;
			}
			if (Form.Zip.value != '')
			{
				Form.Comment.value =  Form.Comment.value +  ' Zip=' + Form.Zip.value;
			}
			Form.Submit.click();
		}
	</SCRIPT>
</HTML>
