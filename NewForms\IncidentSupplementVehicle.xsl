<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>Mobile Enterprise - Incident Supplement Vehicle</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Supplement Vehicle</H4>
						<P><h3>Vehicle Supplement Info for Incident Number : <xsl:value-of select="/results/exemel/NewDataSet/Table/IncidentNumber"/></h3>

	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
							
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
		
	<table cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;color:white;border-width:1px;border-style:None;">
		<tr style="font-weight:bold;">	
			<xsl:if test="LicensePlateNumber!= ''">
				<td style="color:windowtext;background-color:window;" colspan="3" >
				<xsl:value-of select="LicensePlateNumber"/>
				<xsl:if test="LicenseState!= ''">
					<xsl:value-of select="concat(' - ', LicenseState)"/>
				</xsl:if>
				<xsl:if test="LicensePlateType != ''">
				<xsl:value-of select="concat(' - ',LicensePlateType)"/>
				</xsl:if>
				</td>
			</xsl:if>
		</tr>
		<tr style="font-weight:bold;">
			<xsl:if test="Color1!= ''">
				<td>Color1</td><td style="color:windowtext;background-color:window;"  >
				<xsl:value-of select="Color1"/></td>
			</xsl:if>		
			<xsl:if test="Color2!= ''">
				<td>Color2</td><td style="color:windowtext;background-color:window;"  >
				<xsl:value-of select="Color2"/></td>
			</xsl:if>			
			<xsl:if test="LicensePlateYear!= 0">
				<td>PlateYear</td><td style="color:windowtext;background-color:window;"  
				><xsl:value-of select="LicensePlateYear"/></td>
			</xsl:if>
			<xsl:if test="Make!= ''">
				<td>Make</td><td style="color:windowtext;background-color:window;"  >
				<xsl:value-of select="Make"/></td>
			</xsl:if>		
			<xsl:if test="Model!=''">
				<td>Model</td><td style="color:windowtext;background-color:window;"  >
				<xsl:value-of select="Model"/></td>
			</xsl:if>
			<xsl:if test="Style!=''">
				<td>Style</td><td style="color:windowtext;background-color:window;"  >
				<xsl:value-of select="Style"/></td>
			</xsl:if>
		</tr>
		<tr style="font-weight:bold;">
			<xsl:if test="MakeYear!= 0">
				<td>Make Year</td><td style="color:windowtext;background-color:window;"  >
				<xsl:value-of select="MakeYear"/></td>
			</xsl:if>
			<!--<xsl:if test="VehicleType != ''">
				<td>PlateType</td><td style="color:windowtext;background-color:window;"  >
				<xsl:value-of select="VehicleType"/></td>
			</xsl:if>-->	
			<xsl:if test="VIN != ''">
				<td>VIN</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="VIN"/></td>
			</xsl:if>						
			<xsl:if test="TowedBy!= ''">
				<td>TowedBy</td><td style="color:windowtext;background-color:window;" colspan="3" >
				<xsl:value-of select="TowedBy"/></td>
			</xsl:if>
		</tr>
		<tr style="font-weight:bold;">			
			<xsl:if test="Characteristics != ''">
				<td>Characteristics</td>
				<td style="color:windowtext;background-color:window;" colspan="3" >
				<xsl:value-of select="Characteristics"/>
				</td>
			</xsl:if>
		</tr>
		<tr style="font-weight:bold;">
			<xsl:if test="Comments != ''">
				<td>Comments</td>
				<td style="color:windowtext;background-color:window;" colspan="3"  >
				<xsl:value-of select="Comments"/>
				</td>
			</xsl:if>			
		</tr>
	</table>
	<p></p>	

</xsl:template> 

</xsl:transform>