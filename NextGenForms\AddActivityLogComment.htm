﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Add Activity Log Comment</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.min.css"/>

    <link rel="stylesheet" type="text/css" href="MobileStyle.css"/> 

    <script src="jquery.min.js"></script>
    <!-- Compiled and minified JavaScript -->
    <script src="materialize.min.js"></script>

    <script src="clientutilities.js"></script>

    <script language="javascript">
        $(document).ready(function () {

            //handle form submition
            $("#Form").submit(function () {

                if ($(this)[0].checkValidity() == true) {
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;

            });

            //focus on registration id field
            $("#Comment").focus();

        });

    </script>
    
</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">ADD ACTIVITY LOG COMMENT</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="main" action="AddActivityLogComment.aspx?queryfile=AddActivityLogComment.qry" method="post" id="Form" name="Form">
        <div class="row">
            <div class="s12">
                <div class="input-field col s12 m6" style="margin-top: 30px">
                    <textarea id="Comment" placeholder="" class="materialize-textarea" name="Comment"></textarea>
                    <label for="Comment" class="active">Comments</label>
                </div>
            </div>
        </div>
    </form>
</body>
</html>


