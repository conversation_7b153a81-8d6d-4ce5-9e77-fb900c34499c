<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Mobile_Schema" targetNamespace="Mobile_Schema" xmlns="Mobile_Schema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:mstns="Mobile_Schema" elementFormDefault="qualified">
  <xs:include schemaLocation="Base_Schema.xsd" />
  <xs:element name="vm_to_client">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="header" type="header_Type" minOccurs="1" maxOccurs="1" />
        <xs:element name="sender_unique_id" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="connect_confirm" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="key_inbound" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
              <xs:element name="key_outbound" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
              <xs:element name="return_random" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
              <xs:element name="random" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="login" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="login_timeout" type="xs:nonNegativeInteger" minOccurs="1" maxOccurs="1" />
              <xs:element name="inactivity_timeout" type="xs:nonNegativeInteger" minOccurs="1" maxOccurs="1" />
              <xs:element name="first_name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="last_name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="customer_name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="change_password_reason" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="PasswordExpirationWarning" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="unit_on_duty_confirm" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="success" type="xs:boolean" minOccurs="1" maxOccurs="1" />
              <xs:element name="current_status" type="status_Type" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="current_sector" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="home_sector" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="sender_unique_id" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="error_message" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="agency" type="xs:string" minOccurs="0" maxOccurs="1"  />
              <xs:element name="home_agency" type="code_Info" minOccurs="0" maxOccurs="1"  />
              <xs:element name="home_jurisdiction" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="current_jurisdiction" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="home_division" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="current_division" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="home_battalion" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="current_battalion" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="home_station" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="home_sector_ci" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="current_sector_ci" type="code_Info" minOccurs="0" maxOccurs="1" />
              <xs:element name="unit_code" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="camera_settings" type="camera_settings" minOccurs="0" maxOccurs="1" />
              <xs:element name="Personnel" type="PersonName" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="VehicleCapabilities" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="unit_off_duty_confirm" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="success" type="xs:boolean" minOccurs="1" maxOccurs="1" />
              <xs:element name="current_status" type="status_Type" minOccurs="0" maxOccurs="1" />
              <xs:element name="sender_unique_id" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="error_message" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="incident_summary" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="incident" type="incident_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="incident_details" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="incident" type="incident_Type" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="change_key" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="new_incoming_key" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
              <xs:element name="new_outgoing_key" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="error" type="error_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="mail" type="mail_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="unit_update" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="sector" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="PartialList" type="xs:boolean" minOccurs="0" maxOccurs="1" />
              <!-- this tag is only for compatability with 2.1 . It is not used in 2.2 and up. -->
              <xs:element name="unit" type="unitupdate_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="change_status" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="success" type="xs:boolean" minOccurs="0" maxOccurs="1" />
              <xs:element name="current_status" type="status_Type" minOccurs="1" maxOccurs="1" />
              <xs:element name="current_sector" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="unassign_incident" type="xs:boolean" minOccurs="0" maxOccurs="1" />
              <xs:element name="sender_unique_id" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="message" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="diverts" type="facilityDivert_Type" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="fields" type="field_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="FacilityDivertResponse" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="locationName" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="diverts" type="facilityDivert_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="forms" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="client_transaction_id" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="priority" type="xs:boolean" minOccurs="1" maxOccurs="1" />
              <xs:element name="message" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="image_return" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <!--
		<xs:element name="image_identity" type="xs:positiveInteger" minOccurs="1" maxOccurs="1" />							-->
              <xs:element name="image_data" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="incident" type="incident_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="incident_update" type="incident_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="close_incident" type="closeincident_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="ack" type="None" minOccurs="0" maxOccurs="1" />
        <xs:element name="ack_confirm" type="None" minOccurs="0" maxOccurs="1" />
        <xs:element name="emergency" type="unitupdate_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="form_response" type="form_response_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="move_sector" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="move_home_sector" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="driving_directions" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="node" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="lat" type="xs:double" minOccurs="1" maxOccurs="1" />
                    <xs:element name="lon" type="xs:double" minOccurs="1" maxOccurs="1" />
                    <xs:element name="direction" minOccurs="0" maxOccurs="1">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="type" type="xs:string" minOccurs="1" maxOccurs="1" />
                          <!-- Maneuver Type -->
                          <xs:element name="text" type="xs:string" minOccurs="1" maxOccurs="1" />
                          <!--DDD<xs:element name="distance" type="xs:float" minOccurs="1" maxOccurs="1" />-->
                          <!-- distance to next direction -->
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="shift_info_response" type="shift_info_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="attachment" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" type="xs:int" minOccurs="1" maxOccurs="1" />
              <!-- the file name and size are only populated with the fist packet (when position is 0)  -->
              <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="size" type="xs:int" minOccurs="0" maxOccurs="1" />
              <!-- the position in bytes, within the file (0 based) that the data start from -->
              <xs:element name="position" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="data" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="attachment_upload_response" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="key" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="change_password" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="failure_reason" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="reset_password" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="failure_reason" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="FilesReadyForDownload" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FolderName" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CleanClientIncidentCacheAndUpdateStatus" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CurrentStatus" type="status_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RefreshUnitStatuses" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CurrentStatus" type="status_Type" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MapDataFiles" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FolderName" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="Files" type="FileType" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TelephonyCall" type="TelephonyCallType" minOccurs="0" maxOccurs="1" />
        <xs:element name="ConnectedMailboxes" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Mailbox" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="CADMailbox" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="GenericNotification" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Subject" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="Message" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="MessagePriority" type="xs:string" minOccurs="0" maxOccurs="1"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="GeoValidateAddressResults" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MaxResultsReached" type="xs:boolean" minOccurs="1" maxOccurs="1" />
              <xs:element name="ResultsMoreN" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="AddressMatch" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="Name" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="BlockRange" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="StreetName" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="Building" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="Apartment" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="City" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="County" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="StateAbbrev" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="Zip" type="xs:string" minOccurs="0" maxOccurs="1"/>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="unit_details" type="unit_details_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="IncidentSearchResults" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IncidentCount" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="Results" type="incident_Type" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="UnitSearchResults" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="UnitCount" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="Results" type="unit_details_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RadiusSearchResults" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TypeCount" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="Hydrants" type="HydrantStationType" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="Stations" type="HydrantStationType" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="PremiseLocations" type="PremiseLocationType" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BackUpUnitScreenResults" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IncidentID" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="UnitName" type="xs:string" minOccurs="1" maxOccurs="1"  />
              <xs:element name="Unit_Lat" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="Unit_Long" type="xs:double" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RequestMobileEnterpriseClientVersionResult" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="VersionNumber" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="MobileEnterpriseDownloadServicePath" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PremiseLocationSearchResults" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="PremiseLocationCount" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="Results" type="premiseLocation_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PriorHistorySearchResults" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="PriorHistoryCount" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="Results" type="priorHistory_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="LinkedIncidentDetailsResults" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="LinkedIncidentsCount" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="Results" type="incident_Type" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="LinkedIncidentsTotalCount" type="xs:int" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DismissNotification" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Message" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="IsManual" type="xs:boolean" minOccurs="1" maxOccurs="1" />
              <xs:element name="DismissType" type="xs:int" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ConfirmLogin" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Message" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="UpdateUnitLoginInfo" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Personnel" type="PersonName" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="VehicleCapabilities" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="AssignedUnit" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AssignedUnit" type="AssignedUnitType" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="client_to_vm">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="header" type="header_Type" minOccurs="1" maxOccurs="1" />
        <xs:element name="unit_info" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="avl" type="avl_Type" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="connect" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="device_id" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="random" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="connect_confirm" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="return_random" type="xs:base64Binary" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="login" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="login_name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="password" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="password_length" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="version" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="os_version" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="component" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
                    <xs:element name="version" type="xs:string" minOccurs="1" maxOccurs="1" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="OSN" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="OSP" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="new_password" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="new_password_length" type="xs:int" minOccurs="0" maxOccurs="1" />
              <xs:element name="unit_name" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="update_timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
              <xs:element name="stylesheet" minOccurs="0" maxOccurs="1" type="xs:boolean" />
              <xs:element name="max_bytes" type="xs:positiveInteger" minOccurs="0" maxOccurs="1" />
              <xs:element name="chunk_interval" type="xs:positiveInteger" minOccurs="0" maxOccurs="1" />
              <xs:element name="graphics_format" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="format_type" type="xs:string" />
                    <xs:element name="height" type="xs:positiveInteger" />
                    <xs:element name="width" type="xs:positiveInteger" />
                    <xs:element name="colors">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="greyscale" type="xs:boolean" use="optional" />
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="formfill" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:enumeration value="generic" />
                    <xs:enumeration value="specific" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="async_wait" type="xs:positiveInteger" minOccurs="0" maxOccurs="1" />
              <xs:element name="ClientState" type="ClientStateType" minOccurs="0" maxOccurs="1" />
              <xs:element name="NoPendingIncidentsToMobile" type="xs:boolean" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ClientStateInfo" type="ClientStateType" minOccurs="0" maxOccurs="1" />
        <xs:element name="unit_on_duty" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="sector" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="NoPendingIncidentsToMobile" type="xs:boolean" minOccurs="0" maxOccurs="1" />
              <xs:element name="field" type="field_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="unit_off_duty" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="field" type="field_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="avl_data" type="None" minOccurs="0" maxOccurs="1" />
        <xs:element name="request_incident" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:complexContent>
              <xs:extension base="requestincident_Type">
                <xs:attribute name="refresh_only" type="xs:boolean" use="optional" />
                <!-- true means: Just reply to Mobile Client. Don't forward the request to Mobile Interface -->
              </xs:extension>
            </xs:complexContent>
          </xs:complexType>
        </xs:element>
        <xs:element name="request_incident_details" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="incidentid" type="xs:string" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="forms" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="form_type" minOccurs="1" maxOccurs="unbounded">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:enumeration value="PROXY" />
                    <xs:enumeration value="IIS" />
                    <xs:enumeration value="CAD" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="field" minOccurs="1" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="value" type="xs:string" minOccurs="1" maxOccurs="1" />
                    <xs:element name="querytype" minOccurs="1" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="fieldname" type="xs:string" minOccurs="1" maxOccurs="1" />
                          <xs:element name="querycode" type="xs:string" minOccurs="1" maxOccurs="1" />
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="mail" type="mail_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="remove_mail" type="xs:int" minOccurs="0" maxOccurs="1" />
        <xs:element name="read_mail" type="xs:int" minOccurs="0" maxOccurs="1" />
        <xs:element name="logout" type="None" minOccurs="0" maxOccurs="1" />
        <xs:element name="request_update" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="include_home_sector" type="xs:boolean" minOccurs="0" maxOccurs="1" />
              <!-- don't return units with the given status(s) -->
              <xs:element name="exclude_status" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
              <!-- return units from other agencies when their current jurisdiction is in the given AddJurisdiction(s) -->
              <xs:element name="OtherAgenciesJurisdictions" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>

        <xs:element name="request_units" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <!-- filter groups -->
              <xs:element name="exclude_status" type="xs:int" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="include_jurisdictions" type="xs:int" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="include_divisions" type="xs:int" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="include_sectors" type="xs:int" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="include_resourceTypes" type="xs:int" minOccurs="0" maxOccurs="unbounded" />
              <xs:element name="include_capabilities" type="xs:int" minOccurs="0" maxOccurs="unbounded" />
              <!-- order of units returned  
                0 - Home, Agency, Other
                1 - Home, Other, Agency
                2 - Agency, Home, Other
                3 - Agency, Other, Home
                4 - Other, Home, Agency
                5 - Other, Agency, Home
              -->
              <xs:element name="order" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="incident_fields" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="request_unit_details" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="search_date_from" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
              <xs:element name="search_date_to" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="request_assigned_unit" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="incidentId" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="watch_list" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="sector" type="xs:string" minOccurs="1" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="change_status_request" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="status" type="status_Type" minOccurs="1" maxOccurs="1" />
              <xs:element name="field" type="field_Type" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="check_facility_divert" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="locationName" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="incident" type="incident_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="incident_update" type="incident_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="ack" type="None" minOccurs="0" maxOccurs="1" />
        <xs:element name="ack_confirm" type="None" minOccurs="0" maxOccurs="1" />
        <xs:element name="emergency" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="log_error" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="priority" type="priority_Type" minOccurs="1" maxOccurs="1" />
              <xs:element name="version" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="message" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="device_type" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="bad_message" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="message_id" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="message" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="form_request" type="form_request_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="driving_directions_request" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="from_lat" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="from_lon" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="to_lat" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="to_lon" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="destinationAddress" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="out_of_vehicle" type="None" minOccurs="0" maxOccurs="1" />
        <xs:element name="in_vehicle" type="None" minOccurs="0" maxOccurs="1" />
        <xs:element name="shift_info_update" type="shift_info_Type" minOccurs="0" maxOccurs="1" />
        <xs:element name="shift_info_request" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="attachment_request" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" type="xs:int" minOccurs="1" maxOccurs="1" />
              <!-- the position in bytes, within the file (0 based) that we should start send the data from -->
              <xs:element name="position" type="xs:int" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="attachment_upload" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <!-- the file name and size are only populated with the fist packet (when position is 0)  -->
              <xs:element name="incidentid" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="complete" type="xs:boolean" minOccurs="1" maxOccurs="1" />
              <xs:element name="key" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="data" type="xs:base64Binary" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="change_password" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="password" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="new_password" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="reset_password" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="login_name" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="password" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="reset_login_name" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="mag_stripe_parse_failure" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="message" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="unit_route_deviation" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="incidentid" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="latitude" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="longitude" type="xs:double" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="UnitAVLconnection" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="connected" type="xs:boolean" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Notification" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="message" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="incidentid" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="lat" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="lon" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RequestToDownloadFiles" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FolderName" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="Files" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="file" type="xs:string" minOccurs="1" maxOccurs="1" />
                    <xs:element name="timestamp" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
                    <!-- the position in bytes, within the file (0 based) that we should start send the data from -->
                    <xs:element name="position" type="xs:int" minOccurs="1" maxOccurs="1" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="EmpScheduleRadios" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="empScheduleRadio" type="EmployeeRadioType" minOccurs="1" maxOccurs="8" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RequestIncidentAndStatusResync" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MinutesDisconnected" type="xs:int" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RequestConnectedMailboxes" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="IncidentInView" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="incidentid" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="AddToActivityLog" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="timestamp" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
              <xs:element name="incidentid" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="category" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="message" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="VerifyLocation" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Latitude" type="xs:double" minOccurs="0" maxOccurs="1" />
              <xs:element name="Longitude" type="xs:double" minOccurs="0" maxOccurs="1" />
              <xs:element name="Address" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="City" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="ExpandSearchByNth" type="xs:int" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="UXInteractionLog" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="version" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="type" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="action" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ModifyIncidentSupplement" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="action" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:enumeration value="AddPerson" />
                    <xs:enumeration value="UpdatePerson" />
                    <xs:enumeration value="DeletePerson" />
                    <xs:enumeration value="AddVehicle" />
                    <xs:enumeration value="UpdateVehicle" />
                    <xs:enumeration value="DeleteVehicle" />
                    <xs:enumeration value="AddProperty" />
                    <xs:enumeration value="UpdateProperty" />
                    <xs:enumeration value="DeleteProperty" />
                    <xs:enumeration value="AddWeapon" />
                    <xs:enumeration value="UpdateWeapon" />
                    <xs:enumeration value="DeleteWeapon" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="incidentid" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="supplement" type="SupplementType" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ToggleRadioTrackingRequest" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="incidentIDs" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="unitIDs" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="startRadioTracking" type="xs:boolean" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="IncidentSearch" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="agencyName" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="fromDateTime" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
              <xs:element name="toDateTime" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
              <xs:element name="lastThreeDigitsOfIncidentID" type="xs:int" minOccurs="0" maxOccurs="1" />
              <xs:element name="callTaker" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="masterIncidentNumber" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="caseNumber" type="xs:string" minOccurs="0" maxOccurs="1"/>
              <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="zip" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="disposition" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="incidentNumber" type="xs:int" minOccurs="0" maxOccurs="1" />
              <xs:element name="incidentPriority" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="incidentType" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="jurisdiction" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="division" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="locationName" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="map" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="problem" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="responseArea" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="currentSectorID" type="xs:int" minOccurs="0" maxOccurs="1" />
              <xs:element name="personnelIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="primaryUnit" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="assignedUnits" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="tagIds" type="xs:string" minOccurs="0" maxOccurs="1"/>
              <xs:element name="resultParameters" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
              <xs:element name="phoneNumber" type="xs:long" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RequestClosedIncidentDetails" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="incidentID" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="UnitSearch" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="agencyName" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="agencyIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="sectorIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="jurisdictionIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="divisionIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="battalionIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="stationIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="resultParameters" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
              <xs:element name="unitNames" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="vehicleIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="capabilityIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="beatIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="personnelIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="statusIds" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="resourceTypeIds" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RequestMobileEnterpriseClientVersion" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BackUpUnitScreenRequest" type="xs:string" minOccurs="0" maxOccurs="1" />
        <xs:element name="BackUpUnitRequest" minOccurs="0" maxOccurs="1" >
          <xs:complexType>
            <xs:sequence>
              <xs:element name="unitName" type="xs:string" minOccurs="1" maxOccurs="1" />
              <xs:element name="disposition" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PremiseLocationSearch" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="resultParameters" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
              <xs:element name="address" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="apartment" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="building" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="city" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="crossStreet" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="postalCode" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="premiseCode" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="premiseName" type="xs:string" minOccurs="0" maxOccurs="1"/>
              <xs:element name="phoneNumber" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="preplanReference" type="xs:string" minOccurs="0" maxOccurs="1" />
              <xs:element name="onlyActiveFacilityDiversions" type="xs:boolean" minOccurs="0" maxOccurs="1" />
              <xs:element name="locationType" type="xs:int" minOccurs="0" maxOccurs="1" />
              <xs:element name="allActiveFacilityDiverts" type="xs:boolean" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PriorHistorySearch" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="incidentID" type="xs:string" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="LinkedIncidentDetailsRequest" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="resultParameters" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
              <xs:element name="IncidentID" type="xs:int" minOccurs="1" maxOccurs="1" />
              <xs:element name="returnAll" type="xs:boolean" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="UnitLoginInfoRequest" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="unit_name" type="xs:string" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="RadiusSearch" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="SearchType" type="xs:int" minOccurs="1" maxOccurs="1" />
              <!-- 1=Hydrant, 2=Station, 3=Premise-->
              <xs:element name="LocationTypeID" type="xs:int" minOccurs="0" maxOccurs="1" />
              <xs:element name="RadiusInMiles" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="Latitude" type="xs:double" minOccurs="1" maxOccurs="1" />
              <xs:element name="Longitude" type="xs:double" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>


