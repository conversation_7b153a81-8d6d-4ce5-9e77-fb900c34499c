<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Gun Check</TITLE>
		<LINK href="normalstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Gun Check</H4>
						<form action="GunCheck.aspx?queryfile=GunCheck.qry" method="post" id="Form" name="Form">
							<P>
								<TABLE>
									<TR>
										<TD>
											<TABLE>
												<TR>
													<TD width="28%"><b>Checks Requested:</b></TD>
													<TD width="15%"><center>Law Enforcement</center>
													</TD>
													<TD width="17%"><center>History</center>
													</TD>
                                                    <!--
                                                    <td style="text-align:center"><label id="labelSupplement">Supplement</label></td>
                                                    -->
													<!--
<TD width="18%"><center>Both</center>
													</TD>
													<TD width="25%"><center>Mail Response</center>
													</TD>
-->
												</TR>
												<TR>
													<TD></TD>
													<TD><center><input id="doLawEnforcement" type="checkbox" checked="checked" name="doLawEnforcement"/></center>
													</TD>
													<TD><center><input id="doHistory" type="checkbox" name="doHistory"/></center>
													</TD>
                                                    
<!--                                                    <td style="text-align:center"><input id="doSupplement" type="checkbox" name="doSupplement"/></td>
-->                                                    
													<!--
<TD><center><INPUT id="doBoth" type="checkbox" name="doBoth"></center>
													</TD>
													<TD><center><INPUT id="doMailResponse" type="checkbox" name="doMailResponse"></center>
													</TD>
-->
												</TR>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
											<TABLE>
												<TR>
													<TD>Serial:<INPUT id="SER" type="text" size="12" maxLength="20" name="SER"></TD>
													<TD>Make:<INPUT id="MAK" type="text" maxLength="3" size="3" name="MAK"></TD>
													<TD>Caliber:<INPUT id="CAL" type="text" maxLength="4" size="4" name="CAL" numeric="true"></TD>
													<TD>Type:<INPUT id="TYP" type="text" maxLength="1" size="1" name="TYP"></TD>
													<TD>Document:<INPUT id="Document" type="text" maxLength="1" size="1" name="Document"></TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
											<TABLE id="Table2">
												<TR>
													<TD>FCN #:<INPUT id="FCN" type="text" maxLength="13" size="13" name="FCN"></TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
											<TABLE id="Table3">
												<TR>
													<TD>Last Name:<INPUT id="LastName" type="text" size="10" name="LastName"></TD>
													<TD>First:<INPUT id="FirstName" type="text" size="10" name="FirstName"></TD>
													<TD>MI:<INPUT id="MI" type="text" size="5" name="MI"></TD>
													<TD>Exact:<INPUT id="Exact" type="checkbox" name="Exact"></TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
											<TABLE id="Table4">
												<TR>
													<TD width="60%">DOB (MMDDYYYY):<INPUT id="DOB" type="text" maxLength="8" size="8" name="DOB" numeric="true"></TD>
													<TD width="20%">Age:<INPUT id="Age" type="text" maxLength="2" size="2" name="Age" numeric="true"></TD>
													<TD width="20%">CCC:<INPUT id="CCC" type="text" maxLength="4" size="4" name="CCC" numeric="true"></TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
											<TABLE id="Table6">
												<TR>
													<TD>For Mail Response: Reference:<INPUT id="MailReference" type="text" size="20" name="MailReference"></TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
								</TABLE>
							</P>
                            <!--when click on this button, it call the validation.-->
                            <input type="button" name="Query" id="Button" value="Submit" onkeypress="validatepage()" onclick="validatepage()" />
                            <!--when validation is ok, it will call this button to submit the page.  This button is not visible because of it width is zero.-->
                            <input style="width: 0px;" tabindex="-1" type="submit" name="Submit" id="Submit" value="Submit" />


						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
		<script type="text/javascript" src="clientutilities.js"></script>
		<script type="text/javascript" language="javascript">
		function window.onload()
		{

			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.SER.focus();
			PrepareValidation(Form);
			Form.innerHTML = Form.innerHTML + GenerateGunSupplementHiddenFields();
			//statevals.innerHTML = GenerateSelectBox("state", statesource, statestyle, false, false, false, 1, false, false);
			//racevals.innerHTML = GenerateSelectBox("race", racesource, racestyle, false, false, false, 1, false, false);

		}
		function window.validatepage() {

		    if (!HandleGunSupplementFieldsBeforeSubmit()) return;

		    Form.Submit.click();

		}

		</script>
	</body>
</HTML>
