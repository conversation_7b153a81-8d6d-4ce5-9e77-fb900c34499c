<?xml version="1.0" encoding="utf-8" ?>
<xs:schema id="Items_Schema" targetNamespace="Items_Schema" xmlns="Items_Schema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:mstns="Mobile_Schema" elementFormDefault="qualified">
	<xs:complexType name="ItemType">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="code" type="xs:string" use="optional" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:element name="items">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="item" type="ItemType" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
