INPUT
{
	font-size: 18pt;
	font-family: <PERSON><PERSON>sole;
}

TEXTAREA
{
	font-size: 18pt;
	font-family: <PERSON><PERSON> Console;
	BACKGROUND-COLOR:#9FC1E8;
	outline:none;
	border-width:0px;
	border:none;
}
SELECT
{
	font-size: 18pt;
	font-family: Verdana;
}
OPTION
{
	font-size: 18pt;
	font-family: Verdana;
}
A:link
{
	font-weight: bold;
	font-size: 18pt;
	font-family: Verdana;
	text-decoration: none;
}
A:visited
{
	font-weight: bold;
	font-size: 18pt;
	font-family: Verdana;
	text-decoration: none
}
A:hover
{
	font-weight: bold;
	font-size: 18pt;

	font-family: Verdana;
	text-decoration: underline;
}
input[type=text]{
	BACKGROUND-COLOR:#9FC1E8;
}
select{
	BACKGROUND-COLOR:#9FC1E8;
}
B
{
	font-weight: bold;
	font-size: 18pt;
}
H2 {
	FONT-SIZE: 24px;
	FONT-FAMILY: Verdana;
}
H3 {
	BORDER-RIGHT: medium none; 
	PADDING-RIGHT: 2px;
	BORDER-TOP: medium none; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 22px; 
	PADDING-BOTTOM: 3px; 
	TEXT-TRANSFORM: capitalize; 
	BORDER-LEFT: medium none; 
	COLOR: darkblue; 
	PADDING-TOP: 3px;  
	FONT-FAMILY: Verdana; 
	TEXT-ALIGN: left
}
H4 {
	BORDER-RIGHT: medium none; 
	BORDER-TOP: medium none; 
	FONT-SIZE: 20pt; 
	BORDER-LEFT: medium none; 
	BORDER-BOTTOM: medium none; 
	FONT-FAMILY: Verdana;
}
H5 {
	FONT-SIZE: 14px; 
	COLOR: darkgoldenrod; 
	FONT-FAMILY: Verdana
}
xml{
	border-style:none !important;
	border:0 !important;
}
BODY {
	FONT-SIZE: 18pt; 
	BACKGROUND-COLOR:  #EDEFF2; 
	COLOR:#212E40;
	FONT-FAMILY: Verdana; 
}
select{
	border-color:10px solid transparent;
	BACKGROUND-COLOR:#9FC1E8;
	COLOR:#212E40;
}
select empty{
	BACKGROUND-COLOR:#9FC1E8;
	COLOR:#212E40;
}
select option{
	BACKGROUND-COLOR:#9FC1E8;
	COLOR:#212E40;
}
select option:not(:checked) { 
    BACKGROUND-COLOR:#9FC1E8;
	COLOR:#212E40; 
}
LI {
	LIST-STYLE-TYPE: square
}
TABLE {
	FONT-SIZE: 18pt; 
	FONT-FAMILY: Verdana
}
HR {
	BORDER-RIGHT: medium none; 
	BORDER-TOP: medium none; 
	FONT-SIZE: 1px; 
	FLOAT: left; 
	BORDER-LEFT: medium none;  
	BORDER-BOTTOM: medium none; 
	POSITION: relative; 
	HEIGHT: 1px
}
.base {
	PADDING-RIGHT: 10px; 
	PADDING-LEFT: 10px; 
	PADDING-BOTTOM: 10px; 
	MARGIN: 10px; 
	PADDING-TOP: 10px;
	BACKGROUND-COLOR:#FFFFFF; 
	COLOR:#212E40;
}
.rightaligncell {
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	PADDING-BOTTOM: 2px; 
	PADDING-TOP: 2px; 
	TEXT-ALIGN: right
}
.topbottombordercell {
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	PADDING-BOTTOM: 2px; 
	PADDING-TOP: 2px; 
	TEXT-ALIGN: right; 
	background-color:#E8EAED; 
	color:#212E40;
}
.bottombordercell {
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	PADDING-BOTTOM: 2px; 
	PADDING-TOP: 2px; 
}
.bottomborderpanel {
	PADDING-RIGHT: 5px; 
	PADDING-LEFT: 5px; 
	PADDING-BOTTOM: 10px; 
	PADDING-TOP: 5px; 
	TEXT-ALIGN: left
}
.topbordercell {
	PADDING-RIGHT: 10px; 
	PADDING-LEFT: 10px; 
	PADDING-BOTTOM: 10px; 
	PADDING-TOP: 10px; 
	TEXT-ALIGN: left;
}
.message {
	FONT-WEIGHT: bolder; 
	FONT-SIZE: 18pt; 
	TEXT-ALIGN: center
}
.xslsubheading {
	font-size: 12pt;
}
BODY
{
    FONT-SIZE: 18pt;
    FONT-FAMILY: Arial
}
BODY TD
{
    FONT-SIZE: 18pt;
}
BODY TD .title
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 18pt;
    FONT-FAMILY: Arial;
    TEXT-ALIGN: right;
    TEXT-DECORATION: none
}
BODY TD .subtitle
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 18pt;
    FONT-FAMILY: Arial;
    TEXT-ALIGN: center;
    TEXT-DECORATION: none
}
BODY TD .InputNames
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 18pt;
    FONT-FAMILY: Arial
}
BODY TD .InputData
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 18pt;
    FONT-FAMILY: Courier
}
input[type=button]{
	BACKGROUND-COLOR: #1066C7;
	COLOR:#FFFFFF;
	border-style:none !important;
	border:0 !important;
}
input[type=password]{
	BACKGROUND-COLOR:#9FC1E8;
	COLOR:#212E40;
	border-style:none !important;
	border:0 !important;
}
.bigcheckbox
{
	WIDTH: 65px; 
	HEIGHT: 33px;
}

.bigradio
{
	WIDTH: 65px; 
	HEIGHT: 33px;
}

TABLE .radiotable
{
}

TD .RadioTableRowHeader
{
	width:120px; 
	text-align:right;
}

TD .RadioTableRadioIDEntry
{	
	width:120; 
	text-align:left;
}

TD .RadioTableRadioIDEntry INPUT
{	
	width:100%; 
}

TD .RadioTableRadioCodeEntry
{	
	width:160; 
	text-align:left;
}

TD .RadioTableRadioCodeEntry INPUT
{	
	width:100%; 
}

TD .RadioTableRadioDescEntry
{	
	text-align:left;
}

TD .RadioTableRadioDescEntry INPUT
{	
	width:100%; 
}
