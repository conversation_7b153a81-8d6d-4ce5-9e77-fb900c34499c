<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Hazmat Inquiry</TITLE><LINK href="normalstyle.css" type="text/css" rel="stylesheet"></HEAD>
	<body>
		<TABLE class="base" id="Table1" cellPadding="10" align="center" border="0">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Hazmat Inquiry</H4>
						<form id="Form" name="Form" action="HazmatInquiry.aspx?queryfile=HazmatInquiry.qry" method="post">
							<P>&nbsp;</P>
							<TABLE height="55" align="center">
								<TR>
									<TD width="33%" height="23">UN Number:</TD>
									<TD><input id="UNNumber" type="text" maxLength="4" size="6" name="UNNumber"></TD>
								</TR>
							</TABLE>
							<P align="left">
								<input id="Query" type="submit" value="Query" name="Query">
							</P>
						</form>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE>
							<TR>
								<TD><input type="checkbox" name="associateWithIncident" id="RCAssociation" value="associateRCWithIncident" checked>Associate this Records Check with incident?</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
			</TBODY></TABLE>
		<script src="clientutilities.js"></script>
		<SCRIPT language="javascript">
		function window.onload()
		{

			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.UNNumber.focus();
			PrepareValidation(Form);

			//statevals.innerHTML = GenerateSelectBox("state", statesource, statestyle, false, false, false, 1, false, false);
			//racevals.innerHTML = GenerateSelectBox("race", racesource, racestyle, false, false, false, 1, false, false);

		}

		</SCRIPT>
	</body>
</HTML>
