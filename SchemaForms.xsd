<?xml version="1.0" encoding="utf-8" ?> 
<xs:schema id="SchemaForms" targetNamespace="http://tempuri.org/SchemaForms.xsd"                  elementFormDefault="qualified" xmlns="http://tempuri.org/SchemaForms.xsd"                  xmlns:mstns="http://tempuri.org/SchemaForms.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:complexType name="FormType">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="Type" type="xs:string" />
				<xs:attribute name="FilePath" type="xs:string" />
				<xs:attribute name="AckExpirationTime" type="xs:string" use="optional" />
				<xs:attribute name="SupervisorOnly" type="xs:string" use="optional" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>	
	<xs:complexType name="FormGroupType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Form" type="FormType" minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:element name="Forms">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="FormGroup" type="FormGroupType" minOccurs="1" maxOccurs="unbounded" />
			</xs:sequence>	
		</xs:complexType>
	</xs:element>
</xs:schema>
