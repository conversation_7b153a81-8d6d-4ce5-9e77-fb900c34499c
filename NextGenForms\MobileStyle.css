html, body{
    height:100%;
}

.row {
    margin-left: auto !important;
}
.FlexBody {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden !important;
}

.Flex-Form {
    display: flex;
    flex: 1;
    margin-left: 10px;
}

.Flex-Form-MainContent {
    flex: 1;
}

.Flex-Form-Comment {
    /* 12em is the width of the columns */
    flex: 0 0 33%;
    margin-left: 10px;
}

    .Flex-Form-Comment .comment .row {
        margin-left: unset;
    }

.last-row {
    margin-bottom: 0px;
}


.form-fill-height .main-row {
    margin-bottom: 0px;
    height: 100%;
}
.comment {
    height: 100%;
    border-left: 1px solid #DADCE0;
    background: #F5F6F7;
}

.comment .row {
    padding-top: 20px;
    margin-left: -10px;
    padding-bottom: 0px;
}

@font-face {
    font-family: 'Source Sans Pro Bold';
    src: url('SourceSansPro-Bold.ttf') format('truetype');
}

@font-face {
    font-family: 'Source Sans Pro Regular';
    src: url('SourceSansPro-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Source Sans Pro SemiBold';
    src: url('SourceSansPro-SemiBold.ttf') format('truetype');
}

h5, h4 {
    font-weight: 400 !important;
    font-size: 18px;
    font-style: normal !important;
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
    font-family: 'Source Sans Pro Bold', serif !important;
}

h4 {   
    font-size: 16px;
}

.switch {
    margin-left: 10px;
}
.switch label {
    font-size: 14px;
    color: #192231;
    font-style: normal !important;
    font-family: 'Source Sans Pro Bold', serif !important;
}

.switch label .lever {
    background-color: #F1F1F1;
    width:48px;
    height:8px;
}

.switch label .lever:before, .switch label .lever:after {
    width:16px;
    height:16px;
    top: -5px;
}
.switch label input[type=checkbox]:checked + .lever
{
    background-color: #F1F1F1;
}

.switch label input[type=checkbox]:checked + .lever:after {
    background-color: #1066C7;
}
.switch label input[type=checkbox]:checked + .lever:before, .switch label input[type=checkbox]:checked + .lever:after {
    left: 32px;
}

.subheader {
    color: #8D9199;
    background-color: #FAFBFC;
    height: 32px;
    margin-left: -20px !important;
    padding:0px !important;
}
.fullHeight{
    height:100%;
}

.input-field textarea:focus + label {
    color: #1066C7 !important;
}

.input-field textarea:focus {
    border-bottom: 1px solid #1066C7 !important;
    box-shadow: 0 1px 0 0 #1066C7 !important
}

textarea {    
    color: inherit;
}

.Flex-Form-Comment textarea {
    height: 15rem !important;
}

.btn {
    background-color: #1066C7 !important;
    color: white !important;
    text-transform: none;
    font-weight: bold;
    font-size: small !important;
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.header {
    border-bottom: 1px solid #DADCE0;
    padding-top: 20px;
}

.main {
    margin-left: 10px;
}

.input-field input:focus + label {
    color: #1066C7 !important;
}

.autocomplete-content li {
    color: #1066C7 !important;
}

    .autocomplete-content li .highlight {
        color: #1066C7 !important;
    }

.dropdown-content li > a, .dropdown-content li > span {
    color: black !important;
}

.input-field input:focus {
    border-bottom: 1px solid #1066C7 !important;
    box-shadow: 0 1px 0 0 #1066C7 !important;
}

.input-field > input:disabled, .input-field > input:read-only {
    box-shadow: none !important;
    color: #8D9199;
    border-bottom: none !important;
}

input:disabled.autocomplete, input[type=text]:disabled {
    border-bottom: 1px dotted rgba(0,0,0,0.42) !important;
}

.dropdown-content.select-dropdown li span {
    color: #1066C7;
}

.dropdown-content.select-dropdown li.selected span {
    color: white !important;
}

    .dropdown-content.select-dropdown li.selected span [type="checkbox"]:checked + label:before {
        border-right: 2px solid white !important;
        border-bottom: 2px solid white !important;
    }

.dropdown-content {
    top: 55px !important;
    max-height: 55vh;
}

    .dropdown-content [type="checkbox"] + label {
        top: -3px !important;
        display: inline;
    }

.with-gap[type="radio"]:checked + label:after {
    background-color: #1066C7 !important;
    border-color: #1066C7 !important;
}

.with-gap[type="radio"]:checked + label:before {
    border-color: #1066C7 !important;
}

tbody {
    display: block;
    height: 150px;
    overflow-y: auto;
}

    thead, tbody tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }

thead {
    width: 100%;
}

table {
    border: thin solid black !important;
    background-color: white !important;
}

    table.highlight tbody tr:hover {
        background-color: #eee;
    }

    table.highlight {
        background-color: white;
    }


    table thead {
        color: #9FA3AB !important;
    }

tr:nth-child(even) {
    background-color: #FAFBFC;
}

.tabs .tab a {
    color: #96bedc !important;
    background-color: #f5f5f5 !important
    /*Custom Text Color*/
}

.tabs .tab a:focus.active {
    color: #1066C7 !important;
    /*Custom Text Color While Active*/
}

.tabs .indicator {
    background-color: #1066C7;
}

.tabs {
    width: 98%;
}

.selected, .selected:hover {
    background-color: #1066C7 !important;
    color: white !important;
}

input:disabled {
    color: #8D9199 !important;
}

[type="checkbox"]:checked + label:before {
    border-right: 2px solid #1066C7 !important;
    border-bottom: 2px solid #1066C7 !important;
}

.icon-check {
    margin-left: 12px !important;
}

[type="checkbox"] + label {
    font-size: 0.8rem !important;
}

.subsection {
    border-bottom: 1px dashed gray;
    color: gray;
    width: 100%;
    margin-left: 12px;
}

.checkbox-color[type="checkbox"].filled-in:checked + span:after {
    border: 2px solid #1066C7 !important;
    background-color: #1066C7 !important;
}

.checkbox-color[type="checkbox"].filled-in:checked + span:before {
    border-right: 2px solid White !important;
    border-bottom: 2px solid White !important;
}

[type="checkbox"]:checked + span:not(.lever):before {
    border-right: 2px solid black !important;
    border-bottom: 2px solid black !important;
}

.input-field input:invalid {
    border-bottom: 1px solid #E41018 !important;
    box-shadow: 0 1px 0 0 #E41018 !important;
}

input:invalid + label::after {
    color: #E41018 !important;
    opacity: 1 !important;
    overflow: visible;
    white-space: nowrap;
    margin-top: 55px;
}

.select-invalid::after {
    color: #E41018 !important;
    opacity: 1 !important;
    overflow: visible;
    white-space: nowrap;
    margin-top: 45px;
}

.current-lat-invalid + label::after {
    content: "Latitude is Required" !important;
    color: #E41018 !important;
    opacity: 1 !important;
    overflow: visible;
    white-space: nowrap;
    margin-top: 45px;
}

.current-long-invalid + label::after {
    content: "Longitude is Required" !important;
    color: #E41018 !important;
    opacity: 1 !important;
    overflow: visible;
    white-space: nowrap;
    margin-top: 45px;
}

.input-validate-year:invalid + label::after {
    content: "Year is not valid" !important;
}

.input-validate-month:invalid + label::after {
    content: "Month is not valid" !important;
}

.input-validate-transported:invalid + label::after {
    content: "Number Seen must be greater than or equal to the Number Transported" !important;
}

.input-validate-future-date:invalid + label::after {
    content: "Date cannot be in the future" !important;
}

.input-validate-date:invalid + label::after {
    content: "Invalid Date" !important;
}

.input-validate-required:invalid + label::after, .select-invalid::after {
    content: "Field is Required" !important;
}

.table-validate-required {
    color: #E41018 !important;
}

input:invalid + label, input:invalid:focus + label, .select-invalid {
    color: #E41018 !important;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="date"]::-webkit-input-placeholder {
    visibility: hidden !important;
}

input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
}

.select-invalid-underline {
    border-bottom: 1px solid #E41018 !important;
}

.autoSubmitErrorMessage {
    display: none;
    font-size: 16px !important;
    color: black !important;
}

.tableSearch .row {
    margin: 0px !important;
}

.tableSearch .input-field {
    margin-top: 0px;
}

.tableSearch .icon-search {
    margin-top: 12px;
}

.searchStyle {
    margin: 0px !important;
    margin-left: 4px !important;
    border-bottom: 0 !important;
    font-weight: normal;
    /*color: #5f6670;*/
}

.searchStyle::placeholder {
    color: #5f6670;
}

.input-field .searchStyle:focus {
    border-bottom: 0px solid #ffffff00 !important;
    box-shadow: 0 0px 0 0 #ffffff00 !important;
}

.autocomplete {
    display: -ms-flexbox;
    display: flex;
}

.autocomplete .ac-users {
    padding-top: 10px;
}

.autocomplete .ac-users .chip {
    -ms-flex: auto;
    flex: auto;
    margin-bottom: 10px;
    margin-right: 10px;
}

.autocomplete .ac-users .chip:last-child {
    margin-right: 5px;
}

.autocomplete .ac-dropdown .ac-hover {
    background: #eee;
}

.autocomplete .ac-input {
    -ms-flex: 1;
    flex: 1;
    min-width: 150px;
    padding-top: 0.6rem;
}

.autocomplete .ac-input input {
    height: 2.4rem;
}

@media screen and (min-width: 600px) {
    .modal {
        width: 33%;
    }
}

.modal-header {
    padding: 20px;
}

.btn-flat {
    color: #5f6670;
    border: 1px solid #dfd7db;
    box-shadow: 0 0px 0 0 #ffffff00 !important;
    text-transform: none;
    font-weight: bold;
    font-size: small !important;
}

.btn-flat.disabled {
    background-color: #F5F6F7 !important;
    border: 1px solid #F5F6F7;
}

.btn-flat.disabled i {
    background-color: #BFC3C9;
}

.btn-icon-18{
    width :50px;
    height:32px;
    padding-top:2px;
}

.border-left {
    border-left: 1px solid #DADCE0;
    margin-left: 0px;
}

input[type=time]:focus {
    border-bottom: 1px solid #1066C7 !important;
    box-shadow: 0 1px 0 0 #1066C7 !important
}

.timepicker-digital-display {
    background-color: #1066C7;
}

.timepicker-canvas line {
    stroke: #1066C7;
}

.timepicker-canvas-bg, .timepicker-canvas-bearing {
    fill: #1066C7;
}

/*.input-field > input:read-only {
    border-bottom: 0px !important;
    color: #8D9199;
}*/