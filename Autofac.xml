<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Autofac</name>
    </assembly>
    <members>
        <member name="T:Autofac.Builder.ConcreteReflectionActivatorData">
            <summary>
            Reflection activator data for concrete types.
            </summary>
        </member>
        <member name="M:Autofac.Builder.ConcreteReflectionActivatorData.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Builder.ConcreteReflectionActivatorData"/> class.
            </summary>
            <param name="implementer">Type that will be activated.</param>
        </member>
        <member name="P:Autofac.Builder.ConcreteReflectionActivatorData.Activator">
            <summary>
            Gets the instance activator based on the provided data.
            </summary>
        </member>
        <member name="T:Autofac.Builder.ContainerBuildOptions">
            <summary>
            Parameterises the construction of a container by a <see cref="T:Autofac.ContainerBuilder"/>.
            </summary>
        </member>
        <member name="F:Autofac.Builder.ContainerBuildOptions.None">
            <summary>
            No options - the default behavior for container building.
            </summary>
        </member>
        <member name="F:Autofac.Builder.ContainerBuildOptions.ExcludeDefaultModules">
            <summary>
            Prevents inclusion of standard modules like support for
            relationship types including <see cref="T:System.Collections.Generic.IEnumerable`1"/> etc.
            </summary>
        </member>
        <member name="F:Autofac.Builder.ContainerBuildOptions.IgnoreStartableComponents">
            <summary>
            Does not call <see cref="M:Autofac.IStartable.Start"/> on components implementing
            this interface (useful for module testing.)
            </summary>
        </member>
        <member name="T:Autofac.Builder.DeferredCallback">
            <summary>
            Reference object allowing location and update of a registration callback.
            </summary>
        </member>
        <member name="M:Autofac.Builder.DeferredCallback.#ctor(System.Action{Autofac.Core.IComponentRegistry})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Builder.DeferredCallback"/> class.
            </summary>
            <param name="callback">
            An <see cref="T:System.Action`1"/> that executes a registration action
            against an <see cref="T:Autofac.Core.IComponentRegistry"/>.
            </param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="callback" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="P:Autofac.Builder.DeferredCallback.Callback">
            <summary>
            Gets or sets the callback to execute during registration.
            </summary>
            <value>
            An <see cref="T:System.Action`1"/> that executes a registration action
            against an <see cref="T:Autofac.Core.IComponentRegistry"/>.
            </value>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="value" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="P:Autofac.Builder.DeferredCallback.Id">
            <summary>
            Gets the callback identifier.
            </summary>
            <value>
            A <see cref="T:System.Guid"/> that uniquely identifies the callback action
            in a set of callbacks.
            </value>
        </member>
        <member name="T:Autofac.Builder.DynamicRegistrationStyle">
            <summary>
            Registration style for dynamic registrations.
            </summary>
        </member>
        <member name="T:Autofac.Builder.IConcreteActivatorData">
            <summary>
            Activator data that can provide an IInstanceActivator instance.
            </summary>
        </member>
        <member name="P:Autofac.Builder.IConcreteActivatorData.Activator">
            <summary>
            Gets the instance activator based on the provided data.
            </summary>
        </member>
        <member name="T:Autofac.Builder.IHideObjectMembers">
            <summary>
            Hides standard Object members to make fluent interfaces
            easier to read.
            Based on blog post by @kzu here:
            http://www.clariusconsulting.net/blogs/kzu/archive/2008/03/10/58301.aspx
            </summary>
        </member>
        <member name="M:Autofac.Builder.IHideObjectMembers.GetType">
            <summary>
            Standard System.Object member.
            </summary>
            <returns>Standard result.</returns>
        </member>
        <member name="M:Autofac.Builder.IHideObjectMembers.GetHashCode">
            <summary>
            Standard System.Object member.
            </summary>
            <returns>Standard result.</returns>
        </member>
        <member name="M:Autofac.Builder.IHideObjectMembers.ToString">
            <summary>
            Standard System.Object member.
            </summary>
            <returns>Standard result.</returns>
        </member>
        <member name="M:Autofac.Builder.IHideObjectMembers.Equals(System.Object)">
            <summary>
            Standard System.Object member.
            </summary>
            <param name="other">The other.</param>
            <returns>Standard result.</returns>
        </member>
        <member name="T:Autofac.Builder.IRegistrationBuilder`3">
            <summary>
            Data structure used to construct registrations.
            </summary>
            <typeparam name="TLimit">The most specific type to which instances of the registration
            can be cast.</typeparam>
            <typeparam name="TActivatorData">Activator builder type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style type.</typeparam>
        </member>
        <member name="P:Autofac.Builder.IRegistrationBuilder`3.ActivatorData">
            <summary>
            Gets the activator data.
            </summary>
        </member>
        <member name="P:Autofac.Builder.IRegistrationBuilder`3.RegistrationStyle">
            <summary>
            Gets the registration style.
            </summary>
        </member>
        <member name="P:Autofac.Builder.IRegistrationBuilder`3.RegistrationData">
            <summary>
            Gets the registration data.
            </summary>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.ExternallyOwned">
            <summary>
            Configure the component so that instances are never disposed by the container.
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.OwnedByLifetimeScope">
            <summary>
            Configure the component so that instances that support IDisposable are
            disposed by the container (default).
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerDependency">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            gets a new, unique instance (default).
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.SingleInstance">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            gets the same, shared instance.
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerLifetimeScope">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a single ILifetimeScope gets the same, shared instance. Dependent components in
            different lifetime scopes will get different instances.
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerMatchingLifetimeScope(System.Object[])">
            <summary>
            Configure the component so that every dependent component or call to Resolve() within
            a ILifetimeScope tagged with any of the provided tags value gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the tagged scope will
            share the parent's instance. If no appropriately tagged scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <param name="lifetimeScopeTag">Tag applied to matching lifetime scopes.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerOwned``1">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a ILifetimeScope created by an owned instance gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the owned instance scope will
            share the parent's instance. If no appropriate owned instance scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <typeparam name="TService">Service type.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerOwned(System.Type)">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a ILifetimeScope created by an owned instance gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the owned instance scope will
            share the parent's instance. If no appropriate owned instance scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <param name="serviceType">Service type.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerOwned``1(System.Object)">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a ILifetimeScope created by an owned instance gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the owned instance scope will
            share the parent's instance. If no appropriate owned instance scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <typeparam name="TService">The service type provided by the component.</typeparam>
            <param name="serviceKey">Key to associate with the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerOwned(System.Object,System.Type)">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a ILifetimeScope created by an owned instance gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the owned instance scope will
            share the parent's instance. If no appropriate owned instance scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <param name="serviceKey">Key to associate with the component.</param>
            <param name="serviceType">The service type provided by the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.As``1">
            <summary>
            Configure the services that the component will provide. The generic parameter(s) to As()
            will be exposed as TypedService instances.
            </summary>
            <typeparam name="TService">Service type.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.As``2">
            <summary>
            Configure the services that the component will provide. The generic parameter(s) to As()
            will be exposed as TypedService instances.
            </summary>
            <typeparam name="TService1">Service type.</typeparam>
            <typeparam name="TService2">Service type.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.As``3">
            <summary>
            Configure the services that the component will provide. The generic parameter(s) to As()
            will be exposed as TypedService instances.
            </summary>
            <typeparam name="TService1">Service type.</typeparam>
            <typeparam name="TService2">Service type.</typeparam>
            <typeparam name="TService3">Service type.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.As(System.Type[])">
            <summary>
            Configure the services that the component will provide.
            </summary>
            <param name="services">Service types to expose.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.As(Autofac.Core.Service[])">
            <summary>
            Configure the services that the component will provide.
            </summary>
            <param name="services">Services to expose.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.Named(System.String,System.Type)">
            <summary>
            Provide a textual name that can be used to retrieve the component.
            </summary>
            <param name="serviceName">Named service to associate with the component.</param>
            <param name="serviceType">The service type provided by the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.Named``1(System.String)">
            <summary>
            Provide a textual name that can be used to retrieve the component.
            </summary>
            <param name="serviceName">Named service to associate with the component.</param>
            <typeparam name="TService">The service type provided by the component.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.Keyed(System.Object,System.Type)">
            <summary>
            Provide a key that can be used to retrieve the component.
            </summary>
            <param name="serviceKey">Key to associate with the component.</param>
            <param name="serviceType">The service type provided by the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.Keyed``1(System.Object)">
            <summary>
            Provide a key that can be used to retrieve the component.
            </summary>
            <param name="serviceKey">Key to associate with the component.</param>
            <typeparam name="TService">The service type provided by the component.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.OnPreparing(System.Action{Autofac.Core.PreparingEventArgs})">
            <summary>
            Add a handler for the Preparing event. This event allows manipulating of the parameters
            that will be provided to the component.
            </summary>
            <param name="handler">The event handler.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.OnActivating(System.Action{Autofac.Core.IActivatingEventArgs{`0}})">
            <summary>
            Add a handler for the Activating event.
            </summary>
            <param name="handler">The event handler.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.OnActivated(System.Action{Autofac.Core.IActivatedEventArgs{`0}})">
            <summary>
            Add a handler for the Activated event.
            </summary>
            <param name="handler">The event handler.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.PropertiesAutowired(Autofac.Core.IPropertySelector,System.Boolean)">
            <summary>
            Configure the component so that any properties whose types are registered in the
            container and follow specific criteria will be wired to instances of the appropriate service.
            </summary>
            <param name="propertySelector">Selector to determine which properties should be injected.</param>
            <param name="allowCircularDependencies">Determine if circular dependencies should be allowed or not.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.WithMetadata(System.String,System.Object)">
            <summary>
            Associates data with the component.
            </summary>
            <param name="key">Key by which the data can be located.</param>
            <param name="value">The data value.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.WithMetadata(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Associates data with the component.
            </summary>
            <param name="properties">The extended properties to associate with the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.IRegistrationBuilder`3.WithMetadata``1(System.Action{Autofac.Builder.MetadataConfiguration{``0}})">
            <summary>
            Associates data with the component.
            </summary>
            <typeparam name="TMetadata">A type with properties whose names correspond to the
            property names to configure.</typeparam>
            <param name="configurationAction">
            The action used to configure the metadata.
            </param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="T:Autofac.Builder.MetadataConfiguration`1">
            <summary>
            Used with the WithMetadata configuration method to
            associate key-value pairs with an <see cref="T:Autofac.Core.IComponentRegistration"/>.
            </summary>
            <typeparam name="TMetadata">Interface with properties whose names correspond to
            the property keys.</typeparam>
            <remarks>This feature was suggested by OJ Reeves (@TheColonial).</remarks>
        </member>
        <member name="M:Autofac.Builder.MetadataConfiguration`1.For``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
            <summary>
            Set one of the property values.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="propertyAccessor">An expression that accesses the property to set.</param>
            <param name="value">The property value to set.</param>
        </member>
        <member name="T:Autofac.Builder.ReflectionActivatorData">
            <summary>
            Builder for reflection-based activators.
            </summary>
        </member>
        <member name="M:Autofac.Builder.ReflectionActivatorData.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Builder.ReflectionActivatorData"/> class.
            </summary>
            <param name="implementer">Type that will be activated.</param>
        </member>
        <member name="P:Autofac.Builder.ReflectionActivatorData.ImplementationType">
            <summary>
            Gets or sets the implementation type.
            </summary>
        </member>
        <member name="P:Autofac.Builder.ReflectionActivatorData.ConstructorFinder">
            <summary>
            Gets or sets the constructor finder for the registration.
            </summary>
        </member>
        <member name="P:Autofac.Builder.ReflectionActivatorData.ConstructorSelector">
            <summary>
            Gets or sets the constructor selector for the registration.
            </summary>
        </member>
        <member name="P:Autofac.Builder.ReflectionActivatorData.ConfiguredParameters">
            <summary>
            Gets the explicitly bound constructor parameters.
            </summary>
        </member>
        <member name="P:Autofac.Builder.ReflectionActivatorData.ConfiguredProperties">
            <summary>
            Gets the explicitly bound properties.
            </summary>
        </member>
        <member name="T:Autofac.Builder.RegistrationBuilder">
            <summary>
            Static factory methods to simplify the creation and handling of IRegistrationBuilder{L,A,R}.
            </summary>
            <example>
            To create an <see cref="T:Autofac.Core.IComponentRegistration"/> for a specific type, use:
            <code>
            var cr = RegistrationBuilder.ForType(t).CreateRegistration();
            </code>
            The full builder syntax is supported.
            <code>
            var cr = RegistrationBuilder.ForType(t).Named("foo").ExternallyOwned().CreateRegistration();
            </code>
            </example>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder.ForDelegate``1(System.Func{Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},``0})">
            <summary>
            Creates a registration builder for the provided delegate.
            </summary>
            <typeparam name="T">Instance type returned by delegate.</typeparam>
            <param name="delegate">Delegate to register.</param>
            <returns>A registration builder.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder.ForDelegate(System.Type,System.Func{Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Object})">
            <summary>
            Creates a registration builder for the provided delegate.
            </summary>
            <param name="delegate">Delegate to register.</param>
            <param name="limitType">Most specific type return value of delegate can be cast to.</param>
            <returns>A registration builder.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder.ForType``1">
            <summary>
            Creates a registration builder for the provided type.
            </summary>
            <typeparam name="TImplementer">Implementation type to register.</typeparam>
            <returns>A registration builder.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder.ForType(System.Type)">
            <summary>
            Creates a registration builder for the provided type.
            </summary>
            <param name="implementationType">Implementation type to register.</param>
            <returns>A registration builder.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder.CreateRegistration``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Create an <see cref='T:Autofac.Core.IComponentRegistration'/> from a <see cref='T:Autofac.Builder.RegistrationBuilder'/>.
            There is no need to call this method when registering components through a <see cref="T:Autofac.ContainerBuilder"/>.
            </summary>
            <remarks>
            When called on the result of one of the <see cref='T:Autofac.ContainerBuilder'/> methods,
            the returned registration will be different from the one the builder itself registers
            in the container.
            </remarks>
            <example>
            <code>
            var registration = RegistrationBuilder.ForType&lt;Foo&gt;().CreateRegistration();
            </code>
            </example>
            <param name="builder">The registration builder.</param>
            <returns>An IComponentRegistration.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="builder" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder.CreateRegistration(System.Guid,Autofac.Builder.RegistrationData,Autofac.Core.IInstanceActivator,System.Collections.Generic.IEnumerable{Autofac.Core.Service})">
            <summary>
            Create an IComponentRegistration from data.
            </summary>
            <param name="id">Id of the registration.</param>
            <param name="data">Registration data.</param>
            <param name="activator">Activator.</param>
            <param name="services">Services provided by the registration.</param>
            <returns>An IComponentRegistration.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder.CreateRegistration(System.Guid,Autofac.Builder.RegistrationData,Autofac.Core.IInstanceActivator,System.Collections.Generic.IEnumerable{Autofac.Core.Service},Autofac.Core.IComponentRegistration)">
            <summary>
            Create an IComponentRegistration from data.
            </summary>
            <param name="id">Id of the registration.</param>
            <param name="data">Registration data.</param>
            <param name="activator">Activator.</param>
            <param name="services">Services provided by the registration.</param>
            <param name="target">Optional; target registration.</param>
            <returns>An IComponentRegistration.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="activator" /> or <paramref name="data" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder.RegisterSingleComponent``3(Autofac.Core.IComponentRegistry,Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Register a component in the component registry. This helper method is necessary
            in order to execute OnRegistered hooks and respect PreserveDefaults.
            </summary>
            <remarks>Hoping to refactor this out.</remarks>
            <param name="cr">Component registry to make registration in.</param>
            <param name="builder">Registration builder with data for new registration.</param>
        </member>
        <member name="T:Autofac.Builder.RegistrationBuilderResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationBuilderResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationBuilderResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationBuilderResources.ComponentDoesNotSupportService">
            <summary>
              Looks up a localized string similar to The type &apos;{0}&apos; is not assignable to service &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationBuilder`3.ActivatorData">
            <summary>
            Gets the activator data.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationBuilder`3.RegistrationStyle">
            <summary>
            Gets the registration style.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationBuilder`3.RegistrationData">
            <summary>
            Gets the registration data.
            </summary>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.ExternallyOwned">
            <summary>
            Configure the component so that instances are never disposed by the container.
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.OwnedByLifetimeScope">
            <summary>
            Configure the component so that instances that support IDisposable are
            disposed by the container (default).
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.InstancePerDependency">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            gets a new, unique instance (default).
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.SingleInstance">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            gets the same, shared instance.
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.InstancePerLifetimeScope">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a single ILifetimeScope gets the same, shared instance. Dependent components in
            different lifetime scopes will get different instances.
            </summary>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.InstancePerMatchingLifetimeScope(System.Object[])">
            <summary>
            Configure the component so that every dependent component or call to Resolve() within
            a ILifetimeScope tagged with any of the provided tags value gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the tagged scope will
            share the parent's instance. If no appropriately tagged scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <param name="lifetimeScopeTag">Tag applied to matching lifetime scopes.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.InstancePerOwned``1">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a ILifetimeScope created by an owned instance gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the owned instance scope will
            share the parent's instance. If no appropriate owned instance scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <typeparam name="TService">The service type provided by the component.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.InstancePerOwned(System.Type)">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a ILifetimeScope created by an owned instance gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the owned instance scope will
            share the parent's instance. If no appropriate owned instance scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <param name="serviceType">The service type provided by the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.InstancePerOwned``1(System.Object)">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a ILifetimeScope created by an owned instance gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the owned instance scope will
            share the parent's instance. If no appropriate owned instance scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <typeparam name="TService">The service type provided by the component.</typeparam>
            <param name="serviceKey">Key to associate with the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.InstancePerOwned(System.Object,System.Type)">
            <summary>
            Configure the component so that every dependent component or call to Resolve()
            within a ILifetimeScope created by an owned instance gets the same, shared instance.
            Dependent components in lifetime scopes that are children of the owned instance scope will
            share the parent's instance. If no appropriate owned instance scope can be found in the
            hierarchy an <see cref="T:Autofac.Core.DependencyResolutionException"/> is thrown.
            </summary>
            <param name="serviceKey">Key to associate with the component.</param>
            <param name="serviceType">The service type provided by the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.As``1">
            <summary>
            Configure the services that the component will provide. The generic parameter(s) to As()
            will be exposed as TypedService instances.
            </summary>
            <typeparam name="TService">Service type.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.As``2">
            <summary>
            Configure the services that the component will provide. The generic parameter(s) to As()
            will be exposed as TypedService instances.
            </summary>
            <typeparam name="TService1">Service type.</typeparam>
            <typeparam name="TService2">Service type.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.As``3">
            <summary>
            Configure the services that the component will provide. The generic parameter(s) to As()
            will be exposed as TypedService instances.
            </summary>
            <typeparam name="TService1">Service type.</typeparam>
            <typeparam name="TService2">Service type.</typeparam>
            <typeparam name="TService3">Service type.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.As(System.Type[])">
            <summary>
            Configure the services that the component will provide.
            </summary>
            <param name="services">Service types to expose.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.As(Autofac.Core.Service[])">
            <summary>
            Configure the services that the component will provide.
            </summary>
            <param name="services">Services to expose.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.Named(System.String,System.Type)">
            <summary>
            Provide a textual name that can be used to retrieve the component.
            </summary>
            <param name="serviceName">Named service to associate with the component.</param>
            <param name="serviceType">The service type provided by the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.Named``1(System.String)">
            <summary>
            Provide a textual name that can be used to retrieve the component.
            </summary>
            <param name="serviceName">Named service to associate with the component.</param>
            <typeparam name="TService">The service type provided by the component.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.Keyed(System.Object,System.Type)">
            <summary>
            Provide a key that can be used to retrieve the component.
            </summary>
            <param name="serviceKey">Key to associate with the component.</param>
            <param name="serviceType">The service type provided by the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.Keyed``1(System.Object)">
            <summary>
            Provide a key that can be used to retrieve the component.
            </summary>
            <param name="serviceKey">Key to associate with the component.</param>
            <typeparam name="TService">The service type provided by the component.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.OnPreparing(System.Action{Autofac.Core.PreparingEventArgs})">
            <summary>
            Add a handler for the Preparing event. This event allows manipulating of the parameters
            that will be provided to the component.
            </summary>
            <param name="handler">The event handler.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.OnActivating(System.Action{Autofac.Core.IActivatingEventArgs{`0}})">
            <summary>
            Add a handler for the Activating event.
            </summary>
            <param name="handler">The event handler.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.OnActivated(System.Action{Autofac.Core.IActivatedEventArgs{`0}})">
            <summary>
            Add a handler for the Activated event.
            </summary>
            <param name="handler">The event handler.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.PropertiesAutowired(Autofac.Core.IPropertySelector,System.Boolean)">
            <summary>
            Configure the component so that any properties whose types are registered in the
            container and follow specific criteria will be wired to instances of the appropriate service.
            </summary>
            <param name="propertySelector">Selector to determine which properties should be injected.</param>
            <param name="allowCircularDependencies">Determine if circular dependencies should be allowed or not.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.WithMetadata(System.String,System.Object)">
            <summary>
            Associates data with the component.
            </summary>
            <param name="key">Key by which the data can be located.</param>
            <param name="value">The data value.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.WithMetadata(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Associates data with the component.
            </summary>
            <param name="properties">The extended properties to associate with the component.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.Builder.RegistrationBuilder`3.WithMetadata``1(System.Action{Autofac.Builder.MetadataConfiguration{``0}})">
            <summary>
            Associates data with the component.
            </summary>
            <typeparam name="TMetadata">A type with properties whose names correspond to the
            property names to configure.</typeparam>
            <param name="configurationAction">
            The action used to configure the metadata.
            </param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="T:Autofac.Builder.RegistrationData">
            <summary>
            Data common to all registrations made in the container, both direct (IComponentRegistration)
            and dynamic (IRegistrationSource).
            </summary>
        </member>
        <member name="M:Autofac.Builder.RegistrationData.#ctor(Autofac.Core.Service)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Builder.RegistrationData"/> class.
            </summary>
            <param name="defaultService">The default service that will be used if no others
            are added.</param>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.Services">
            <summary>
            Gets the services explicitly assigned to the component.
            </summary>
        </member>
        <member name="M:Autofac.Builder.RegistrationData.AddServices(System.Collections.Generic.IEnumerable{Autofac.Core.Service})">
            <summary>
            Add multiple services for the registration, overriding the default.
            </summary>
            <param name="services">The services to add.</param>
            <remarks>If an empty collection is specified, this will still
            clear the default service.</remarks>
        </member>
        <member name="M:Autofac.Builder.RegistrationData.AddService(Autofac.Core.Service)">
            <summary>
            Add a service to the registration, overriding the default.
            </summary>
            <param name="service">The service to add.</param>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.Ownership">
            <summary>
            Gets or sets the instance ownership assigned to the component.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.Lifetime">
            <summary>
            Gets or sets the lifetime assigned to the component.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.Sharing">
            <summary>
            Gets or sets the sharing mode assigned to the component.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.Metadata">
            <summary>
            Gets the extended properties assigned to the component.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.DeferredCallback">
            <summary>
            Gets or sets the callback used to register this component.
            </summary>
            <value>
            A <see cref="T:Autofac.Builder.DeferredCallback"/> that contains the delegate
            used to register this component with an <see cref="T:Autofac.Core.IComponentRegistry"/>.
            </value>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.PreparingHandlers">
            <summary>
            Gets the handlers for the Preparing event.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.ActivatingHandlers">
            <summary>
            Gets the handlers for the Activating event.
            </summary>
        </member>
        <member name="P:Autofac.Builder.RegistrationData.ActivatedHandlers">
            <summary>
            Gets the handlers for the Activated event.
            </summary>
        </member>
        <member name="M:Autofac.Builder.RegistrationData.CopyFrom(Autofac.Builder.RegistrationData,System.Boolean)">
            <summary>
            Copies the contents of another RegistrationData object into this one.
            </summary>
            <param name="that">The data to copy.</param>
            <param name="includeDefaultService">When true, the default service
            will be changed to that of the other.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="that" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Autofac.Builder.RegistrationData.ClearServices">
            <summary>
            Empties the configured services.
            </summary>
        </member>
        <member name="T:Autofac.Builder.RegistrationExtensions">
            <summary>
            Adds registration syntax for less commonly-used features.
            </summary>
            <remarks>
            These features are in this namespace because they will remain accessible to
            applications originally written against Autofac 1.4. In Autofac 2, this functionality
            is implicitly provided and thus making explicit registrations is rarely necessary.
            </remarks>
        </member>
        <member name="M:Autofac.Builder.RegistrationExtensions.RegisterGeneratedFactory(Autofac.ContainerBuilder,System.Type)">
            <summary>
            Registers a factory delegate.
            </summary>
            <param name="builder">Container builder.</param>
            <param name="delegateType">Factory type to generate.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <remarks>Factory delegates are provided automatically in Autofac 2,
            and this method is generally not required.</remarks>
        </member>
        <member name="M:Autofac.Builder.RegistrationExtensions.RegisterGeneratedFactory(Autofac.ContainerBuilder,System.Type,Autofac.Core.Service)">
            <summary>
            Registers a factory delegate.
            </summary>
            <param name="builder">Container builder.</param>
            <param name="delegateType">Factory type to generate.</param>
            <param name="service">The service that the delegate will return instances of.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <remarks>Factory delegates are provided automatically in Autofac 2, and
            this method is generally not required.</remarks>
        </member>
        <member name="M:Autofac.Builder.RegistrationExtensions.RegisterGeneratedFactory``1(Autofac.ContainerBuilder,Autofac.Core.Service)">
            <summary>
            Registers a factory delegate.
            </summary>
            <typeparam name="TDelegate">The type of the delegate.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="service">The service that the delegate will return instances of.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <remarks>Factory delegates are provided automatically in Autofac 2,
            and this method is generally not required.</remarks>
        </member>
        <member name="M:Autofac.Builder.RegistrationExtensions.RegisterGeneratedFactory``1(Autofac.ContainerBuilder)">
            <summary>
            Registers a factory delegate.
            </summary>
            <typeparam name="TDelegate">The type of the delegate.</typeparam>
            <param name="builder">Container builder.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <remarks>Factory delegates are provided automatically in Autofac 2,
            and this method is generally not required.</remarks>
        </member>
        <member name="M:Autofac.Builder.RegistrationExtensions.NamedParameterMapping``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Changes the parameter mapping mode of the supplied delegate type to match
            parameters by name.
            </summary>
            <typeparam name="TDelegate">Factory delegate type.</typeparam>
            <typeparam name="TGeneratedFactoryActivatorData">Activator data type.</typeparam>
            <typeparam name="TSingleRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to change parameter mapping mode of.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registration" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Autofac.Builder.RegistrationExtensions.PositionalParameterMapping``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Changes the parameter mapping mode of the supplied delegate type to match
            parameters by position.
            </summary>
            <typeparam name="TDelegate">Factory delegate type.</typeparam>
            <typeparam name="TGeneratedFactoryActivatorData">Activator data type.</typeparam>
            <typeparam name="TSingleRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to change parameter mapping mode of.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registration" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Autofac.Builder.RegistrationExtensions.TypedParameterMapping``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Changes the parameter mapping mode of the supplied delegate type to match
            parameters by type.
            </summary>
            <typeparam name="TDelegate">Factory delegate type.</typeparam>
            <typeparam name="TGeneratedFactoryActivatorData">Activator data type.</typeparam>
            <typeparam name="TSingleRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to change parameter mapping mode of.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registration" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="T:Autofac.Builder.SimpleActivatorData">
            <summary>
            An activator builder with no parameters.
            </summary>
        </member>
        <member name="M:Autofac.Builder.SimpleActivatorData.#ctor(Autofac.Core.IInstanceActivator)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Builder.SimpleActivatorData"/> class.
            </summary>
            <param name="activator">The activator to return.</param>
        </member>
        <member name="P:Autofac.Builder.SimpleActivatorData.Activator">
            <summary>
            Gets the activator.
            </summary>
        </member>
        <member name="T:Autofac.Builder.SingleRegistrationStyle">
            <summary>
            Registration style for individual components.
            </summary>
        </member>
        <member name="P:Autofac.Builder.SingleRegistrationStyle.Id">
            <summary>
            Gets or sets the ID used for the registration.
            </summary>
        </member>
        <member name="P:Autofac.Builder.SingleRegistrationStyle.RegisteredHandlers">
            <summary>
            Gets the handlers to notify of the component registration event.
            </summary>
        </member>
        <member name="P:Autofac.Builder.SingleRegistrationStyle.PreserveDefaults">
            <summary>
            Gets or sets a value indicating whether default registrations should be preserved.
            By default, new registrations override existing registrations as defaults.
            If set to true, new registrations will not change existing defaults.
            </summary>
        </member>
        <member name="P:Autofac.Builder.SingleRegistrationStyle.Target">
            <summary>
            Gets or sets the component upon which this registration is based.
            </summary>
        </member>
        <member name="M:Autofac.Builder.StartableManager.StartStartableComponents(Autofac.IComponentContext)">
            <summary>
            Executes the startable and auto-activate components in a context.
            </summary>
            <param name="componentContext">
            The <see cref="T:Autofac.IComponentContext"/> in which startables should execute.
            </param>
        </member>
        <member name="T:Autofac.ContainerBuilder">
             <summary>
             Used to build an <see cref="T:Autofac.IContainer"/> from component registrations.
             </summary>
             <example>
             <code>
             var builder = new ContainerBuilder();
            
             builder.RegisterType&lt;Logger&gt;()
                 .As&lt;ILogger&gt;()
                 .SingleInstance();
            
             builder.Register(c => new MessageHandler(c.Resolve&lt;ILogger&gt;()));
            
             var container = builder.Build();
             // resolve components from container...
             </code>
             </example>
             <remarks>Most <see cref="T:Autofac.ContainerBuilder"/> functionality is accessed
             via extension methods in <see cref="T:Autofac.RegistrationExtensions"/>.</remarks>
             <seealso cref="T:Autofac.IContainer"/>
             <see cref="T:Autofac.RegistrationExtensions"/>
        </member>
        <member name="M:Autofac.ContainerBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.ContainerBuilder"/> class.
            </summary>
        </member>
        <member name="M:Autofac.ContainerBuilder.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.ContainerBuilder"/> class.
            </summary>
            <param name="properties">The properties used during component registration.</param>
        </member>
        <member name="P:Autofac.ContainerBuilder.Properties">
            <summary>
            Gets the set of properties used during component registration.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.IDictionary`2"/> that can be used to share
            context across registrations.
            </value>
        </member>
        <member name="M:Autofac.ContainerBuilder.RegisterCallback(System.Action{Autofac.Core.IComponentRegistry})">
            <summary>
            Register a callback that will be invoked when the container is configured.
            </summary>
            <remarks>This is primarily for extending the builder syntax.</remarks>
            <param name="configurationCallback">Callback to execute.</param>
        </member>
        <member name="M:Autofac.ContainerBuilder.RegisterBuildCallback(System.Action{Autofac.IContainer})">
            <summary>
            Register a callback that will be invoked when the container is built.
            </summary>
            <param name="buildCallback">Callback to execute.</param>
            <returns>The <see cref="T:Autofac.ContainerBuilder"/> instance to continue registration calls.</returns>
        </member>
        <member name="M:Autofac.ContainerBuilder.Build(Autofac.Builder.ContainerBuildOptions)">
            <summary>
            Create a new container with the component registrations that have been made.
            </summary>
            <param name="options">Options that influence the way the container is initialised.</param>
            <remarks>
            Build can only be called once per <see cref="T:Autofac.ContainerBuilder"/>
            - this prevents ownership issues for provided instances.
            Build enables support for the relationship types that come with Autofac (e.g.
            Func, Owned, Meta, Lazy, IEnumerable.) To exclude support for these types,
            first create the container, then call Update() on the builder.
            </remarks>
            <returns>A new container with the configured component registrations.</returns>
        </member>
        <member name="M:Autofac.ContainerBuilder.Update(Autofac.IContainer)">
            <summary>
            Configure an existing container with the component registrations
            that have been made.
            </summary>
            <remarks>
            Update can only be called once per <see cref="T:Autofac.ContainerBuilder"/>
            - this prevents ownership issues for provided instances.
            </remarks>
            <param name="container">An existing container to make the registrations in.</param>
        </member>
        <member name="M:Autofac.ContainerBuilder.Update(Autofac.IContainer,Autofac.Builder.ContainerBuildOptions)">
            <summary>
            Configure an existing container with the component registrations
            that have been made and allows additional build options to be specified.
            </summary>
            <remarks>
            Update can only be called once per <see cref="T:Autofac.ContainerBuilder"/>
            - this prevents ownership issues for provided instances.
            </remarks>
            <param name="container">An existing container to make the registrations in.</param>
            <param name="options">Options that influence the way the container is updated.</param>
        </member>
        <member name="M:Autofac.ContainerBuilder.Update(Autofac.Core.IComponentRegistry)">
            <summary>
            Configure an existing registry with the component registrations
            that have been made.
            </summary>
            <remarks>
            Update can only be called once per <see cref="T:Autofac.ContainerBuilder"/>
            - this prevents ownership issues for provided instances.
            </remarks>
            <param name="componentRegistry">An existing registry to make the registrations in.</param>
        </member>
        <member name="M:Autofac.ContainerBuilder.UpdateRegistry(Autofac.Core.IComponentRegistry)">
            <summary>
            Configure an existing registry with the component registrations
            that have been made. Primarily useful in dynamically adding registrations
            to a child lifetime scope.
            </summary>
            <remarks>
            Update can only be called once per <see cref="T:Autofac.ContainerBuilder"/>
            - this prevents ownership issues for provided instances.
            </remarks>
            <param name="componentRegistry">An existing registry to make the registrations in.</param>
        </member>
        <member name="T:Autofac.ContainerBuilderResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.ContainerBuilderResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.ContainerBuilderResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.ContainerBuilderResources.BuildCanOnlyBeCalledOnce">
            <summary>
              Looks up a localized string similar to Build() or Update() can only be called once on a ContainerBuilder..
            </summary>
        </member>
        <member name="P:Autofac.ContainerBuilderResources.ErrorAutoActivating">
            <summary>
              Looks up a localized string similar to An error occurred while attempting to automatically activate registration &apos;{0}&apos;. See the inner exception for information on the source of the failure..
            </summary>
        </member>
        <member name="T:Autofac.Core.ActivatedEventArgs`1">
            <summary>
            Fired when the activation process for a new instance is complete.
            </summary>
        </member>
        <member name="M:Autofac.Core.ActivatedEventArgs`1.#ctor(Autofac.IComponentContext,Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.ActivatedEventArgs`1"/> class.
            </summary>
            <param name="context">The context.</param>
            <param name="component">The component.</param>
            <param name="parameters">The parameters.</param>
            <param name="instance">The instance.</param>
        </member>
        <member name="P:Autofac.Core.ActivatedEventArgs`1.Context">
            <summary>
            Gets the context in which the activation occurred.
            </summary>
        </member>
        <member name="P:Autofac.Core.ActivatedEventArgs`1.Component">
            <summary>
            Gets the component providing the instance.
            </summary>
        </member>
        <member name="P:Autofac.Core.ActivatedEventArgs`1.Parameters">
            <summary>
            Gets the paramters provided when resolved.
            </summary>
        </member>
        <member name="P:Autofac.Core.ActivatedEventArgs`1.Instance">
            <summary>
            Gets the instance that will be used to satisfy the request.
            </summary>
        </member>
        <member name="T:Autofac.Core.ActivatingEventArgs`1">
            <summary>
            Fired after the construction of an instance but before that instance
            is shared with any other or any members are invoked on it.
            </summary>
        </member>
        <member name="M:Autofac.Core.ActivatingEventArgs`1.#ctor(Autofac.IComponentContext,Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.ActivatingEventArgs`1"/> class.
            </summary>
            <param name="context">The context.</param>
            <param name="component">The component.</param>
            <param name="parameters">The parameters.</param>
            <param name="instance">The instance.</param>
        </member>
        <member name="P:Autofac.Core.ActivatingEventArgs`1.Context">
            <summary>
            Gets the context in which the activation occurred.
            </summary>
        </member>
        <member name="P:Autofac.Core.ActivatingEventArgs`1.Component">
            <summary>
            Gets the component providing the instance.
            </summary>
        </member>
        <member name="P:Autofac.Core.ActivatingEventArgs`1.Instance">
            <summary>
            Gets or sets the instance that will be used to satisfy the request.
            </summary>
            <remarks>
            The instance can be replaced if needed, e.g. by an interface proxy.
            </remarks>
        </member>
        <member name="M:Autofac.Core.ActivatingEventArgs`1.ReplaceInstance(System.Object)">
            <summary>
            The instance can be replaced if needed, e.g. by an interface proxy.
            </summary>
            <param name="instance">The object to use instead of the activated instance.</param>
        </member>
        <member name="P:Autofac.Core.ActivatingEventArgs`1.Parameters">
            <summary>
            Gets the parameters supplied to the activator.
            </summary>
        </member>
        <member name="T:Autofac.Core.DefaultPropertySelector">
            <summary>
            Provides default property selector that applies appropriate filters to ensure only
            public settable properties are selected (including filtering for value types and indexed
            properties).
            </summary>
        </member>
        <member name="M:Autofac.Core.DefaultPropertySelector.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.DefaultPropertySelector"/> class
            that provides default selection criteria.
            </summary>
            <param name="preserveSetValues">Determines if values should be preserved or not.</param>
        </member>
        <member name="P:Autofac.Core.DefaultPropertySelector.PreserveSetValues">
            <summary>
            Gets or sets a value indicating whether the value should be set if the value is already
            set (ie non-null).
            </summary>
        </member>
        <member name="P:Autofac.Core.DefaultPropertySelector.OverwriteSetValueInstance">
            <summary>
            Gets an instance of DefaultPropertySelector that will cause values to be overwritten.
            </summary>
        </member>
        <member name="P:Autofac.Core.DefaultPropertySelector.PreserveSetValueInstance">
            <summary>
            Gets an instance of DefaultPropertySelector that will preserve any values already set.
            </summary>
        </member>
        <member name="M:Autofac.Core.DefaultPropertySelector.InjectProperty(System.Reflection.PropertyInfo,System.Object)">
            <summary>
            Provides default filtering to determine if property should be injected by rejecting
            non-public settable properties.
            </summary>
            <param name="propertyInfo">Property to be injected.</param>
            <param name="instance">Instance that has the property to be injected.</param>
            <returns>Whether property should be injected.</returns>
        </member>
        <member name="T:Autofac.Core.DelegatePropertySelector">
            <summary>
            Provides a property selector that applies a filter defined by a delegate.
            </summary>
        </member>
        <member name="M:Autofac.Core.DelegatePropertySelector.#ctor(System.Func{System.Reflection.PropertyInfo,System.Object,System.Boolean})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.DelegatePropertySelector"/> class
            that invokes a delegate to determine selection.
            </summary>
            <param name="finder">Delegate to determine whether a property should be injected.</param>
        </member>
        <member name="T:Autofac.Core.Activators.Delegate.DelegateActivator">
            <summary>
            Activate instances using a delegate.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Delegate.DelegateActivator.#ctor(System.Type,System.Func{Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Delegate.DelegateActivator"/> class.
            </summary>
            <param name="limitType">The most specific type to which activated instances can be cast.</param>
            <param name="activationFunction">Activation delegate.</param>
        </member>
        <member name="M:Autofac.Core.Activators.Delegate.DelegateActivator.ActivateInstance(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Activate an instance in the provided context.
            </summary>
            <param name="context">Context in which to activate instances.</param>
            <param name="parameters">Parameters to the instance.</param>
            <returns>The activated instance.</returns>
            <remarks>
            The context parameter here should probably be ILifetimeScope in order to reveal Disposer,
            but will wait until implementing a concrete use case to make the decision.
            </remarks>
        </member>
        <member name="T:Autofac.Core.Activators.Delegate.DelegateActivatorResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Delegate.DelegateActivatorResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Delegate.DelegateActivatorResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Delegate.DelegateActivatorResources.NullFromActivationDelegateFor">
            <summary>
              Looks up a localized string similar to A delegate registered to create instances of &apos;{0}&apos; returned null..
            </summary>
        </member>
        <member name="T:Autofac.Core.Activators.InstanceActivator">
            <summary>
            Base class for instance activators.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.InstanceActivator.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.InstanceActivator"/> class.
            </summary>
            <param name="limitType">Most derived type to which instances can be cast.</param>
        </member>
        <member name="P:Autofac.Core.Activators.InstanceActivator.LimitType">
            <summary>
            Gets the most specific type that the component instances are known to be castable to.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.InstanceActivator.ToString">
            <summary>
            Gets a string representation of the activator.
            </summary>
            <returns>A string describing the activator.</returns>
        </member>
        <member name="T:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivator">
            <summary>
            Provides a pre-constructed instance.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivator.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivator"/> class.
            </summary>
            <param name="instance">The instance to provide.</param>
        </member>
        <member name="M:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivator.ActivateInstance(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Activate an instance in the provided context.
            </summary>
            <param name="context">Context in which to activate instances.</param>
            <param name="parameters">Parameters to the instance.</param>
            <returns>The activated instance.</returns>
            <remarks>
            The context parameter here should probably be ILifetimeScope in order to reveal Disposer,
            but will wait until implementing a concrete use case to make the decision.
            </remarks>
        </member>
        <member name="P:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivator.DisposeInstance">
            <summary>
            Gets or sets a value indicating whether the activator disposes the instance that it holds.
            Necessary because otherwise instances that are never resolved will never be
            disposed.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivator.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="T:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivatorResources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivatorResources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivatorResources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.ProvidedInstance.ProvidedInstanceActivatorResources.InstanceAlreadyActivated">
            <summary>
               Looks up a localized string similar to The provided instance of &apos;{0}&apos; has already been used in an activation request. Did you combine a provided instance with non-root/single-instance lifetime/sharing?.
            </summary>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.AutowiringParameter">
            <summary>
            Supplies values based on the target parameter type.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.AutowiringParameter.CanSupplyValue(System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Func{System.Object}@)">
            <summary>
            Returns true if the parameter is able to provide a value to a particular site.
            </summary>
            <param name="pi">Constructor, method, or property-mutator parameter.</param>
            <param name="context">The component context in which the value is being provided.</param>
            <param name="valueProvider">If the result is true, the valueProvider parameter will
            be set to a function that will lazily retrieve the parameter value. If the result is false,
            will be set to null.</param>
            <returns>True if a value can be supplied; otherwise, false.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="pi" /> or <paramref name="context" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.ConstructorParameterBinding">
            <summary>
            Binds a constructor to the parameters that will be used when it is invoked.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBinding.TargetConstructor">
            <summary>
            Gets the constructor on the target type. The actual constructor used
            might differ, e.g. if using a dynamic proxy.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBinding.CanInstantiate">
            <summary>
            Gets a value indicating whether the binding is valid.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.ConstructorParameterBinding.#ctor(System.Reflection.ConstructorInfo,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},Autofac.IComponentContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.ConstructorParameterBinding"/> class.
            </summary>
            <param name="ci">ConstructorInfo to bind.</param>
            <param name="availableParameters">Available parameters.</param>
            <param name="context">Context in which to construct instance.</param>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.ConstructorParameterBinding.Instantiate">
            <summary>
            Invoke the constructor with the parameter bindings.
            </summary>
            <returns>The constructed instance.</returns>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBinding.Description">
            <summary>
            Gets a description of the constructor parameter binding.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.ConstructorParameterBinding.ToString">
            <summary>Returns a System.String that represents the current System.Object.</summary>
            <returns>A System.String that represents the current System.Object.</returns>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.ConstructorParameterBindingResources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBindingResources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBindingResources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBindingResources.BoundConstructor">
            <summary>
               Looks up a localized string similar to Bound constructor &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBindingResources.CannotInstantitate">
            <summary>
               Looks up a localized string similar to The binding cannot be instantiated: {0}.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBindingResources.ExceptionDuringInstantiation">
            <summary>
               Looks up a localized string similar to An exception was thrown while invoking the constructor &apos;{0}&apos; on type &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ConstructorParameterBindingResources.NonBindableConstructor">
            <summary>
               Looks up a localized string similar to Cannot resolve parameter &apos;{1}&apos; of constructor &apos;{0}&apos;..
            </summary>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.DefaultConstructorFinder">
            <summary>
            Finds constructors that match a finder function.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.DefaultConstructorFinder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.DefaultConstructorFinder" /> class.
            </summary>
            <remarks>
            Default to selecting all public constructors.
            </remarks>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.DefaultConstructorFinder.#ctor(System.Func{System.Type,System.Reflection.ConstructorInfo[]})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.DefaultConstructorFinder" /> class.
            </summary>
            <param name="finder">The finder function.</param>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.DefaultConstructorFinder.FindConstructors(System.Type)">
            <summary>
            Finds suitable constructors on the target type.
            </summary>
            <param name="targetType">Type to search for constructors.</param>
            <returns>Suitable constructors.</returns>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.DefaultValueParameter">
            <summary>
            Provides parameters that have a default value, set with an optional parameter
            declaration in C# or VB.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.DefaultValueParameter.CanSupplyValue(System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Func{System.Object}@)">
            <summary>
            Returns true if the parameter is able to provide a value to a particular site.
            </summary>
            <param name="pi">Constructor, method, or property-mutator parameter.</param>
            <param name="context">The component context in which the value is being provided.</param>
            <param name="valueProvider">If the result is true, the <paramref name="valueProvider" /> parameter will
            be set to a function that will lazily retrieve the parameter value. If the result is <see langword="false" />,
            will be set to <see langword="null" />.</param>
            <returns><see langword="true" /> if a value can be supplied; otherwise, <see langword="false" />.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="pi" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.IConstructorFinder">
            <summary>
            Find suitable constructors from which to select.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.IConstructorFinder.FindConstructors(System.Type)">
            <summary>
            Finds suitable constructors on the target type.
            </summary>
            <param name="targetType">Type to search for constructors.</param>
            <returns>Suitable constructors.</returns>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.IConstructorSelector">
            <summary>
            Selects the best constructor from a set of available constructors.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.IConstructorSelector.SelectConstructorBinding(Autofac.Core.Activators.Reflection.ConstructorParameterBinding[],System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Selects the best constructor from the available constructors.
            </summary>
            <param name="constructorBindings">Available constructors.</param>
            <param name="parameters">Parameters to the instance being resolved.</param>
            <returns>The best constructor.</returns>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelector">
            <summary>
            Selects a constructor based on its signature.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelector.#ctor(System.Type[])">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelector"/> class.
            </summary>
            <param name="signature">Signature to match.</param>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelector.SelectConstructorBinding(Autofac.Core.Activators.Reflection.ConstructorParameterBinding[],System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Selects the best constructor from the available constructors.
            </summary>
            <param name="constructorBindings">Available constructors.</param>
            <param name="parameters">Parameters to the instance being resolved.</param>
            <returns>The best constructor.</returns>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelectorResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelectorResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelectorResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelectorResources.AtLeastOneBindingRequired">
            <summary>
              Looks up a localized string similar to At least one binding must be provided in order to select a constructor..
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelectorResources.RequiredConstructorNotAvailable">
            <summary>
              Looks up a localized string similar to The required constructor on type &apos;{0}&apos;  with signature &apos;{1}&apos; is unavailable..
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.MatchingSignatureConstructorSelectorResources.TooManyConstructorsMatch">
            <summary>
              Looks up a localized string similar to More than one constructor matches the signature &apos;{0}&apos;..
            </summary>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.MostParametersConstructorSelector">
            <summary>
            Selects the constructor with the most parameters.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.MostParametersConstructorSelector.SelectConstructorBinding(Autofac.Core.Activators.Reflection.ConstructorParameterBinding[],System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Selects the best constructor from the available constructors.
            </summary>
            <param name="constructorBindings">Available constructors.</param>
            <param name="parameters">Parameters to the instance being resolved.</param>
            <returns>The best constructor.</returns>
            <exception cref='T:Autofac.Core.DependencyResolutionException'>A single unambiguous match could not be chosen.</exception>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.MostParametersConstructorSelectorResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.MostParametersConstructorSelectorResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.MostParametersConstructorSelectorResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.MostParametersConstructorSelectorResources.UnableToChooseFromMultipleConstructors">
            <summary>
              Looks up a localized string similar to Cannot choose between multiple constructors with equal length {0} on type &apos;{1}&apos;. Select the constructor explicitly, with the UsingConstructor() configuration method, when the component is registered..
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.NoConstructorsFoundException.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.NoConstructorsFoundException"/> class.
            </summary>
            <param name="offendingType">The <see cref="T:System.Type"/> whose constructor was not found.</param>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.NoConstructorsFoundException.#ctor(System.Type,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.NoConstructorsFoundException"/> class.
            </summary>
            <param name="offendingType">The <see cref="T:System.Type"/> whose constructor was not found.</param>
            <param name="message">Exception message.</param>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.NoConstructorsFoundException.#ctor(System.Type,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.NoConstructorsFoundException"/> class.
            </summary>
            <param name="offendingType">The <see cref="T:System.Type"/> whose constructor was not found.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.NoConstructorsFoundException.#ctor(System.Type,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.NoConstructorsFoundException"/> class.
            </summary>
            <param name="offendingType">The <see cref="T:System.Type"/> whose constructor was not found.</param>
            <param name="message">Exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.NoConstructorsFoundException.OffendingType">
            <summary>
            Gets the type without found constructors.
            </summary>
            <value>
            A <see cref="T:System.Type"/> that was processed by an <see cref="T:Autofac.Core.Activators.Reflection.IConstructorFinder"/>
            or similar mechanism and was determined to have no available constructors.
            </value>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.NoConstructorsFoundExceptionResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.NoConstructorsFoundExceptionResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.NoConstructorsFoundExceptionResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.NoConstructorsFoundExceptionResources.Message">
            <summary>
              Looks up a localized string similar to No accessible constructors were found for the type &apos;{0}&apos;..
            </summary>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.ReflectionActivator">
            <summary>
            Uses reflection to activate instances of a type.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.ReflectionActivator.#ctor(System.Type,Autofac.Core.Activators.Reflection.IConstructorFinder,Autofac.Core.Activators.Reflection.IConstructorSelector,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Activators.Reflection.ReflectionActivator"/> class.
            </summary>
            <param name="implementationType">Type to activate.</param>
            <param name="constructorFinder">Constructor finder.</param>
            <param name="constructorSelector">Constructor selector.</param>
            <param name="configuredParameters">Parameters configured explicitly for this instance.</param>
            <param name="configuredProperties">Properties configured explicitly for this instance.</param>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ReflectionActivator.ConstructorFinder">
            <summary>
            Gets the constructor finder.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ReflectionActivator.ConstructorSelector">
            <summary>
            Gets the constructor selector.
            </summary>
        </member>
        <member name="M:Autofac.Core.Activators.Reflection.ReflectionActivator.ActivateInstance(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Activate an instance in the provided context.
            </summary>
            <param name="context">Context in which to activate instances.</param>
            <param name="parameters">Parameters to the instance.</param>
            <returns>The activated instance.</returns>
            <remarks>
            The context parameter here should probably be ILifetimeScope in order to reveal Disposer,
            but will wait until implementing a concrete use case to make the decision.
            </remarks>
        </member>
        <member name="T:Autofac.Core.Activators.Reflection.ReflectionActivatorResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ReflectionActivatorResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ReflectionActivatorResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ReflectionActivatorResources.NoConstructorsAvailable">
            <summary>
              Looks up a localized string similar to No constructors on type &apos;{0}&apos; can be found with the constructor finder &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.Core.Activators.Reflection.ReflectionActivatorResources.NoConstructorsBindable">
            <summary>
              Looks up a localized string similar to None of the constructors found with &apos;{0}&apos; on type &apos;{1}&apos; can be invoked with the available services and parameters:{2}.
            </summary>
        </member>
        <member name="T:Autofac.Core.IPropertySelector">
            <summary>
            Find suitable properties to inject
            </summary>
        </member>
        <member name="M:Autofac.Core.IPropertySelector.InjectProperty(System.Reflection.PropertyInfo,System.Object)">
            <summary>
            Provides filtering to determine if property should be injected.
            </summary>
            <param name="propertyInfo">Property to be injected.</param>
            <param name="instance">Instance that has the property to be injected.</param>
            <returns>Whether property should be injected.</returns>
        </member>
        <member name="T:Autofac.Core.AutoActivateService">
            <summary>
            Service used as a "flag" to indicate a particular component should be
            automatically activated on container build.
            </summary>
        </member>
        <member name="P:Autofac.Core.AutoActivateService.Description">
            <summary>
            Gets the service description.
            </summary>
            <value>
            Always returns <c>AutoActivate</c>.
            </value>
        </member>
        <member name="M:Autofac.Core.AutoActivateService.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>.</param>
            <returns>
            <see langword="true" /> if the specified <see cref="T:System.Object"/> is not <see langword="null" />
            and is an <see cref="T:Autofac.Core.AutoActivateService"/>; otherwise, <see langword="false" />.
            </returns>
            <remarks>
            <para>
            All services of this type are considered "equal."
            </para>
            </remarks>
        </member>
        <member name="M:Autofac.Core.AutoActivateService.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>. Always <c>0</c> for this type.
            </returns>
            <remarks>
            <para>
            All services of this type are considered "equal" and use the same hash code.
            </para>
            </remarks>
        </member>
        <member name="T:Autofac.Core.ComponentRegisteredEventArgs">
            <summary>
            Information about the ocurrence of a component being registered
            with a container.
            </summary>
        </member>
        <member name="P:Autofac.Core.ComponentRegisteredEventArgs.ComponentRegistry">
            <summary>
            Gets the container into which the registration was made.
            </summary>
        </member>
        <member name="P:Autofac.Core.ComponentRegisteredEventArgs.ComponentRegistration">
            <summary>
            Gets the component registration.
            </summary>
        </member>
        <member name="M:Autofac.Core.ComponentRegisteredEventArgs.#ctor(Autofac.Core.IComponentRegistry,Autofac.Core.IComponentRegistration)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.ComponentRegisteredEventArgs"/> class.
            </summary>
            <param name="registry">The container into which the registration
            was made.</param>
            <param name="componentRegistration">The component registration.</param>
        </member>
        <member name="T:Autofac.Core.ComponentRegistrationExtensions">
            <summary>
            Extension methods for <see cref="T:Autofac.Core.IComponentRegistration"/>.
            </summary>
        </member>
        <member name="M:Autofac.Core.ComponentRegistrationExtensions.MatchingLifetimeScopeTags(Autofac.Core.IComponentRegistration)">
            <summary>
            For components registered instance-per-matching-lifetime-scope, retrieves the set
            of lifetime scope tags to match.
            </summary>
            <param name="registration">
            The <see cref="T:Autofac.Core.IComponentRegistration"/> to query for matching lifetime scope tags.
            </param>
            <returns>
            If the component is registered instance-per-matching-lifetime-scope, this method returns
            the set of matching lifetime scope tags. If the component is singleton, instance-per-scope,
            instance-per-dependency, or otherwise not an instance-per-matching-lifetime-scope
            component, this method returns an empty enumeration.
            </returns>
        </member>
        <member name="T:Autofac.Core.ConstantParameter">
            <summary>
            Base class for parameters that provide a constant value.
            </summary>
        </member>
        <member name="P:Autofac.Core.ConstantParameter.Value">
            <summary>
            Gets the value of the parameter.
            </summary>
        </member>
        <member name="M:Autofac.Core.ConstantParameter.#ctor(System.Object,System.Predicate{System.Reflection.ParameterInfo})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.ConstantParameter"/> class.
            </summary>
            <param name="value">
            The constant parameter value.
            </param>
            <param name="predicate">
            A predicate used to locate the parameter that should be populated by the constant.
            </param>
        </member>
        <member name="M:Autofac.Core.ConstantParameter.CanSupplyValue(System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Func{System.Object}@)">
            <summary>
            Returns true if the parameter is able to provide a value to a particular site.
            </summary>
            <param name="pi">Constructor, method, or property-mutator parameter.</param>
            <param name="context">The component context in which the value is being provided.</param>
            <param name="valueProvider">If the result is true, the valueProvider parameter will
            be set to a function that will lazily retrieve the parameter value. If the result is false,
            will be set to null.</param>
            <returns>True if a value can be supplied; otherwise, false.</returns>
        </member>
        <member name="T:Autofac.Core.Container">
            <summary>
            Standard container implementation.
            </summary>
        </member>
        <member name="M:Autofac.Core.Container.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Container"/> class.
            </summary>
            <param name="properties">The properties used during component registration.</param>
        </member>
        <member name="M:Autofac.Core.Container.BeginLifetimeScope">
            <summary>
            Begin a new sub-scope. Instances created via the sub-scope
            will be disposed along with it.
            </summary>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="M:Autofac.Core.Container.BeginLifetimeScope(System.Object)">
            <summary>
            Begin a new sub-scope. Instances created via the sub-scope
            will be disposed along with it.
            </summary>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="M:Autofac.Core.Container.BeginLifetimeScope(System.Action{Autofac.ContainerBuilder})">
            <summary>
            Begin a new nested scope, with additional components available to it.
            Component instances created via the new scope
            will be disposed along with it.
            </summary>
            <param name="configurationAction">Action on a <see cref="T:Autofac.ContainerBuilder"/>
            that adds component registrations visible only in the new scope.</param>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="M:Autofac.Core.Container.BeginLifetimeScope(System.Object,System.Action{Autofac.ContainerBuilder})">
            <summary>
            Begin a new nested scope, with additional components available to it.
            Component instances created via the new scope
            will be disposed along with it.
            </summary>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <param name="configurationAction">Action on a <see cref="T:Autofac.ContainerBuilder"/>
            that adds component registrations visible only in the new scope.</param>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="P:Autofac.Core.Container.Disposer">
            <summary>
            Gets the disposer associated with this container. Instances can be associated
            with it manually if required.
            </summary>
        </member>
        <member name="P:Autofac.Core.Container.Tag">
            <summary>
            Gets the tag applied to the lifetime scope.
            </summary>
            <remarks>The tag applied to this scope and the contexts generated when
            it resolves component dependencies.</remarks>
        </member>
        <member name="E:Autofac.Core.Container.ChildLifetimeScopeBeginning">
            <summary>
            Fired when a new scope based on the current scope is beginning.
            </summary>
        </member>
        <member name="E:Autofac.Core.Container.CurrentScopeEnding">
            <summary>
            Fired when this scope is ending.
            </summary>
        </member>
        <member name="E:Autofac.Core.Container.ResolveOperationBeginning">
            <summary>
            Fired when a resolve operation is beginning in this scope.
            </summary>
        </member>
        <member name="P:Autofac.Core.Container.ComponentRegistry">
            <summary>
            Gets associated services with the components that provide them.
            </summary>
        </member>
        <member name="M:Autofac.Core.Container.ResolveComponent(Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Resolve an instance of the provided registration within the context.
            </summary>
            <param name="registration">The registration.</param>
            <param name="parameters">Parameters for the instance.</param>
            <returns>
            The component instance.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.Core.Container.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Autofac.Core.Container.GetService(System.Type)">
            <summary>
            Gets the service object of the specified type.
            </summary>
            <param name="serviceType">An object that specifies the type of service object
            to get.</param>
            <returns>
            A service object of type <paramref name="serviceType"/>.-or- null if there is
            no service object of type <paramref name="serviceType"/>.
            </returns>
        </member>
        <member name="T:Autofac.Core.ContainerResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.ContainerResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.ContainerResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.ContainerResources.SelfRegistrationCannotBeActivated">
            <summary>
              Looks up a localized string similar to The container&apos;s self-registration of context interfaces should never be activated as it is hard-wired into the LifetimeScope class..
            </summary>
        </member>
        <member name="T:Autofac.Core.DependencyResolutionException">
            <summary>
            Base exception type thrown whenever the dependency resolution process fails. This is a fatal
            exception, as Autofac is unable to 'roll back' changes to components that may have already
            been made during the operation. For example, 'on activated' handlers may have already been
            fired, or 'single instance' components partially constructed.
            </summary>
        </member>
        <member name="M:Autofac.Core.DependencyResolutionException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.DependencyResolutionException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:Autofac.Core.DependencyResolutionException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.DependencyResolutionException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="P:Autofac.Core.DependencyResolutionException.Message">
            <summary>
            Gets a message that describes the current exception.
            </summary>
            <value>
            The error message that explains the reason for the exception, or an empty string("").
            </value>
        </member>
        <member name="T:Autofac.Core.DependencyResolutionExceptionResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.DependencyResolutionExceptionResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.DependencyResolutionExceptionResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.DependencyResolutionExceptionResources.MessageNestingFormat">
            <summary>
              Looks up a localized string similar to {0} ---&gt; {1} (See inner exception for details.).
            </summary>
        </member>
        <member name="T:Autofac.Core.Diagnostics.IContainerAwareComponent">
            <summary>
            Marks a module as container-aware (for the purposes of attaching to diagnostic events.)
            </summary>
        </member>
        <member name="M:Autofac.Core.Diagnostics.IContainerAwareComponent.SetContainer(Autofac.IContainer)">
            <summary>
            Initialise the module with the container into which it is being registered.
            </summary>
            <param name="container">The container.</param>
        </member>
        <member name="T:Autofac.Core.Disposer">
            <summary>
            Maintains a set of objects to dispose, and disposes them in the reverse order
            from which they were added when the Disposer is itself disposed.
            </summary>
        </member>
        <member name="F:Autofac.Core.Disposer._items">
            <summary>
            Contents all implement IDisposable.
            </summary>
        </member>
        <member name="M:Autofac.Core.Disposer.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Autofac.Core.Disposer.AddInstanceForDisposal(System.IDisposable)">
            <summary>
            Adds an object to the disposer. When the disposer is
            disposed, so will the object be.
            </summary>
            <param name="instance">The instance.</param>
        </member>
        <member name="T:Autofac.Core.IActivatedEventArgs`1">
            <summary>
            Fired when the activation process for a new instance is complete.
            </summary>
        </member>
        <member name="P:Autofac.Core.IActivatedEventArgs`1.Context">
            <summary>
            Gets the context in which the activation occurred.
            </summary>
        </member>
        <member name="P:Autofac.Core.IActivatedEventArgs`1.Component">
            <summary>
            Gets the component providing the instance.
            </summary>
        </member>
        <member name="P:Autofac.Core.IActivatedEventArgs`1.Parameters">
            <summary>
            Gets the paramters provided when resolved.
            </summary>
        </member>
        <member name="P:Autofac.Core.IActivatedEventArgs`1.Instance">
            <summary>
            Gets the instance that will be used to satisfy the request.
            </summary>
        </member>
        <member name="T:Autofac.Core.IActivatingEventArgs`1">
            <summary>
            Fired after the construction of an instance but before that instance
            is shared with any other or any members are invoked on it.
            </summary>
        </member>
        <member name="P:Autofac.Core.IActivatingEventArgs`1.Context">
            <summary>
            Gets the context in which the activation occurred.
            </summary>
        </member>
        <member name="P:Autofac.Core.IActivatingEventArgs`1.Component">
            <summary>
            Gets the component providing the instance.
            </summary>
        </member>
        <member name="P:Autofac.Core.IActivatingEventArgs`1.Instance">
            <summary>
            Gets the instance that will be used to satisfy the request.
            </summary>
        </member>
        <member name="M:Autofac.Core.IActivatingEventArgs`1.ReplaceInstance(System.Object)">
            <summary>
            The instance can be replaced if needed, e.g. by an interface proxy.
            </summary>
            <param name="instance">The object to use instead of the activated instance.</param>
        </member>
        <member name="P:Autofac.Core.IActivatingEventArgs`1.Parameters">
            <summary>
            Gets the parameters supplied to the activator.
            </summary>
        </member>
        <member name="T:Autofac.Core.IComponentLifetime">
            <summary>
            Locates the lifetime to which instances of a component should be attached.
            </summary>
        </member>
        <member name="M:Autofac.Core.IComponentLifetime.FindScope(Autofac.Core.ISharingLifetimeScope)">
            <summary>
            Given the most nested scope visible within the resolve operation, find
            the scope for the component.
            </summary>
            <param name="mostNestedVisibleScope">The most nested visible scope.</param>
            <returns>The scope for the component.</returns>
        </member>
        <member name="T:Autofac.Core.IComponentRegistration">
            <summary>
            Describes a logical component within the container.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistration.Id">
            <summary>
            Gets a unique identifier for this component (shared in all sub-contexts.)
            This value also appears in Services.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistration.Activator">
            <summary>
            Gets the activator used to create instances.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistration.Lifetime">
            <summary>
            Gets the lifetime associated with the component.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistration.Sharing">
            <summary>
            Gets a value indicating whether the component instances are shared or not.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistration.Ownership">
            <summary>
            Gets a value indicating whether the instances of the component should be disposed by the container.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistration.Services">
            <summary>
            Gets the services provided by the component.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistration.Metadata">
            <summary>
            Gets additional data associated with the component.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistration.Target">
            <summary>
            Gets the component registration upon which this registration is based.
            </summary>
        </member>
        <member name="E:Autofac.Core.IComponentRegistration.Preparing">
            <summary>
            Fired when a new instance is required. The instance can be
            provided in order to skip the regular activator, by setting the Instance property in
            the provided event arguments.
            </summary>
        </member>
        <member name="M:Autofac.Core.IComponentRegistration.RaisePreparing(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter}@)">
            <summary>
            Called by the container when an instance is required.
            </summary>
            <param name="context">The context in which the instance will be activated.</param>
            <param name="parameters">Parameters for activation. These may be modified by the event handler.</param>
        </member>
        <member name="E:Autofac.Core.IComponentRegistration.Activating">
            <summary>
            Fired when a new instance is being activated. The instance can be
            wrapped or switched at this time by setting the Instance property in
            the provided event arguments.
            </summary>
        </member>
        <member name="M:Autofac.Core.IComponentRegistration.RaiseActivating(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Object@)">
            <summary>
            Called by the container once an instance has been constructed.
            </summary>
            <param name="context">The context in which the instance was activated.</param>
            <param name="parameters">The parameters supplied to the activator.</param>
            <param name="instance">The instance.</param>
        </member>
        <member name="E:Autofac.Core.IComponentRegistration.Activated">
            <summary>
            Fired when the activation process for a new instance is complete.
            </summary>
        </member>
        <member name="M:Autofac.Core.IComponentRegistration.RaiseActivated(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Object)">
            <summary>
            Called by the container once an instance has been fully constructed, including
            any requested objects that depend on the instance.
            </summary>
            <param name="context">The context in which the instance was activated.</param>
            <param name="parameters">The parameters supplied to the activator.</param>
            <param name="instance">The instance.</param>
        </member>
        <member name="T:Autofac.Core.IComponentRegistry">
            <summary>
            Provides component registrations according to the services they provide.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistry.Properties">
            <summary>
            Gets the set of properties used during component registration.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.IDictionary`2"/> that can be used to share
            context across registrations.
            </value>
        </member>
        <member name="M:Autofac.Core.IComponentRegistry.TryGetRegistration(Autofac.Core.Service,Autofac.Core.IComponentRegistration@)">
            <summary>
            Attempts to find a default registration for the specified service.
            </summary>
            <param name="service">The service to look up.</param>
            <param name="registration">The default registration for the service.</param>
            <returns>True if a registration exists.</returns>
        </member>
        <member name="M:Autofac.Core.IComponentRegistry.IsRegistered(Autofac.Core.Service)">
            <summary>
            Determines whether the specified service is registered.
            </summary>
            <param name="service">The service to test.</param>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.Core.IComponentRegistry.Register(Autofac.Core.IComponentRegistration)">
            <summary>
            Register a component.
            </summary>
            <param name="registration">The component registration.</param>
        </member>
        <member name="M:Autofac.Core.IComponentRegistry.Register(Autofac.Core.IComponentRegistration,System.Boolean)">
            <summary>
            Register a component.
            </summary>
            <param name="registration">The component registration.</param>
            <param name="preserveDefaults">If true, existing defaults for the services provided by the
            component will not be changed.</param>
        </member>
        <member name="P:Autofac.Core.IComponentRegistry.Registrations">
            <summary>
            Gets the set of registered components.
            </summary>
        </member>
        <member name="M:Autofac.Core.IComponentRegistry.RegistrationsFor(Autofac.Core.Service)">
            <summary>
            Selects from the available registrations after ensuring that any
            dynamic registration sources that may provide <paramref name="service"/>
            have been invoked.
            </summary>
            <param name="service">The service for which registrations are sought.</param>
            <returns>Registrations supporting <paramref name="service"/>.</returns>
        </member>
        <member name="E:Autofac.Core.IComponentRegistry.Registered">
            <summary>
            Fired whenever a component is registered - either explicitly or via a
            <see cref="T:Autofac.Core.IRegistrationSource"/>.
            </summary>
        </member>
        <member name="M:Autofac.Core.IComponentRegistry.AddRegistrationSource(Autofac.Core.IRegistrationSource)">
            <summary>
            Add a registration source that will provide registrations on-the-fly.
            </summary>
            <param name="source">The source to register.</param>
        </member>
        <member name="P:Autofac.Core.IComponentRegistry.Sources">
            <summary>
            Gets the registration sources that are used by the registry.
            </summary>
        </member>
        <member name="P:Autofac.Core.IComponentRegistry.HasLocalComponents">
            <summary>
            Gets a value indicating whether the registry contains its own components.
            True if the registry contains its own components; false if it is forwarding
            registrations from another external registry.
            </summary>
            <remarks>This property is used when walking up the scope tree looking for
            registrations for a new customised scope.</remarks>
        </member>
        <member name="E:Autofac.Core.IComponentRegistry.RegistrationSourceAdded">
            <summary>
            Fired when an <see cref="T:Autofac.Core.IRegistrationSource"/> is added to the registry.
            </summary>
        </member>
        <member name="T:Autofac.Core.IDisposer">
            <summary>
            Provided on an object that will dispose of other objects when it is
            itself disposed.
            </summary>
        </member>
        <member name="M:Autofac.Core.IDisposer.AddInstanceForDisposal(System.IDisposable)">
            <summary>
            Adds an object to the disposer. When the disposer is
            disposed, so will the object be.
            </summary>
            <param name="instance">The instance.</param>
        </member>
        <member name="T:Autofac.Core.IInstanceActivator">
            <summary>
            Activates component instances.
            </summary>
        </member>
        <member name="M:Autofac.Core.IInstanceActivator.ActivateInstance(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Activate an instance in the provided context.
            </summary>
            <param name="context">Context in which to activate instances.</param>
            <param name="parameters">Parameters to the instance.</param>
            <returns>The activated instance.</returns>
            <remarks>
            The context parameter here should probably be ILifetimeScope in order to reveal Disposer,
            but will wait until implementing a concrete use case to make the decision.
            </remarks>
        </member>
        <member name="P:Autofac.Core.IInstanceActivator.LimitType">
            <summary>
            Gets the most specific type that the component instances are known to be castable to.
            </summary>
        </member>
        <member name="T:Autofac.Core.IModule">
            <summary>
            Represents a set of components and related functionality
            packaged together.
            </summary>
        </member>
        <member name="M:Autofac.Core.IModule.Configure(Autofac.Core.IComponentRegistry)">
            <summary>
            Apply the module to the component registry.
            </summary>
            <param name="componentRegistry">Component registry to apply configuration to.</param>
        </member>
        <member name="T:Autofac.Core.InstanceOwnership">
            <summary>
            Determines when instances supporting IDisposable are disposed.
            </summary>
        </member>
        <member name="F:Autofac.Core.InstanceOwnership.ExternallyOwned">
            <summary>
            The lifetime scope does not dispose the instances.
            </summary>
        </member>
        <member name="F:Autofac.Core.InstanceOwnership.OwnedByLifetimeScope">
            <summary>
            The instances are disposed when the lifetime scope is disposed.
            </summary>
        </member>
        <member name="T:Autofac.Core.InstanceSharing">
            <summary>
            Determines whether instances are shared within a lifetime scope.
            </summary>
        </member>
        <member name="F:Autofac.Core.InstanceSharing.None">
            <summary>
            Each request for an instance will return a new object.
            </summary>
        </member>
        <member name="F:Autofac.Core.InstanceSharing.Shared">
            <summary>
            Each request for an instance will return the same object.
            </summary>
        </member>
        <member name="T:Autofac.Core.IRegistrationSource">
            <summary>
            Allows registrations to be made on-the-fly when unregistered
            services are requested (lazy registrations.)
            </summary>
        </member>
        <member name="M:Autofac.Core.IRegistrationSource.RegistrationsFor(Autofac.Core.Service,System.Func{Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.IComponentRegistration}})">
            <summary>
            Retrieve registrations for an unregistered service, to be used
            by the container.
            </summary>
            <param name="service">The service that was requested.</param>
            <param name="registrationAccessor">A function that will return existing registrations for a service.</param>
            <returns>Registrations providing the service.</returns>
            <remarks>
            If the source is queried for service s, and it returns a component that implements both s and s', then it
            will not be queried again for either s or s'. This means that if the source can return other implementations
            of s', it should return these, plus the transitive closure of other components implementing their
            additional services, along with the implementation of s. It is not an error to return components
            that do not implement <paramref name="service"/>.
            </remarks>
        </member>
        <member name="P:Autofac.Core.IRegistrationSource.IsAdapterForIndividualComponents">
            <summary>
            Gets a value indicating whether the registrations provided by this source are 1:1 adapters on top
            of other components (e.g., Meta, Func, or Owned).
            </summary>
        </member>
        <member name="T:Autofac.Core.IServiceWithType">
            <summary>
            Interface supported by services that carry type information.
            </summary>
        </member>
        <member name="P:Autofac.Core.IServiceWithType.ServiceType">
            <summary>
            Gets the type of the service.
            </summary>
            <value>The type of the service.</value>
        </member>
        <member name="M:Autofac.Core.IServiceWithType.ChangeType(System.Type)">
            <summary>
            Return a new service of the same kind, but carrying
            <paramref name="newType"/> as the <see cref="P:Autofac.Core.IServiceWithType.ServiceType"/>.
            </summary>
            <param name="newType">The new service type.</param>
            <returns>A new service with the service type.</returns>
        </member>
        <member name="T:Autofac.Core.ISharingLifetimeScope">
            <summary>
            Defines a nested structure of lifetimes.
            </summary>
        </member>
        <member name="P:Autofac.Core.ISharingLifetimeScope.RootLifetimeScope">
            <summary>
            Gets the root of the sharing hierarchy.
            </summary>
        </member>
        <member name="P:Autofac.Core.ISharingLifetimeScope.ParentLifetimeScope">
            <summary>
            Gets the parent of this node of the hierarchy, or null.
            </summary>
        </member>
        <member name="M:Autofac.Core.ISharingLifetimeScope.GetOrCreateAndShare(System.Guid,System.Func{System.Object})">
            <summary>
            Try to retrieve an instance based on a GUID key. If the instance
            does not exist, invoke <paramref name="creator"/> to create it.
            </summary>
            <param name="id">Key to look up.</param>
            <param name="creator">Creation function.</param>
            <returns>An instance.</returns>
        </member>
        <member name="T:Autofac.Core.KeyedService">
            <summary>
            Identifies a service using a key in addition to its type.
            </summary>
        </member>
        <member name="M:Autofac.Core.KeyedService.#ctor(System.Object,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.KeyedService"/> class.
            </summary>
            <param name="serviceKey">Key of the service.</param>
            <param name="serviceType">Type of the service.</param>
        </member>
        <member name="P:Autofac.Core.KeyedService.ServiceKey">
            <summary>
            Gets the key of the service.
            </summary>
            <value>The key of the service.</value>
        </member>
        <member name="P:Autofac.Core.KeyedService.ServiceType">
            <summary>
            Gets the type of the service.
            </summary>
            <value>The type of the service.</value>
        </member>
        <member name="P:Autofac.Core.KeyedService.Description">
            <summary>
            Gets a human-readable description of the service.
            </summary>
            <value>The description.</value>
        </member>
        <member name="M:Autofac.Core.KeyedService.Equals(Autofac.Core.KeyedService)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
        </member>
        <member name="M:Autofac.Core.KeyedService.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>.</param>
            <returns>
            true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.
            </returns>
            <exception cref="T:System.NullReferenceException">The <paramref name="obj"/> parameter is null.</exception>
        </member>
        <member name="M:Autofac.Core.KeyedService.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:Autofac.Core.KeyedService.ChangeType(System.Type)">
            <summary>
            Return a new service of the same kind, but carrying
            <paramref name="newType"/> as the <see cref="P:Autofac.Core.KeyedService.ServiceType"/>.
            </summary>
            <param name="newType">The new service type.</param>
            <returns>A new service with the service type.</returns>
        </member>
        <member name="T:Autofac.Core.Lifetime.CurrentScopeLifetime">
            <summary>
            Attaches the instance's lifetime to the current lifetime scope.
            </summary>
        </member>
        <member name="M:Autofac.Core.Lifetime.CurrentScopeLifetime.FindScope(Autofac.Core.ISharingLifetimeScope)">
            <summary>
            Given the most nested scope visible within the resolve operation, find
            the scope for the component.
            </summary>
            <param name="mostNestedVisibleScope">The most nested visible scope.</param>
            <returns>The scope for the component.</returns>
        </member>
        <member name="T:Autofac.Core.Lifetime.LifetimeScope">
            <summary>
            Lifetime scope implementation.
            </summary>
        </member>
        <member name="F:Autofac.Core.Lifetime.LifetimeScope._synchRoot">
            <summary>
            Protects shared instances from concurrent access. Other members and the base class are threadsafe.
            </summary>
        </member>
        <member name="F:Autofac.Core.Lifetime.LifetimeScope.RootTag">
            <summary>
            The tag applied to root scopes when no other tag is specified.
            </summary>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.#ctor(Autofac.Core.IComponentRegistry,Autofac.Core.Lifetime.LifetimeScope,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Lifetime.LifetimeScope"/> class.
            </summary>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <param name="componentRegistry">Components used in the scope.</param>
            <param name="parent">Parent scope.</param>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.#ctor(Autofac.Core.IComponentRegistry,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Lifetime.LifetimeScope"/> class.
            </summary>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <param name="componentRegistry">Components used in the scope.</param>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.#ctor(Autofac.Core.IComponentRegistry)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Lifetime.LifetimeScope"/> class.
            </summary>
            <param name="componentRegistry">Components used in the scope.</param>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.BeginLifetimeScope">
            <summary>
            Begin a new anonymous sub-scope. Instances created via the sub-scope
            will be disposed along with it.
            </summary>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.BeginLifetimeScope(System.Object)">
            <summary>
            Begin a new tagged sub-scope. Instances created via the sub-scope
            will be disposed along with it.
            </summary>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.BeginLifetimeScope(System.Action{Autofac.ContainerBuilder})">
            <summary>
            Begin a new anonymous sub-scope, with additional components available to it.
            Component instances created via the new scope
            will be disposed along with it.
            </summary>
            <param name="configurationAction">Action on a <see cref="T:Autofac.ContainerBuilder"/>
            that adds component registrations visible only in the new scope.</param>
            <returns>A new lifetime scope.</returns>
            <example>
            <code>
            IContainer cr = // ...
            using (var lifetime = cr.BeginLifetimeScope(builder =&gt; {
                    builder.RegisterType&lt;Foo&gt;();
                    builder.RegisterType&lt;Bar&gt;().As&lt;IBar&gt;(); })
            {
                var foo = lifetime.Resolve&lt;Foo&gt;();
            }
            </code>
            </example>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.BeginLifetimeScope(System.Object,System.Action{Autofac.ContainerBuilder})">
            <summary>
            Begin a new tagged sub-scope, with additional components available to it.
            Component instances created via the new scope
            will be disposed along with it.
            </summary>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <param name="configurationAction">Action on a <see cref="T:Autofac.ContainerBuilder"/>
            that adds component registrations visible only in the new scope.</param>
            <returns>A new lifetime scope.</returns>
            <example>
            <code>
            IContainer cr = // ...
            using (var lifetime = cr.BeginLifetimeScope("unitOfWork", builder =&gt; {
                    builder.RegisterType&lt;Foo&gt;();
                    builder.RegisterType&lt;Bar&gt;().As&lt;IBar&gt;(); })
            {
                var foo = lifetime.Resolve&lt;Foo&gt;();
            }
            </code>
            </example>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.CreateScopeRestrictedRegistry(System.Object,System.Action{Autofac.ContainerBuilder})">
            <summary>
            Creates and setup the registry for a child scope.
            </summary>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <param name="configurationAction">Action on a <see cref="T:Autofac.ContainerBuilder"/>
            that adds component registrations visible only in the child scope.</param>
            <returns>Registry to use for a child scope.</returns>
            <remarks>It is the responsibility of the caller to make sure that the registry is properly
            disposed of. This is generally done by adding the registry to the <see cref="P:Autofac.Core.Lifetime.LifetimeScope.Disposer"/>
            property of the child scope.</remarks>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Resolve an instance of the provided registration within the context.
            </summary>
            <param name="registration">The registration.</param>
            <param name="parameters">Parameters for the instance.</param>
            <returns>
            The component instance.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScope.ParentLifetimeScope">
            <summary>
            Gets the parent of this node of the hierarchy, or null.
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScope.RootLifetimeScope">
            <summary>
            Gets the root of the sharing hierarchy.
            </summary>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.GetOrCreateAndShare(System.Guid,System.Func{System.Object})">
            <summary>
            Try to retrieve an instance based on a GUID key. If the instance
            does not exist, invoke <paramref name="creator"/> to create it.
            </summary>
            <param name="id">Key to look up.</param>
            <param name="creator">Creation function.</param>
            <returns>An instance.</returns>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScope.Disposer">
            <summary>
            Gets the disposer associated with this container. Instances can be associated
            with it manually if required.
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScope.Tag">
            <summary>
            Gets the tag applied to the lifetime scope.
            </summary>
            <remarks>The tag applied to this scope and the contexts generated when
            it resolves component dependencies.</remarks>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScope.ComponentRegistry">
            <summary>
            Gets the services associated with the components that provide them.
            </summary>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScope.GetService(System.Type)">
            <summary>
            Gets the service object of the specified type.
            </summary>
            <param name="serviceType">An object that specifies the type of service object
            to get.</param>
            <returns>
            A service object of type <paramref name="serviceType"/>.-or- null if there is
            no service object of type <paramref name="serviceType"/>.
            </returns>
        </member>
        <member name="E:Autofac.Core.Lifetime.LifetimeScope.ChildLifetimeScopeBeginning">
            <summary>
            Fired when a new scope based on the current scope is beginning.
            </summary>
        </member>
        <member name="E:Autofac.Core.Lifetime.LifetimeScope.CurrentScopeEnding">
            <summary>
            Fired when this scope is ending.
            </summary>
        </member>
        <member name="E:Autofac.Core.Lifetime.LifetimeScope.ResolveOperationBeginning">
            <summary>
            Fired when a resolve operation is beginning in this scope.
            </summary>
        </member>
        <member name="T:Autofac.Core.Lifetime.LifetimeScopeBeginningEventArgs">
            <summary>
            Describes when a lifetime scope is beginning.
            </summary>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScopeBeginningEventArgs.#ctor(Autofac.ILifetimeScope)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Lifetime.LifetimeScopeBeginningEventArgs"/> class.
            </summary>
            <param name="lifetimeScope">The lifetime scope that is beginning.</param>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScopeBeginningEventArgs.LifetimeScope">
            <summary>
            Gets the lifetime scope that is beginning.
            </summary>
        </member>
        <member name="T:Autofac.Core.Lifetime.LifetimeScopeEndingEventArgs">
            <summary>
            Describes when a lifetime scope is ending.
            </summary>
        </member>
        <member name="M:Autofac.Core.Lifetime.LifetimeScopeEndingEventArgs.#ctor(Autofac.ILifetimeScope)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Lifetime.LifetimeScopeEndingEventArgs"/> class.
            </summary>
            <param name="lifetimeScope">The lifetime scope that is ending.</param>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScopeEndingEventArgs.LifetimeScope">
            <summary>
            Gets the lifetime scope that is ending.
            </summary>
        </member>
        <member name="T:Autofac.Core.Lifetime.LifetimeScopeResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScopeResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScopeResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScopeResources.DuplicateTagDetected">
            <summary>
              Looks up a localized string similar to The tag &apos;{0}&apos; has already been assigned to a parent lifetime scope..
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScopeResources.ScopeIsDisposed">
            <summary>
              Looks up a localized string similar to Instances cannot be resolved and nested lifetimes cannot be created from this LifetimeScope as it has already been disposed..
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.LifetimeScopeResources.SelfConstructingDependencyDetected">
            <summary>
              Looks up a localized string similar to The constructor of type &apos;{0}&apos; attempted to create another instance of itself. This is not permitted because the service is configured to only allowed a single instance per lifetime scope..
            </summary>
        </member>
        <member name="T:Autofac.Core.Lifetime.MatchingScopeLifetime">
            <summary>
            Attaches the component's lifetime to scopes matching a supplied expression.
            </summary>
        </member>
        <member name="M:Autofac.Core.Lifetime.MatchingScopeLifetime.#ctor(System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Lifetime.MatchingScopeLifetime"/> class.
            </summary>
            <param name="lifetimeScopeTagsToMatch">The tags applied to matching scopes.</param>
        </member>
        <member name="P:Autofac.Core.Lifetime.MatchingScopeLifetime.TagsToMatch">
            <summary>
            Gets the list of lifetime scope tags to match.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of object tags to match
            when searching for the lifetime scope for the component.
            </value>
        </member>
        <member name="M:Autofac.Core.Lifetime.MatchingScopeLifetime.FindScope(Autofac.Core.ISharingLifetimeScope)">
            <summary>
            Given the most nested scope visible within the resolve operation, find
            the scope for the component.
            </summary>
            <param name="mostNestedVisibleScope">The most nested visible scope.</param>
            <returns>The scope for the component.</returns>
        </member>
        <member name="T:Autofac.Core.Lifetime.MatchingScopeLifetimeResources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.MatchingScopeLifetimeResources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.MatchingScopeLifetimeResources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Lifetime.MatchingScopeLifetimeResources.MatchingScopeNotFound">
             <summary>
                Looks up a localized string similar to No scope with a tag matching &apos;{0}&apos; is visible from the scope in which the instance was requested.
            
            If you see this during execution of a web application, it generally indicates that a component registered as per-HTTP request is being requested by a SingleInstance() component (or a similar scenario). Under the web integration always request dependencies from the dependency resolver or the request lifetime scope, never from the container itself..
             </summary>
        </member>
        <member name="T:Autofac.Core.Lifetime.MatchingScopeLifetimeTags">
            <summary>
            Well-known tags used in setting up matching lifetime scopes.
            </summary>
        </member>
        <member name="F:Autofac.Core.Lifetime.MatchingScopeLifetimeTags.RequestLifetimeScopeTag">
            <summary>
            Tag used in setting up per-request lifetime scope registrations
            (e.g., per-HTTP-request or per-API-request).
            </summary>
        </member>
        <member name="T:Autofac.Core.Lifetime.RootScopeLifetime">
            <summary>
            Attaches the component's lifetime to the root scope.
            </summary>
        </member>
        <member name="M:Autofac.Core.Lifetime.RootScopeLifetime.FindScope(Autofac.Core.ISharingLifetimeScope)">
            <summary>
            Given the most nested scope visible within the resolve operation, find
            the scope for the component.
            </summary>
            <param name="mostNestedVisibleScope">The most nested visible scope.</param>
            <returns>The scope for the component.</returns>
        </member>
        <member name="T:Autofac.Core.NamedPropertyParameter">
            <summary>
            A property identified by name. When applied to a reflection-based
            component, the name will be matched against property names.
            </summary>
        </member>
        <member name="P:Autofac.Core.NamedPropertyParameter.Name">
            <summary>
            Gets the name of the property.
            </summary>
        </member>
        <member name="M:Autofac.Core.NamedPropertyParameter.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.NamedPropertyParameter"/> class.
            </summary>
            <param name="name">The name of the property.</param>
            <param name="value">The property value.</param>
        </member>
        <member name="T:Autofac.Core.Parameter">
            <summary>
            Used in order to provide a value to a constructor parameter or property on an instance
            being created by the container.
            </summary>
            <remarks>
            Not all parameters can be applied to all sites.
            </remarks>
        </member>
        <member name="M:Autofac.Core.Parameter.CanSupplyValue(System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Func{System.Object}@)">
            <summary>
            Returns true if the parameter is able to provide a value to a particular site.
            </summary>
            <param name="pi">Constructor, method, or property-mutator parameter.</param>
            <param name="context">The component context in which the value is being provided.</param>
            <param name="valueProvider">If the result is true, the valueProvider parameter will
            be set to a function that will lazily retrieve the parameter value. If the result is false,
            will be set to null.</param>
            <returns>True if a value can be supplied; otherwise, false.</returns>
        </member>
        <member name="T:Autofac.Core.PreparingEventArgs">
            <summary>
            Fired before the activation process to allow parameters to be changed or an alternative
            instance to be provided.
            </summary>
        </member>
        <member name="M:Autofac.Core.PreparingEventArgs.#ctor(Autofac.IComponentContext,Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.PreparingEventArgs"/> class.
            </summary>
            <param name="context">The context.</param>
            <param name="component">The component.</param>
            <param name="parameters">The parameters.</param>
        </member>
        <member name="P:Autofac.Core.PreparingEventArgs.Context">
            <summary>
            Gets the context in which the activation is occurring.
            </summary>
        </member>
        <member name="P:Autofac.Core.PreparingEventArgs.Component">
            <summary>
            Gets the component providing the instance being activated.
            </summary>
        </member>
        <member name="P:Autofac.Core.PreparingEventArgs.Parameters">
            <summary>
            Gets or sets the parameters supplied to the activator.
            </summary>
        </member>
        <member name="T:Autofac.Core.RegistrationSourceAddedEventArgs">
            <summary>
            Fired when an <see cref="T:Autofac.Core.IRegistrationSource"/> is added to the registry.
            </summary>
        </member>
        <member name="M:Autofac.Core.RegistrationSourceAddedEventArgs.#ctor(Autofac.Core.IComponentRegistry,Autofac.Core.IRegistrationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.RegistrationSourceAddedEventArgs"/> class.
            </summary>
            <param name="componentRegistry">The registry to which the source was added.</param>
            <param name="registrationSource">The source that was added.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Autofac.Core.RegistrationSourceAddedEventArgs.RegistrationSource">
            <summary>
            Gets the registry to which the source was added.
            </summary>
        </member>
        <member name="P:Autofac.Core.RegistrationSourceAddedEventArgs.ComponentRegistry">
            <summary>
            Gets the source that was added.
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.ComponentNotRegisteredException">
            <summary>
            A service was requested that cannot be provided by the container. To avoid this exception, either register a component
            to provide the required service, check for service registration using IsRegistered(), or use the ResolveOptional()
            method to resolve an optional dependency.
            </summary>
            <remarks>This exception is fatal. See <see cref="T:Autofac.Core.DependencyResolutionException"/> for more information.</remarks>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentNotRegisteredException.#ctor(Autofac.Core.Service)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/> class.
            </summary>
            <param name="service">The service.</param>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentNotRegisteredException.#ctor(Autofac.Core.Service,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/> class.
            </summary>
            <param name="service">The service.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:Autofac.Core.Registration.ComponentNotRegisteredExceptionResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentNotRegisteredExceptionResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentNotRegisteredExceptionResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentNotRegisteredExceptionResources.Message">
            <summary>
              Looks up a localized string similar to The requested service &apos;{0}&apos; has not been registered. To avoid this exception, either register a component to provide the service, check for service registration using IsRegistered(), or use the ResolveOptional() method to resolve an optional dependency..
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.ComponentRegistration">
            <summary>
            Describes a logical component within the container.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistration.#ctor(System.Guid,Autofac.Core.IInstanceActivator,Autofac.Core.IComponentLifetime,Autofac.Core.InstanceSharing,Autofac.Core.InstanceOwnership,System.Collections.Generic.IEnumerable{Autofac.Core.Service},System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ComponentRegistration"/> class.
            </summary>
            <param name="id">Unique identifier for the component.</param>
            <param name="activator">Activator used to activate instances.</param>
            <param name="lifetime">Determines how the component will be associated with its lifetime.</param>
            <param name="sharing">Whether the component is shared within its lifetime scope.</param>
            <param name="ownership">Whether the component instances are disposed at the end of their lifetimes.</param>
            <param name="services">Services the component provides.</param>
            <param name="metadata">Data associated with the component.</param>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistration.#ctor(System.Guid,Autofac.Core.IInstanceActivator,Autofac.Core.IComponentLifetime,Autofac.Core.InstanceSharing,Autofac.Core.InstanceOwnership,System.Collections.Generic.IEnumerable{Autofac.Core.Service},System.Collections.Generic.IDictionary{System.String,System.Object},Autofac.Core.IComponentRegistration)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ComponentRegistration"/> class.
            </summary>
            <param name="id">Unique identifier for the component.</param>
            <param name="activator">Activator used to activate instances.</param>
            <param name="lifetime">Determines how the component will be associated with its lifetime.</param>
            <param name="sharing">Whether the component is shared within its lifetime scope.</param>
            <param name="ownership">Whether the component instances are disposed at the end of their lifetimes.</param>
            <param name="services">Services the component provides.</param>
            <param name="metadata">Data associated with the component.</param>
            <param name="target">The component registration upon which this registration is based.</param>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistration.Target">
            <summary>
            Gets the component registration upon which this registration is based.
            If this registration was created directly by the user, returns this.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistration.Id">
            <summary>
            Gets a unique identifier for this component (shared in all sub-contexts.)
            This value also appears in Services.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistration.Activator">
            <summary>
            Gets or sets the activator used to create instances.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistration.Lifetime">
            <summary>
            Gets the lifetime associated with the component.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistration.Sharing">
            <summary>
            Gets information about whether the component instances are shared or not.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistration.Ownership">
            <summary>
            Gets information about whether the instances of the component should be disposed by the container.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistration.Services">
            <summary>
            Gets the services provided by the component.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistration.Metadata">
            <summary>
            Gets additional data associated with the component.
            </summary>
        </member>
        <member name="E:Autofac.Core.Registration.ComponentRegistration.Preparing">
            <summary>
            Fired when a new instance is required. The instance can be
            provided in order to skip the regular activator, by setting the Instance property in
            the provided event arguments.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistration.RaisePreparing(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter}@)">
            <summary>
            Called by the container when an instance is required.
            </summary>
            <param name="context">The context in which the instance will be activated.</param>
            <param name="parameters">Parameters for activation.</param>
        </member>
        <member name="E:Autofac.Core.Registration.ComponentRegistration.Activating">
            <summary>
            Fired when a new instance is being activated. The instance can be
            wrapped or switched at this time by setting the Instance property in
            the provided event arguments.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistration.RaiseActivating(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Object@)">
            <summary>
            Called by the container once an instance has been constructed.
            </summary>
            <param name="context">The context in which the instance was activated.</param>
            <param name="parameters">The parameters supplied to the activator.</param>
            <param name="instance">The instance.</param>
        </member>
        <member name="E:Autofac.Core.Registration.ComponentRegistration.Activated">
            <summary>
            Fired when the activation process for a new instance is complete.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistration.RaiseActivated(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Object)">
            <summary>
            Called by the container once an instance has been fully constructed, including
            any requested objects that depend on the instance.
            </summary>
            <param name="context">The context in which the instance was activated.</param>
            <param name="parameters">The parameters supplied to the activator.</param>
            <param name="instance">The instance.</param>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistration.ToString">
            <summary>
            Describes the component in a human-readable form.
            </summary>
            <returns>A description of the component.</returns>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistration.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="T:Autofac.Core.Registration.ComponentRegistrationLifetimeDecorator">
            <summary>
            Wraps a component registration, switching its lifetime.
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.ComponentRegistrationResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistrationResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistrationResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistrationResources.ToStringFormat">
            <summary>
              Looks up a localized string similar to Activator = {0}, Services = [{1}], Lifetime = {2}, Sharing = {3}, Ownership = {4}.
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.ComponentRegistry">
            <summary>
            Maps services onto the components that provide them.
            </summary>
            <remarks>
            The component registry provides services directly from components,
            and also uses <see cref="T:Autofac.Core.IRegistrationSource"/> to generate components
            on-the-fly or as adapters for other components. A component registry
            is normally used through a <see cref="T:Autofac.ContainerBuilder"/>, and not
            directly by application code.
            </remarks>
        </member>
        <member name="F:Autofac.Core.Registration.ComponentRegistry._synchRoot">
            <summary>
            Protects instance variables from concurrent access.
            </summary>
        </member>
        <member name="F:Autofac.Core.Registration.ComponentRegistry._dynamicRegistrationSources">
            <summary>
            External registration sources.
            </summary>
        </member>
        <member name="F:Autofac.Core.Registration.ComponentRegistry._registrations">
            <summary>
            All registrations.
            </summary>
        </member>
        <member name="F:Autofac.Core.Registration.ComponentRegistry._serviceInfo">
            <summary>
            Keeps track of the status of registered services.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ComponentRegistry"/> class.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ComponentRegistry"/> class.
            </summary>
            <param name="properties">The properties used during component registration.</param>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistry.Properties">
            <summary>
            Gets the set of properties used during component registration.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.IDictionary`2"/> that can be used to share
            context across registrations.
            </value>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.TryGetRegistration(Autofac.Core.Service,Autofac.Core.IComponentRegistration@)">
            <summary>
            Attempts to find a default registration for the specified service.
            </summary>
            <param name="service">The service to look up.</param>
            <param name="registration">The default registration for the service.</param>
            <returns>True if a registration exists.</returns>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.IsRegistered(Autofac.Core.Service)">
            <summary>
            Determines whether the specified service is registered.
            </summary>
            <param name="service">The service to test.</param>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.Register(Autofac.Core.IComponentRegistration)">
            <summary>
            Register a component.
            </summary>
            <param name="registration">The component registration.</param>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.Register(Autofac.Core.IComponentRegistration,System.Boolean)">
            <summary>
            Register a component.
            </summary>
            <param name="registration">The component registration.</param>
            <param name="preserveDefaults">If true, existing defaults for the services provided by the
            component will not be changed.</param>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistry.Registrations">
            <summary>
            Gets the registered components.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.RegistrationsFor(Autofac.Core.Service)">
            <summary>
            Selects from the available registrations after ensuring that any
            dynamic registration sources that may provide <paramref name="service"/>
            have been invoked.
            </summary>
            <param name="service">The service for which registrations are sought.</param>
            <returns>Registrations supporting <paramref name="service"/>.</returns>
        </member>
        <member name="E:Autofac.Core.Registration.ComponentRegistry.Registered">
            <summary>
            Fired whenever a component is registered - either explicitly or via a
            <see cref="T:Autofac.Core.IRegistrationSource"/>.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ComponentRegistry.AddRegistrationSource(Autofac.Core.IRegistrationSource)">
            <summary>
            Add a registration source that will provide registrations on-the-fly.
            </summary>
            <param name="source">The source to register.</param>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistry.Sources">
            <summary>
            Gets the registration sources that are used by the registry.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ComponentRegistry.HasLocalComponents">
            <summary>
            Gets a value indicating whether the registry contains its own components.
            True if the registry contains its own components; false if it is forwarding
            registrations from another external registry.
            </summary>
            <remarks>This property is used when walking up the scope tree looking for
            registrations for a new customised scope.</remarks>
        </member>
        <member name="E:Autofac.Core.Registration.ComponentRegistry.RegistrationSourceAdded">
            <summary>
            Fired when an <see cref="T:Autofac.Core.IRegistrationSource"/> is added to the registry.
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.CopyOnWriteRegistry">
            <summary>
            Delegates registration lookups to a specified registry. When write operations are applied,
            initialises a new 'writeable' registry.
            </summary>
            <remarks>
            Safe for concurrent access by multiple readers. Write operations are single-threaded.
            </remarks>
        </member>
        <member name="P:Autofac.Core.Registration.CopyOnWriteRegistry.Properties">
            <summary>
            Gets or sets the set of properties used during component registration.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.IDictionary`2"/> that can be used to share
            context across registrations.
            </value>
        </member>
        <member name="T:Autofac.Core.Registration.ExternalRegistrySource">
            <summary>
            Pulls registrations from another component registry.
            Excludes most auto-generated registrations - currently has issues with
            collection registrations.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ExternalRegistrySource.#ctor(Autofac.Core.IComponentRegistry)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ExternalRegistrySource"/> class.
            </summary>
            <param name="registry">Component registry to pull registrations from.</param>
        </member>
        <member name="M:Autofac.Core.Registration.ExternalRegistrySource.RegistrationsFor(Autofac.Core.Service,System.Func{Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.IComponentRegistration}})">
            <summary>
            Retrieve registrations for an unregistered service, to be used
            by the container.
            </summary>
            <param name="service">The service that was requested.</param>
            <param name="registrationAccessor">A function that will return existing registrations for a service.</param>
            <returns>Registrations providing the service.</returns>
        </member>
        <member name="P:Autofac.Core.Registration.ExternalRegistrySource.IsAdapterForIndividualComponents">
            <summary>
            Gets a value indicating whether components are adapted from the same logical scope.
            In this case because the components that are adapted do not come from the same
            logical scope, we must return false to avoid duplicating them.
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.ExternalRegistrySource.ExternalComponentRegistration">
            <summary>
             ComponentRegistration subtyped only to distinguish it from other adapted registrations.
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.IModuleRegistrar">
            <summary>
            Interface providing fluent syntax for chaining module registrations.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.IModuleRegistrar.RegisterModule(Autofac.Core.IModule)">
            <summary>
            Add a module to the container.
            </summary>
            <param name="module">The module to add.</param>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="T:Autofac.Core.Registration.ModuleRegistrar">
            <summary>
            Basic implementation of the <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/>
            interface allowing registration of modules into a <see cref="T:Autofac.ContainerBuilder"/>
            in a fluent format.
            </summary>
        </member>
        <member name="F:Autofac.Core.Registration.ModuleRegistrar._builder">
            <summary>
            The <see cref="T:Autofac.ContainerBuilder"/> into which registrations will be made.
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ModuleRegistrar.#ctor(Autofac.ContainerBuilder)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ModuleRegistrar"/> class.
            </summary>
            <param name="builder">
            The <see cref="T:Autofac.ContainerBuilder"/> into which registrations will be made.
            </param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="builder" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Autofac.Core.Registration.ModuleRegistrar.RegisterModule(Autofac.Core.IModule)">
            <summary>
            Add a module to the container.
            </summary>
            <param name="module">The module to add.</param>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="module" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="T:Autofac.Core.Registration.ScopeRestrictedRegistry">
            <summary>
            Switches components with a RootScopeLifetime (singletons) with
            decorators exposing MatchingScopeLifetime targeting the specified scope.
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.ServiceRegistrationInfo">
            <summary>
            Tracks the services known to the registry.
            </summary>
        </member>
        <member name="F:Autofac.Core.Registration.ServiceRegistrationInfo._defaultImplementations">
            <summary>
             List of implicit default service implementations. Overriding default implementations are appended to the end,
             so the enumeration should begin from the end too, and the most default implementation comes last.
            </summary>
        </member>
        <member name="F:Autofac.Core.Registration.ServiceRegistrationInfo._sourceImplementations">
            <summary>
             List of service implementations coming from sources. Sources have priority over preserve-default implementations.
             Implementations from sources are enumerated in preserve-default order, so the most default implementation comes first.
            </summary>
        </member>
        <member name="F:Autofac.Core.Registration.ServiceRegistrationInfo._preserveDefaultImplementations">
            <summary>
             List of explicit service implementations specified with the PreserveExistingDefaults option.
             Enumerated in preserve-defaults order, so the most default implementation comes first.
            </summary>
        </member>
        <member name="F:Autofac.Core.Registration.ServiceRegistrationInfo._sourcesToQuery">
            <summary>
            Used for bookkeeping so that the same source is not queried twice (may be null).
            </summary>
        </member>
        <member name="M:Autofac.Core.Registration.ServiceRegistrationInfo.#ctor(Autofac.Core.Service)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Registration.ServiceRegistrationInfo"/> class.
            </summary>
            <param name="service">The tracked service.</param>
        </member>
        <member name="P:Autofac.Core.Registration.ServiceRegistrationInfo.IsInitialized">
            <summary>
            Gets a value indicating whether the first time a service is requested, initialization (e.g. reading from sources)
            happens. This value will then be set to true. Calling many methods on this type before
            initialisation is an error.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ServiceRegistrationInfo.Implementations">
            <summary>
            Gets the known implementations. The first implementation is a default one.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ServiceRegistrationInfo.IsRegistered">
            <summary>
            Gets a value indicating whether any implementations are known.
            </summary>
        </member>
        <member name="T:Autofac.Core.Registration.ServiceRegistrationInfoResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ServiceRegistrationInfoResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ServiceRegistrationInfoResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ServiceRegistrationInfoResources.NotDuringInitialization">
            <summary>
              Looks up a localized string similar to The operation is only valid during initialization..
            </summary>
        </member>
        <member name="P:Autofac.Core.Registration.ServiceRegistrationInfoResources.NotInitialized">
            <summary>
              Looks up a localized string similar to The operation is not valid until the object is initialized..
            </summary>
        </member>
        <member name="T:Autofac.Core.ResolvedParameter">
            <summary>
            Flexible parameter type allows arbitrary values to be retrieved
            from the resolution context.
            </summary>
        </member>
        <member name="M:Autofac.Core.ResolvedParameter.#ctor(System.Func{System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Boolean},System.Func{System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.ResolvedParameter"/> class.
            </summary>
            <param name="predicate">A predicate that determines which parameters on a constructor will be supplied by this instance.</param>
            <param name="valueAccessor">A function that supplies the parameter value given the context.</param>
        </member>
        <member name="M:Autofac.Core.ResolvedParameter.CanSupplyValue(System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Func{System.Object}@)">
            <summary>
            Returns true if the parameter is able to provide a value to a particular site.
            </summary>
            <param name="pi">Constructor, method, or property-mutator parameter.</param>
            <param name="context">The component context in which the value is being provided.</param>
            <param name="valueProvider">If the result is true, the valueProvider parameter will
            be set to a function that will lazily retrieve the parameter value. If the result is false,
            will be set to null.</param>
            <returns>True if a value can be supplied; otherwise, false.</returns>
        </member>
        <member name="M:Autofac.Core.ResolvedParameter.ForNamed``1(System.String)">
            <summary>
            Construct a <see cref="T:Autofac.Core.ResolvedParameter"/> that will match parameters of type
            <typeparamref name="TService"/> and resolve for those parameters an implementation
            registered with the name <paramref name="serviceName"/>.
            </summary>
            <typeparam name="TService">The type of the parameter to match.</typeparam>
            <param name="serviceName">The name of the matching service to resolve.</param>
            <returns>A configured <see cref="T:Autofac.Core.ResolvedParameter"/> instance.</returns>
            <remarks>
            </remarks>
        </member>
        <member name="M:Autofac.Core.ResolvedParameter.ForKeyed``1(System.Object)">
            <summary>
            Construct a <see cref="T:Autofac.Core.ResolvedParameter"/> that will match parameters of type
            <typeparamref name="TService"/> and resolve for those parameters an implementation
            registered with the key <paramref name="serviceKey"/>.
            </summary>
            <typeparam name="TService">The type of the parameter to match.</typeparam>
            <param name="serviceKey">The key of the matching service to resolve.</param>
            <returns>A configured <see cref="T:Autofac.Core.ResolvedParameter"/> instance.</returns>
        </member>
        <member name="F:Autofac.Core.Resolving.CircularDependencyDetector.MaxResolveDepth">
            <summary>
            Catch circular dependencies that are triggered by post-resolve processing (e.g. 'OnActivated').
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.CircularDependencyDetectorResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.CircularDependencyDetectorResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.CircularDependencyDetectorResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.CircularDependencyDetectorResources.CircularDependency">
            <summary>
              Looks up a localized string similar to Circular component dependency detected: {0}..
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.CircularDependencyDetectorResources.MaxDepthExceeded">
            <summary>
              Looks up a localized string similar to Probable circular dependency between factory-scoped components. Chain includes &apos;{0}&apos;.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.ComponentActivationResources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ComponentActivationResources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ComponentActivationResources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ComponentActivationResources.ActivationAlreadyExecuted">
            <summary>
               Looks up a localized string similar to The activation has already been executed: {0}.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ComponentActivationResources.ErrorDuringActivation">
            <summary>
               Looks up a localized string similar to An error occurred during the activation of a particular registration. See the inner exception for details. Registration: {0}.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ComponentActivationResources.UnableToLocateLifetimeScope">
             <summary>
                Looks up a localized string similar to Unable to resolve the type &apos;{0}&apos; because the lifetime scope it belongs in can&apos;t be located. The following services are exposed by this registration:
            {1}
            Details.
             </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.IInstanceLookup">
            <summary>
            Represents the process of finding a component during a resolve operation.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.IInstanceLookup.ComponentRegistration">
            <summary>
            Gets the component for which an instance is to be looked up.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.IInstanceLookup.ActivationScope">
            <summary>
            Gets the scope in which the instance will be looked up.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.IInstanceLookup.Parameters">
            <summary>
            Gets the parameters provided for new instance creation.
            </summary>
        </member>
        <member name="E:Autofac.Core.Resolving.IInstanceLookup.InstanceLookupEnding">
            <summary>
            Raised when the lookup phase of the operation is ending.
            </summary>
        </member>
        <member name="E:Autofac.Core.Resolving.IInstanceLookup.CompletionBeginning">
            <summary>
            Raised when the completion phase of an instance lookup operation begins.
            </summary>
        </member>
        <member name="E:Autofac.Core.Resolving.IInstanceLookup.CompletionEnding">
            <summary>
            Raised when the completion phase of an instance lookup operation ends.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.InstanceLookupBeginningEventArgs">
            <summary>
            Fired when instance lookup is complete.
            </summary>
        </member>
        <member name="M:Autofac.Core.Resolving.InstanceLookupBeginningEventArgs.#ctor(Autofac.Core.Resolving.IInstanceLookup)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Resolving.InstanceLookupBeginningEventArgs"/> class.
            </summary>
            <param name="instanceLookup">The instance lookup that is ending.</param>
        </member>
        <member name="P:Autofac.Core.Resolving.InstanceLookupBeginningEventArgs.InstanceLookup">
            <summary>
            Gets the instance lookup operation that is beginning.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.InstanceLookupCompletionBeginningEventArgs">
            <summary>
            Raised when the completion phase of an instance lookup operation begins.
            </summary>
        </member>
        <member name="M:Autofac.Core.Resolving.InstanceLookupCompletionBeginningEventArgs.#ctor(Autofac.Core.Resolving.IInstanceLookup)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Resolving.InstanceLookupCompletionBeginningEventArgs"/> class.
            </summary>
            <param name="instanceLookup">The instance lookup that is beginning the completion phase.</param>
        </member>
        <member name="P:Autofac.Core.Resolving.InstanceLookupCompletionBeginningEventArgs.InstanceLookup">
            <summary>
            Gets the instance lookup operation that is beginning the completion phase.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.InstanceLookupCompletionEndingEventArgs">
            <summary>
            Raised when the completion phase of an instance lookup operation ends.
            </summary>
        </member>
        <member name="M:Autofac.Core.Resolving.InstanceLookupCompletionEndingEventArgs.#ctor(Autofac.Core.Resolving.IInstanceLookup)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Resolving.InstanceLookupCompletionEndingEventArgs"/> class.
            </summary>
            <param name="instanceLookup">The instance lookup that is ending the completion phase.</param>
        </member>
        <member name="P:Autofac.Core.Resolving.InstanceLookupCompletionEndingEventArgs.InstanceLookup">
            <summary>
            Gets the instance lookup operation that is ending the completion phase.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.InstanceLookupEndingEventArgs">
            <summary>
            Fired when an instance is looked up.
            </summary>
        </member>
        <member name="M:Autofac.Core.Resolving.InstanceLookupEndingEventArgs.#ctor(Autofac.Core.Resolving.IInstanceLookup,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Resolving.InstanceLookupEndingEventArgs"/> class.
            </summary>
            <param name="instanceLookup">The instance lookup that is ending.</param>
            <param name="newInstanceActivated">True if a new instance was created as part of the operation.</param>
        </member>
        <member name="P:Autofac.Core.Resolving.InstanceLookupEndingEventArgs.NewInstanceActivated">
            <summary>
            Gets a value indicating whether a new instance was created as part of the operation.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.InstanceLookupEndingEventArgs.InstanceLookup">
            <summary>
            Gets the instance lookup operation that is ending.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.IResolveOperation">
            <summary>
            An <see cref="T:Autofac.Core.Resolving.IResolveOperation"/> is a component context that sequences and monitors the multiple
            activations that go into producing a single requested object graph.
            </summary>
        </member>
        <member name="M:Autofac.Core.Resolving.IResolveOperation.GetOrCreateInstance(Autofac.Core.ISharingLifetimeScope,Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Get or create and share an instance of <paramref name="registration"/> in the <paramref name="currentOperationScope"/>.
            </summary>
            <param name="currentOperationScope">The scope in the hierarchy in which the operation will begin.</param>
            <param name="registration">The component to resolve.</param>
            <param name="parameters">Parameters for the component.</param>
            <returns>The component instance.</returns>
        </member>
        <member name="E:Autofac.Core.Resolving.IResolveOperation.CurrentOperationEnding">
            <summary>
            Raised when the entire operation is complete.
            </summary>
        </member>
        <member name="E:Autofac.Core.Resolving.IResolveOperation.InstanceLookupBeginning">
            <summary>
            Raised when an instance is looked up within the operation.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.ResolveOperation">
            <summary>
            A <see cref="T:Autofac.Core.Resolving.ResolveOperation"/> is a component context that sequences and monitors the multiple
            activations that go into producing a single requested object graph.
            </summary>
        </member>
        <member name="M:Autofac.Core.Resolving.ResolveOperation.#ctor(Autofac.Core.ISharingLifetimeScope)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Resolving.ResolveOperation"/> class.
            </summary>
            <param name="mostNestedLifetimeScope">The most nested scope in which to begin the operation. The operation
            can move upward to less nested scopes as components with wider sharing scopes are activated.</param>
        </member>
        <member name="M:Autofac.Core.Resolving.ResolveOperation.ResolveComponent(Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Resolve an instance of the provided registration within the context.
            </summary>
            <param name="registration">The registration.</param>
            <param name="parameters">Parameters for the instance.</param>
            <returns>
            The component instance.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.Core.Resolving.ResolveOperation.Execute(Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Execute the complete resolve operation.
            </summary>
            <param name="registration">The registration.</param>
            <param name="parameters">Parameters for the instance.</param>
        </member>
        <member name="M:Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(Autofac.Core.ISharingLifetimeScope,Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Continue building the object graph by instantiating <paramref name="registration"/> in the
            current <paramref name="currentOperationScope"/>.
            </summary>
            <param name="currentOperationScope">The current scope of the operation.</param>
            <param name="registration">The component to activate.</param>
            <param name="parameters">The parameters for the component.</param>
            <returns>The resolved instance.</returns>
            <exception cref="T:System.ArgumentNullException"/>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperation.ComponentRegistry">
            <summary>
            Gets the services associated with the components that provide them.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.ResolveOperationBeginningEventArgs">
            <summary>
            Describes the commencement of a new resolve operation.
            </summary>
        </member>
        <member name="M:Autofac.Core.Resolving.ResolveOperationBeginningEventArgs.#ctor(Autofac.Core.Resolving.IResolveOperation)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Resolving.ResolveOperationBeginningEventArgs"/> class.
            </summary>
            <param name="resolveOperation">The resolve operation that is beginning.</param>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperationBeginningEventArgs.ResolveOperation">
            <summary>
            Gets the resolve operation that is beginning.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.ResolveOperationEndingEventArgs">
            <summary>
            Describes the commencement of a new resolve operation.
            </summary>
        </member>
        <member name="M:Autofac.Core.Resolving.ResolveOperationEndingEventArgs.#ctor(Autofac.Core.Resolving.IResolveOperation,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.Resolving.ResolveOperationEndingEventArgs"/> class.
            </summary>
            <param name="resolveOperation">The resolve operation that is ending.</param>
            <param name="exception">If included, the exception causing the operation to end; otherwise, null.</param>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperationEndingEventArgs.Exception">
            <summary>
            Gets the exception causing the operation to end, or null.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperationEndingEventArgs.ResolveOperation">
            <summary>
            Gets the resolve operation that is ending.
            </summary>
        </member>
        <member name="T:Autofac.Core.Resolving.ResolveOperationResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperationResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperationResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperationResources.ExceptionDuringResolve">
            <summary>
              Looks up a localized string similar to An exception was thrown while executing a resolve operation. See the InnerException for details..
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperationResources.MaxDepthExceeded">
            <summary>
              Looks up a localized string similar to Probable circular dependency between factory-scoped components. Chain includes &apos;{0}&apos;.
            </summary>
        </member>
        <member name="P:Autofac.Core.Resolving.ResolveOperationResources.TemporaryContextDisposed">
            <summary>
              Looks up a localized string similar to This resolve operation has already ended. When registering components using lambdas, the IComponentContext &apos;c&apos; parameter to the lambda cannot be stored. Instead, either resolve IComponentContext again from &apos;c&apos;, or resolve a Func&lt;&gt; based factory to create subsequent components from..
            </summary>
        </member>
        <member name="T:Autofac.Core.Service">
            <summary>
            Services are the lookup keys used to locate component instances.
            </summary>
        </member>
        <member name="P:Autofac.Core.Service.Description">
            <summary>
            Gets a human-readable description of the service.
            </summary>
            <value>The description.</value>
        </member>
        <member name="M:Autofac.Core.Service.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:Autofac.Core.Service.op_Equality(Autofac.Core.Service,Autofac.Core.Service)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="left">The left operand.</param>
            <param name="right">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Autofac.Core.Service.op_Inequality(Autofac.Core.Service,Autofac.Core.Service)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="left">The left operand.</param>
            <param name="right">The right operand.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Autofac.Core.Service.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>.</param>
            <returns>
            true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.
            </returns>
            <exception cref="T:System.NullReferenceException">The <paramref name="obj"/> parameter is null.</exception>
        </member>
        <member name="M:Autofac.Core.Service.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="T:Autofac.Core.ServiceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Core.ServiceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Core.ServiceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Core.ServiceResources.MustOverrideEquals">
            <summary>
              Looks up a localized string similar to Subclasses of Autofac.Service must override Object.Equals().
            </summary>
        </member>
        <member name="P:Autofac.Core.ServiceResources.MustOverrideGetHashCode">
            <summary>
              Looks up a localized string similar to Subclasses of Autofac.Service must override Object.GetHashCode().
            </summary>
        </member>
        <member name="T:Autofac.Core.TypedService">
            <summary>
            Identifies a service according to a type to which it can be assigned.
            </summary>
        </member>
        <member name="M:Autofac.Core.TypedService.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.TypedService"/> class.
            </summary>
            <param name="serviceType">Type of the service.</param>
        </member>
        <member name="P:Autofac.Core.TypedService.ServiceType">
            <summary>
            Gets the type of the service.
            </summary>
            <value>The type of the service.</value>
        </member>
        <member name="P:Autofac.Core.TypedService.Description">
            <summary>
            Gets a human-readable description of the service.
            </summary>
            <value>The description.</value>
        </member>
        <member name="M:Autofac.Core.TypedService.Equals(Autofac.Core.TypedService)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.
            </returns>
        </member>
        <member name="M:Autofac.Core.TypedService.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>.</param>
            <returns>
            true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.
            </returns>
            <exception cref="T:System.NullReferenceException">The <paramref name="obj"/> parameter is null.</exception>
        </member>
        <member name="M:Autofac.Core.TypedService.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:Autofac.Core.TypedService.ChangeType(System.Type)">
            <summary>
            Return a new service of the same kind, but carrying
            <paramref name="newType"/> as the <see cref="P:Autofac.Core.TypedService.ServiceType"/>.
            </summary>
            <param name="newType">The new service type.</param>
            <returns>A new service with the service type.</returns>
        </member>
        <member name="T:Autofac.Core.UniqueService">
            <summary>
            A handy unique service identifier type - all instances will be regarded as unequal.
            </summary>
        </member>
        <member name="M:Autofac.Core.UniqueService.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.UniqueService"/> class.
            </summary>
        </member>
        <member name="M:Autofac.Core.UniqueService.#ctor(System.Guid)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Core.UniqueService"/> class.
            </summary>
            <param name="id">The id.</param>
        </member>
        <member name="P:Autofac.Core.UniqueService.Description">
            <summary>
            Gets a programmer-readable description of the identifying feature of the service.
            </summary>
        </member>
        <member name="M:Autofac.Core.UniqueService.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>.</param>
            <returns>
            true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.
            </returns>
            <exception cref="T:System.NullReferenceException">The <paramref name="obj"/> parameter is null.</exception>
        </member>
        <member name="M:Autofac.Core.UniqueService.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="T:Autofac.Features.AttributeFilters.KeyFilterAttribute">
             <summary>
             Provides an annotation to resolve constructor dependencies
             according to their registered key.
             </summary>
             <remarks>
             <para>
             This attribute allows constructor dependencies to be resolved by key.
             By marking your dependencies with this attribute and associating
             an attribute filter with your type registration, you can be selective
             about which service registration should be used to provide the
             dependency.
             </para>
             </remarks>
             <example>
             <para>
             A simple example might be registration of a specific logger type to be
             used by a class. If many loggers are registered with their own key,
             the consumer can simply specify the key filter as an attribute to
             the constructor parameter.
             </para>
             <code lang="C#">
             public class Manager
             {
               public Manager([KeyFilter("Manager")] ILogger logger)
               {
                 // ...
               }
             }
             </code>
             <para>
             The same thing can be done for enumerable:
             </para>
             <code lang="C#">
             public class SolutionExplorer
             {
               public SolutionExplorer(
                 [KeyFilter("Solution")] IEnumerable&lt;IAdapter&gt; adapters,
                 [KeyFilter("Solution")] ILogger logger)
               {
                 this.Adapters = adapters.ToList();
                 this.Logger = logger;
               }
             }
             </code>
             <para>
             When registering your components, the associated key on the
             dependencies will be used. Be sure to specify the
             <see cref="M:Autofac.Features.AttributeFilters.RegistrationExtensions.WithAttributeFiltering``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})" />
             extension on the type with the filtered constructor parameters.
             </para>
             <code lang="C#">
             var builder = new ContainerBuilder();
            
             // Register the components getting filtered with keys
             builder.RegisterType&lt;ConsoleLogger&gt;().Keyed&lt;ILogger&gt;(&quot;Solution&quot;);
             builder.RegisterType&lt;FileLogger&gt;().Keyed&lt;ILogger&gt;(&quot;Other&quot;);
            
             // Attach the filtering behavior to the component with the constructor
             builder.RegisterType&lt;SolutionExplorer&gt;().WithAttributeFiltering();
            
             var container = builder.Build();
            
             // The resolved instance will have the appropriate services in place
             var explorer = container.Resolve&lt;SolutionExplorer&gt;();
             </code>
             </example>
        </member>
        <member name="M:Autofac.Features.AttributeFilters.KeyFilterAttribute.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.AttributeFilters.KeyFilterAttribute"/> class.
            </summary>
            <param name="key">The key that the dependency should have in order to satisfy the parameter.</param>
        </member>
        <member name="P:Autofac.Features.AttributeFilters.KeyFilterAttribute.Key">
            <summary>
            Gets the key the dependency is expected to have to satisfy the parameter.
            </summary>
            <value>
            The <see cref="T:System.Object"/> corresponding to a registered service key on a component.
            Resolved components must be keyed with this value to satisfy the filter.
            </value>
        </member>
        <member name="M:Autofac.Features.AttributeFilters.KeyFilterAttribute.ResolveParameter(System.Reflection.ParameterInfo,Autofac.IComponentContext)">
            <summary>
            Resolves a constructor parameter based on keyed service requirements.
            </summary>
            <param name="parameter">The specific parameter being resolved that is marked with this attribute.</param>
            <param name="context">The component context under which the parameter is being resolved.</param>
            <returns>
            The instance of the object that should be used for the parameter value.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="parameter" /> or <paramref name="context" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="T:Autofac.Features.AttributeFilters.MetadataFilterAttribute">
             <summary>
             Provides an annotation to filter constructor dependencies
             according to their specified metadata.
             </summary>
             <remarks>
             <para>
             This attribute allows constructor dependencies to be filtered by metadata.
             By marking your dependencies with this attribute and associating
             an attribute filter with your type registration, you can be selective
             about which service registration should be used to provide the
             dependency.
             </para>
             </remarks>
             <example>
             <para>
             A simple example might be registration of a specific logger type to be
             used by a class. If many loggers are registered with the <c>LoggerName</c>
             metadata, the consumer can simply specify the filter as an attribute to
             the constructor parameter.
             </para>
             <code lang="C#">
             public class Manager
             {
               public Manager([MetadataFilter("LoggerName", "Manager")] ILogger logger)
               {
                 // ...
               }
             }
             </code>
             <para>
             The same thing can be done for enumerable:
             </para>
             <code lang="C#">
             public class SolutionExplorer
             {
               public SolutionExplorer(
                 [MetadataFilter("Target", "Solution")] IEnumerable&lt;IAdapter&gt; adapters,
                 [MetadataFilter("LoggerName", "Solution")] ILogger logger)
               {
                 this.Adapters = adapters.ToList();
                 this.Logger = logger;
               }
             }
             </code>
             <para>
             When registering your components, the associated metadata on the dependencies will be used.
             Be sure to specify the <see cref="M:Autofac.Features.AttributeFilters.RegistrationExtensions.WithAttributeFiltering``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})" />
             extension on the type with the filtered constructor parameters.
             </para>
             <code lang="C#">
             var builder = new ContainerBuilder();
            
             // Attach metadata to the components getting filtered
             builder.RegisterType&lt;ConsoleLogger&gt;().WithMetadata(&quot;LoggerName&quot;, &quot;Solution&quot;).As&lt;ILogger&gt;();
             builder.RegisterType&lt;FileLogger&gt;().WithMetadata(&quot;LoggerName&quot;, &quot;Other&quot;).As&lt;ILogger&gt;();
            
             // Attach the filtering behavior to the component with the constructor
             builder.RegisterType&lt;SolutionExplorer&gt;().WithAttributeFiltering();
            
             var container = builder.Build();
            
             // The resolved instance will have the appropriate services in place
             var explorer = container.Resolve&lt;SolutionExplorer&gt;();
             </code>
             </example>
        </member>
        <member name="M:Autofac.Features.AttributeFilters.MetadataFilterAttribute.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.AttributeFilters.MetadataFilterAttribute"/> class.
            </summary>
            <param name="key">The metadata key that the dependency should have in order to satisfy the parameter.</param>
            <param name="value">The metadata value that the dependency should have in order to satisfy the parameter.</param>
        </member>
        <member name="P:Autofac.Features.AttributeFilters.MetadataFilterAttribute.Key">
            <summary>
            Gets the key the dependency is expected to have to satisfy the parameter.
            </summary>
            <value>
            The <see cref="T:System.String"/> corresponding to a registered metadata
            key on a component. Resolved components must have this metadata key to
            satisfy the filter.
            </value>
        </member>
        <member name="P:Autofac.Features.AttributeFilters.MetadataFilterAttribute.Value">
            <summary>
            Gets the value the dependency is expected to have to satisfy the parameter.
            </summary>
            <value>
            The <see cref="T:System.Object"/> corresponding to a registered metadata
            value on a component. Resolved components must have the metadata
            <see cref="P:Autofac.Features.AttributeFilters.MetadataFilterAttribute.Key"/> with
            this value to satisfy the filter.
            </value>
        </member>
        <member name="M:Autofac.Features.AttributeFilters.MetadataFilterAttribute.ResolveParameter(System.Reflection.ParameterInfo,Autofac.IComponentContext)">
            <summary>
            Resolves a constructor parameter based on metadata requirements.
            </summary>
            <param name="parameter">The specific parameter being resolved that is marked with this attribute.</param>
            <param name="context">The component context under which the parameter is being resolved.</param>
            <returns>
            The instance of the object that should be used for the parameter value.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="parameter" /> or <paramref name="context" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="T:Autofac.Features.AttributeFilters.ParameterFilterAttribute">
            <summary>
            Base attribute class for marking constructor parameters and enabling
            filtering by attributed criteria.
            </summary>
            <remarks>
            <para>
            Implementations of this attribute can be used to mark constructor parameters
            so filtering can be done based on registered service data. For example, the
            <see cref="T:Autofac.Features.AttributeFilters.MetadataFilterAttribute"/> allows constructor
            parameters to be filtered by registered metadata criteria and the
            <see cref="T:Autofac.Features.AttributeFilters.KeyFilterAttribute"/> allows constructor
            parameters to be filtered by a keyed service registration.
            </para>
            <para>
            If a type uses these attributes, it should be registered with Autofac
            using the
            <see cref="M:Autofac.Features.AttributeFilters.RegistrationExtensions.WithAttributeFiltering``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})" />
            extension to enable the behavior.
            </para>
            <para>
            For specific attribute usage examples, see the attribute documentation.
            </para>
            </remarks>
            <seealso cref="T:Autofac.Features.AttributeFilters.MetadataFilterAttribute"/>
            <seealso cref="T:Autofac.Features.AttributeFilters.KeyFilterAttribute"/>
        </member>
        <member name="M:Autofac.Features.AttributeFilters.ParameterFilterAttribute.ResolveParameter(System.Reflection.ParameterInfo,Autofac.IComponentContext)">
            <summary>
            Implemented in derived classes to resolve a specific parameter marked with this attribute.
            </summary>
            <param name="parameter">The specific parameter being resolved that is marked with this attribute.</param>
            <param name="context">The component context under which the parameter is being resolved.</param>
            <returns>The instance of the object that should be used for the parameter value.</returns>
        </member>
        <member name="T:Autofac.Features.AttributeFilters.RegistrationExtensions">
            <summary>
            Extends registration syntax for attribute scenarios.
            </summary>
        </member>
        <member name="M:Autofac.Features.AttributeFilters.RegistrationExtensions.WithAttributeFiltering``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Applies attribute-based filtering on constructor dependencies for use with attributes
            derived from the <see cref="T:Autofac.Features.AttributeFilters.ParameterFilterAttribute"/>.
            </summary>
            <typeparam name="TLimit">The type of the registration limit.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style type.</typeparam>
            <param name="builder">The registration builder containing registration data.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="builder" /> is <see langword="null" />.
            </exception>
            <remarks>
            <para>
            Apply this extension to component registrations that use attributes
            that derive from the <see cref="T:Autofac.Features.AttributeFilters.ParameterFilterAttribute"/>
            like the <see cref="T:Autofac.Features.AttributeFilters.MetadataFilterAttribute"/>
            in their constructors. Doing so will allow the attribute-based filtering to occur. See
            <see cref="T:Autofac.Features.AttributeFilters.MetadataFilterAttribute"/> for an
            example on how to use the filter and attribute together.
            </para>
            </remarks>
            <seealso cref="T:Autofac.Features.AttributeFilters.MetadataFilterAttribute"/>
        </member>
        <member name="T:Autofac.Features.Collections.CollectionRegistrationSource">
            <summary>
            Registration source providing implicit collection/list/enumerable support.
            </summary>
            <remarks>
            <para>
            This registration source provides enumerable support to allow resolving
            the set of all registered services of a given type.
            </para>
            <para>
            What may not be immediately apparent is that it also means any time there
            are no items of a particular type registered, it will always return an
            empty set rather than <see langword="null" /> or throwing an exception.
            This is by design.
            </para>
            <para>
            Consider the [possibly majority] use case where you're resolving a set
            of message handlers or event handlers from the container. If there aren't
            any handlers, you want an empty set - not <see langword="null" /> or
            an exception. It's valid to have no handlers registered.
            </para>
            <para>
            This implicit support means other areas (like MVC support or manual
            property injection) must take care to only request enumerable values they
            expect to get something back for. In other words, "Don't ask the container
            for something you don't expect to resolve."
            </para>
            </remarks>
        </member>
        <member name="M:Autofac.Features.Collections.CollectionRegistrationSource.RegistrationsFor(Autofac.Core.Service,System.Func{Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.IComponentRegistration}})">
            <summary>
            Retrieve registrations for an unregistered service, to be used
            by the container.
            </summary>
            <param name="service">The service that was requested.</param>
            <param name="registrationAccessor">A function that will return existing registrations for a service.</param>
            <returns>Registrations providing the service.</returns>
        </member>
        <member name="T:Autofac.Features.Collections.CollectionRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.Collections.CollectionRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.Collections.CollectionRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.Collections.CollectionRegistrationSourceResources.CollectionRegistrationSourceDescription">
            <summary>
              Looks up a localized string similar to Collection Support (Arrays and Generic Collection Interfaces).
            </summary>
        </member>
        <member name="T:Autofac.Features.GeneratedFactories.FactoryGenerator">
            <summary>
            Generates context-bound closures that represent factories from
            a set of heuristics based on delegate type signatures.
            </summary>
        </member>
        <member name="M:Autofac.Features.GeneratedFactories.FactoryGenerator.#ctor(System.Type,Autofac.Core.Service,Autofac.Features.GeneratedFactories.ParameterMapping)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.GeneratedFactories.FactoryGenerator"/> class.
            </summary>
            <param name="service">The service that will be activated in
            order to create the products of the factory.</param>
            <param name="delegateType">The delegate to provide as a factory.</param>
            <param name="parameterMapping">The parameter mapping mode to use.</param>
        </member>
        <member name="M:Autofac.Features.GeneratedFactories.FactoryGenerator.#ctor(System.Type,Autofac.Core.IComponentRegistration,Autofac.Features.GeneratedFactories.ParameterMapping)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.GeneratedFactories.FactoryGenerator"/> class.
            </summary>
            <param name="productRegistration">The component that will be activated in
            order to create the products of the factory.</param>
            <param name="delegateType">The delegate to provide as a factory.</param>
            <param name="parameterMapping">The parameter mapping mode to use.</param>
        </member>
        <member name="M:Autofac.Features.GeneratedFactories.FactoryGenerator.GenerateFactory(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Generates a factory delegate that closes over the provided context.
            </summary>
            <param name="context">The context in which the factory will be used.</param>
            <param name="parameters">Parameters provided to the resolve call for the factory itself.</param>
            <returns>A factory delegate that will work within the context.</returns>
        </member>
        <member name="M:Autofac.Features.GeneratedFactories.FactoryGenerator.GenerateFactory``1(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Generates a factory delegate that closes over the provided context.
            </summary>
            <param name="context">The context in which the factory will be used.</param>
            <param name="parameters">Parameters provided to the resolve call for the factory itself.</param>
            <returns>A factory delegate that will work within the context.</returns>
        </member>
        <member name="T:Autofac.Features.GeneratedFactories.GeneratedFactoryActivatorData">
            <summary>
            Data used to create factory activators.
            </summary>
        </member>
        <member name="M:Autofac.Features.GeneratedFactories.GeneratedFactoryActivatorData.#ctor(System.Type,Autofac.Core.Service)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.GeneratedFactories.GeneratedFactoryActivatorData"/> class.
            </summary>
            <param name="delegateType">The type of the factory.</param>
            <param name="productService">The service used to provide the products of the factory.</param>
        </member>
        <member name="P:Autofac.Features.GeneratedFactories.GeneratedFactoryActivatorData.ParameterMapping">
            <summary>
            Gets or sets a value determining how the parameters of the delegate type are passed on
            to the generated Resolve() call as Parameter objects.
            For Func-based delegates, this defaults to ByType. Otherwise, the
            parameters will be mapped by name.
            </summary>
        </member>
        <member name="P:Autofac.Features.GeneratedFactories.GeneratedFactoryActivatorData.Activator">
            <summary>
            Gets the activator data that can provide an IInstanceActivator instance.
            </summary>
        </member>
        <member name="M:Autofac.Features.GeneratedFactories.GeneratedFactoryRegistrationSource.RegistrationsFor(Autofac.Core.Service,System.Func{Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.IComponentRegistration}})">
            <summary>
            Retrieve registrations for an unregistered service, to be used
            by the container.
            </summary>
            <param name="service">The service that was requested.</param>
            <param name="registrationAccessor">A function that will return existing registrations for a service.</param>
            <returns>Registrations providing the service.</returns>
        </member>
        <member name="T:Autofac.Features.GeneratedFactories.GeneratedFactoryRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.GeneratedFactories.GeneratedFactoryRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.GeneratedFactories.GeneratedFactoryRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.GeneratedFactories.GeneratedFactoryRegistrationSourceResources.DuplicateTypesInTypeMappedFuncParameterList">
            <summary>
              Looks up a localized string similar to Unable to generate a function to return type &apos;{0}&apos; with input parameter types [{1}]. The input parameter type list has duplicate types. Try registering a custom delegate type instead of using a generic Func relationship..
            </summary>
        </member>
        <member name="P:Autofac.Features.GeneratedFactories.GeneratedFactoryRegistrationSourceResources.GeneratedFactoryRegistrationSourceDescription">
            <summary>
              Looks up a localized string similar to Delegate Support (Func&lt;T&gt;and Custom Delegates).
            </summary>
        </member>
        <member name="T:Autofac.Features.GeneratedFactories.ParameterMapping">
            <summary>
            Determines how the parameters of the delegate type are passed on
            to the generated Resolve() call as Parameter objects.
            </summary>
        </member>
        <member name="F:Autofac.Features.GeneratedFactories.ParameterMapping.Adaptive">
            <summary>
            Chooses parameter mapping based on the factory type.
            For Func-based factories this is equivalent to ByType, for all
            others ByName will be used.
            </summary>
        </member>
        <member name="F:Autofac.Features.GeneratedFactories.ParameterMapping.ByName">
            <summary>
            Pass the parameters supplied to the delegate through to the
            underlying registration as NamedParameters based on the parameter
            names in the delegate type's formal argument list.
            </summary>
        </member>
        <member name="F:Autofac.Features.GeneratedFactories.ParameterMapping.ByType">
            <summary>
            Pass the parameters supplied to the delegate through to the
            underlying registration as TypedParameters based on the parameter
            types in the delegate type's formal argument list.
            </summary>
        </member>
        <member name="F:Autofac.Features.GeneratedFactories.ParameterMapping.ByPosition">
            <summary>
            Pass the parameters supplied to the delegate through to the
            underlying registration as PositionalParameters based on the parameter
            indices in the delegate type's formal argument list.
            </summary>
        </member>
        <member name="T:Autofac.Features.Indexed.IIndex`2">
            <summary>
            Provides components by lookup operations via an index (key) type.
            </summary>
            <typeparam name="TKey">The type of the index.</typeparam>
            <typeparam name="TValue">The service provided by the indexed components.</typeparam>
            <example>
            Retrieving a value given a key:
            <code>
            IIndex&lt;AccountType, IRenderer&gt; accountRenderers = // ...
            var renderer = accountRenderers[AccountType.User];
            </code>
            </example>
        </member>
        <member name="P:Autofac.Features.Indexed.IIndex`2.Item(`0)">
            <summary>
            Get the value associated with <paramref name="key"/>.
            </summary>
            <param name="key">The value to retrieve.</param>
            <returns>The associated value.</returns>
        </member>
        <member name="M:Autofac.Features.Indexed.IIndex`2.TryGetValue(`0,`1@)">
            <summary>
            Get the value associated with <paramref name="key"/> if any is available.
            </summary>
            <param name="key">The key to look up.</param>
            <param name="value">The retrieved value.</param>
            <returns>True if a value associated with the key exists.</returns>
        </member>
        <member name="T:Autofac.Features.LazyDependencies.LazyRegistrationSource">
            <summary>
            Support the <see cref="T:System.Lazy`1"/>
            type automatically whenever type T is registered with the container.
            When a dependency of a lazy type is used, the instantiation of the underlying
            component will be delayed until the Value property is first accessed.
            </summary>
        </member>
        <member name="T:Autofac.Features.LazyDependencies.LazyRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.LazyDependencies.LazyRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.LazyDependencies.LazyRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.LazyDependencies.LazyRegistrationSourceResources.LazyRegistrationSourceDescription">
            <summary>
              Looks up a localized string similar to Lazy&lt;T&gt; Support.
            </summary>
        </member>
        <member name="T:Autofac.Features.LazyDependencies.LazyWithMetadataRegistrationSource">
            <summary>
            Support the <c>System.Lazy&lt;T, TMetadata&gt;</c>
            types automatically whenever type T is registered with the container.
            Metadata values come from the component registration's metadata.
            When a dependency of a lazy type is used, the instantiation of the underlying
            component will be delayed until the Value property is first accessed.
            </summary>
        </member>
        <member name="T:Autofac.Features.LazyDependencies.LazyWithMetadataRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.LazyDependencies.LazyWithMetadataRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.LazyDependencies.LazyWithMetadataRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.LazyDependencies.LazyWithMetadataRegistrationSourceResources.LazyWithMetadataRegistrationSourceDescription">
            <summary>
              Looks up a localized string similar to Lazy&lt;T, TMetadata&gt; Support.
            </summary>
        </member>
        <member name="T:Autofac.Features.LightweightAdapters.LightweightAdapterActivatorData">
            <summary>
            Describes the basic requirements for generating a lightweight adapter.
            </summary>
        </member>
        <member name="M:Autofac.Features.LightweightAdapters.LightweightAdapterActivatorData.#ctor(Autofac.Core.Service,System.Func{Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Object,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.LightweightAdapters.LightweightAdapterActivatorData"/> class.
            </summary>
            <param name="fromService">The service that will be adapted from.</param>
            <param name="adapter">The adapter function.</param>
        </member>
        <member name="P:Autofac.Features.LightweightAdapters.LightweightAdapterActivatorData.Adapter">
            <summary>
            Gets the adapter function.
            </summary>
        </member>
        <member name="P:Autofac.Features.LightweightAdapters.LightweightAdapterActivatorData.FromService">
            <summary>
            Gets the service to be adapted from.
            </summary>
        </member>
        <member name="T:Autofac.Features.LightweightAdapters.LightweightAdapterRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.LightweightAdapters.LightweightAdapterRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.LightweightAdapters.LightweightAdapterRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.LightweightAdapters.LightweightAdapterRegistrationSourceResources.AdapterFromToDescription">
            <summary>
              Looks up a localized string similar to Lightweight Adapter from {0} to {1}.
            </summary>
        </member>
        <member name="P:Autofac.Features.LightweightAdapters.LightweightAdapterRegistrationSourceResources.FromAndToMustDiffer">
            <summary>
              Looks up a localized string similar to The service {0} cannot be both the adapter&apos;s from and to parameters - these must differ..
            </summary>
        </member>
        <member name="T:Autofac.Features.Metadata.MetadataViewProviderResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.MetadataViewProviderResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.MetadataViewProviderResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.MetadataViewProviderResources.InvalidViewImplementation">
            <summary>
              Looks up a localized string similar to The type &apos;{0}&apos; cannot be used as a metadata view. A metadata view must be a concrete class with a parameterless or dictionary constructor..
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.MetadataViewProviderResources.MissingMetadata">
            <summary>
              Looks up a localized string similar to Export metadata for &apos;{0}&apos; is missing and no default value was supplied..
            </summary>
        </member>
        <member name="T:Autofac.Features.Metadata.MetaRegistrationSource">
            <summary>
            Support the <see cref="T:Autofac.Features.Metadata.Meta`1"/>
            types automatically whenever type T is registered with the container.
            Metadata values come from the component registration's metadata.
            </summary>
        </member>
        <member name="T:Autofac.Features.Metadata.MetaRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.MetaRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.MetaRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.MetaRegistrationSourceResources.MetaRegistrationSourceDescription">
            <summary>
              Looks up a localized string similar to Meta&lt;T&gt; Support.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.MetaRegistrationSourceResources.StronglyTypedMetaRegistrationSourceDescription">
            <summary>
              Looks up a localized string similar to Meta&lt;T, TMetadata&gt; Support.
            </summary>
        </member>
        <member name="T:Autofac.Features.Metadata.Meta`2">
            <summary>
            Provides a value along with metadata describing the value.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <typeparam name="TMetadata">An interface to which metadata values can be bound.</typeparam>
        </member>
        <member name="M:Autofac.Features.Metadata.Meta`2.#ctor(`0,`1)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.Metadata.Meta`2"/> class.
            </summary>
            <param name="value">The value described by the instance.</param>
            <param name="metadata">The metadata describing the value.</param>
        </member>
        <member name="P:Autofac.Features.Metadata.Meta`2.Value">
            <summary>
            Gets the value described by <see cref="P:Autofac.Features.Metadata.Meta`2.Metadata"/>.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.Meta`2.Metadata">
            <summary>
            Gets metadata describing the value.
            </summary>
        </member>
        <member name="T:Autofac.Features.Metadata.Meta`1">
            <summary>
            Provides a value along with a dictionary of metadata describing the value.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
        </member>
        <member name="M:Autofac.Features.Metadata.Meta`1.#ctor(`0,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.Metadata.Meta`1"/> class.
            </summary>
            <param name="value">The value described by the instance.</param>
            <param name="metadata">The metadata describing the value.</param>
        </member>
        <member name="P:Autofac.Features.Metadata.Meta`1.Value">
            <summary>
            Gets the value described by <see cref="P:Autofac.Features.Metadata.Meta`1.Metadata"/>.
            </summary>
        </member>
        <member name="P:Autofac.Features.Metadata.Meta`1.Metadata">
            <summary>
            Gets the metadata describing the value.
            </summary>
        </member>
        <member name="T:Autofac.Features.Metadata.StronglyTypedMetaRegistrationSource">
            <summary>
            Support the <see cref="T:Autofac.Features.Metadata.Meta`2"/>
            types automatically whenever type T is registered with the container.
            Metadata values come from the component registration's metadata.
            </summary>
        </member>
        <member name="T:Autofac.Features.OpenGenerics.OpenGenericDecoratorActivatorData">
            <summary>
            Describes the activator for an open generic decorator.
            </summary>
        </member>
        <member name="M:Autofac.Features.OpenGenerics.OpenGenericDecoratorActivatorData.#ctor(System.Type,Autofac.Core.IServiceWithType)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.OpenGenerics.OpenGenericDecoratorActivatorData"/> class.
            </summary>
            <param name="implementer">The decorator type.</param>
            <param name="fromService">The open generic service type to decorate.</param>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericDecoratorActivatorData.FromService">
            <summary>
            Gets the open generic service type to decorate.
            </summary>
        </member>
        <member name="T:Autofac.Features.OpenGenerics.OpenGenericDecoratorActivatorDataResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericDecoratorActivatorDataResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericDecoratorActivatorDataResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericDecoratorActivatorDataResources.DecoratedServiceIsNotOpenGeneric">
            <summary>
              Looks up a localized string similar to The service &apos;{0}&apos; is not an open generic type..
            </summary>
        </member>
        <member name="T:Autofac.Features.OpenGenerics.OpenGenericDecoratorRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericDecoratorRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericDecoratorRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericDecoratorRegistrationSourceResources.FromAndToMustDiffer">
            <summary>
              Looks up a localized string similar to The service {0} cannot be both the adapter&apos;s from and to parameters - these must differ..
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericDecoratorRegistrationSourceResources.OpenGenericDecoratorRegistrationSourceImplFromTo">
            <summary>
              Looks up a localized string similar to Open Generic Decorator {0} from {1} to {2}.
            </summary>
        </member>
        <member name="T:Autofac.Features.OpenGenerics.OpenGenericRegistrationExtensionsResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericRegistrationExtensionsResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericRegistrationExtensionsResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericRegistrationExtensionsResources.ImplementorMustBeOpenGenericType">
            <summary>
              Looks up a localized string similar to The type {0} is not an open generic type definition..
            </summary>
        </member>
        <member name="T:Autofac.Features.OpenGenerics.OpenGenericRegistrationSource">
            <summary>
            Generates activators for open generic types.
            </summary>
        </member>
        <member name="T:Autofac.Features.OpenGenerics.OpenGenericRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericRegistrationSourceResources.OpenGenericRegistrationSourceDescription">
            <summary>
              Looks up a localized string similar to {0} providing {1}.
            </summary>
        </member>
        <member name="T:Autofac.Features.OpenGenerics.OpenGenericServiceBinderResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericServiceBinderResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericServiceBinderResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericServiceBinderResources.ImplementorDoesntImplementService">
            <summary>
              Looks up a localized string similar to The type &apos;{0}&apos; does not implement the interface &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericServiceBinderResources.ImplementorMustBeOpenGenericTypeDefinition">
            <summary>
              Looks up a localized string similar to The implementation type &apos;{0}&apos; is not an open generic type definition..
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericServiceBinderResources.InterfaceIsNotImplemented">
            <summary>
              Looks up a localized string similar to The implementation type &apos;{0}&apos; does not support the interface &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericServiceBinderResources.ServiceTypeMustBeOpenGenericTypeDefinition">
            <summary>
              Looks up a localized string similar to The service &apos;{0}&apos; is not an open generic type definition..
            </summary>
        </member>
        <member name="P:Autofac.Features.OpenGenerics.OpenGenericServiceBinderResources.TypesAreNotConvertible">
            <summary>
              Looks up a localized string similar to The service &apos;{1}&apos; is not assignable from implementation type &apos;{0}&apos;..
            </summary>
        </member>
        <member name="T:Autofac.Features.OwnedInstances.Owned`1">
             <summary>
             Represents a dependency that can be released by the dependent component.
             </summary>
             <typeparam name="T">The service provided by the dependency.</typeparam>
             <remarks>
             <para>
             Autofac automatically provides instances of <see cref="T:Autofac.Features.OwnedInstances.Owned`1"/> whenever the
             service <typeparamref name="T"/> is registered.
             </para>
             <para>
             It is not necessary for <typeparamref name="T"/>, or the underlying component, to implement <see cref="T:System.IDisposable"/>.
             Disposing of the <see cref="T:Autofac.Features.OwnedInstances.Owned`1"/> object is the correct way to handle cleanup of the dependency,
             as this will dispose of any other components created indirectly as well.
             </para>
             <para>
             When <see cref="T:Autofac.Features.OwnedInstances.Owned`1"/> is resolved, a new <see cref="T:Autofac.ILifetimeScope"/> is created for the
             underlying <typeparamref name="T"/>, and tagged with the service matching <typeparamref name="T"/>,
             generally a <see cref="T:Autofac.Core.TypedService"/>. This means that shared instances can be tied to this
             scope by registering them as InstancePerMatchingLifetimeScope(new TypedService(typeof(T))).
             </para>
             </remarks>
             <example>
             The component D below is disposable and implements IService:
             <code>
             public class D : IService, IDisposable
             {
               // ...
             }
             </code>
             The dependent component C can dispose of the D instance whenever required by taking a dependency on
             <see cref="T:Autofac.Features.OwnedInstances.Owned`1"/>:
             <code>
             public class C
             {
               IService _service;
            
               public C(Owned&lt;IService&gt; service)
               {
                 _service = service;
               }
            
               void DoWork()
               {
                 _service.Value.DoSomething();
               }
            
               void OnFinished()
               {
                 _service.Dispose();
               }
             }
             </code>
             In general, rather than depending on <see cref="T:Autofac.Features.OwnedInstances.Owned`1"/> directly, components will depend on
             System.Func&lt;Owned&lt;T&gt;&gt; in order to create and dispose of other components as required.
             </example>
        </member>
        <member name="M:Autofac.Features.OwnedInstances.Owned`1.#ctor(`0,System.IDisposable)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.OwnedInstances.Owned`1"/> class.
            </summary>
            <param name="value">The value representing the instance.</param>
            <param name="lifetime">An IDisposable interface through which ownership can be released.</param>
        </member>
        <member name="P:Autofac.Features.OwnedInstances.Owned`1.Value">
            <summary>
            Gets or sets the owned value.
            </summary>
        </member>
        <member name="M:Autofac.Features.OwnedInstances.Owned`1.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="T:Autofac.Features.OwnedInstances.OwnedInstanceRegistrationSource">
            <summary>
            Generates registrations for services of type <see cref="T:Autofac.Features.OwnedInstances.Owned`1"/> whenever the service
            T is available.
            </summary>
        </member>
        <member name="M:Autofac.Features.OwnedInstances.OwnedInstanceRegistrationSource.RegistrationsFor(Autofac.Core.Service,System.Func{Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.IComponentRegistration}})">
            <summary>
            Retrieve registrations for an unregistered service, to be used
            by the container.
            </summary>
            <param name="service">The service that was requested.</param>
            <param name="registrationAccessor">A function that will return existing registrations for a service.</param>
            <returns>Registrations providing the service.</returns>
        </member>
        <member name="T:Autofac.Features.OwnedInstances.OwnedInstanceRegistrationSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.OwnedInstances.OwnedInstanceRegistrationSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OwnedInstances.OwnedInstanceRegistrationSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.OwnedInstances.OwnedInstanceRegistrationSourceResources.OwnedInstanceRegistrationSourceDescription">
            <summary>
              Looks up a localized string similar to Owned&lt;T&gt; Support.
            </summary>
        </member>
        <member name="T:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource">
            <summary>
            Provides registrations on-the-fly for any concrete type not already registered with
            the container.
            </summary>
        </member>
        <member name="M:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource"/> class.
            </summary>
        </member>
        <member name="M:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource.#ctor(System.Func{System.Type,System.Boolean})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource"/> class.
            </summary>
            <param name="predicate">A predicate that selects types the source will register.</param>
        </member>
        <member name="M:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource.RegistrationsFor(Autofac.Core.Service,System.Func{Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.IComponentRegistration}})">
            <summary>
            Retrieve registrations for an unregistered service, to be used
            by the container.
            </summary>
            <param name="service">The service that was requested.</param>
            <param name="registrationAccessor">A function that will return existing registrations for a service.</param>
            <returns>Registrations providing the service.</returns>
        </member>
        <member name="P:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource.IsAdapterForIndividualComponents">
            <summary>
            Gets a value indicating whether the registrations provided by this source are 1:1 adapters on top
            of other components (e.g., Meta, Func, or Owned).
            </summary>
        </member>
        <member name="P:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource.RegistrationConfiguration">
            <summary>
            Gets or sets an expression used to configure generated registrations.
            </summary>
            <value>
            A <see cref="T:System.Action`1"/> that can be used to modify the behavior
            of registrations that are generated by this source.
            </value>
        </member>
        <member name="M:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="T:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSourceExtensions">
            <summary>
            Extension methods for configuring the <see cref="T:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource"/>.
            </summary>
        </member>
        <member name="M:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSourceExtensions.WithRegistrationsAs(Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource,System.Action{Autofac.Builder.IRegistrationBuilder{System.Object,Autofac.Builder.ConcreteReflectionActivatorData,Autofac.Builder.SingleRegistrationStyle}})">
            <summary>
            Fluent method for setting the registration configuration on <see cref="T:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSource"/>.
            </summary>
            <param name="source">The registration source to configure.</param>
            <param name="configurationAction">A configuration action that will run on any registration provided by the source.</param>
            <returns>
            The <paramref name="source" /> with the registration configuration set.
            </returns>
        </member>
        <member name="T:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSourceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSourceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSourceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Features.ResolveAnything.AnyConcreteTypeNotAlreadyRegisteredSourceResources.AnyConcreteTypeNotAlreadyRegisteredSourceDescription">
            <summary>
              Looks up a localized string similar to &quot;Resolve Anything&quot; Support.
            </summary>
        </member>
        <member name="T:Autofac.Features.Scanning.ScanningActivatorData">
            <summary>
            Activation data for types located by scanning assemblies.
            </summary>
        </member>
        <member name="M:Autofac.Features.Scanning.ScanningActivatorData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Features.Scanning.ScanningActivatorData"/> class.
            </summary>
        </member>
        <member name="P:Autofac.Features.Scanning.ScanningActivatorData.Filters">
            <summary>
            Gets the filters applied to the types from the scanned assembly.
            </summary>
        </member>
        <member name="P:Autofac.Features.Scanning.ScanningActivatorData.ConfigurationActions">
            <summary>
            Gets the additional actions to be performed on the concrete type registrations.
            </summary>
        </member>
        <member name="P:Autofac.Features.Scanning.ScanningActivatorData.PostScanningCallbacks">
            <summary>
            Gets the actions to be called once the scanning operation is complete.
            </summary>
        </member>
        <member name="T:Autofac.Features.Variance.ContravariantRegistrationSource">
             <summary>
             Enables contravariant <code>Resolve()</code> for interfaces that have a single contravariant ('in') parameter.
             </summary>
             <example>
             <code>
             interface IHandler&lt;in TCommand&gt;
             {
                 void Handle(TCommand command);
             }
            
             class Command { }
            
             class DerivedCommand : Command { }
            
             class CommandHandler : IHandler&lt;Command&gt; { ... }
            
             var builder = new ContainerBuilder();
             builder.RegisterSource(new ContravariantRegistrationSource());
             builder.RegisterType&lt;CommandHandler&gt;();
             var container = builder.Build();
             // Source enables this line, even though IHandler&lt;Command&gt; is the
             // actual registered type.
             var handler = container.Resolve&lt;IHandler&lt;DerivedCommand&gt;&gt;();
             handler.Handle(new DerivedCommand());
             </code>
             </example>
        </member>
        <member name="M:Autofac.Features.Variance.ContravariantRegistrationSource.RegistrationsFor(Autofac.Core.Service,System.Func{Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.IComponentRegistration}})">
            <summary>
            Retrieve registrations for an unregistered service, to be used
            by the container.
            </summary>
            <param name="service">The service that was requested.</param>
            <param name="registrationAccessor">A function that will return existing registrations for a service.</param>
            <returns>Registrations providing the service.</returns>
            <remarks>
            If the source is queried for service s, and it returns a component that implements both s and s', then it
            will not be queried again for either s or s'. This means that if the source can return other implementations
            of s', it should return these, plus the transitive closure of other components implementing their
            additional services, along with the implementation of s. It is not an error to return components
            that do not implement <paramref name="service"/>.
            </remarks>
        </member>
        <member name="P:Autofac.Features.Variance.ContravariantRegistrationSource.IsAdapterForIndividualComponents">
            <summary>
            Gets a value indicating whether the registrations provided by this source are 1:1 adapters on top
            of other components (e.g., Meta, Func, or Owned).
            </summary>
        </member>
        <member name="T:Autofac.IComponentContext">
            <summary>
            The context in which a service can be accessed or a component's
            dependencies resolved. Disposal of a context will dispose any owned
            components.
            </summary>
        </member>
        <member name="P:Autofac.IComponentContext.ComponentRegistry">
            <summary>
            Gets the associated services with the components that provide them.
            </summary>
        </member>
        <member name="M:Autofac.IComponentContext.ResolveComponent(Autofac.Core.IComponentRegistration,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Resolve an instance of the provided registration within the context.
            </summary>
            <param name="registration">The registration.</param>
            <param name="parameters">Parameters for the instance.</param>
            <returns>
            The component instance.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="T:Autofac.IContainer">
            <summary>
            Creates, wires dependencies and manages lifetime for a set of components.
            Most instances of <see cref="T:Autofac.IContainer"/> are created
            by a <see cref="T:Autofac.ContainerBuilder"/>.
            </summary>
            <example>
            <code>
            // See ContainerBuilder for the definition of the builder variable
            using (var container = builder.Build())
            {
                var program = container.Resolve&lt;Program&gt;();
                program.Run();
            }
            </code>
            </example>
            <remarks>
            Most <see cref="T:Autofac.IContainer"/> functionality is provided by extension methods
            on the inherited <see cref="T:Autofac.IComponentContext"/> interface.
            </remarks>
            <seealso cref="T:Autofac.ILifetimeScope"/>
            <seealso cref="T:Autofac.IComponentContext"/>
            <seealso cref="T:Autofac.ResolutionExtensions"/>
            <seealso cref="T:Autofac.ContainerBuilder"/>
        </member>
        <member name="T:Autofac.ILifetimeScope">
             <summary>
             An <see cref="T:Autofac.ILifetimeScope"/> tracks the instantiation of component instances.
             It defines a boundary in which instances are shared and configured.
             Disposing an <see cref="T:Autofac.ILifetimeScope"/> will dispose the components that were
             resolved through it.
             </summary>
             <example>
             <code>
             // See IContainer for definition of the container variable
             using (var requestScope = container.BeginLifetimeScope())
             {
                 // Note that handler is resolved from requestScope, not
                 // from the container:
            
                 var handler = requestScope.Resolve&lt;IRequestHandler&gt;();
                 handler.Handle(request);
            
                 // When requestScope is disposed, all resources used in processing
                 // the request will be released.
             }
             </code>
             </example>
             <remarks>
             All long-running applications should resolve components via an
             <see cref="T:Autofac.ILifetimeScope"/>. Choosing the duration of the lifetime is application-
             specific. The standard Autofac WCF and ASP.NET/MVC integrations are already configured
             to create and release <see cref="T:Autofac.ILifetimeScope"/>s as appropriate. For example, the
             ASP.NET integration will create and release an <see cref="T:Autofac.ILifetimeScope"/> per HTTP
             request.
             Most <see cref="T:Autofac.ILifetimeScope"/> functionality is provided by extension methods
             on the inherited <see cref="T:Autofac.IComponentContext"/> interface.
             </remarks>
             <seealso cref="T:Autofac.IContainer"/>
             <seealso cref="T:Autofac.IComponentContext"/>
             <seealso cref="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerMatchingLifetimeScope(System.Object[])"/>
             <seealso cref="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerLifetimeScope"/>
             <seealso cref="T:Autofac.Core.InstanceSharing"/>
             <seealso cref="T:Autofac.Core.IComponentLifetime"/>
        </member>
        <member name="M:Autofac.ILifetimeScope.BeginLifetimeScope">
            <summary>
            Begin a new nested scope. Component instances created via the new scope
            will be disposed along with it.
            </summary>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="M:Autofac.ILifetimeScope.BeginLifetimeScope(System.Object)">
            <summary>
            Begin a new nested scope. Component instances created via the new scope
            will be disposed along with it.
            </summary>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="M:Autofac.ILifetimeScope.BeginLifetimeScope(System.Action{Autofac.ContainerBuilder})">
            <summary>
            Begin a new nested scope, with additional components available to it.
            Component instances created via the new scope
            will be disposed along with it.
            </summary>
            <remarks>
            The components registered in the sub-scope will be treated as though they were
            registered in the root scope, i.e., SingleInstance() components will live as long
            as the root scope.
            </remarks>
            <param name="configurationAction">Action on a <see cref="T:Autofac.ContainerBuilder"/>
            that adds component registrations visible only in the new scope.</param>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="M:Autofac.ILifetimeScope.BeginLifetimeScope(System.Object,System.Action{Autofac.ContainerBuilder})">
            <summary>
            Begin a new nested scope, with additional components available to it.
            Component instances created via the new scope
            will be disposed along with it.
            </summary>
            <remarks>
            The components registered in the sub-scope will be treated as though they were
            registered in the root scope, i.e., SingleInstance() components will live as long
            as the root scope.
            </remarks>
            <param name="tag">The tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.</param>
            <param name="configurationAction">Action on a <see cref="T:Autofac.ContainerBuilder"/>
            that adds component registrations visible only in the new scope.</param>
            <returns>A new lifetime scope.</returns>
        </member>
        <member name="P:Autofac.ILifetimeScope.Disposer">
            <summary>
            Gets the disposer associated with this <see cref="T:Autofac.ILifetimeScope"/>.
            Component instances can be associated with it manually if required.
            </summary>
            <remarks>Typical usage does not require interaction with this member- it
            is used when extending the container.</remarks>
        </member>
        <member name="P:Autofac.ILifetimeScope.Tag">
            <summary>
            Gets the tag applied to the <see cref="T:Autofac.ILifetimeScope"/>.
            </summary>
            <remarks>Tags allow a level in the lifetime hierarchy to be identified.
            In most applications, tags are not necessary.</remarks>
            <seealso cref="M:Autofac.Builder.IRegistrationBuilder`3.InstancePerMatchingLifetimeScope(System.Object[])"/>
        </member>
        <member name="E:Autofac.ILifetimeScope.ChildLifetimeScopeBeginning">
            <summary>
            Fired when a new scope based on the current scope is beginning.
            </summary>
        </member>
        <member name="E:Autofac.ILifetimeScope.CurrentScopeEnding">
            <summary>
            Fired when this scope is ending.
            </summary>
        </member>
        <member name="E:Autofac.ILifetimeScope.ResolveOperationBeginning">
            <summary>
            Fired when a resolve operation is beginning in this scope.
            </summary>
        </member>
        <member name="T:Autofac.IStartable">
            <summary>
            When implemented by a component, an instance of the component will be resolved
            and started as soon as the container is built. Autofac will not call the Start()
            method when subsequent instances are resolved. If this behavior is required, use
            an <code>OnActivated()</code> event handler instead.
            </summary>
            <remarks>
            For equivalent "Stop" functionality, implement <see cref="T:System.IDisposable"/>. Autofac
            will always dispose a component before any of its dependencies (except in the presence
            of circular dependencies, in which case the components in the cycle are disposed in
            reverse-construction order.)
            </remarks>
        </member>
        <member name="M:Autofac.IStartable.Start">
            <summary>
            Perform once-off startup processing.
            </summary>
        </member>
        <member name="T:Autofac.Module">
             <summary>
             Base class for user-defined modules. Modules can add a set of related components
             to a container (<see cref="M:Autofac.Module.Load(Autofac.ContainerBuilder)"/>) or attach cross-cutting functionality
             to other components (<see cref="M:Autofac.Module.AttachToComponentRegistration(Autofac.Core.IComponentRegistry,Autofac.Core.IComponentRegistration)"/>.
             Modules are given special support in the XML configuration feature - see
             http://code.google.com/p/autofac/wiki/StructuringWithModules.
             </summary>
             <remarks>Provides a user-friendly way to implement <see cref="T:Autofac.Core.IModule"/>
             via <see cref="T:Autofac.ContainerBuilder"/>.</remarks>
             <example>
             Defining a module:
             <code>
             public class DataAccessModule : Module
             {
                 public string ConnectionString { get; set; }
            
                 public override void Load(ContainerBuilder moduleBuilder)
                 {
                     moduleBuilder.RegisterGeneric(typeof(MyRepository&lt;&gt;))
                         .As(typeof(IRepository&lt;&gt;))
                         .InstancePerMatchingLifetimeScope(WebLifetime.Request);
            
                     moduleBuilder.Register(c =&gt; new MyDbConnection(ConnectionString))
                         .As&lt;IDbConnection&gt;()
                         .InstancePerMatchingLifetimeScope(WebLifetime.Request);
                 }
             }
             </code>
             Using the module...
             <code>
             var builder = new ContainerBuilder();
             builder.RegisterModule(new DataAccessModule { ConnectionString = "..." });
             var container = builder.Build();
             var customers = container.Resolve&lt;IRepository&lt;Customer&gt;&gt;();
             </code>
             </example>
        </member>
        <member name="M:Autofac.Module.Configure(Autofac.Core.IComponentRegistry)">
            <summary>
            Apply the module to the component registry.
            </summary>
            <param name="componentRegistry">Component registry to apply configuration to.</param>
        </member>
        <member name="M:Autofac.Module.Load(Autofac.ContainerBuilder)">
            <summary>
            Override to add registrations to the container.
            </summary>
            <remarks>
            Note that the ContainerBuilder parameter is unique to this module.
            </remarks>
            <param name="builder">The builder through which components can be
            registered.</param>
        </member>
        <member name="M:Autofac.Module.AttachToComponentRegistration(Autofac.Core.IComponentRegistry,Autofac.Core.IComponentRegistration)">
            <summary>
            Override to attach module-specific functionality to a
            component registration.
            </summary>
            <remarks>This method will be called for all existing <i>and future</i> component
            registrations - ordering is not important.</remarks>
            <param name="componentRegistry">The component registry.</param>
            <param name="registration">The registration to attach functionality to.</param>
        </member>
        <member name="M:Autofac.Module.AttachToRegistrationSource(Autofac.Core.IComponentRegistry,Autofac.Core.IRegistrationSource)">
            <summary>
            Override to perform module-specific processing on a registration source.
            </summary>
            <remarks>This method will be called for all existing <i>and future</i> sources
            - ordering is not important.</remarks>
            <param name="componentRegistry">The component registry into which the source was added.</param>
            <param name="registrationSource">The registration source.</param>
        </member>
        <member name="P:Autofac.Module.ThisAssembly">
            <summary>
            Gets the assembly in which the concrete module type is located. To avoid bugs whereby deriving from a module will
            change the target assembly, this property can only be used by modules that inherit directly from
            <see cref="T:Autofac.Module"/>.
            </summary>
        </member>
        <member name="T:Autofac.ModuleRegistrationExtensions">
            <summary>
            Extension methods for registering <see cref="T:Autofac.Core.IModule"/> instances with a container.
            </summary>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterAssemblyModules(Autofac.ContainerBuilder,System.Reflection.Assembly[])">
            <summary>
            Registers modules found in an assembly.
            </summary>
            <param name="builder">The builder to register the modules with.</param>
            <param name="assemblies">The assemblies from which to register modules.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="builder"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterAssemblyModules(Autofac.Core.Registration.IModuleRegistrar,System.Reflection.Assembly[])">
            <summary>
            Registers modules found in an assembly.
            </summary>
            <param name="registrar">The module registrar that will make the registrations into the container.</param>
            <param name="assemblies">The assemblies from which to register modules.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registrar"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterAssemblyModules``1(Autofac.ContainerBuilder,System.Reflection.Assembly[])">
            <summary>
            Registers modules found in an assembly.
            </summary>
            <param name="builder">The builder to register the modules with.</param>
            <param name="assemblies">The assemblies from which to register modules.</param>
            <typeparam name="TModule">The type of the module to add.</typeparam>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="builder"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterAssemblyModules``1(Autofac.Core.Registration.IModuleRegistrar,System.Reflection.Assembly[])">
            <summary>
            Registers modules found in an assembly.
            </summary>
            <param name="registrar">The module registrar that will make the registrations into the container.</param>
            <param name="assemblies">The assemblies from which to register modules.</param>
            <typeparam name="TModule">The type of the module to add.</typeparam>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registrar"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterAssemblyModules(Autofac.ContainerBuilder,System.Type,System.Reflection.Assembly[])">
            <summary>
            Registers modules found in an assembly.
            </summary>
            <param name="builder">The builder to register the modules with.</param>
            <param name="moduleType">The <see cref="T:System.Type"/> of the module to add.</param>
            <param name="assemblies">The assemblies from which to register modules.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="builder"/> or <paramref name="moduleType"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterAssemblyModules(Autofac.Core.Registration.IModuleRegistrar,System.Type,System.Reflection.Assembly[])">
            <summary>
            Registers modules found in an assembly.
            </summary>
            <param name="registrar">The module registrar that will make the registrations into the container.</param>
            <param name="moduleType">The <see cref="T:System.Type"/> of the module to add.</param>
            <param name="assemblies">The assemblies from which to register modules.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registrar"/> or <paramref name="moduleType"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterModule``1(Autofac.ContainerBuilder)">
            <summary>
            Add a module to the container.
            </summary>
            <param name="builder">The builder to register the module with.</param>
            <typeparam name="TModule">The module to add.</typeparam>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="builder"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterModule``1(Autofac.Core.Registration.IModuleRegistrar)">
            <summary>
            Add a module to the container.
            </summary>
            <param name="registrar">The module registrar that will make the registration into the container.</param>
            <typeparam name="TModule">The module to add.</typeparam>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registrar"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="M:Autofac.ModuleRegistrationExtensions.RegisterModule(Autofac.ContainerBuilder,Autofac.Core.IModule)">
            <summary>
            Add a module to the container.
            </summary>
            <param name="builder">The builder to register the module with.</param>
            <param name="module">The module to add.</param>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="builder"/> or <paramref name="module"/> is <see langword="null"/>.
            </exception>
            <returns>
            The <see cref="T:Autofac.Core.Registration.IModuleRegistrar"/> to allow
            additional chained module registrations.
            </returns>
        </member>
        <member name="T:Autofac.ModuleResources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.ModuleResources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.ModuleResources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.ModuleResources.ThisAssemblyUnavailable">
            <summary>
               Looks up a localized string similar to Module.ThisAssembly is only available in modules that inherit directly from Module. It can&apos;t be used in &apos;{0}&apos; which inherits from &apos;{1}&apos;..
            </summary>
        </member>
        <member name="T:Autofac.NamedParameter">
            <summary>
            A parameter identified by name. When applied to a reflection-based
            component, <see cref="P:Autofac.NamedParameter.Name"/> will be matched against
            the name of the component's constructor arguments. When applied to
            a delegate-based component, the parameter can be accessed using
            <see cref="M:Autofac.ParameterExtensions.Named``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.String)"/>.
            </summary>
            <example>
            <para>
            Component with parameter...
            </para>
            <code>
            public class MyComponent
            {
                public MyComponent(int amount) { ... }
            }
            </code>
            <para>
            Providing the parameter...
            </para>
            <code>
            var builder = new ContainerBuilder();
            builder.RegisterType&lt;MyComponent&gt;();
            var container = builder.Build();
            var myComponent = container.Resolve&lt;MyComponent&gt;(new NamedParameter("amount", 123));
            </code>
            </example>
        </member>
        <member name="P:Autofac.NamedParameter.Name">
            <summary>
            Gets the name of the parameter.
            </summary>
        </member>
        <member name="M:Autofac.NamedParameter.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.NamedParameter"/> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="value">The parameter value.</param>
        </member>
        <member name="T:Autofac.ParameterExtensions">
            <summary>
            Extension methods that simplify extraction of parameter values from
            an <see cref="T:System.Collections.Generic.IEnumerable`1"/> where T is <see cref="T:Autofac.Core.Parameter"/>.
            Each method returns the first matching parameter value, or throws an exception if
            none is provided.
            </summary>
            <example>
            At configuration time, delegate registrations can retrieve parameter values using
            the methods <see cref="M:Autofac.ParameterExtensions.Named``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.String)"/>, <see cref="M:Autofac.ParameterExtensions.Positional``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Int32)"/> and <see cref="M:Autofac.ParameterExtensions.TypedAs``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})"/>:
            <code>
            builder.Register((c, p) => new FtpClient(p.Named&lt;string&gt;("server")));
            </code>
            These parameters can be provided at resolution time:
            <code>
            container.Resolve&lt;FtpClient&gt;(new NamedParameter("server", "ftp.example.com"));
            </code>
            Alternatively, the parameters can be provided via a <i>Generated Factory</i> - http://code.google.com/p/autofac/wiki/DelegateFactories.
            </example>
        </member>
        <member name="M:Autofac.ParameterExtensions.Named``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.String)">
            <summary>
            Retrieve a named parameter value from a <see cref="T:Autofac.NamedParameter"/> instance.
            </summary>
            <typeparam name="T">The type to which the returned value will be cast.</typeparam>
            <param name="parameters">The available parameters to choose from.</param>
            <param name="name">The name of the parameter to select.</param>
            <returns>The value of the selected parameter.</returns>
            <seealso cref="T:Autofac.NamedParameter"/>
        </member>
        <member name="M:Autofac.ParameterExtensions.Positional``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Int32)">
            <summary>
            Retrieve a positional parameter value from a <see cref="T:Autofac.PositionalParameter"/> instance.
            </summary>
            <typeparam name="T">The type to which the returned value will be cast.</typeparam>
            <param name="parameters">The available parameters to choose from.</param>
            <param name="position">The zero-based position of the parameter to select.</param>
            <returns>The value of the selected parameter.</returns>
            <remarks>The position value is the one associated with the parameter when
            it was constructed, <b>not</b> its index into the <paramref name="parameters"/>
            sequence.</remarks>
            <seealso cref="T:Autofac.PositionalParameter"/>
        </member>
        <member name="M:Autofac.ParameterExtensions.TypedAs``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a typed parameter value from a <see cref="T:Autofac.TypedParameter"/> instance.
            </summary>
            <typeparam name="T">The type to which the returned value will be cast.</typeparam>
            <param name="parameters">The available parameters to choose from.</param>
            <returns>The value of the selected parameter.</returns>
            <seealso cref="T:Autofac.TypedParameter"/>
        </member>
        <member name="T:Autofac.PositionalParameter">
            <summary>
            A parameter that is identified according to an integer representing its
            position in an argument list. When applied to a reflection-based
            component, <see cref="P:Autofac.PositionalParameter.Position"/> will be matched against
            the indices of the component's constructor arguments. When applied to
            a delegate-based component, the parameter can be accessed using
            <see cref="M:Autofac.ParameterExtensions.Positional``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Int32)"/>.
            </summary>
            <example>
            <para>
            Component with parameter...
            </para>
            <code>
            public class MyComponent
            {
                public MyComponent(int amount) { ... }
            }
            </code>
            <para>
            Providing the parameter...
            </para>
            <code>
            var builder = new ContainerBuilder();
            builder.RegisterType&lt;MyComponent&gt;();
            var container = builder.Build();
            var myComponent = container.Resolve&lt;MyComponent&gt;(new PositionalParameter(0, 123));
            </code>
            </example>
        </member>
        <member name="P:Autofac.PositionalParameter.Position">
            <summary>
            Gets the zero-based position of the parameter.
            </summary>
        </member>
        <member name="M:Autofac.PositionalParameter.#ctor(System.Int32,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.PositionalParameter"/> class.
            </summary>
            <param name="position">The zero-based position of the parameter.</param>
            <param name="value">The parameter value.</param>
        </member>
        <member name="T:Autofac.PropertyWiringOptions">
            <summary>
            Options that can be applied when autowiring properties on a component. (Multiple options can
            be specified using bitwise 'or' - e.g. AllowCircularDependencies | PreserveSetValues.
            </summary>
        </member>
        <member name="F:Autofac.PropertyWiringOptions.None">
            <summary>
            Default behavior. Circular dependencies are not allowed; existing non-default
            property values are overwritten.
            </summary>
        </member>
        <member name="F:Autofac.PropertyWiringOptions.AllowCircularDependencies">
            <summary>
            Allows property-property and property-constructor circular dependency wiring.
            This flag moves property wiring from the Activating to the Activated event.
            </summary>
        </member>
        <member name="F:Autofac.PropertyWiringOptions.PreserveSetValues">
            <summary>
            If specified, properties that already have a non-default value will be left
            unchanged in the wiring operation.
            </summary>
        </member>
        <member name="T:Autofac.RegistrationExtensions">
            <summary>
            Adds registration syntax to the <see cref="T:Autofac.ContainerBuilder"/> type.
            </summary>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterComponent(Autofac.ContainerBuilder,Autofac.Core.IComponentRegistration)">
            <summary>
            Add a component to the container.
            </summary>
            <param name="builder">The builder to register the component with.</param>
            <param name="registration">The component to add.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterSource(Autofac.ContainerBuilder,Autofac.Core.IRegistrationSource)">
            <summary>
            Add a registration source to the container.
            </summary>
            <param name="builder">The builder to register the registration source via.</param>
            <param name="registrationSource">The registration source to add.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterInstance``1(Autofac.ContainerBuilder,``0)">
            <summary>
            Register an instance as a component.
            </summary>
            <typeparam name="T">The type of the instance.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="instance">The instance to register.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <remarks>If no services are explicitly specified for the instance, the
            static type <typeparamref name="T"/> will be used as the default service (i.e. *not* <code>instance.GetType()</code>).</remarks>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterType``1(Autofac.ContainerBuilder)">
            <summary>
            Register a component to be created through reflection.
            </summary>
            <typeparam name="TImplementer">The type of the component implementation.</typeparam>
            <param name="builder">Container builder.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterType(Autofac.ContainerBuilder,System.Type)">
            <summary>
            Register a component to be created through reflection.
            </summary>
            <param name="implementationType">The type of the component implementation.</param>
            <param name="builder">Container builder.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Register``1(Autofac.ContainerBuilder,System.Func{Autofac.IComponentContext,``0})">
            <summary>
            Register a delegate as a component.
            </summary>
            <typeparam name="T">The type of the instance.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="delegate">The delegate to register.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Register``1(Autofac.ContainerBuilder,System.Func{Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},``0})">
            <summary>
            Register a delegate as a component.
            </summary>
            <typeparam name="T">The type of the instance.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="delegate">The delegate to register.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterGeneric(Autofac.ContainerBuilder,System.Type)">
            <summary>
            Register an un-parameterised generic type, e.g. Repository&lt;&gt;.
            Concrete types will be made as they are requested, e.g. with Resolve&lt;Repository&lt;int&gt;&gt;().
            </summary>
            <param name="builder">Container builder.</param>
            <param name="implementer">The open generic implementation type.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.PreserveExistingDefaults``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Specifies that the component being registered should only be made the default for services
            that have not already been registered.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TActivatorData">Activator data type.</typeparam>
            <typeparam name="TSingleRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.PreserveExistingDefaults``2(Autofac.Builder.IRegistrationBuilder{``0,Autofac.Features.Scanning.ScanningActivatorData,``1})">
            <summary>
            Specifies that the components being registered should only be made the default for services
            that have not already been registered.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterAssemblyTypes(Autofac.ContainerBuilder,System.Reflection.Assembly[])">
            <summary>
            Register all types in an assembly.
            </summary>
            <param name="builder">Container builder.</param>
            <param name="assemblies">The assemblies from which to register types.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterTypes(Autofac.ContainerBuilder,System.Type[])">
            <summary>
            Register the types in a list.
            </summary>
            <param name="builder">Container builder.</param>
            <param name="types">The types to register.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Where``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,System.Boolean})">
            <summary>
            Specifies a subset of types to register from a scanned assembly.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to filter types from.</param>
            <param name="predicate">Predicate that returns true for types to register.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.As``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,System.Collections.Generic.IEnumerable{Autofac.Core.Service}})">
            <summary>
            Specifies how a type from a scanned assembly is mapped to a service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="serviceMapping">Function mapping types to services.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.As``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,Autofac.Core.Service})">
            <summary>
            Specifies how a type from a scanned assembly is mapped to a service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="serviceMapping">Function mapping types to services.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.As``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,System.Type})">
            <summary>
            Specifies how a type from a scanned assembly is mapped to a service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="serviceMapping">Function mapping types to services.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.As``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,System.Collections.Generic.IEnumerable{System.Type}})">
            <summary>
            Specifies how a type from a scanned assembly is mapped to a service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="serviceMapping">Function mapping types to services.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsSelf``1(Autofac.Builder.IRegistrationBuilder{``0,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle})">
            <summary>
            Specifies that a type from a scanned assembly provides its own concrete type as a service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsSelf``2(Autofac.Builder.IRegistrationBuilder{``0,``1,Autofac.Builder.SingleRegistrationStyle})">
            <summary>
            Specifies that a type provides its own concrete type as a service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TConcreteActivatorData">Activator data type.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsSelf``1(Autofac.Builder.IRegistrationBuilder{``0,Autofac.Builder.ReflectionActivatorData,Autofac.Builder.DynamicRegistrationStyle})">
            <summary>
            Specifies that a type provides its own concrete type as a service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithMetadata``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}}})">
            <summary>
            Specify how a type from a scanned assembly provides metadata.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set metadata on.</param>
            <param name="metadataMapping">A function mapping the type to a list of metadata items.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithMetadataFrom``1(Autofac.Builder.IRegistrationBuilder{System.Object,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle})">
            <summary>
            Use the properties of an attribute (or interface implemented by an attribute) on the scanned type
            to provide metadata values.
            </summary>
            <remarks>Inherited attributes are supported; however, there must be at most one matching attribute
            in the inheritance chain.</remarks>
            <typeparam name="TAttribute">The attribute applied to the scanned type.</typeparam>
            <param name="registration">Registration to set metadata on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithMetadata``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.String,System.Func{System.Type,System.Object})">
            <summary>
            Specify how a type from a scanned assembly provides metadata.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="metadataKey">Key of the metadata item.</param>
            <param name="metadataValueMapping">A function retrieving the value of the item from the component type.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Named``1(Autofac.Builder.IRegistrationBuilder{System.Object,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle},System.Func{System.Type,System.String})">
            <summary>
            Specifies how a type from a scanned assembly is mapped to a named service.
            </summary>
            <param name="registration">Registration to set service mapping on.</param>
            <typeparam name="TService">Service type provided by the component.</typeparam>
            <param name="serviceNameMapping">Function mapping types to service names.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Named``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,System.String},System.Type)">
            <summary>
            Specifies how a type from a scanned assembly is mapped to a named service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="serviceType">Service type provided by the component.</param>
            <param name="serviceNameMapping">Function mapping types to service names.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Keyed``1(Autofac.Builder.IRegistrationBuilder{System.Object,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle},System.Func{System.Type,System.Object})">
            <summary>
            Specifies how a type from a scanned assembly is mapped to a keyed service.
            </summary>
            <param name="registration">Registration to set service mapping on.</param>
            <typeparam name="TService">Service type provided by the component.</typeparam>
            <param name="serviceKeyMapping">Function mapping types to service keys.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Keyed``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,System.Object},System.Type)">
            <summary>
            Specifies how a type from a scanned assembly is mapped to a keyed service.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="serviceType">Service type provided by the component.</param>
            <param name="serviceKeyMapping">Function mapping types to service keys.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsImplementedInterfaces``1(Autofac.Builder.IRegistrationBuilder{``0,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle})">
            <summary>
            Specifies that a type from a scanned assembly is registered as providing all of its
            implemented interfaces.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsImplementedInterfaces``2(Autofac.Builder.IRegistrationBuilder{``0,``1,Autofac.Builder.SingleRegistrationStyle})">
            <summary>
            Specifies that a type is registered as providing all of its implemented interfaces.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TConcreteActivatorData">Activator data type.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsImplementedInterfaces``1(Autofac.Builder.IRegistrationBuilder{``0,Autofac.Builder.ReflectionActivatorData,Autofac.Builder.DynamicRegistrationStyle})">
            <summary>
            Specifies that a type is registered as providing all of its implemented interfaces.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.FindConstructorsWith``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},Autofac.Core.Activators.Reflection.IConstructorFinder)">
            <summary>
            Set the policy used to find candidate constructors on the implementation type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set policy on.</param>
            <param name="constructorFinder">Policy to be used when searching for constructors.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.FindConstructorsWith``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Type,System.Reflection.ConstructorInfo[]})">
            <summary>
            Set the policy used to find candidate constructors on the implementation type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set policy on.</param>
            <param name="finder">A function that returns the constructors to select from.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.PropertiesAutowired``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},Autofac.PropertyWiringOptions)">
            <summary>
            Configure the component so that any properties whose types are registered in the
            container will be wired to instances of the appropriate service.
            </summary>
            <param name="registration">Registration to auto-wire properties.</param>
            <param name="wiringFlags">Set wiring options such as circular dependency wiring support.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.PropertiesAutowired``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Reflection.PropertyInfo,System.Object,System.Boolean})">
            <summary>
            Set the policy used to find candidate properties on the implementation type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set policy on.</param>
            <param name="propertySelector">Policy to be used when searching for properties to inject.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.UsingConstructor``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Type[])">
            <summary>
            Set the policy used to select from available constructors on the implementation type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set policy on.</param>
            <param name="signature">Constructor signature to match.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.UsingConstructor``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},Autofac.Core.Activators.Reflection.IConstructorSelector)">
            <summary>
            Set the policy used to select from available constructors on the implementation type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set policy on.</param>
            <param name="constructorSelector">Policy to be used when selecting a constructor.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.UsingConstructor``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Set the policy used to select from available constructors on the implementation type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set policy on.</param>
            <param name="constructorSelector">Expression demonstrating how the constructor is called.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithParameter``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.String,System.Object)">
            <summary>
            Configure an explicit value for a constructor parameter.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set parameter on.</param>
            <param name="parameterName">Name of a constructor parameter on the target type.</param>
            <param name="parameterValue">Value to supply to the parameter.</param>0
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithParameter``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},Autofac.Core.Parameter)">
            <summary>
            Configure an explicit value for a constructor parameter.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set parameter on.</param>
            <param name="parameter">The parameter to supply to the constructor.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithParameter``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Func{System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Boolean},System.Func{System.Reflection.ParameterInfo,Autofac.IComponentContext,System.Object})">
            <summary>
            Configure an explicit value for a constructor parameter.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set parameter on.</param>
            <param name="parameterSelector">A predicate selecting the parameter to set.</param>
            <param name="valueProvider">The provider that will generate the parameter value.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithParameters``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Configure explicit values for constructor parameters.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set parameter on.</param>
            <param name="parameters">The parameters to supply to the constructor.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithProperty``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.String,System.Object)">
            <summary>
            Configure an explicit value for a property.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set property on.</param>
            <param name="propertyName">Name of a property on the target type.</param>
            <param name="propertyValue">Value to supply to the property.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithProperty``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},Autofac.Core.Parameter)">
            <summary>
            Configure an explicit value for a property.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set parameter on.</param>
            <param name="property">The property to supply.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.WithProperties``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Configure explicit values for properties.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TReflectionActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">Registration to set parameter on.</param>
            <param name="properties">The properties to supply.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Targeting``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},Autofac.Core.IComponentRegistration)">
            <summary>
            Sets the target of the registration (used for metadata generation).
            </summary>
            <typeparam name="TLimit">The type of the limit.</typeparam>
            <typeparam name="TActivatorData">The type of the activator data.</typeparam>
            <typeparam name="TSingleRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set target for.</param>
            <param name="target">The target.</param>
            <returns>
            Registration builder allowing the registration to be configured.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registration" /> or <paramref name="target" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Autofac.RegistrationExtensions.OnRegistered``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Action{Autofac.Core.ComponentRegisteredEventArgs})">
            <summary>
            Provide a handler to be called when the component is registered.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TActivatorData">Activator data type.</typeparam>
            <typeparam name="TSingleRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration add handler to.</param>
            <param name="handler">The handler.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.OnRegistered``2(Autofac.Builder.IRegistrationBuilder{``0,Autofac.Features.Scanning.ScanningActivatorData,``1},System.Action{Autofac.Core.ComponentRegisteredEventArgs})">
            <summary>
            Provide a handler to be called when the component is registred.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration add handler to.</param>
            <param name="handler">The handler.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsClosedTypesOf``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Type)">
            <summary>
            Specifies that a type from a scanned assembly is registered if it implements an interface
            that closes the provided open generic interface type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="openGenericServiceType">The open generic interface or base class type for which implementations will be found.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsClosedTypesOf``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Type,System.Object)">
            <summary>
            Specifies that a type from a scanned assembly is registered if it implements an interface
            that closes the provided open generic interface type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="openGenericServiceType">The open generic interface or base class type for which implementations will be found.</param>
            <param name="serviceKey">Key of the service.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AsClosedTypesOf``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Type,System.Func{System.Type,System.Object})">
            <summary>
            Specifies that a type from a scanned assembly is registered if it implements an interface
            that closes the provided open generic interface type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set service mapping on.</param>
            <param name="openGenericServiceType">The open generic interface or base class type for which implementations will be found.</param>
            <param name="serviceKeyMapping">Function mapping types to service keys.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AssignableTo``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Type)">
            <summary>
            Filters the scanned types to include only those assignable to the provided
            type.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to filter types from.</param>
            <param name="type">The type or interface which all classes must be assignable from.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AssignableTo``1(Autofac.Builder.IRegistrationBuilder{System.Object,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle})">
            <summary>
            Filters the scanned types to include only those assignable to the provided
            type.
            </summary>
            <param name="registration">Registration to filter types from.</param>
            <typeparam name="T">The type or interface which all classes must be assignable from.</typeparam>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Except``1(Autofac.Builder.IRegistrationBuilder{System.Object,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle})">
            <summary>
            Filters the scanned types to exclude the provided type.
            </summary>
            <param name="registration">Registration to filter types from.</param>
            <typeparam name="T">The concrete type to exclude.</typeparam>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.Except``1(Autofac.Builder.IRegistrationBuilder{System.Object,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle},System.Action{Autofac.Builder.IRegistrationBuilder{``0,Autofac.Builder.ConcreteReflectionActivatorData,Autofac.Builder.SingleRegistrationStyle}})">
            <summary>
            Filters the scanned types to exclude the provided type, providing specific configuration for
            the excluded type.
            </summary>
            <param name="registration">Registration to filter types from.</param>
            <param name="customizedRegistration">Registration for the excepted type.</param>
            <typeparam name="T">The concrete type to exclude.</typeparam>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.InNamespaceOf``1(Autofac.Builder.IRegistrationBuilder{System.Object,Autofac.Features.Scanning.ScanningActivatorData,Autofac.Builder.DynamicRegistrationStyle})">
            <summary>
            Filters the scanned types to include only those in the namespace of the provided type
            or one of its sub-namespaces.
            </summary>
            <param name="registration">Registration to filter types from.</param>
            <typeparam name="T">A type in the target namespace.</typeparam>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.InNamespace``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.String)">
            <summary>
            Filters the scanned types to include only those in the provided namespace
            or one of its sub-namespaces.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to filter types from.</param>
            <param name="ns">The namespace from which types will be selected.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterAdapter``2(Autofac.ContainerBuilder,System.Func{Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},``0,``1})">
            <summary>
            Adapt all components implementing service <typeparamref name="TFrom"/>
            to provide <typeparamref name="TTo"/> using the provided <paramref name="adapter"/>
            function.
            </summary>
            <typeparam name="TFrom">Service type to adapt from.</typeparam>
            <typeparam name="TTo">Service type to adapt to. Must not be the
            same as <typeparamref name="TFrom"/>.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="adapter">Function adapting <typeparamref name="TFrom"/> to
            service <typeparamref name="TTo"/>, given the context and parameters.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterAdapter``2(Autofac.ContainerBuilder,System.Func{Autofac.IComponentContext,``0,``1})">
            <summary>
            Adapt all components implementing service <typeparamref name="TFrom"/>
            to provide <typeparamref name="TTo"/> using the provided <paramref name="adapter"/>
            function.
            </summary>
            <typeparam name="TFrom">Service type to adapt from.</typeparam>
            <typeparam name="TTo">Service type to adapt to. Must not be the
            same as <typeparamref name="TFrom"/>.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="adapter">Function adapting <typeparamref name="TFrom"/> to
            service <typeparamref name="TTo"/>, given the context.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterAdapter``2(Autofac.ContainerBuilder,System.Func{``0,``1})">
            <summary>
            Adapt all components implementing service <typeparamref name="TFrom"/>
            to provide <typeparamref name="TTo"/> using the provided <paramref name="adapter"/>
            function.
            </summary>
            <typeparam name="TFrom">Service type to adapt from.</typeparam>
            <typeparam name="TTo">Service type to adapt to. Must not be the
            same as <typeparamref name="TFrom"/>.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="adapter">Function adapting <typeparamref name="TFrom"/> to
            service <typeparamref name="TTo"/>.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterGenericDecorator(Autofac.ContainerBuilder,System.Type,System.Type,System.Object,System.Object)">
            <summary>
            Decorate all components implementing open generic service <paramref name="decoratedServiceType"/>.
            The <paramref name="fromKey"/> and <paramref name="toKey"/> parameters must be different values.
            </summary>
            <param name="builder">Container builder.</param>
            <param name="decoratedServiceType">Service type being decorated. Must be an open generic type.</param>
            <param name="fromKey">Service key or name associated with the components being decorated.</param>
            <param name="toKey">Service key or name given to the decorated components.</param>
            <param name="decoratorType">The type of the decorator. Must be an open generic type, and accept a parameter
            of type <paramref name="decoratedServiceType"/>, which will be set to the instance being decorated.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterDecorator``1(Autofac.ContainerBuilder,System.Func{Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},``0,``0},System.Object,System.Object)">
            <summary>
            Decorate all components implementing service <typeparamref name="TService"/>
            using the provided <paramref name="decorator"/> function.
            The <paramref name="fromKey"/> and <paramref name="toKey"/> parameters must be different values.
            </summary>
            <typeparam name="TService">Service type being decorated.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="decorator">Function decorating a component instance that provides
            <typeparamref name="TService"/>, given the context and parameters.</param>
            <param name="fromKey">Service key or name associated with the components being decorated.</param>
            <param name="toKey">Service key or name given to the decorated components.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterDecorator``1(Autofac.ContainerBuilder,System.Func{Autofac.IComponentContext,``0,``0},System.Object,System.Object)">
            <summary>
            Decorate all components implementing service <typeparamref name="TService"/>
            using the provided <paramref name="decorator"/> function.
            The <paramref name="fromKey"/> and <paramref name="toKey"/> parameters must be different values.
            </summary>
            <typeparam name="TService">Service type being decorated.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="decorator">Function decorating a component instance that provides
            <typeparamref name="TService"/>, given the context.</param>
            <param name="fromKey">Service key or name associated with the components being decorated.</param>
            <param name="toKey">Service key or name given to the decorated components.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.RegisterDecorator``1(Autofac.ContainerBuilder,System.Func{``0,``0},System.Object,System.Object)">
            <summary>
            Decorate all components implementing service <typeparamref name="TService"/>
            using the provided <paramref name="decorator"/> function.
            The <paramref name="fromKey"/> and <paramref name="toKey"/> parameters must be different values.
            </summary>
            <typeparam name="TService">Service type being decorated.</typeparam>
            <param name="builder">Container builder.</param>
            <param name="decorator">Function decorating a component instance that provides
            <typeparamref name="TService"/>.</param>
            <param name="fromKey">Service key or name associated with the components being decorated.</param>
            <param name="toKey">Service key or name given to the decorated components.</param>
        </member>
        <member name="M:Autofac.RegistrationExtensions.OnRelease``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Action{``0})">
            <summary>
            Run a supplied action instead of disposing instances when they're no
            longer required.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to set release action for.</param>
            <param name="releaseAction">An action to perform instead of disposing the instance.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
            <remarks>Only one release action can be configured per registration.</remarks>
        </member>
        <member name="M:Autofac.RegistrationExtensions.AutoActivate``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Wraps a registration in an implicit <see cref="T:Autofac.IStartable"/> and automatically
            activates the registration after the container is built.
            </summary>
            <param name="registration">Registration to set release action for.</param>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <returns>A registration builder allowing further configuration of the component.</returns>
            <remarks>
            <para>
            While you can implement an <see cref="T:Autofac.IStartable"/> to perform some logic at
            container build time, sometimes you need to just activate a registered component and
            that's it. This extension allows you to automatically activate a registration on
            container build. No additional logic is executed and the resolved instance is not held
            so container disposal will end up disposing of the instance.
            </para>
            <para>
            Depending on how you register the lifetime of the component, you may get an exception
            when you build the container - components that are scoped to specific lifetimes (like
            ASP.NET components scoped to a request lifetime) will fail to resolve because the
            appropriate lifetime is not available.
            </para>
            </remarks>
        </member>
        <member name="M:Autofac.RegistrationExtensions.InstancePerRequest``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Object[])">
            <summary>
            Share one instance of the component within the context of a single
            web/HTTP/API request. Only available for integration that supports
            per-request dependencies (e.g., MVC, Web API, web forms, etc.).
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">The registration to configure.</param>
            <param name="lifetimeScopeTags">Additional tags applied for matching lifetime scopes.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registration" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="M:Autofac.RegistrationExtensions.OnlyIf``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Predicate{Autofac.Core.IComponentRegistry})">
            <summary>
            Attaches a predicate to evaluate prior to executing the registration.
            The predicate will run at registration time, not runtime, to determine
            whether the registration should execute.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">The registration to configure.</param>
            <param name="predicate">The predicate to run to determine if the registration should be made.</param>
            <returns>A registration builder allowing further configuration of the component.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registration" /> or <paramref name="predicate" /> is <see langword="null" />.
            </exception>
            <exception cref="T:System.NotSupportedException">
            Thrown if <paramref name="registration" /> has no reference to the original callback
            with which it was associated (e.g., it wasn't made with a standard registration method
            as part of a <see cref="T:Autofac.ContainerBuilder"/>).
            </exception>
        </member>
        <member name="M:Autofac.RegistrationExtensions.IfNotRegistered``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2},System.Type)">
            <summary>
            Attaches a predicate such that a registration will only be made if
            a specific service type is not already registered.
            The predicate will run at registration time, not runtime, to determine
            whether the registration should execute.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TActivatorData">Activator data type.</typeparam>
            <typeparam name="TStyle">Registration style.</typeparam>
            <param name="registration">The registration to configure.</param>
            <param name="serviceType">
            The service type to check for to determine if the registration should be made.
            Note this is the *service type* - the <code>As&lt;T&gt;</code> part.
            </param>
            <returns>A registration builder allowing further configuration of the component.</returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="registration" /> or <paramref name="serviceType" /> is <see langword="null" />.
            </exception>
            <exception cref="T:System.NotSupportedException">
            Thrown if <paramref name="registration" /> has no reference to the original callback
            with which it was associated (e.g., it wasn't made with a standard registration method
            as part of a <see cref="T:Autofac.ContainerBuilder"/>).
            </exception>
        </member>
        <member name="T:Autofac.RegistrationExtensionsResources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.RegistrationExtensionsResources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.RegistrationExtensionsResources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.RegistrationExtensionsResources.InstanceRegistrationsAreSingleInstanceOnly">
            <summary>
               Looks up a localized string similar to The instance  registration &apos;{0}&apos; can support SingleInstance() sharing only..
            </summary>
        </member>
        <member name="P:Autofac.RegistrationExtensionsResources.MetadataAttributeNotFound">
            <summary>
               Looks up a localized string similar to A metadata attribute of type &apos;{0}&apos; was not found on &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.RegistrationExtensionsResources.MultipleMetadataAttributesSameType">
            <summary>
               Looks up a localized string similar to More than one metadata attribute of type &apos;{0}&apos; was found on &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.RegistrationExtensionsResources.NoMatchingConstructorExists">
            <summary>
               Looks up a localized string similar to No matching constructor exists on type &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Autofac.RegistrationExtensionsResources.OnlyIfRequiresCallbackContainer">
            <summary>
               Looks up a localized string similar to You can only attach a registration predicate to a registration that has a callback container attached (e.g., one that was made with a standard ContainerBuilder extension method)..
            </summary>
        </member>
        <member name="T:Autofac.ResolutionExtensions">
            <summary>
            Adds syntactic convenience methods to the <see cref="T:Autofac.IComponentContext"/> interface.
            </summary>
        </member>
        <member name="F:Autofac.ResolutionExtensions.PropertyInjectedInstanceTypeNamedParameter">
            <summary>
            The <see cref="T:Autofac.NamedParameter"/> name, provided when properties are injected onto an existing instance.
            </summary>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectProperties``1(Autofac.IComponentContext,``0)">
            <summary>
            Set any properties on <paramref name="instance"/> that can be
            resolved in the context.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectProperties``1(Autofac.IComponentContext,``0,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Set any properties on <paramref name="instance"/> that can be
            resolved in the context.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <param name="parameters">Optional parameters to use during the property injection.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectProperties``1(Autofac.IComponentContext,``0,Autofac.Core.Parameter[])">
            <summary>
            Set any properties on <paramref name="instance"/> that can be
            resolved in the context.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <param name="parameters">Optional parameters to use during the property injection.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectProperties``1(Autofac.IComponentContext,``0,Autofac.Core.IPropertySelector)">
            <summary>
            Set any properties on <paramref name="instance"/> that can be resolved by service and that satisfy the
            constraints imposed by <paramref name="propertySelector"/>.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <param name="propertySelector">Selector to determine with properties should be injected.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectProperties``1(Autofac.IComponentContext,``0,Autofac.Core.IPropertySelector,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Set any properties on <paramref name="instance"/> that can be resolved by service and that satisfy the
            constraints imposed by <paramref name="propertySelector"/>.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <param name="propertySelector">Selector to determine with properties should be injected.</param>
            <param name="parameters">Optional parameters to use during the property injection.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectProperties``1(Autofac.IComponentContext,``0,Autofac.Core.IPropertySelector,Autofac.Core.Parameter[])">
            <summary>
            Set any properties on <paramref name="instance"/> that can be resolved by service and that satisfy the
            constraints imposed by <paramref name="propertySelector"/>.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <param name="propertySelector">Selector to determine with properties should be injected.</param>
            <param name="parameters">Optional parameters to use during the property injection.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectUnsetProperties``1(Autofac.IComponentContext,``0)">
            <summary>
            Set any null-valued properties on <paramref name="instance"/> that can be
            resolved by the container.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectUnsetProperties``1(Autofac.IComponentContext,``0,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Set any null-valued properties on <paramref name="instance"/> that can be
            resolved by the container.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <param name="parameters">Optional parameters to use during the property injection.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.InjectUnsetProperties``1(Autofac.IComponentContext,``0,Autofac.Core.Parameter[])">
            <summary>
            Set any null-valued properties on <paramref name="instance"/> that can be
            resolved by the container.
            </summary>
            <typeparam name="TService">Type of instance. Used only to provide method chaining.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The instance to inject properties into.</param>
            <param name="parameters">Optional parameters to use during the property injection.</param>
            <returns><paramref name="instance"/>.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.IsRegistered``1(Autofac.IComponentContext)">
            <summary>
            Determine whether the specified service is available in the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <typeparam name="TService">The service to test for the registration of.</typeparam>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.IsRegistered(Autofac.IComponentContext,System.Type)">
            <summary>
            Determine whether the specified service is available in the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceType">The service to test for the registration of.</param>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.IsRegisteredService(Autofac.IComponentContext,Autofac.Core.Service)">
            <summary>
            Determine whether the specified service is available in the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="service">The service to test for the registration of.</param>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.IsRegisteredWithKey``1(Autofac.IComponentContext,System.Object)">
            <summary>
            Determine whether the specified service is available in the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">The key of the service to test for the registration of.</param>
            <typeparam name="TService">Type type of the service to test for the registration of.</typeparam>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.IsRegisteredWithKey(Autofac.IComponentContext,System.Object,System.Type)">
            <summary>
            Determine whether the specified service is available in the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">The key of the service to test for the registration of.</param>
            <param name="serviceType">Type type of the service to test for the registration of.</param>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.IsRegisteredWithName``1(Autofac.IComponentContext,System.String)">
            <summary>
            Determine whether the specified service is available in the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceName">The name of the service to test for the registration of.</param>
            <typeparam name="TService">Type type of the service to test for the registration of.</typeparam>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.IsRegisteredWithName(Autofac.IComponentContext,System.String,System.Type)">
            <summary>
            Determine whether the specified service is available in the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceName">The name of the service to test for the registration of.</param>
            <param name="serviceType">Type type of the service to test for the registration of.</param>
            <returns>True if the service is registered.</returns>
        </member>
        <member name="M:Autofac.ResolutionExtensions.Resolve``1(Autofac.IComponentContext)">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The service to retrieve.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <returns>The component instance that provides the service.</returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException" />
            <exception cref="T:Autofac.Core.DependencyResolutionException" />
        </member>
        <member name="M:Autofac.ResolutionExtensions.Resolve``1(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The type to which the result will be cast.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.Resolve``1(Autofac.IComponentContext,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The type to which the result will be cast.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.Resolve(Autofac.IComponentContext,System.Type)">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceType">The service type.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.Resolve(Autofac.IComponentContext,System.Type,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceType">The service type.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.Resolve(Autofac.IComponentContext,System.Type,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceType">The service type.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveKeyed``1(Autofac.IComponentContext,System.Object)">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The type to which the result will be cast.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">Key of the service.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveKeyed``1(Autofac.IComponentContext,System.Object,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The type to which the result will be cast.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">Key of the service.</param>
            <param name="parameters">The parameters.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveKeyed``1(Autofac.IComponentContext,System.Object,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The type to which the result will be cast.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">Key of the service.</param>
            <param name="parameters">The parameters.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveKeyed(Autofac.IComponentContext,System.Object,System.Type)">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">Key of the service.</param>
            <param name="serviceType">Type of the service.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveKeyed(Autofac.IComponentContext,System.Object,System.Type,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">Key of the service.</param>
            <param name="serviceType">Type of the service.</param>
            <param name="parameters">The parameters.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveKeyed(Autofac.IComponentContext,System.Object,System.Type,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">Key of the service.</param>
            <param name="serviceType">Type of the service.</param>
            <param name="parameters">The parameters.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveNamed``1(Autofac.IComponentContext,System.String)">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The type to which the result will be cast.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceName">Name of the service.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveNamed``1(Autofac.IComponentContext,System.String,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The type to which the result will be cast.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceName">Name of the service.</param>
            <param name="parameters">The parameters.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveNamed``1(Autofac.IComponentContext,System.String,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context.
            </summary>
            <typeparam name="TService">The type to which the result will be cast.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceName">Name of the service.</param>
            <param name="parameters">The parameters.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveNamed(Autofac.IComponentContext,System.String,System.Type)">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceName">The service name.</param>
            <param name="serviceType">Type of the service.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveNamed(Autofac.IComponentContext,System.String,System.Type,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceName">The service name.</param>
            <param name="serviceType">Type of the service.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveNamed(Autofac.IComponentContext,System.String,System.Type,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceName">The service name.</param>
            <param name="serviceType">Type of the service.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptional``1(Autofac.IComponentContext)">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptional``1(Autofac.IComponentContext,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptional``1(Autofac.IComponentContext,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptional(Autofac.IComponentContext,System.Type)">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceType">The type of the service.</param>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptional(Autofac.IComponentContext,System.Type,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceType">The type of the service.</param>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptional(Autofac.IComponentContext,System.Type,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceType">The type of the service.</param>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalKeyed``1(Autofac.IComponentContext,System.Object)">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">The name of the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalKeyed``1(Autofac.IComponentContext,System.Object,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceKey">The name of the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalKeyed``1(Autofac.IComponentContext,System.Object,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceKey">The key of the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalNamed``1(Autofac.IComponentContext,System.String)">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceName">The name of the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalNamed``1(Autofac.IComponentContext,System.String,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceName">The name of the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalNamed``1(Autofac.IComponentContext,System.String,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="serviceName">The name of the service.</param>
            <typeparam name="TService">The service to resolve.</typeparam>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalService(Autofac.IComponentContext,Autofac.Core.Service)">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="service">The service.</param>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalService(Autofac.IComponentContext,Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="service">The service.</param>
            <param name="parameters">Parameters for the service.</param>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveOptionalService(Autofac.IComponentContext,Autofac.Core.Service,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context, or null if the service is not
            registered.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="service">The service.</param>
            <param name="parameters">Parameters for the service.</param>
            <returns>
            The component instance that provides the service, or null.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveService(Autofac.IComponentContext,Autofac.Core.Service)">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="service">The service to resolve.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveService(Autofac.IComponentContext,Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="service">The service to resolve.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.ResolveService(Autofac.IComponentContext,Autofac.Core.Service,Autofac.Core.Parameter[])">
            <summary>
            Retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="parameters">Parameters for the service.</param>
            <param name="service">The service to resolve.</param>
            <returns>
            The component instance that provides the service.
            </returns>
            <exception cref="T:Autofac.Core.Registration.ComponentNotRegisteredException"/>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.TryResolve``1(Autofac.IComponentContext,``0@)">
            <summary>
            Try to retrieve a service from the context.
            </summary>
            <typeparam name="T">The service type to resolve.</typeparam>
            <param name="context">The context from which to resolve the service.</param>
            <param name="instance">The resulting component instance providing the service, or default(T).</param>
            <returns>
            True if a component providing the service is available.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.TryResolve(Autofac.IComponentContext,System.Type,System.Object@)">
            <summary>
            Try to retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceType">The service type to resolve.</param>
            <param name="instance">The resulting component instance providing the service, or null.</param>
            <returns>
            True if a component providing the service is available.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.TryResolveKeyed(Autofac.IComponentContext,System.Object,System.Type,System.Object@)">
            <summary>
            Try to retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceKey">The key of the service to resolve.</param>
            <param name="serviceType">The type of the service to resolve.</param>
            <param name="instance">The resulting component instance providing the service, or null.</param>
            <returns>
            True if a component providing the service is available.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.TryResolveNamed(Autofac.IComponentContext,System.String,System.Type,System.Object@)">
            <summary>
            Try to retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="serviceName">The name of the service to resolve.</param>
            <param name="serviceType">The type of the service to resolve.</param>
            <param name="instance">The resulting component instance providing the service, or null.</param>
            <returns>
            True if a component providing the service is available.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.TryResolveService(Autofac.IComponentContext,Autofac.Core.Service,System.Object@)">
            <summary>
            Try to retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="service">The service to resolve.</param>
            <param name="instance">The resulting component instance providing the service, or null.</param>
            <returns>
            True if a component providing the service is available.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
        </member>
        <member name="M:Autofac.ResolutionExtensions.TryResolveService(Autofac.IComponentContext,Autofac.Core.Service,System.Collections.Generic.IEnumerable{Autofac.Core.Parameter},System.Object@)">
            <summary>
            Try to retrieve a service from the context.
            </summary>
            <param name="context">The context from which to resolve the service.</param>
            <param name="service">The service to resolve.</param>
            <param name="instance">The resulting component instance providing the service, or null.</param>
            <param name="parameters">The parameters.</param>
            <returns>
            True if a component providing the service is available.
            </returns>
            <exception cref="T:Autofac.Core.DependencyResolutionException"/>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="context" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="T:Autofac.ScanningFilterExtensions">
            <summary>
            Convenience filters for use with assembly scanning registrations.
            </summary>
        </member>
        <member name="M:Autofac.ScanningFilterExtensions.PublicOnly``3(Autofac.Builder.IRegistrationBuilder{``0,``1,``2})">
            <summary>
            Filters scanned assembly types to be only the public types.
            </summary>
            <typeparam name="TLimit">Registration limit type.</typeparam>
            <typeparam name="TScanningActivatorData">Activator data type.</typeparam>
            <typeparam name="TRegistrationStyle">Registration style.</typeparam>
            <param name="registration">Registration to filter types from.</param>
            <returns>Registration builder allowing the registration to be configured.</returns>
        </member>
        <member name="T:Autofac.TypedParameter">
            <summary>
            A parameter that can supply values to sites that exactly
            match a specified type. When applied to a reflection-based
            component, <see cref="P:Autofac.TypedParameter.Type"/> will be matched against
            the types of the component's constructor arguments. When applied to
            a delegate-based component, the parameter can be accessed using
            <see cref="M:Autofac.ParameterExtensions.TypedAs``1(System.Collections.Generic.IEnumerable{Autofac.Core.Parameter})"/>.
            </summary>
            <example>
            <para>
            Component with parameter...
            </para>
            <code>
            public class MyComponent
            {
                public MyComponent(int amount) { ... }
            }
            </code>
            <para>
            Providing the parameter...
            </para>
            <code>
            var builder = new ContainerBuilder();
            builder.RegisterType&lt;MyComponent&gt;();
            var container = builder.Build();
            var myComponent = container.Resolve&lt;MyComponent&gt;(new TypedParameter(typeof(int), 123));
            </code>
            </example>
        </member>
        <member name="P:Autofac.TypedParameter.Type">
            <summary>
            Gets the type against which targets are matched.
            </summary>
        </member>
        <member name="M:Autofac.TypedParameter.#ctor(System.Type,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.TypedParameter"/> class.
            </summary>
            <param name="type">The exact type to match.</param>
            <param name="value">The parameter value.</param>
        </member>
        <member name="M:Autofac.TypedParameter.From``1(``0)">
            <summary>
            Shortcut for creating <see cref="T:Autofac.TypedParameter"/>
            by using the <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">Type to be used for the parameter.</typeparam>
            <param name="value">The parameter value.</param>
            <returns>New typed parameter.</returns>
        </member>
        <member name="T:Autofac.TypeExtensions">
            <summary>
            Extends <see cref="T:System.Type"/> with methods that are useful in
            building scanning rules for <see cref="M:Autofac.RegistrationExtensions.RegisterAssemblyTypes(Autofac.ContainerBuilder,System.Reflection.Assembly[])"/>.
            </summary>
        </member>
        <member name="M:Autofac.TypeExtensions.IsInNamespace(System.Type,System.String)">
            <summary>
            Returns true if this type is in the <paramref name="namespace"/> namespace
            or one of its sub-namespaces.
            </summary>
            <param name="this">The type to test.</param>
            <param name="namespace">The namespace to test.</param>
            <returns>True if this type is in the <paramref name="namespace"/> namespace
            or one of its sub-namespaces; otherwise, false.</returns>
        </member>
        <member name="M:Autofac.TypeExtensions.IsInNamespaceOf``1(System.Type)">
            <summary>
            Returns true if this type is in the same namespace as <typeparamref name="T"/>
            or one of its sub-namespaces.
            </summary>
            <param name="this">The type to test.</param>
            <returns>True if this type is in the same namespace as <typeparamref name="T"/>
            or one of its sub-namespaces; otherwise, false.</returns>
        </member>
        <member name="M:Autofac.TypeExtensions.IsClosedTypeOf(System.Type,System.Type)">
            <summary>
            Determines whether the candidate type supports any base or
            interface that closes the provided generic type.
            </summary>
            <param name="this">The type to test.</param>
            <param name="openGeneric">The open generic against which the type should be tested.</param>
        </member>
        <member name="M:Autofac.TypeExtensions.IsAssignableTo``1(System.Type)">
            <summary>
            Determines whether this type is assignable to <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The type to test assignability to.</typeparam>
            <param name="this">The type to test.</param>
            <returns>True if this type is assignable to references of type
            <typeparamref name="T"/>; otherwise, False.</returns>
        </member>
        <member name="M:Autofac.TypeExtensions.GetMatchingConstructor(System.Type,System.Type[])">
            <summary>
            Finds a constructor with the matching type parameters.
            </summary>
            <param name="type">The type being tested.</param>
            <param name="constructorParameterTypes">The types of the contractor to find.</param>
            <returns>The <see cref="T:System.Reflection.ConstructorInfo"/> is a match is found; otherwise, <c>null</c>.</returns>
        </member>
        <member name="T:Autofac.TypeExtensionsResources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.TypeExtensionsResources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.TypeExtensionsResources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.TypeExtensionsResources.NotOpenGenericType">
            <summary>
               Looks up a localized string similar to The type &apos;{0}&apos; is not an open generic class or interface type so it won&apos;t work with methods that act on open generics..
            </summary>
        </member>
        <member name="T:Autofac.Util.AssemblyExtensions">
            <summary>
            Extension methods for <see cref="T:System.Reflection.Assembly"/>.
            </summary>
        </member>
        <member name="M:Autofac.Util.AssemblyExtensions.GetLoadableTypes(System.Reflection.Assembly)">
            <summary>
            Safely returns the set of loadable types from an assembly.
            </summary>
            <param name="assembly">The <see cref="T:System.Reflection.Assembly"/> from which to load types.</param>
            <returns>
            The set of types from the <paramref name="assembly" />, or the subset
            of types that could be loaded if there was any error.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="assembly" /> is <see langword="null" />.
            </exception>
        </member>
        <member name="T:Autofac.Util.Disposable">
            <summary>
            Base class for disposable objects.
            </summary>
        </member>
        <member name="M:Autofac.Util.Disposable.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Autofac.Util.Disposable.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="P:Autofac.Util.Disposable.IsDisposed">
            <summary>
            Gets a value indicating whether the current instance has been disposed.
            </summary>
        </member>
        <member name="T:Autofac.Util.Enforce">
            <summary>
            Helper methods used throughout the codebase.
            </summary>
        </member>
        <member name="M:Autofac.Util.Enforce.ArgumentElementNotNull``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Enforce that sequence does not contain null. Returns the
            value if valid so that it can be used inline in
            base initialiser syntax.
            </summary>
            <param name="value">The value.</param>
            <param name="name">The parameter name.</param>
        </member>
        <member name="M:Autofac.Util.Enforce.NotNull``1(``0)">
            <summary>
            Enforces that the provided object is non-null.
            </summary>
            <typeparam name="T">The type of value being checked.</typeparam>
            <param name="value">The value.</param>
            <returns><paramref name="value"/></returns>
        </member>
        <member name="M:Autofac.Util.Enforce.ArgumentNotNullOrEmpty(System.String,System.String)">
            <summary>
            Enforce that an argument is not null or empty. Returns the
            value if valid so that it can be used inline in
            base initialiser syntax.
            </summary>
            <param name="value">The value.</param>
            <param name="description">The description.</param>
            <returns><paramref name="value"/></returns>
        </member>
        <member name="M:Autofac.Util.Enforce.ArgumentTypeIsFunction(System.Type)">
            <summary>
            Enforce that the argument is a delegate type.
            </summary>
            <param name="delegateType">The type to test.</param>
        </member>
        <member name="T:Autofac.Util.EnforceResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Util.EnforceResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Util.EnforceResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Util.EnforceResources.CannotBeEmpty">
            <summary>
              Looks up a localized string similar to The argument &apos;{0}&apos; cannot be empty..
            </summary>
        </member>
        <member name="P:Autofac.Util.EnforceResources.CannotBeNull">
            <summary>
              Looks up a localized string similar to The object of type &apos;{0}&apos; cannot be null..
            </summary>
        </member>
        <member name="P:Autofac.Util.EnforceResources.DelegateReturnsVoid">
            <summary>
              Looks up a localized string similar to Type {0} returns void..
            </summary>
        </member>
        <member name="P:Autofac.Util.EnforceResources.ElementCannotBeNull">
            <summary>
              Looks up a localized string similar to The sequence provided as argument &apos;{0}&apos; cannot contain null elements..
            </summary>
        </member>
        <member name="P:Autofac.Util.EnforceResources.NotDelegate">
            <summary>
              Looks up a localized string similar to Type {0} is not a delegate type..
            </summary>
        </member>
        <member name="T:Autofac.Util.FallbackDictionary`2">
            <summary>
            Dictionary used to allow local property get/set and fall back to parent values.
            </summary>
        </member>
        <member name="F:Autofac.Util.FallbackDictionary`2._localValues">
            <summary>
            Storage for local values set in the dictionary.
            </summary>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Util.FallbackDictionary`2"/> class
            with an empty parent.
            </summary>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Util.FallbackDictionary`2"/> class
            with a specified parent.
            </summary>
            <param name="parent">
            The parent dictionary to which values should fall back when not present in the current dictionary.
            </param>
        </member>
        <member name="P:Autofac.Util.FallbackDictionary`2.Count">
            <summary>
            Gets the number of elements contained in the dictionary.
            </summary>
            <value>
            The number of elements contained in this collection plus the parent collection, minus overlapping key counts.
            </value>
        </member>
        <member name="P:Autofac.Util.FallbackDictionary`2.IsReadOnly">
            <summary>
            Gets a value indicating whether this collection is read-only.
            </summary>
            <value>
            Always returns <see langword="false" />.
            </value>
        </member>
        <member name="P:Autofac.Util.FallbackDictionary`2.Keys">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.ICollection`1"/> containing the keys of the dictionary.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.ICollection`1"/> containing the keys of the dictionary without duplicates.
            </value>
            <remarks>
            The order of the keys in the returned <see cref="T:System.Collections.Generic.ICollection`1"/> is unspecified,
            but it is guaranteed to be the same order as the corresponding values in the <see cref="T:System.Collections.Generic.ICollection`1"/>
            returned by the <see cref="P:Autofac.Util.FallbackDictionary`2.Values"/> property.
            </remarks>
        </member>
        <member name="P:Autofac.Util.FallbackDictionary`2.Parent">
            <summary>
            Gets the parent dictionary.
            </summary>
            <value>
            The parent dictionary to which values should fall back when not present in the current dictionary.
            </value>
        </member>
        <member name="P:Autofac.Util.FallbackDictionary`2.Values">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.ICollection`1"/> containing the values of the dictionary.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.ICollection`1"/> containing the values of the dictionary with overrides taken into account.
            </value>
            <remarks>
            The order of the keys in the returned <see cref="T:System.Collections.Generic.ICollection`1"/> is unspecified,
            but it is guaranteed to be the same order as the corresponding keys in the <see cref="T:System.Collections.Generic.ICollection`1"/>
            returned by the <see cref="P:Autofac.Util.FallbackDictionary`2.Keys"/> property.
            </remarks>
        </member>
        <member name="P:Autofac.Util.FallbackDictionary`2.Item(`0)">
            <summary>
            Gets or sets the <typeparamref name="TValue"/> with the specified key.
            </summary>
            <value>
            The <typeparamref name="TValue"/>.
            </value>
            <param name="key">The key.</param>
            <remarks>
            <para>
            Changes made to this dictionary do not affect the parent.
            </para>
            </remarks>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.Add(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Adds an item to the dictionary.
            </summary>
            <param name="item">The object to add to the dictionary.</param>
            <remarks>
            <para>
            Changes made to this dictionary do not affect the parent.
            </para>
            </remarks>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.Add(`0,`1)">
            <summary>
            Adds an element with the provided key and value to the dictionary.
            </summary>
            <param name="key">The object to use as the key of the element to add.</param>
            <param name="value">The object to use as the value of the element to add.</param>
            <remarks>
            <para>
            Changes made to this dictionary do not affect the parent.
            </para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">
            Thrown if <paramref name="key" /> is <see langword="null" />.
            </exception>
            <exception cref="T:System.ArgumentException">
            Thrown if an element with the same key is already present in the local or parent dictionary.
            </exception>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.Clear">
            <summary>
            Removes all items from the dictionary. Does not clear parent entries, only local overrides.
            </summary>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Determines whether the dictionary contains a specific value.
            </summary>
            <param name="item">The object to locate in the dictionary.</param>
            <returns>
            <see langword="true" /> if <paramref name="item" /> is found in the dictionary; otherwise, <see langword="false" />.
            </returns>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.ContainsKey(`0)">
            <summary>
            Determines whether the dictionary contains an element with the specified key.
            </summary>
            <param name="key">The key to locate in the dictionary.</param>
            <returns>
            <see langword="true" /> if the dictionary or its parent contains an element with the key; otherwise, <see langword="false" />.
            </returns>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
            <summary>
            Copies the elements of the dictionary to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.
            </summary>
            <param name="array">
            The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from
            the dictionary. The <see cref="T:System.Array" /> must have zero-based indexing.
            </param>
            <param name="arrayIndex">
            The zero-based index in <paramref name="array" /> at which copying begins.
            </param>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            An enumerator that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Removes the first occurrence of a specific object from the dictionary.
            </summary>
            <param name="item">The object to remove from the dictionary.</param>
            <returns>
            <see langword="true" /> if <paramref name="item" /> was successfully removed from the dictionary; otherwise, <see langword="false" />.
            This method also returns <see langword="false" /> if <paramref name="item" /> is not found in the original dictionary.
            </returns>
            <remarks>
            <para>
            Changes made to this dictionary do not affect the parent.
            </para>
            </remarks>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.Remove(`0)">
            <summary>
            Removes the element with the specified key from the dictionary.
            </summary>
            <param name="key">The key of the element to remove.</param>
            <returns>
            <see langword="true" /> if the element is successfully removed; otherwise, <see langword="false" />.
            This method also returns <see langword="false" /> if <paramref name="key" /> was not found in the original dictionary.
            </returns>
            <remarks>
            <para>
            Changes made to this dictionary do not affect the parent.
            </para>
            </remarks>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.TryGetValue(`0,`1@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
            <returns>
            <see langword="true" /> if the dictionary or parent contains an element with the specified key; otherwise, <see langword="false" />.
            </returns>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Autofac.Util.FallbackDictionary`2.OrderedKeys">
            <summary>
            Gets the list of correctly ordered unique keys from the local and parent dictionaries.
            </summary>
            <returns>
            An <see cref="T:System.Collections.Generic.IEnumerable`1"/> with the unique set of all keys.
            </returns>
        </member>
        <member name="T:Autofac.Util.FallbackDictionaryResources">
            <summary>
               A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Util.FallbackDictionaryResources.ResourceManager">
            <summary>
               Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Util.FallbackDictionaryResources.Culture">
            <summary>
               Overrides the current thread's CurrentUICulture property for all
               resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Util.FallbackDictionaryResources.DuplicateItem">
            <summary>
               Looks up a localized string similar to Item has already been added with key &apos;{0}&apos;..
            </summary>
        </member>
        <member name="T:Autofac.Util.ReflectionExtensions">
            <summary>
            Extension methods for reflection-related types.
            </summary>
        </member>
        <member name="M:Autofac.Util.ReflectionExtensions.TryGetDeclaringProperty(System.Reflection.ParameterInfo,System.Reflection.PropertyInfo@)">
            <summary>
            Maps from a property-set-value parameter to the declaring property.
            </summary>
            <param name="pi">Parameter to the property setter.</param>
            <param name="prop">The property info on which the setter is specified.</param>
            <returns>True if the parameter is a property setter.</returns>
        </member>
        <member name="M:Autofac.Util.ReflectionExtensions.GetProperty``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Get a PropertyInfo object from an expression of the form
            x =&gt; x.P.
            </summary>
            <typeparam name="TDeclaring">Type declaring the property.</typeparam>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="propertyAccessor">Expression mapping an instance of the
            declaring type to the property value.</param>
            <returns>Property info.</returns>
        </member>
        <member name="M:Autofac.Util.ReflectionExtensions.GetMethod``1(System.Linq.Expressions.Expression{System.Action{``0}})">
            <summary>
            Get the MethodInfo for a method called in the
            expression.
            </summary>
            <typeparam name="TDeclaring">Type on which the method is called.</typeparam>
            <param name="methodCallExpression">Expression demonstrating how the method appears.</param>
            <returns>The method info for the called method.</returns>
        </member>
        <member name="M:Autofac.Util.ReflectionExtensions.GetConstructor``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Gets the <see cref="T:System.Reflection.ConstructorInfo"/> for the new operation called in the expression.
            </summary>
            <typeparam name="TDeclaring">The type on which the constructor is called.</typeparam>
            <param name="constructorCallExpression">Expression demonstrating how the constructor is called.</param>
            <returns>The <see cref="T:System.Reflection.ConstructorInfo"/> for the called constructor.</returns>
        </member>
        <member name="T:Autofac.Util.ReflectionExtensionsResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Autofac.Util.ReflectionExtensionsResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Autofac.Util.ReflectionExtensionsResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Autofac.Util.ReflectionExtensionsResources.ExpressionNotConstructorCall">
            <summary>
              Looks up a localized string similar to The provided expression must be of the form () =&gt;new X(), but the provided expression was {0}..
            </summary>
        </member>
        <member name="P:Autofac.Util.ReflectionExtensionsResources.ExpressionNotMethodCall">
            <summary>
              Looks up a localized string similar to The provided expression must be of the form x =&gt;x.M(), but the provided expression was {0}..
            </summary>
        </member>
        <member name="P:Autofac.Util.ReflectionExtensionsResources.ExpressionNotPropertyAccessor">
            <summary>
              Looks up a localized string similar to The provided expression must be of the form x =&gt;x.P, but the provided expression was {0}..
            </summary>
        </member>
        <member name="T:Autofac.Util.ReleaseAction`1">
            <summary>
            Adapts an action to the <see cref="T:System.IDisposable"/> interface.
            </summary>
        </member>
        <member name="M:Autofac.Util.ReleaseAction`1.#ctor(System.Action{`0},System.Func{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Autofac.Util.ReleaseAction`1"/> class.
            </summary>
            <param name="action">
            The action to execute on disposal.
            </param>
            <param name="factory">
            A factory that retrieves the value on which the <paramref name="action" />
            should be executed.
            </param>
        </member>
        <member name="M:Autofac.Util.SequenceExtensions.JoinWith(System.Collections.Generic.IEnumerable{System.String},System.String)">
            <summary>
            Joins the strings into one single string interspersing the elements with the separator (a-la
            System.String.Join()).
            </summary>
            <param name="elements">The elements.</param>
            <param name="separator">The separator.</param>
            <returns>The joined string.</returns>
        </member>
        <member name="M:Autofac.Util.SequenceExtensions.AppendItem``1(System.Collections.Generic.IEnumerable{``0},``0)">
            <summary>
            Appends the item to the specified sequence.
            </summary>
            <typeparam name="T">The type of element in the sequence.</typeparam>
            <param name="sequence">The sequence.</param>
            <param name="trailingItem">The trailing item.</param>
            <returns>The sequence with an item appended to the end.</returns>
        </member>
        <member name="M:Autofac.Util.SequenceExtensions.Prepend``1(System.Collections.Generic.IEnumerable{``0},``0)">
            <summary>
            Prepends the item to the specified sequence.
            </summary>
            <typeparam name="T">The type of element in the sequence.</typeparam>
            <param name="sequence">The sequence.</param>
            <param name="leadingItem">The leading item.</param>
            <returns>The sequence with an item prepended.</returns>
        </member>
        <member name="M:Autofac.Util.TypeExtensions.GetTypesThatClose(System.Type,System.Type)">
            <summary>Returns the first concrete interface supported by the candidate type that
            closes the provided open generic service type.</summary>
            <param name="this">The type that is being checked for the interface.</param>
            <param name="openGeneric">The open generic type to locate.</param>
            <returns>The type of the interface.</returns>
        </member>
        <member name="M:Autofac.Util.TypeExtensions.FindAssignableTypesThatClose(System.Type,System.Type)">
            <summary>
            Looks for an interface on the candidate type that closes the provided open generic interface type.
            </summary>
            <param name="candidateType">The type that is being checked for the interface.</param>
            <param name="openGenericServiceType">The open generic service type to locate.</param>
            <returns>True if a closed implementation was found; otherwise false.</returns>
        </member>
        <member name="T:Autofac.Util.ValidatedNotNullAttribute">
            <summary>
            Signal attribute for static analysis that indicates a helper method is
            validating arguments for <see langword="null" />.
            </summary>
        </member>
    </members>
</doc>
