﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Set Incident Disposition</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">

        $(document).ready(function () {

            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    $(':disabled').each(function (e) {
                        $(this).removeAttr('disabled');
                    })
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });
        });

        function AfterFillForm() {

            GetIncidentNumber().then(function (result) {
                $("#incidentnumber").val(result);
                M.updateTextFields();
            });
            GetProblemNature().then(function (result) {
                $("#pnvals").val(result);
            });

            var problemnature = $("#pnvals").val();

            // < !--To allow users to select multiple dispositions: 1. Comment out SingleSelect(problemnature); 2. Uncomment MultiSelect(problemnature)-- >
            SingleSelect(problemnature);
            //MultiSelect(problemnature);

        }
        function SingleSelect(problemnature) {

            // By default form does not set problem nature so it uses DispositionCodes.xml
            // To use DispositionCodesByProblemNature.xml 1. Comment out section labeled HIDE PROBLEM NATURE and Uncomment section labeled SHOW PROBLEM NATURE
            // 2. Comment out section labeled Use DispositionCodes.xml and Uncomment section labeled Use DispositionCodesByProblemNature.xml

            //<!--Use DispositionCodes.xml-->
            //<!--START-->
            GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
                $("#dispositionvals").prepend(result);
                $("#dispositionvals option:first").attr('selected', 'selected');
                $('select').formSelect();
                SetSelectBoxFromParameters($("#ResponseDisposition"));
                $(".select-dropdown").focus();
            });
            //<!--END-->
            //<!--Use DispositionCodesByProblemNature.xml-->
            // Disposition Codes are limited by ProblemNature using the DispositionCodesByProblemNature.xml. If DispositionCodesByProblemNature.xml does not exist or Problem Nature is not pulled from incident then DispositionCodes.xml will be used.
            //<!-- START-->
            //if (problemnature != undefined && problemnature != "") {
            //        GenerateListBox("ResponseDisposition", 8, "DispositionCodesByProblemNature.xml", "dispositionbyproblemnaturelist.xsl", problemnature).then(function (result) {
            //            if (result != "") {
            //                $("#dispositionvals").prepend(result);
            //                $("#dispositionvals option:first").attr('selected', 'selected');
            //                $('select').formSelect();
            //                $(".select-dropdown").focus();
            //            }
            //            else {
            //                GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
            //                    $("#dispositionvals").prepend(result);
            //                    $("#dispositionvals option:first").attr('selected', 'selected');
            //                    $('select').formSelect();
            //                    $(".select-dropdown").focus();
            //                });
            //            }
            //        });
            //    }
            //    else {
            //        GenerateListBox("ResponseDisposition", 8, "DispositionCodes.xml", "genericlist.xsl").then(function (result) {
            //            $("#dispositionvals").prepend(result);
            //            $("#dispositionvals option:first").attr('selected', 'selected');
            //            $('select').formSelect();
            //                $(".select-dropdown").focus();
            //        });
            //}
            //<!--END-->

        }
        function MultiSelect(problemnature) {

            // By default form does not set problem nature so it uses DispositionCodes.xml
            // To use DispositionCodesByProblemNature.xml 1. Comment out section labeled HIDE PROBLEM NATURE and Uncomment section labeled SHOW PROBLEM NATURE
            // 2. Comment out section labeled Use DispositionCodes.xml and Uncomment section labeled Use DispositionCodesByProblemNature.xml

            //<!--Use DispositionCodes.xml-->
            //<!--START-->
            GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
                $("#dispositionvals").prepend(result);
                $("#dispositionvals option:first").attr('selected', 'selected');
                $('select').formSelect();
                SetSelectBoxFromParameters($("#ResponseDisposition"));
                $("#ResponseDisposition").prop("required", true);
                $("#ResponseDisposition").change(ValidateRequiredSelect);
                $(".select-dropdown").focus();
            });
            //<!--END-->
            //<!--Use DispositionCodesByProblemNature.xml-->
            // Disposition Codes are limited by ProblemNature using the DispositionCodesByProblemNature.xml. If DispositionCodesByProblemNature.xml does not exist or Problem Nature is not pulled from incident then DispositionCodes.xml will be used.
            //<!-- START-->
            //if (problemnature != undefined && problemnature != "") {
            //        GenerateSelectBox("ResponseDisposition", "DispositionCodesByProblemNature.xml", "dispositionbyproblemnatureselect.xsl", false, true, true, 8, true, true, problemnature).then(function (result) {
            //            if (result != "") {
            //                $("#dispositionvals").prepend(result);
            //                $("#dispositionvals option:first").attr('selected', 'selected');
            //                $('select').formSelect();
            //                $(".select-dropdown").focus();
            //            }
            //            else {
            //                GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
            //                    $("#dispositionvals").prepend(result);
            //                    $("#dispositionvals option:first").attr('selected', 'selected');
            //                    $('select').formSelect();
            //                    $(".select-dropdown").focus();
            //                });
            //            }
            //        });
            //    }
            //    else {
            //        GenerateSelectBox("ResponseDisposition", "DispositionCodes.xml", "genericselect.xsl", true, true, true, 8, true, true).then(function (result) {
            //            $("#dispositionvals").prepend(result);
            //            $("#dispositionvals option:first").attr('selected', 'selected');
            //            $('select').formSelect();
            //            $(".select-dropdown").focus();
            //        });
            //}
            //<!--END-->
        }
    </script>
</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">SET INCIDENT DISPOSITION</h5>
                </div>
            </div>
        </div>
    </div>
    <form  class="Flex-Form" action="SetIncidentDisposition.aspx?queryfile=SetIncidentDisposition.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <div class="col s8">
                <div class="row" style="margin-top:20px">
                    <div class="input-field col s12 m5">
                        <input name="incidentnumber" id="incidentnumber" type="text" disabled style="color:black;">
                        <label for="incidentnumber">Incident Number</label>
                    </div>
                    <div class="input-field col s12 m5">
                        <!--HIDE PROBLEM NATURE-->
                        <!--Start-->
                        <input name="ProblemNature" id="ProblemNature" type="hidden">
                        <!--End-->
                        <!--SHOW PROBLEM NATURE-->
                        <!--Start-->
                        <!--<input name="pnvals" id="pnvals" type="text" disabled style="color:black;">
                    <label for="pnvals">Problem Nature</label>-->
                        <!--End-->
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s12 m5" type="selectlist" id="dispositionvals" name="dispositionvals">
                        <label for="ResponseDisposition">Response Disposition</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="Flex-Form-Comment">
            <div class="comment">
                <div class="row">
                    <div class="input-field col s12">
                        <textarea id="comments" placeholder="Enter Comment Here" class="materialize-textarea" name="Comments"></textarea>
                        <label for="comments" class="active">Comments</label>
                    </div>
                </div>
            </div>
        </div>

        <!--Hidden Inputs that are passed to the query server-->
        <input type="hidden" name="incidentid" id="incidentid">

    </form>
                 
</body>
</html>