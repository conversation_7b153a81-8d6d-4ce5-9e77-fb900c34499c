//start share
var m_gunSerialName = "SER";
//end share
//start supplement
var m_gunSuppCheckboxName = "doSupplement";
var m_gunSuppHiddenFieldSerialName = "SupplementSerialNumber";
function CheckGunSupplementRequiredFields()
{
    //this method is called by CheckGunSupplementRequiredFields and HandleGunSupplementFieldsBeforeSubmit.
    //check to see if the checkbox and required input fields exist
    var elementGunDoSupplement = document.getElementById(m_gunSuppCheckboxName);
    var elementGunSER = document.getElementById(m_gunSerialName);
    if (elementGunDoSupplement != null)
    {
        //check to see if the serial input is there.
        if (elementGunSER == null)
        {
            alert("Configuration error: " + m_gunSuppCheckboxName + " requires " + m_gunSerialName + " field.");
            return false;
        }
        else
        {
            //the checkbox and serial field are there
            return true;

        }
    }
    else
    {
        return false;
    }
}
function GenerateGunSupplementHiddenFields()
{
    //this method is called as part of the window.onload.

    //check to see if the checkbox exist
    if (CheckGunSupplementRequiredFields())
    {

        //add the hidden field
        return "<input type=\"hidden\" name=\"" + m_gunSuppHiddenFieldSerialName + "\" id=\"" + m_gunSuppHiddenFieldSerialName + "\" />"

    }
    else
    {
        return "";
    }
}

function HandleGunSupplementFieldsBeforeSubmit()
{
    //how to use supplement:
    //To enable:
    //  labelSupplement, doSupplement,SER, and SupplementSerialNumber  are needed.
    //To disable:
    //  labelSupplement and doSupplement must be commented out.  We do not remove the nodes, just commented them out.

    var isValid = false;
    var elementGunDoSupplement = document.getElementById(m_gunSuppCheckboxName);
    var elementGunSER = document.getElementById(m_gunSerialName);
    var elementGunSupplementSerialNumber = document.getElementById(m_gunSuppHiddenFieldSerialName);
    if ((elementGunDoSupplement != null) && (elementGunDoSupplement.checked))
    {
        if (CheckGunSupplementRequiredFields())
        {
            if (elementGunSER.value != "")
            {
                elementGunSupplementSerialNumber.value = elementGunSER.value;
                isValid = true;
            }
            else
            {
                //required field is not met.
                alert("Serial number is needed to query supplement.");
                isValid = false;
            }
        }
    }
    else
    {
        isValid = true;
    }
    return isValid;
}
//end supplement