<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Radius Search</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<script src="clientutilities.js"></script>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Radius Search</H4>
						<form action="RadiusSearch.aspx?queryfile=RadiusSearch.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td>Type:</td>
									<td>
<Select name="Type" id="Type" savelast="true" onchange="TypeChanged()">
	<option value=1>Hydrants</option>
	<option value=2>Stations/Posts</option>
	<option value=3>Locations by Type</option>
</Select>
									</td>
								</tr>
								<tr name="LocationType" id="LocationType">
									<td>Location Type:</td>
									<td>
										<XML id="locationtypestyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="locationtypevals" name="locationtypevals">
											<XML id="locationtypesource" src="LocationTypes.xml"></XML>
										</SPAN>
									</td>
								</tr>
								<tr>
									<td>Radius (<script language="javascript"> DisplayDistanceUnitCaption(); </script>):</td>
									<td><input type="text" name="localizedRadius" size="10" id="localizedRadius" mandatory="true" minvalue = "1" maxvalue = "10000" savelast="true"></td>
								</tr>
								<tr>
									<td valign="top" width="185"><b>Location:</b></td>
								</tr>
								<tr>
									<td align="right" width="185">Current<input type="radio" class="bigradio" name="location" id="currentlocation" value="currentlocation"></td>
									<td width="400">Lat.&nbsp;&nbsp;&nbsp;&nbsp;<span id="curlat" name="curlat" formvalue="true"></span></td>
								</tr>
								<tr>
									<td width="185"></td>
									<td width="400">Long.&nbsp;<span id="curlong" name="curlong" formvalue="true"></span></td>
								</tr>
								<tr>
									<td align="right" width="185">Selected<input type="radio" class="bigradio" name="location" id="selectedlocation" value="selectedlocation"></td>
									<td width="400">Lat.&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="sellat" name="sellat" fillbutton="selectbutton" size="9">&nbsp;&nbsp;<INPUT type="image" align="absMiddle" enablefill="true" value="Select from map" alt="Select from map"
											src="map.gif" id="selectbutton" name="selectbutton" simulateclickcontrolid="selectedlocation"></td>
								</tr>
								<tr>
									<td width="185"></td>
									<td width="400">Long.&nbsp;<input type="text" id="sellong" name="sellong" fillbutton="selectbutton" size="9"></td>
								</tr>
							</table>
							<input type="hidden" name="Radius" id="Radius">
							<input type="hidden" name="Lat" id="Lat">
							<input type="hidden" name="Long" id="Long">
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="TypeChanged()"><!--This will make the LocationTypes visible/hidden according to savelast value-->
							<input type="button" name="Query" id="Button1" value="Submit" onkeypress="ValidatePage()" onclick="ValidatePage()">
							<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<SCRIPT language="javascript">
		function window.onload()
		{
			PrepareValidation(Form);
			Form.Type.focus();
			Form.currentlocation.checked = true;

		    // Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			locationtypevals.innerHTML = GenerateSelectBox("LocationTypeID", locationtypesource, locationtypestyle, false, false, true, 1, false, false);
			// set the first (empty) item text to "All"
			Form.LocationTypeID.options[0].text = "All";
			Form.LocationTypeID.options[0].value = "-1";

			LocationType.style.visibility = "hidden";
		}

		function TypeChanged()
		{
			if (Form.Type.value != "3")
				LocationType.style.visibility = "hidden";
			else
				LocationType.style.visibility = "visible";
		}

		function ValidatePage()
		{
			var radius = parseFloat( Form.localizedRadius.value );
			if (isNaN(radius))
			{
				Form.localizedRadius.focus();
				alert('Radius must be a numeric value.');
				return;
			}
			else
			{
				Form.Radius.value = ConvertLocalizedDistanceToMiles(radius)
			}
			
			if (Form.currentlocation.checked)
			{     
				if (curlat != null)
				{
					if (curlat.innerText == null)
						Form.Lat.value = '';
					else
						Form.Lat.value = curlat.innerText;
            			}
				else
					Form.Lat.value = '';

				if (curlong != null)
				{
					if (curlong.innerText == null)
						Form.Long.value = '';
					else
						Form.Long.value = curlong.innerText;
				}
				else
					Form.Long.value = '';
			}
			else if (Form.selectedlocation.checked)
			{
				Form.Lat.value = Form.sellat.value;
				Form.Long.value = Form.sellong.value;
			}			

			Form.Submit.click();
		}
	</SCRIPT>
</HTML>
