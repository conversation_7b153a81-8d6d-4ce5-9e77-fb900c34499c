<?xml version="1.0" encoding="utf-8" ?>
<xs:schema id="SchemaComSetting"
                  targetNamespace="http://tempuri.org/SchemaComSetting.xsd"
                  elementFormDefault="qualified"
                  xmlns="http://tempuri.org/SchemaComSetting.xsd"
                  xmlns:mstns="http://tempuri.org/SchemaComSetting.xsd"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema">


  <xs:simpleType name="Parity">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Odd" />
      <xs:enumeration value="Even" />
      <xs:enumeration value="Mark" />
      <xs:enumeration value="Space" />
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="StopBits">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="One" />
      <xs:enumeration value="Two" />
      <xs:enumeration value="OnePointFive" />
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="Handshake">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="XOnXOff" />
      <xs:enumeration value="RequestToSendXOnXOff" />
      <xs:enumeration value="RequestToSend" />
    </xs:restriction>
  </xs:simpleType>

  <xs:element name="ComSetting">
    <xs:complexType>
      <xs:sequence>
	<xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" />
	<xs:element name="Port" type="xs:int" minOccurs="1" maxOccurs="1" />
	<xs:element name="Baud" type="xs:int" minOccurs="1" maxOccurs="1" />
        <xs:element name="Parity" type="Parity" minOccurs="1" maxOccurs="1" />
	<xs:element name="DataBits" type="xs:int" minOccurs="1" maxOccurs="1" />
	<xs:element name="StopBits" type="StopBits" minOccurs="1" maxOccurs="1" />
	<xs:element name="FlowControlDsrDtr" type="xs:boolean" minOccurs="1" maxOccurs="1" />
	<xs:element name="FlowControlXOnOff" type="xs:boolean" minOccurs="1" maxOccurs="1" />
	<xs:element name="FlowControlCtsRts" type="xs:boolean" minOccurs="1" maxOccurs="1" />
	<xs:element name="FlowControlRs485" type="xs:boolean" minOccurs="1" maxOccurs="1" />
	<xs:element name="EnableDtr" type="xs:boolean" minOccurs="1" maxOccurs="1" />
	<xs:element name="EnableRts" type="xs:boolean" minOccurs="1" maxOccurs="1" />
	<xs:element name="Handshake" type="Handshake" minOccurs="1" maxOccurs="1" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
