﻿var CefSharpEnabled = false;

(async function () {
    if (typeof CefSharp != "undefined") {
        CefSharpEnabled = true;
        await CefSharp.BindObjectAsync('ClientJsObject');
    }
})();

//On Document Ready
$(document).ready(function () {

    //check longitude before reverse geo-coding
    $('#sellat').focusout(function () {
        var iNum = parseFloat($("#sellong").val());
        if (iNum != 0) {
            GetReverseGeoCode($('#sellat').val(), $('#sellong').val()).then(function (result) {
                $("#seladdress").val(result[0])
                $("#selcity").val(result[1]);
                $("#selzip").val(result[2]);
            });
        }
    });

    //check latitude before reverse geo-coding
    $('#sellong').focusout(function () {
        var iNum = parseFloat($("#sellat").val());
        if (iNum != 0) {
            GetReverseGeoCode($('#sellat').val(), $('#sellong').val()).then(function (result) {
                $("#seladdress").val(result[0])
                $("#selcity").val(result[1]);
                $("#selzip").val(result[2]);
            });
        }
    });

    //check longitude before reverse geo-coding
    $('#lat').focusout(function () {
        var iNum = parseFloat($("#long").val());
        if (iNum != 0) {
            GetReverseGeoCode($('#lat').val(), $('#long').val()).then(function (result) {
                $("#seladdress").val(result[0])
                $("#selcity").val(result[1]);
                $("#selcross_street").val(result[2]);
            });
        }
    });

    //check latitude before reverse geo-coding
    $('#long').focusout(function () {
        var iNum = parseFloat($("#lat").val());
        if (iNum != 0) {
            GetReverseGeoCode($('#lat').val(), $('#long').val()).then(function (result) {
                $("#seladdress").val(result[0])
                $("#selcity").val(result[1]);
                $("#selcross_street").val(result[2]);
            });
        }
    });

    //Geo-Validate addresses entered on the address tab.
    $(".Geo_Validate_Address").click(function () {
        var address = $("#Address").val();
        var city = $("#City").val();
        var zip = $("#Zip").val();
        $("#preloader_container").show();
        GeoValidateAddress(address, city, zip, false);
    });

    //Geo-Validate addresses entered on the address tab.
    $(".More_Button").click(function () {
        var address = $("#Address").val();
        var city = $("#City").val();
        var zip = $("#Zip").val();
        //need to update preloader visibility
        GeoValidateAddress(address, city, zip, true);
    });

    //Make a selection from the map
    $(".Select_From_Map").click(function () {
        GetLocationFromMap();
        return false;
    });

});

//GeoValidate button pressed
function GeoValidateAddress(address, city, zip, more) {
    if (CefSharpEnabled == true) {//WPF implementation
        ClientJsObject.geoValidateAddress(address, city, zip, more);
    }
    else {//Xamarin implementation
        var param = {
            _method: 'geoValidateAddress',
            _address: address,
            _city: city,
            _zip: zip,
            _more: more
        }
        invokeCSharp(JSON.stringify(param));
    }
}

//ReverseGeo Coding Performed
async function GetReverseGeoCode(Latitude, longitude) {
    if (CefSharpEnabled == true) {//WPF implementation
        var ret = await ClientJsObject.getReverseGeoCode(Latitude, longitude);
        return ret;
    }
    else {//Xamarin implementation
        var param = {
            _method: 'getReverseGeoCode',
            _callback: 'GetReverseGeoCode_callback',
            _Latitude: Latitude,
            _longitude: longitude
        }
        GetReverseGeoCode_callback.df = $.Deferred();
        GetReverseGeoCode_callback.parameters = param;
        invokeCSharp(JSON.stringify(param));
        return $.when(GetReverseGeoCode_callback.df).done().promise();
    }
}

function GetReverseGeoCode_callback(result) {

    if (result == '__fail__') {
        GetReverseGeoCode_callback.df.fail(result);
    }
    else {
        GetReverseGeoCode_callback.df.resolve(result);
    }
}

//List of Return GeoValidated Addreses
function GeoValidateAddressReturn(addressList, verifyMoreN, verifyMaxResultsReturned) {
    $("#preloader_container").hide();
    $("#verifiedLocationsList").empty();
    if (addressList != undefined) {
        addressList = JSON.parse(addressList);
        if (Array.isArray(addressList)) {
            for (var i = 0; i < addressList.length; i++) {
                var address = addressList[i];
                $row = $("<tr><td>" + address.Address + "</td></tr>");
                $row.data("address", address);
                $row.data("index", i);
                $row.click(verifiedLocationsListItemClicked);
                $("#verifiedLocationsList").append($row);
            }
        }
        else {
            $("#verifiedLocationsList").append("<tr><td>" + addressList + "</td></tr>");
        }
    }
    $("#geovalid_address_selected").hide();
}

function setVerifiedAddress() {

    // Allow geo verified address to be reloaded into form
    if ($("#SelectedAddressTab").val() == "verifiedaddresstab") {
        var value = $("#Address").val().trim();

        if ($("#transportationbuilding").val() != "") {
            value += ", " + $("#transportationbuilding").val();
        }
        if ($("#transportationapartment").val() != "") {
            value += ", " + $("#transportationapartment").val();
        }
        if ($("#City").val() != "") {
            value += ", " + $("#City").val();
        }
        if ($("#transportstate").val() != "") {
            value += ", " + $("#transportstate").val();
        }
        if ($("#Zip").val() != "") {
            value += " " + $("#Zip").val();
        }

        $row = $("<tr class='selected'><td>" + value + "</td></tr>");
        $row.data("index", 0);
        $("#verifiedLocationsList").append($row);
        GeoButtonVisibility();
        $("#geovalid_address_selected").show();
    }
}

//Click Event of Row in GeoValidated Addresses Table
function verifiedLocationsListItemClicked() {
    var address = $(this).data("address");
    if (address != undefined) {
        var match = address.AddressMatch;
        var addrStr = "", building = "", apt = "", state = "", city = "", zip = "";
        if (match.Name != "") {
            addrStr += match.Name + " ";
        }
        if (match.BlockRange != "") {
            addrStr += match.BlockRange + " ";
        }
        if (match.StreetName != "") {
            addrStr += match.StreetName + " ";
        }
        building = match.Building;
        apt = match.Apartment;
        state = match.StateAbbrev;
        city = match.City;
        zip = match.Zip;

        ///form responding second location & transport
        $("#postalcode").val(zip);
        $("#transportationapartment").val(apt);
        $("#transportationbuilding").val(building);
        setTab('verifiedaddresstab');
        if ($("#transportstate").length == 1 && $("#transportstate").val() == "") {
            var $option = $("#transportstate").find("option[value='" + state + "']");
            $option.prop("selected", true);
            if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
                $("#transportstate").formSelect();
            }
            else {
                $("#transportstate").material_select();
            }
        }

        $("#Address").val(addrStr);
        $("#building").val(building);
        $("#apartment").val(apt);
        if ($("#State").length == 1 && $("#State").val() == "") {
            var $option = $("#State").find("option[value='" + state + "']");
            $option.prop("selected", true);
            if (typeof M != "undefined" && typeof (M.updateTextFields) == typeof (Function)) {
                $("#State").formSelect();
            }
            else {
                $("#State").material_select();
            }
        }
        $("#City").val(city);
        $("#Zip").val(zip);
        $("#postal_code").val(zip);
        //check needed to stop setting location only when on responding second location forms.  Location is used differently across forms
        if ($("#SelectedAddressTab").length < 1) {
            setLocation('verifiedlocation');
        }
        $("#geovalid_address_selected").show();
        $("#verifiedLocationsList").find("tr").removeClass("selected");
        $(this).addClass("selected");
        $("#SelectedAddressIndex").val($(this).data("index"));

        if (typeof (LocationSelected) == typeof (Function)) {
            // notify form location selected
            LocationSelected(match.Name);
        }
    }
}

//Select Location From Map
function GetLocationFromMap() {
    var formValues = GetFormData();

    if (CefSharpEnabled == true) {//WPF implementation
        ClientJsObject.getLocationFromMap(formValues);
    }
    else {//Xamarin implementation
        var param = {
            _method: 'getLocationFromMap',
            _formValues: formValues
        }
        invokeCSharp(JSON.stringify(param));
    }
}

function setLocation(value) {
    $("#location").val(value);
}

function setLocationType(value) {
    $("#locationtype").val(value);
}

function setTab(value) {

    // if the address tab is verified then the value should be verifiedaddresstab
    if (value == "addresstab" && $("#geovalid_address_selected").css("display") != "none") {
        value = "verifiedaddresstab";
    }

    $("#SelectedAddressTab").val(value);

    if (typeof (AfterTabChanged) == typeof (Function)) {
        // notify for that tab has changed
        AfterTabChanged(value);
    }
}

function ValidateLocationForms(selectedaddresstab, location, address, tablesearch) {

    var ret = false;

    if (selectedaddresstab == null) {
        selectedaddresstab = $("#SelectedAddressTab");
    }

    if (location == null) {
        location = $("#location")
        location[0].setCustomValidity("");
    }
    else {
        location[0].setCustomValidity("");
    }

    if (address == null) {
        address = $("#Address")
        address[0].setCustomValidity("");
    }
    else {
        address[0].setCustomValidity("");
    }

    switch (selectedaddresstab.val()) {
        case "locationtab":
            {
                if (location.val() != "") {
                    ret = true;
                }
                else {
                    $(".table-validate-required").show()
                }
            }
            break;
        case "addresstab":
            {
                if (address.val() != "") {
                    ret = true;
                }
                else {
                    address[0].setCustomValidity("Required Field");
                    address.addClass("input-validate-required");
                }
                break;
            }
            break;
        case "verifiedaddresstab":
            var selectedIndex = $("#SelectedAddressIndex").val();
            if (selectedIndex >= 0) {
                ret = true;
            }
            break;
    }

    return ret;
}

//defaults to the basic OnSite Form
function ValidateLocationInformation(location, curlat, curlong, sellat, sellong, address) {

    var ret = false;

    if (location == null) {
        location = $("#location");
    }

    if (curlat == null) {
        if ($("#curlat").length > 0) {
            curlat = $("#curlat");
            curlong = $("#curlong");
        }
    }

    if (sellat == null) {
        if ($("#sellat").length > 0) {
            sellat = $("#sellat");
            sellong = $("#sellong");
            sellat[0].setCustomValidity("");
            sellong[0].setCustomValidity("");
        }
    }
    else {
        sellat[0].setCustomValidity("");
        sellong[0].setCustomValidity("");
    }

    if (address == null) {
        address = $("#Address")
        address[0].setCustomValidity("");
    }
    else {
        address[0].setCustomValidity("");
    }

    switch (location.val()) {
        case "currentlocation":
            {
                var lat = Number(curlat.val());
                var long = Number(curlong.val());
                if (lat != 0 && long != 0 && !isNaN(lat) && !isNaN(long)) {
                    ret = true;
                }
                else {
                    curlat.addClass("current-lat-invalid");
                    curlong.addClass("current-long-invalid");
                }
            }
            break;
        case "selectedlocation":
            {
                var lat = Number(sellat.val());
                var long = Number(sellong.val());
                if (lat != 0 && long != 0 && !isNaN(lat) && !isNaN(long)) {
                    ret = true;
                }
                else {
                    sellat[0].setCustomValidity("Required Field");
                    sellong[0].setCustomValidity("Required Field");
                    sellat.addClass("input-validate-required");
                    sellong.addClass("input-validate-required");
                }
            }
            break;
        case "addresslocation":
            if (address.val() != "") {
                ret = true;
            }
            else {
                address[0].setCustomValidity("Required Field");
                address.addClass("input-validate-required");
            }
            break;
        case "verifiedlocation":
            var selectedIndex = $("#SelectedAddressIndex").val();
            if (selectedIndex >= 0) {
                ret = true;
            }
            break;
    }

    return ret;
}

function GeoButtonVisibility() {
    var address = $("#Address").val();
    if (address == "") {
        $(".Geo_Validate_Address").addClass("disabled");
    }
    else {
        $(".Geo_Validate_Address").removeClass("disabled");
    }
}

function ClearAddress() {

    $("#Address").val("");
    $("#City").val("");
    $("#postalcode").val("");
    $("#roomapt").val("");
    $("#building").val("");
    $("#State").val("");
}
