<?xml version="1.0" encoding="utf-8"?>
<MapConfig xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://tritech.com/InformMobile/SchemaMapConfigArcGISRT.xsd">
  <GeneralConstants>
    <RecenterTolerance>0.05</RecenterTolerance>
    <RotationTolerance>10</RotationTolerance>
    <MinAutoZoomMiles>0.25</MinAutoZoomMiles>
    <MaximumZoomResolutionRange>400</MaximumZoomResolutionRange>
    <MinimumZoomResolutionRange>1</MinimumZoomResolutionRange>
  </GeneralConstants>
  <DefaultExtent>
    <MapEnvelope XMin="2308789.7742964979" YMin="647402.589733194" XMax="3278407.8298520846" YMax="1197749.8119554338" />
  </DefaultExtent>
  <MapItemsInfoBoxSettings>
    <Opacity>0.9</Opacity>
  </MapItemsInfoBoxSettings>
  <ClickedLocationOutputFields>
    <OutputFieldName>Owner</OutputFieldName>
    <OutputFieldName>Land Value</OutputFieldName>
    <OutputFieldName>Building Value</OutputFieldName>
    <OutputFieldName>Total Value</OutputFieldName>
  </ClickedLocationOutputFields>
  <GeocodingPackage>
    <ReverseGeocodeToleranceInMeters>5000</ReverseGeocodeToleranceInMeters>
    <ReverseGeocodeStreetOutput>
      <Label>Addr</Label>
      <Pattern>[Street]</Pattern>
    </ReverseGeocodeStreetOutput>
    <ReverseGeocodeZoneOutput>
      <Label>City</Label>
      <Pattern>[City]</Pattern>
    </ReverseGeocodeZoneOutput>
    <ReverseGeocodeCrossStreetOutput>
      <Label>CrossSt</Label>
      <Pattern>[STREETNAME]</Pattern>
      <SearchSource MapPackageDisplayName="BaseMap" LayerIdentfierType="LayerName" Layer="CAD Streets" />
    </ReverseGeocodeCrossStreetOutput>
    <FindAddressSimpleOutput>[Addr_type]: [Match_addr]</FindAddressSimpleOutput>
    <FindAddressFromAddressNumberFieldName>AddNumFrom</FindAddressFromAddressNumberFieldName>
    <FindAddressToAddressNumberFieldName>AddNumTo</FindAddressToAddressNumberFieldName>
  </GeocodingPackage>
  <UserLayerVisibilityGroups InitialBaseMapForWhenNotTrackingLocation="PrimaryMobileDayMap" InitialBaseMapForWhenTrackingLocation="PrimaryMobileDayMap" InitialBaseMapForWhenNotTrackingLocationInNightMode="PrimaryMobileNightMap" InitialBaseMapForWhenTrackingLocationInNightMode="PrimaryMobileNightMap">
    <UserLayerVisibilityGroup Name="PrimaryMobileDayMap" IsBaseMap="true" />
    <UserLayerVisibilityGroup Name="PrimaryMobileNightMap" InitiallyVisible="false" IsBaseMap="true" DayNightModeDisplay="DisplayOnlyInNightMode" />
    <UserLayerVisibilityGroup Name="Aerial" IsBaseMap="false" DayNightModeDisplay="DisplayRegardless" />
    <UserLayerVisibilityGroup Name="Boundary" IsBaseMap="false" DayNightModeDisplay="DisplayRegardless" />
    <UserLayerVisibilityGroup Name="Trails" IsBaseMap="false" DayNightModeDisplay="DisplayRegardless" />
    <UserLayerVisibilityGroup Name="Transit" IsBaseMap="false" DayNightModeDisplay="DisplayRegardless" />
    <UserLayerVisibilityGroup Name="Lakes/Rivers" IsBaseMap="false" DayNightModeDisplay="DisplayRegardless" />
    <UserLayerVisibilityGroup Name="Parks" IsBaseMap="false" DayNightModeDisplay="DisplayRegardless" />
  </UserLayerVisibilityGroups>
  <DisplayLayers>
    <ArcGISLocalTiledLayer ID="d259a569-71a8-4821-922b-84bafdacfc2c" DisplayName="Aerial" Path="Map\Dakota_County_Aerial.tpk" UserLayerVisibilityGroupName="Aerial">
      <BaseMapsToDisplayWith>
        <BaseMap Name="PrimaryMobileNightMap" DayNightModeDisplay="DisplayOnlyInNightMode" />
        <BaseMap Name="PrimaryMobileDayMap" DayNightModeDisplay="DisplayOnlyInDayMode" />
      </BaseMapsToDisplayWith>
    </ArcGISLocalTiledLayer>
    <ArcGISLocalDynamicMapServiceLayer ID="10e1f8a4-5477-402b-8ca8-ff4754e08028" DisplayName="BaseMap" Path="Map\Mobile_Map.mpk" Opacity="0.75">
      <BaseMapsToDisplayWith>
        <BaseMap Name="PrimaryMobileDayMap" DayNightModeDisplay="DisplayOnlyInDayMode" />
        <BaseMap Name="PrimaryMobileNightMap" DayNightModeDisplay="DisplayOnlyInNightMode" />
      </BaseMapsToDisplayWith>
      <LayersWithOptions>
        <LayerOptions LayerIdentfierType="LayerName" Layer="Day Mode" DisplayName="Day Mode">
          <BaseMapsToDisplayWith>
            <BaseMap Name="PrimaryMobileDayMap" DayNightModeDisplay="DisplayOnlyInDayMode" />
          </BaseMapsToDisplayWith>
          <ClickInfoDisplayFields ignoreWhenNotVisible="false" />
        </LayerOptions>
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Mode" DisplayName="Night Mode">
          <BaseMapsToDisplayWith>
            <BaseMap Name="PrimaryMobileNightMap" DayNightModeDisplay="DisplayOnlyInNightMode" />
          </BaseMapsToDisplayWith>
          <ClickInfoDisplayFields ignoreWhenNotVisible="false" />
        </LayerOptions>
        <LayerOptions LayerIdentfierType="LayerName" Layer="Parcels" DisplayName="Address Info">
          <ClickInfoDisplayFields ignoreWhenNotVisible="false">
            <ClickInfoDisplayField clickedLocationDisplayLabel="Owner">
              <DisplayLabel>Owner</DisplayLabel>
              <WhenContentEmpty action="SuppressLabel" />
              <DisplayContent>
                <Field tblField="OWNER_NAME" />
              </DisplayContent>
            </ClickInfoDisplayField>
            <ClickInfoDisplayField clickedLocationDisplayLabel="Building Value">
              <DisplayLabel>Building Value</DisplayLabel>
              <WhenContentEmpty action="SuppressLabel" />
              <DisplayContent>
                <Field tblField="BLDGVAL" />
              </DisplayContent>
            </ClickInfoDisplayField>
            <ClickInfoDisplayField clickedLocationDisplayLabel="Land Value">
              <DisplayLabel>Land Value</DisplayLabel>
              <WhenContentEmpty action="SuppressLabel" />
              <DisplayContent>
                <Field tblField="LANDVAL" />
              </DisplayContent>
            </ClickInfoDisplayField>
            <ClickInfoDisplayField clickedLocationDisplayLabel="Total Value">
              <DisplayLabel>Total Value</DisplayLabel>
              <WhenContentEmpty action="SuppressLabel" />
              <DisplayContent>
                <Field tblField="TOTALVAL" />
              </DisplayContent>
            </ClickInfoDisplayField>
          </ClickInfoDisplayFields>
        </LayerOptions>
        <LayerOptions LayerIdentfierType="LayerName" Layer="Hydrants" DisplayName="Hydrants">
          <BaseMapsToDisplayWith>
            <BaseMap Name="PrimaryMobileDayMap" DayNightModeDisplay="DisplayOnlyInDayMode" />
            <BaseMap Name="PrimaryMobileNightMap" DayNightModeDisplay="DisplayOnlyInNightMode" />
          </BaseMapsToDisplayWith>
          <ClickInfoDisplayFields ignoreWhenNotVisible="false" />
        </LayerOptions>
        <LayerOptions LayerIdentfierType="LayerName" Layer="Counties" UserLayerVisibilityGroupName="Boundary" DisplayName="Counties" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="City Boundaries" UserLayerVisibilityGroupName="Boundary" DisplayName="City Boundaries" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Lakes" UserLayerVisibilityGroupName="Lakes/Rivers" DisplayName="Lakes" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Parks" UserLayerVisibilityGroupName="Parks" DisplayName="Parks" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Lakes Labels" UserLayerVisibilityGroupName="Lakes/Rivers" DisplayName="Lakes Labels" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Streams Labels" UserLayerVisibilityGroupName="Lakes/Rivers" DisplayName="Streams Labels" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Streams" UserLayerVisibilityGroupName="Lakes/Rivers" DisplayName="Streams" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Trails" UserLayerVisibilityGroupName="Trails" DisplayName="Trails" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Transitways" UserLayerVisibilityGroupName="Transit" DisplayName="Transitways" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Parks Labels" UserLayerVisibilityGroupName="Parks" DisplayName="Parks Labels" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night City Boundaries" UserLayerVisibilityGroupName="Boundary" DisplayName="Night City Boundaries" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Counties" UserLayerVisibilityGroupName="Boundary" DisplayName="Night Counties" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Parks" UserLayerVisibilityGroupName="Parks" DisplayName="Night Parks" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Parks Labels" UserLayerVisibilityGroupName="Parks" DisplayName="Night Parks Labels" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Transitways" UserLayerVisibilityGroupName="Transit" DisplayName="Night Transitways" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Trails" UserLayerVisibilityGroupName="Trails" DisplayName="Night Trails" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Lakes" UserLayerVisibilityGroupName="Lakes/Rivers" DisplayName="Night Lakes" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Lakes Labels" UserLayerVisibilityGroupName="Lakes/Rivers" DisplayName="Night Lakes Labels" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Streams" UserLayerVisibilityGroupName="Lakes/Rivers" DisplayName="Night Streams" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Streams Labels" UserLayerVisibilityGroupName="Lakes/Rivers" DisplayName="Night Streams Labels" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Building Outline" UserLayerVisibilityGroupName="Boundary" DisplayName="Building Outline" />
        <LayerOptions LayerIdentfierType="LayerName" Layer="Night Building Outline" UserLayerVisibilityGroupName="Boundary" DisplayName="Night Building Outline" />
      </LayersWithOptions>
    </ArcGISLocalDynamicMapServiceLayer>
  </DisplayLayers>
</MapConfig>