<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Reverse Phone Search Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Reverse Phone Search Results</H4>
						<P>
							<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
								<tr style="font-weight:bold;color:white;">
									<td>AreaCode</td>
									<td>Phone</td>
									<td>Address</td>
									<td>City</td>
									<td>State</td>
									<td>Zip</td>
								</tr>
	<xsl:apply-templates select="results/exemel/NewDataSet"/>						
							</table>
						</P>
					</TD>
				</TR>
			</TBODY>
    </TABLE>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet">
  <xsl:for-each select="Table"> 
    <TR style="color:windowtext;background-color:window;">
			<td><xsl:value-of select="AreaCode"/></td>
			<td><xsl:value-of select="PhoneNumber"/></td>
			<td><xsl:value-of select="Address"/></td>
			<td><xsl:value-of select="City"/></td>
			<td><xsl:value-of select="State"/></td>
			<td><xsl:value-of select="Zip"/></td>
    </TR>
  </xsl:for-each>
</xsl:template> 
</xsl:transform>


