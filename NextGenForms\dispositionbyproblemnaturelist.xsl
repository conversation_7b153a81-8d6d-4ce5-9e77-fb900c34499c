<?xml version="1.0"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
    <xsl:output method="html"/>
    <xsl:param name="listname"/>
    <xsl:param name="size"/>
    <xsl:param name="problemnature"/>
    <xsl:variable name="lowercase" select="'abcdefghijklmnopqrstuvwxyz'" />
    <xsl:variable name="uppercase" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'" />
    <xsl:template match="/">	
        <!-- Only the root item has an .XSL transform defined. -->
        <select id="{$listname}" name="{$listname}" size="{$size}" Class="Input" savelast="true">
        	<xsl:attribute name="onKeyPress">SearchListBox(this, window.event)</xsl:attribute>
            <!-- For a list, there is no blank item. --> 
            <!-- <xsl:element name="option" /> -->
          <xsl:for-each select="//ProblemNatures/ProblemNature[translate(@description, $lowercase, $uppercase)=translate($problemnature, $lowercase, $uppercase)]/Dispositions/Disposition">
                <xsl:element name="option">
                    <xsl:choose>
			<xsl:when test="@selected">
			    <xsl:attribute name="selected"><xsl:value-of select="true"/></xsl:attribute>
			</xsl:when>
		    </xsl:choose>
                <!-- The XML tag that will be used as the select value goes here. -->
                <xsl:attribute name="value"><xsl:value-of select="text()"/></xsl:attribute>
                <!-- The XML tag that will be used as the option text goes here. -->
                <xsl:value-of select="text()"/>
                </xsl:element>
            </xsl:for-each>		
        </select>
    </xsl:template>
</xsl:stylesheet>