﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Out Of Service</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>

    <script language="javascript">

        $(document).ready(function () {

            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    var NoneSelected = false;
                    if ($("#Reason").val().length < 1) {
                        NoneSelected = true;
                    }
                    var values = $(this).serialize();
                    if (NoneSelected) {
                        values += "Reason=";
                    }
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });
        });

        function AfterFillForm() {

            GenerateSelectBox("Reason", "Reason.xml", "genericselect.xsl", false, false, false, 8, true, false).then(function (result) {
                $("#reasonvals").prepend(result);
                $('select').formSelect();
                $("#reasonvals").find("input[type=text]").val("");
                SetMultiSelectBoxFromParameters($("#Reason"));
                $(".select-dropdown").focus();
            });

        }

    </script>
</head>
    <body>
        <div class="header">
            <div class="row">
                <div class="s12">
                    <div class="valign-wrapper">
                        <h5 style="margin-left: 20px;">OUT OF SERVICE</h5>
                    </div>
                </div>
            </div>
        </div>
        <form class="main" action="OutOfServiceQuery.aspx?queryfile=outofservice.qry" method="post" id="Form" name="Form">
            <div class="row" style="margin-top:20px">
                <div class="col s12">
                    <div class="row">
                        <div class="input-field col s12 m4" type="selectlist" id="reasonvals" name="reasonvals">
                            <label for="Reason">Reason</label>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </body>
</html>