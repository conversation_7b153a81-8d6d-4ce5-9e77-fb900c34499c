<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Capability Search Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="CapabilitySearchResult.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Capability Search</H4>
	<xsl:apply-templates select="results/errormessage"/>
						<p>
							<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:white;border-width:1px;border-style:None;">
								<tr style="font-weight:bold;color:black;">
									<td>Locn</td>
									<td>Vehicle</td>
									<td>Unit</td>
									<td>Resource</td>
									<td>Curr Loc</td>
									<td>Curr Status</td>
									<td>Capability</td>
								</tr>
							<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
							</table>
						<BR></BR>
						</p>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
    <xsl:variable name="recordid"><xsl:value-of select="ID"/></xsl:variable>
	<tr>
		<td>
			<xsl:element name="input">
				<xsl:attribute name="type">button</xsl:attribute>
				<xsl:attribute name="id">MapIt</xsl:attribute>
				<xsl:attribute name="executefunction">MapIt</xsl:attribute>
				<xsl:attribute name="parameters">Latitude=<xsl:apply-templates select="Current_Lat"/>&amp;Longitude=<xsl:apply-templates select="Current_Lon"/></xsl:attribute>
				<xsl:attribute name="value">Map It</xsl:attribute>
			</xsl:element>
		</td>
		<td>
			<xsl:value-of select="Veh"/>
		</td>
		<td>
			<xsl:value-of select="RadioName"/>
		</td>
		<td>
			<xsl:value-of select="Resrc"/>
		</td>
		<td>
			<xsl:value-of select="CurrLoc"/>
		</td>
		<td>
			<xsl:value-of select="Status"/>
		</td>
		<td>
			<xsl:value-of select="Cap"/>
		</td>
    </tr>

</xsl:template> 
<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
