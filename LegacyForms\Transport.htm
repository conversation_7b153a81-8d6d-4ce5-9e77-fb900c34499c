<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Transport</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Transport</H4>
						<form action="TransportQuery.aspx?queryfile=person.qry" method="post" id="TransportQuery"
							name="TransportQuery">
							<table ID="Table2">
								<tr>
									<td NOWRAP><b>Current odometer:</b>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" name="currentodometer" id="currentodometer" size="15" mandatory="true"></td>
								</tr>
								<!--
								<tr>
									<td colspan="2" NOWRAP><b>Destination:</b>
									&nbsp;&nbsp;&nbsp;<input type="text" name="destination" id="destination" mandatory="true"></td>
								</tr>
								-->
								<tr>
									<td colspan="2" NOWRAP><b>Number Transported:</b>
									<input type="text" name="PatientsTransported" id="PatientsTransported" size="15" value="1"  mandatory="true"></td>
								</tr>
								<tr>
									<td colspan="2" NOWRAP><b>Number Seen:</b>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" name="PatientsSeen" id="PatientsSeen" size="15" value="1"></td>
								</tr>
								<tr>
									<td NOWRAP><b>Is Juvenile:</b>
									<input type="checkbox" class="bigcheckbox" name="isjuvenile" id="isjuvenile"></td>
								</tr>
								<tr>
									<td><b>Gender:</b>
									&nbsp;&nbsp;&nbsp;<input type="radio" class="bigradio" name="gender" id="M" value="M">Male
										&nbsp;&nbsp; <input type="radio" class="bigradio" name="gender" id="F" value="F">Female
									</td>
								</tr>
								<tr>
									<td>&nbsp;</td>
								</tr>
								<tr>
									<td valign="top" colspan="2"><b>Transport Location:</b></td>
								</tr>
								<tr>
									<!-- Modified by Harshad on 6/23/05 for enahncement request   -->
									<td valign="top" colspan="2">
										<input type="radio" class="bigradio" name="loc" id="L" value="L">Location:
									</td>

									<!-- End Modifications by Harshad   -->
								</tr>
								<tr>
									<td colspan="2">
										<!-- Root item requires a SPAN element to contain it. -->
										<XML id="locationstyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="locationvals" name="locationvals">
											<XML id="locationsource" src="TransportLocation.xml"></XML>
										</SPAN>
									</td>
								</tr>
								<tr>
									<!-- Modified by Harshad on 6/23/05 for enahncement request   -->
									<td valign="top" colspan="2">
										<input type="radio" class="bigradio" name="loc" id="A" value="A">Address:
									</td>

									<!-- End Modifications by Harshad   -->
								</tr>
								<tr>
									<td colspan="2"><input type="text" name="Address" id="Address" size="35"></td>
								</tr>
								<tr>
									<td colspan="2">
										Apartment:&nbsp;<input type="text" name="transportationapartment" id="transportationapartment" size="7">
										&nbsp; Building:&nbsp;<input type="text" name="transportationbuilding" id="transportationbuilding" size="7">
									</td>
								</tr>
								<tr>
									<td colspan="2">
									<!--Modified by Harshad on 6/23/05 for enahncement request -->
										City:&nbsp;&nbsp;&nbsp;<input type="text" name="City" id="City" size="20">
									</td>
								</tr>
								<tr>
									<td colspan="2">State:
										<XML id="statestyle" src="genericcombo.xsl"></XML>
										<SPAN type="selectlist" id="statevals" name="statevals">
											<XML id="statesource" src="state.xml"></XML>
										</SPAN>
										&nbsp; ZIP:&nbsp;<input type="text" name="Zip" id="Zip" size="5">
									</td>
									<!--End modifications by Harshad -->
								</tr>
								<tr>
									<td>&nbsp;</td>
								</tr>
								<tr>
									<td valign="top" colspan="2"><b>Transport Protocol:</b></td>
								</tr>
								<tr>
									<td colspan="2">
										<XML id="protocolstyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="protocolvals" name="protocolvals">
											<XML id="protocolsource" src="transportprotocol.xml"></XML>
										</SPAN>
									</td>
								</tr>
								<tr>
									<td>&nbsp;</td>
								</tr>
								<tr>
								<tr>
									<td valign="top" colspan="2"><b>Transport Priority:</b></td>
								</tr>
									<td colspan="2">
										<XML id="prioritystyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="priorityvals" name="priorityvals">
											<XML id="prioritysource" src="transportpriority.xml"></XML>
										</SPAN>
									</td>
				</TR>
				<tr>
					<td>&nbsp;</td>
				</tr>
				<!--
				<tr>
					<td valign="top"><b>Transport Mode:</b></td>
				</tr>
				<tr>
					<td width="300">
						<XML id="transmodestyle" src="genericlist.xsl"></XML>
						<SPAN type="selectlist" id="transmodevals" name="transmodevals">
							<XML id="transmodesource" src="transportmode.xml"></XML>
						</SPAN>
					</td>
				</tr>
				<tr>
					<td>&nbsp;</td>
				</tr>
				-->
				<tr>
					<td valign="top"><b>Comments:</b></td>
				</tr>
				<tr>
					<td colspan="2"><textarea style="width:500px;" name="comments" id="comments" rows="8" onkeyup="CheckTextAreaLength(this.form.comments,200);"></textarea></td>
				</tr>
		</TABLE>
		<br>
							<br>
		<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
		<!-- Modified by Harshad on 6/23/05 for enahncement request -->
		<!-- Hidden button to call sub OnAfterFormfill routine to pre select the Address/location radio  -->
		<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
		<!--End modifications by Harshad -->
		<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
		</form>
		</TD>
		</TR>
		</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">

		// var originalHTML;

		function window.onload()
		{
		    PrepareValidation(TransportQuery);
		    TransportQuery.currentodometer.focus();
			locationvals.innerHTML = GenerateSelectBox("location", locationsource, locationstyle, false, false, false, 8, false, false);
		    // Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
		    statevals.innerHTML = GenerateSelectBox("state", statesource, statestyle, true, false, false, 1, false, false);
			protocolvals.innerHTML = GenerateSelectBox("transportationprotocol", protocolsource, protocolstyle, true, false, true, 4, false, false);
			priorityvals.innerHTML = GenerateSelectBox("transportationpriority", prioritysource, prioritystyle, true, false, true, 4, false, false);

		    // originalHTML = TransportQuery.removegroup.innerHTML;
		}
<!--Modified by Harshad on 6/23/05 for enahncement request -->
<!--Function call to determine pre selection of Location/Address radio -->
		function OnAfterFormFill()
		{

			var sLocation = "";
			if (TransportQuery.location.selectedIndex != -1)
			{

				sLocation = TransportQuery.location.options[TransportQuery.location.selectedIndex].text;
			}

			if (TransportQuery.Address.value.length > 0)
			{
				//Check Address radio
				TransportQuery.A.checked = true;
			}
			else
			{
				//Check Location Radio
				TransportQuery.L.checked = true;
			}

		}
<!--End Modifications by Harshad -->


		function window.validatepage()
		{
            if (TransportQuery.PatientsTransported.value > TransportQuery.PatientsSeen.value)
            {
				            alert ("Number of victims seen is less than the number transported, please enter valid number.");
				            TransportQuery.PatientsSeen.focus();
				            return;
            }
			var sOdometer = TransportQuery.currentodometer.value;
			if(isNaN(sOdometer))
			{
				alert ("Please enter a valid number for odometer");
				TransportQuery.currentodometer.focus();
				return;
			}

			<!-- Determine the selection. If location radio is selected user must select location value	-->
			<!-- If Address radio is selected, user must enter the Address -->
			var sLoc = "";
			if (TransportQuery.L.checked)
			{
				if (TransportQuery.location.selectedIndex != -1)
				{
					sLoc = TransportQuery.location.options[TransportQuery.location.selectedIndex].text;
				}

				if (sLoc == "")
				{
					TransportQuery.location.focus();
					alert ("Please select a location");
				}
				else
				{
					<!-- As Location is selected, making Address field null -->
					TransportQuery.Address.value = "";
					TransportQuery.Submit.click();
					return;
				}

			}

			var sAddress = "";
			if (TransportQuery.A.checked)
			{
				sAddress = TransportQuery.Address.value;

				if (sAddress == "")
				{
					TransportQuery.Address.focus();
					alert ("Please enter an address");
				}
				else
				{
					<!-- As Address is selected, making Location field null -->
					TransportQuery.location.selectedIndex = -1;
					TransportQuery.Submit.click();
					return;
				}

			}
				<!--End Modifications by Harshad	-->
		}
	</SCRIPT>
</HTML>
