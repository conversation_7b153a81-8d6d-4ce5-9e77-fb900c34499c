{"MaximumCommentLengthInCharacters": 2000, "AddComments": true, "CommentsConfidential": true, "DisplayTime": true, "DisplayAuthor": true, "AllowSelfDispatchToCallType": 2, "IncidentDetailsScreenConfiguration": {"Tabs": [{"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Table, InformMobile", "TableConfiguration": {"Columns": [{"Type": 5, "HeaderText": "Unit Name", "SortMemberPath": "UnitName", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "UnitName", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true, "IsEnabled": true}, {"Type": 5, "HeaderText": "Unit Alias", "SortMemberPath": "UnitAlias", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "UnitAlias", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true}, {"Type": 5, "HeaderText": "Vehicle Name", "SortMemberPath": "VehicleName", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "VehicleName", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true}, {"HeaderText": "Personnel Count", "SortMemberPath": "PersonnelCount", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "PersonnelCount", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true}, {"Type": 8, "HeaderText": "Status", "SortMemberPath": "CurrentStatus", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CurrentStatus", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Time Status Changed", "SortMemberPath": "TimeStatusChanged", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "TimeStatusChanged", "Format": "MM/dd HH:mm:ss", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"Type": 6, "HeaderText": "Location", "SortMemberPath": "Location", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Location", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true, "IsEnabled": true}, {"Type": 7, "HeaderText": "Destination", "SortMemberPath": "Destination", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Destination", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Current ETA", "SortMemberPath": "CurrentETA", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CurrentETA", "Format": "HH:mm:ss", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Unit Response #", "SortMemberPath": "ResponseNumber", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ResponseNumber", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Unit Response Priority", "SortMemberPath": "ResponsePriority", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ResponsePriority", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Meets Criteria", "SortMemberPath": "MeetsCriteria", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "MeetsCriteria", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Unit Responsibility", "SortMemberPath": "UnitResponsibility", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "UnitResponsibility", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}], "IsEnabled": true}, "TabDisplayName": "UNITS", "SourcePropertyPrefix": "Incident.AssignedUnitType", "IsEnabled": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Table, InformMobile", "TableConfiguration": {"Columns": [{"HeaderText": "Date", "SortMemberPath": "Timestamp", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Timestamp", "Format": "M/d/yyyy", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"Type": 4, "HeaderText": "Description", "SortMemberPath": "Description", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Description", "FallbackValue": ""}, "Width": "4*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Type", "SortMemberPath": "Type", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Type", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Size", "SortMemberPath": "Size", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Size", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Copy Progress State", "SortMemberPath": "CopyProgressState", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CopyProgressState", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "ID", "SortMemberPath": "ID", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ID", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Incident ID", "SortMemberPath": "IncidentID", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "IncidentID", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "<PERSON><PERSON>", "SortMemberPath": "PercentCopied", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "PercentCopied", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}], "IsEnabled": true}, "TabDisplayName": "ATTACH", "SourcePropertyPrefix": "Incident.Attachment", "IsEnabled": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Table, InformMobile", "TableConfiguration": {"Columns": [{"HeaderText": "Chemical", "SortMemberPath": "ChemicalName", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ChemicalName", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Location", "SortMemberPath": "StorageLocation", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "StorageLocation", "FallbackValue": ""}, "Width": "4*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Quantity", "SortMemberPath": "Quantity", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Quantity", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Notify Distance", "SortMemberPath": "NotifyDistance", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "NotifyDistance", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Chemical Guide", "SortMemberPath": "ChemicalGuide", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ChemicalGuide", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Chemical ID", "SortMemberPath": "ChemicalID", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ChemicalID", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}], "IsEnabled": true}, "TabDisplayName": "HAZMAT", "SourcePropertyPrefix": "Incident.Hazards", "ChangeTabStyleToAlertIfNonEmpty": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Table, InformMobile", "TableConfiguration": {"Columns": [{"Type": 2, "HeaderText": "Date", "SortMemberPath": "Timestamp", "DefaultSortDirection": 1, "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Timestamp", "Format": "M/d/yyyy", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Time", "SortMemberPath": "Timestamp.TimeOfDay", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Timestamp", "Format": "HH:mm:sss", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Problem", "SortMemberPath": "ProblemNature", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ProblemNature", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Disposition", "SortMemberPath": "CallDisp", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CallDisp", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Type", "SortMemberPath": "MatchType", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "MatchType", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Additional Info", "SortMemberPath": "AdditionalInfo", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "AdditionalInfo", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true}, {"HeaderText": "Location", "SortMemberPath": "Location", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Location", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true}, {"HeaderText": "Distance", "SortMemberPath": "Distance", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Distance", "FallbackValue": ""}, "Width": "Auto", "CanUserSort": true}, {"HeaderText": "Agency", "SortMemberPath": "Agency", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Agency", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}], "DefaultColumnSortOrder": [0], "IsEnabled": true}, "TabDisplayName": "PRIORS", "SourcePropertyPrefix": "Incident.PremiseHistory.Incident", "IsEnabled": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_List, InformMobile", "ListViewItems": [{"ColumnWidth": "*", "SourcePropertyInfo": {"Path": "Note", "FallbackValue": ""}, "Margin": "16,8,16,8", "IsEnabled": true}, {"ColumnWidth": "120", "SourcePropertyInfo": {"Path": "Type", "FallbackValue": ""}, "Margin": "8,8,8,8", "IsEnabled": true}, {"ColumnWidth": "100", "SourcePropertyInfo": {"Path": "Priority", "FallbackValue": ""}, "Margin": "8,8,8,8", "IsEnabled": true}, {"ColumnWidth": "100", "SourcePropertyInfo": {"Path": "Category", "FallbackValue": ""}, "Margin": "8,8,8,8", "IsEnabled": true}], "TabDisplayName": "CAUTION", "SourcePropertyPrefix": "Incident.CautionNotesCollection", "ChangeTabStyleToAlertIfNonEmpty": true, "IsEnabled": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_ReadOnlyInput, InformMobile", "ReadOnlyInputs": [{"Label": "Duplicate Incident Count", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "DuplicateIncidentCount", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Location", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Caller_LocationName", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Address of Occ Resp Area", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "AddressOccurrence.ResponseArea", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Agency", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.agency", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Alarm Level", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Alarm Level", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Alternate TAC Channel", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Alternate_TAC_Channel", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Base Response Number", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Base_Response_Number", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Battalion", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Battalion", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Building", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Building", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Call Back Phone Ext", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.CallBackPhoneExt", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Address", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Building", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Caller_Building", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Name", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "<PERSON><PERSON><PERSON><PERSON>", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Case Number", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CaseNumber", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Phone Number", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CallerPhoneNumberAndExtensionCombined", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "Type": 4, "VersionNumber": 1}, {"Label": "Cross Street", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CrossStreet", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Call Taker", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.CallTaking_Performed_By", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Caller Type", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Caller_Type", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Command Channel", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Command_Channel", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "County", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.County", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Determinant", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Determinant", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Division", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.division", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Fire Box", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Fire Box", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Incident ID", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "IncidentId", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Incident Number", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "IncidentNumber", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Incident Status", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "IncidentStatus", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Incident Type", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "IncidentType", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Juris<PERSON>", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.jurisdiction", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Latitude", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Latitude", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Longitude", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Longitude", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Location Info", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.LocationInfo", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Map", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Map Reference", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Method Call Received", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.MethodOfCallRcvd", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Patient First Name", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Patient First Name", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Patient Last Name", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "<PERSON>.Patient Last Name", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Pick Up Info", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.PickUpAddressInfo", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Preplan", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Preplan", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Priority", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Priority", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Primary Unit", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Primary Unit", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Tac Channel", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Primary_TAC_Channel", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Problem Code", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.ProblemCode", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Problem Nature", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ProblemNature", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Response Area", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Response_Area", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Sector", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Sector", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Stacked Unit", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "StackedUnit", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Street ID", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "StreetID", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Date", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Timestamp", "Format": "M/dd/yyyy", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Assign Time", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Time Call Assigned", "Format": "HH:mm:ss", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Time Call Entered Queue", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Time_CallEnteredQueue", "Format": "M/dd/yyyy HH:mm:ss", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Time First Unit Assigned", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Time_First_Unit_Assigned", "Format": "M/dd/yyyy HH:mm:ss", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Time First Unit Enroute", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Time_First_Unit_Enroute", "Format": "M/dd/yyyy HH:mm:ss", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Time First Unit Arrived", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Time_First_Unit_Arrived", "Format": "M/dd/yyyy HH:mm:ss", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Time First Call Taking Key Stroke", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Time_FirstCallTakingKeyStroke", "Format": "M/dd/yyyy HH:mm:ss", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Phone Number 2", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CallerPhoneNumber2", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "Type": 4, "VersionNumber": 1}, {"Label": "UDF 1", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF1", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 2", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF2", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 3", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF3", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 4", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF4", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 5", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF5", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 6", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF6", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 7", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF7", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 8", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF8", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 9", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF9", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 10", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF10", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 11", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF11", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 12", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF12", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 13", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF13", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 14", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF14", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 15", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF15", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 16", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF16", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 17", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF17", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 18", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF18", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 19", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF19", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 20", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF20", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 21", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF21", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "UDF 22", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.UDF22", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}], "TabDisplayName": "INFO", "SourcePropertyPrefix": "Incident.", "IsEnabled": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_ReadOnlyInput, InformMobile", "ReadOnlyInputs": [{"Label": "Caller Location", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Caller_LocationName", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Building", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Caller_Building", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Name", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "<PERSON><PERSON><PERSON><PERSON>", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Type", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Caller_Type", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Phone Number", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CallerPhoneNumberAndExtensionCombined", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "Type": 4, "IsEnabled": true, "VersionNumber": 1}, {"Label": "Address", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Call Taker", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.CallTaking_Performed_By", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Pickup Info", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.PickUpAddressInfo", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Caller Phone Number 2", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CallerPhoneNumber2", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "Type": 4, "VersionNumber": 1}], "TabDisplayName": "CALLER", "SourcePropertyPrefix": "Incident.", "IsEnabled": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Table, InformMobile", "TabDisplayName": "PREMISE"}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Table, InformMobile", "TableConfiguration": {"Columns": [{"Type": 3, "HeaderText": "Address", "SortMemberPath": "Address", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.MultiSourcePropertyConfiguration, InformMobile", "Paths": ["Name", "Address", "City", "State", "Zip", "Apartment", "Building"], "Separators": ["", " ", " ", ", ", " ", " Apt. ", ", Bld. "], "FallbackValue": ""}, "Width": "5*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Unit", "SortMemberPath": "Unit", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Unit", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Cross Street", "SortMemberPath": "CrossStreet", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CrossStreet", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}], "IsEnabled": true}, "TabDisplayName": "2ND LOC", "SourcePropertyPrefix": "Incident.MultiAndSecondaryLocations", "IsEnabled": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_ReadOnlyInput, InformMobile", "ReadOnlyInputs": [{"Label": "Patient Name", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.MultiSourcePropertyConfiguration, InformMobile", "Paths": ["Fields.Patient First Name", "<PERSON>.Patient Last Name"], "Separators": ["", " "], "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Address", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.MultiSourcePropertyConfiguration, InformMobile", "Paths": ["Fields.Transport To Location", "Fields.Transport To Address", "Fields.Transport To City", "Fields.Transport To State", "Fields.Transport To Zip", "Fields.Transport To Apartment", "Fields.Transport To Building"], "Separators": ["", "\r\n", "\r\n", ", ", " ", " Apt. ", ", Bld. "], "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Protocol", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Transport Protocol", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, {"Label": "Odometer At Scene", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Odometer At Scene", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Odometer At Destination", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Odometer At Destination", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Transport Mileage", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Transport Mileage", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Transport Priority", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Transport Priority", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Unit", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Unit", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Depart Scene Time", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Depart Scene Time", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "At Destination Time", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.At Destination Time", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}, {"Label": "Available Time", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Available Time", "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "VersionNumber": 1}], "TabDisplayName": "TRNSPRT", "SourcePropertyPrefix": "Incident.", "IsEnabled": true}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_ReadOnlyInput, InformMobile", "ReadOnlyInputs": [{"Label": "Closest to the incident location", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "HydrantClosestToIncident", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "Type": 3, "IsEnabled": true, "VersionNumber": 1}, {"Label": "Amount within a XX mile radius", "LabelFontSize": 13.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "HydrantCount", "NotifyOnTargetUpdated": true, "FallbackValue": ""}, "ValueFontSize": 16.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}], "TabDisplayName": "HYDRANT", "SourcePropertyPrefix": "Incident."}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Table, InformMobile", "TableConfiguration": {"Columns": [{"HeaderText": "Start Date", "SortMemberPath": "StartDate", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "StartDate", "Format": "M/d/yyyy", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "End Date", "SortMemberPath": "EndDate", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "EndDate", "Format": "M/d/yyyy", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Permit #", "SortMemberPath": "PermitNumber", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "PermitNumber", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Permit Status", "SortMemberPath": "PermitStatus", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "PermitStatus", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Permit Type", "SortMemberPath": "Type", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Type", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Comments", "SortMemberPath": "Comments", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Comments", "FallbackValue": ""}, "Width": "3*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Address", "SortMemberPath": "Address", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Address", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true}, {"HeaderText": "Location Name", "SortMemberPath": "LocationName", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "LocationName", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true}, {"HeaderText": "Building", "SortMemberPath": "Building", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Building", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Apartment", "SortMemberPath": "Apartment", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Apartment", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "City", "SortMemberPath": "City", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "City", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "County", "SortMemberPath": "County", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "County", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "State", "SortMemberPath": "State", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "State", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Zip", "SortMemberPath": "Zip", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Zip", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Primary Contact", "SortMemberPath": "PrimaryContact", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "PrimaryContact", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Primary Contact Phone 1", "SortMemberPath": "PrimaryContactPhone1", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "PrimaryContactPhone1", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Primary Contact Phone 2", "SortMemberPath": "PrimaryContactPhone2", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "PrimaryContactPhone2", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Alternate Contact", "SortMemberPath": "AlternateContact", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "AlternateContact", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Alternate Contact Phone 1", "SortMemberPath": "AlternateContactPhone1", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "AlternateContactPhone1", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Alternate Contact Phone 2", "SortMemberPath": "AlternateContactPhone2", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "AlternateContactPhone2", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Alarm Company Name", "SortMemberPath": "AlarmCompanyName", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "AlarmCompanyName", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Alarm Company Phone", "SortMemberPath": "AlarmCompanyPhone", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "AlarmCompanyPhone", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Distance", "SortMemberPath": "Distance", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Distance", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}], "IsEnabled": true}, "TabDisplayName": "PERMIT", "SourcePropertyPrefix": "Incident.Permit"}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Tags, InformMobile", "TabDisplayName": "TAGS", "SourcePropertyPrefix": "Incident.Tags"}, {"$type": "VSI.Mobile.Client.Configuration.IncidentTabItemConfiguration_Table, InformMobile", "TableConfiguration": {"Columns": [{"Type": 3, "HeaderText": "Address", "SortMemberPath": "Address", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Address", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true}, {"Type": 3, "HeaderText": "Address (Building and Apartment)", "SortMemberPath": "Address", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.MultiSourcePropertyConfiguration, InformMobile", "Paths": ["Address", "Apartment", "Building"], "Separators": ["", " Apt. ", ", Bld. "], "FallbackValue": ""}, "Width": "2*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Agency", "SortMemberPath": "Agency", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Agency", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "City", "SortMemberPath": "City", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "City", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Date", "SortMemberPath": "Timestamp", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Timestamp", "Format": "M/d/yyyy", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Disposition", "SortMemberPath": "Disposition", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Disposition", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Incident ID", "SortMemberPath": "IncidentIdShortened", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "IncidentIdShortened", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Incident Number", "SortMemberPath": "IncidentNumber", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "IncidentNumber", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Incident Type", "SortMemberPath": "IncidentType", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "IncidentType", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"Type": 6, "HeaderText": "Location Name", "SortMemberPath": "LocationName", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "LocationName", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Priority", "SortMemberPath": "Priority", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Priority", "FallbackValue": ""}, "Width": "*", "CanUserSort": true}, {"HeaderText": "Problem Nature", "SortMemberPath": "ProblemNature", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "ProblemNature", "FallbackValue": ""}, "Width": "2*", "CanUserSort": true, "IsEnabled": true}, {"HeaderText": "Time", "SortMemberPath": "Timestamp.TimeOfDay", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Timestamp", "Format": "HH:mm:sss", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}, {"Type": 9, "HeaderText": "Units", "SortMemberPath": "UnitName", "SourceBinding": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "UnitName", "FallbackValue": ""}, "Width": "*", "CanUserSort": true, "IsEnabled": true}], "IsEnabled": true}, "TabDisplayName": "LINKED INC", "SourcePropertyPrefix": "Incident.LinkedIncidents", "IsEnabled": true}], "IncidentDetailsButtons": {"Buttons": [{"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 72, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 54, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 55, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 57, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 56, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 53, "MobileAction": 3}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 70, "MobileAction": 3}], "NumberOfButtonsOnIncidentDetails": 7, "IsEnabled": true}, "AutomaticallyOpenTabItem": true, "DefaultOpenTabIndex": 5, "MaxCharacterLengthForCommentsShownInList": 200, "CommentDateTimeStringFormat": "H:mm:ss", "IncidentSummaryInfoConfiguration": {"Rows": ["90*", "90*"], "Columns": ["352*", "336*", "253*"], "InfoItems": [{"IsAddressField": true, "ExpandDirection": 8, "ReadOnlyInput": {"Label": "Address", "LabelFontSize": 14.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.MultiSourcePropertyConfiguration, InformMobile", "Paths": ["LocationName", "Address", "City", "Apartment", "Fields.Building"], "Separators": ["", "\r\n", "\r\n", "\r\nApt. ", ", Bld. "], "FallbackValue": ""}, "ValueFontSize": 26.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, "RowSpan": 2, "ColumnSpan": 1, "Border": "0,1,1,1", "IsEnabled": true}, {"ExpandDirection": 8, "ReadOnlyInput": {"Label": "Cross St. ", "LabelFontSize": 14.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CrossStreet", "FallbackValue": ""}, "ValueFontSize": 21.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, "RowSpan": 1, "Column": 1, "ColumnSpan": 2, "Border": "0,1,0,1", "IsEnabled": true}, {"ExpandDirection": 4, "ReadOnlyInput": {"Label": "Response Area", "LabelFontSize": 14.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Response_Area", "FallbackValue": ""}, "ValueFontSize": 21.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, "Row": 1, "RowSpan": 1, "Column": 1, "ColumnSpan": 1, "Border": "0,0,1,1", "IsEnabled": true}, {"ExpandDirection": 8, "ReadOnlyInput": {"Label": "Alarm Level", "LabelFontSize": 14.0, "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "Fields.Alarm Level", "FallbackValue": ""}, "ValueFontSize": 21.0, "ValueStringFormat": "", "ValueTextTrimming": 1, "Orientation": 1, "MaxWidth": "Infinity", "MaxHeight": "Infinity", "IsEnabled": true, "VersionNumber": 1}, "Row": 1, "RowSpan": 1, "Column": 2, "ColumnSpan": 1, "Border": "0,0,0,1", "IsEnabled": true}], "IsEnabled": true}, "IncidentOverviewField1": {"Label": "Caller Name", "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "<PERSON><PERSON><PERSON><PERSON>", "FallbackValue": ""}, "IsEnabled": true}, "IncidentOverviewField2": {"Label": "Caller Phone", "ValueSourcePropertyInfo": {"$type": "VSI.Mobile.Client.Configuration.SingleSourcePropertyConfiguration, InformMobile", "Path": "CallerPhoneNumberAndExtensionCombined", "FallbackValue": ""}, "IsEnabled": true}, "IsEnabled": true, "VersionNumber": 21}, "AllowAttachmentRequestForIncidentNotAssignedToMe": true, "IncidentAttachmentSizeLimitInBytes": 10485760, "AllowAddingAttachmentsToIncidentsNotAssignedToUnit": 2, "IncidentAttachmentUploadSizeLimitInBytes": 5000, "IsEnabled": true, "VersionNumber": 9}