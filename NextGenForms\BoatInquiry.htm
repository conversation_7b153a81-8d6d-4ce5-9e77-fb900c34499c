﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Boat Inquiry</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.min.css" />

    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />

    <script src="jquery.min.js"></script>
    <!-- Compiled and minified JavaScript -->
    <script src="materialize.min.js"></script>

    <script src="clientutilities.js"></script>

    <script language="javascript">
        $(document).ready(function () {
            //Generate select input for states
            GenerateSelectBox("State", "state.xml", "genericselectvalue.xsl", false, false, false, 1, false, false).then(function (result) {
                $("#statevals").prepend(result);
                $('select').material_select();
            });

            //handle form submition
            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true)
                {
                    if ($('#RCArea').is(":visible") && $('#RCAssociationRadio').is(':checked')) {
                        $('#RCAssociation').val(true);
                    }
                    else {
                        $('#RCAssociation').val(false);
                    }
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
               
                return false;

            });

            CanShowRecordsCheck().then(function (result) {
                if (!result) {
                    $("#RCArea").hide();
                }
            });

            //focus on registration id field
            $("#RegID").focus();
        });
    </script>

</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">BOAT INQUIRY</h5>
                </div>
            </div>
        </div>
    </div> 
    <form class="main" action="BoatInquiry.aspx?queryfile=BoatInquiry.qry" method="post" id="Form" name="Form">
        <div class="row" style="padding-top:20px">
            <div class="s6">
                <div class="input-field col s12 m4" name="Registration" id="Registration">
                    <input placeholder="" id="RegID" type="text" name="RegID" maxlength="8">
                    <label for="RegID" class="active">Registration #</label>
                </div>
            </div>
            <div class="s6">
                <div class="input-field col s12 m4" type="selectlist" id="statevals" name="statevals">
                    <label>State</label>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="s6">
                <div class="input-field col s12 m4" name="Hull" id="Hull">
                    <input placeholder="" id="HullNo" type="text" name="HullNo" maxlength="20">
                    <label for="HullNo" class="active">Hull #</label>
                </div>
            </div>
            <div class="s6">
                <div class="input-field col s12 m4" name="OAM" id="OAM">
                    <input placeholder="" id="OANNO" type="text" size="20" name="OANNO" maxlength="20">
                    <label for="OANNO" class="active">OAN #</label>
                </div>
            </div>
        </div>
        <div class="row" id="RCArea">
            <div class="row">
                <div class="col s4">
                    <label for="RCAssociation">Associate this Records Check with incident?</label>
                </div>
            </div>
            <div class="row" style="margin-left:-5px">
                <div class="col">
                    <input class="with-gap" name="group1" type="radio" id="RCAssociationRadio" checked />
                    <label for="RCAssociationRadio">Yes</label>
                </div>
                <div class="col">
                    <input class="with-gap" name="group1" type="radio" id="NotRCAssociationRadio" />
                    <label for="NotRCAssociationRadio">No</label>
                </div>
            </div>
        </div>

        <input type="hidden" name="RCAssociation" id="RCAssociation" value="false">

    </form>
    
</body>
</html>


