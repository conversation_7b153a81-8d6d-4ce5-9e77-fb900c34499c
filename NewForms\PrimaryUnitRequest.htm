<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Primary Unit Request</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Primary Unit Request</H4>
						<form action="PrimaryUnitRequestQuery.aspx?queryfile=primaryunitrequest.qry" method="post" id="PrimaryUnitRequestQuery" name="PrimaryUnitRequestQuery">
							<table ID="Table2">
								<tr>
									<td><b>Incident Number:</b></td>
									<td><span id="incidentnumber" name="incidentnumber" formvalue="true"></span>
									<input type="hidden" name="incidentid" id="incidentid"></td>
								</tr>
								<tr>
									<td valign="top"><b>Radio/ Unit Name:</b></td>
									<td>
<!--
										<XML id="radiostyle" src="configurationunits.xsl"></XML>										
										<SPAN type="selectlist" id="radiovals" name="radiovals">
											<XML id="radiosource" src="../Configuration_Units.xml"></XML>
										</SPAN>
-->
										<span id="unitname" name="unitname" formvalue="true">
									</td>
								</tr>
								<tr>
									<td>&nbsp;</td>
								</tr>
								<tr>
									<td>&nbsp;</td>
								</tr>
							</table>
							<input type="submit" name="Query" id="Query" value="Submit">
							<br>
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
		function window.onload()
		{
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			//radiovals.innerHTML = GenerateSelectBox("unitname", radiosource, radiostyle, true, false, false, 4, false, false);
			PrimaryUnitRequestQuery.Query.focus();
		}
	</SCRIPT>
</HTML>
