<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Client Setup</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD class="bottombordercell" align="middle" vAlign="top" width="100%" colSpan="3">
						<IMG src="logo.gif" border="0">
						<hr class="presentationdelimiter">
					</TD>
				</TR>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Client Setup</H4>
						<form action="ClientSetup.aspx?queryfile=ClientSetup.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td>Registration ID:</td>
									<td><input type="text" name="RegistrationID" id="RegistrationID"></td>
								</tr>
								<tr>
									<td>Mobile Server IP or Name:</td>
									<td><input type="text" name="MobileServerIP" id="MobileServerIP"></td>
								</tr>
								<tr>
									<td>S/W Server IP or Name:</td>
									<td><input type="text" name="SWServerIP" id="SWServerIP"></td>
								</tr>
								<tr>
									<td>Software Update Folder Name:</td>
									<td><input type="text" name="SWServerAgencyName" id="SWServerAgencyName"></td>
								</tr>
								<tr>
									<td>WLAN mask:</td>
									<td><input type="text" name="WLANMask" id="WLANMask"></td>
								</tr>
								<tr>
									<td colspan="2" style="font-size:15px">Use WLAN mask to force software updates to be done only over Wi-Fi.</td>
								</tr>
								<tr>
									<td colspan="2" style="font-size:15px">If WLAN mask is ANY, then software updates are done over any connected network.</td>
								</tr>
							</table>
							<br/>
							<span style="width:250px;"></span><input type="submit" name="Query" id="Query" value="Submit">
						</form>
					</TD>
				</TR>
				<TR>
					<TD class="topbordercell" vAlign="top" width="100%" colSpan="3">
						<hr class="presentationdelimiter">
						<IMG src="../forms_logo.gif" border="0">
						<br><BLOCKQUOTE>&copy; 2003-2018 TriTech Software Systems. All Rights Reserved</BLOCKQUOTE>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<SCRIPT language="javascript">
		function window.onload()
		{
			Form.RegistrationID.focus();
		}
	</SCRIPT>
</HTML>
