<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Radius Search Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>		
	</HEAD>
<xsl:element name="script">
	<xsl:attribute name="type">text/javascript</xsl:attribute>
	<xsl:attribute name="src">clientutilities.js</xsl:attribute>
	<xsl:text> </xsl:text>
</xsl:element>
	<BODY>
		<form action="RadiusSearchResult.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Radius Search Results</H4>
	<xsl:apply-templates select="results/errormessage"/>
							<p>
								<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
									<tr style="font-weight:bold;color:white;">
										<td>Distance (<script language="javascript"> DisplayDistanceUnitCaption(); </script>)</td>
										<td>Name/Code</td>
										<td>Address</td>
										<td>Detailed Info</td>
									</tr>
	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
								</table>
							</p>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>	
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
	<tr style="background-color:window;color:windowtext;">				
		<td><script language="javascript"> document.write(ConvertMilesToLocalizedDistance(<xsl:value-of select="Distance"/>)); </script></td>
		<xsl:variable name="locationname"><xsl:value-of select="Name"/></xsl:variable>
		<td>
			<xsl:if test="LocationType!=''">
				<xsl:value-of select="LocationType"/>:&#32;
			</xsl:if>
			<a href="GetPremiseDetails.aspx?location={$locationname}&#38;queryfile=GetPremiseDetails.qry"><xsl:value-of select="Name"/></a>
		</td>
		<td>
			<table>
				<tr><td>
					<xsl:element name="a">
						<xsl:attribute name="href">#</xsl:attribute>
						<xsl:attribute name="id">MapIt</xsl:attribute>
						<xsl:attribute name="executefunction">MapIt</xsl:attribute>
						<xsl:attribute name="parameters">Latitude=<xsl:value-of select="Latitude"/>&amp;Longitude=<xsl:value-of select="Longitude"/></xsl:attribute>
						<xsl:choose>
							<xsl:when test="Address!=''"><xsl:value-of select="Address"/></xsl:when>
							<xsl:otherwise>Map It</xsl:otherwise>
						</xsl:choose>
					</xsl:element>
				</td></tr>
				<xsl:if test="CrossStreet!=''">
					<tr><td>Cross:&#32;<xsl:value-of select="CrossStreet"/></td></tr>
				</xsl:if>
				<xsl:if test="Building!=''">
					<tr><td>Bldg:&#32;<xsl:value-of select="Building"/></td></tr>
				</xsl:if>
				<xsl:if test="Apartment!=''">
					<tr><td>Apt:&#32;<xsl:value-of select="Apartment"/></td></tr>
				</xsl:if>
				<xsl:if test="City!=''">
					<tr><td><xsl:value-of select="City"/></td></tr>
				</xsl:if>
				<xsl:if test="State!='' or Zip!=''">
					<tr><td><xsl:value-of select="State"/>&#32;<xsl:value-of select="Zip"/></td></tr>
				</xsl:if>
			</table>
		</td>
		<td><xsl:value-of select="AdditionalInfo"/></td>
	</tr>
</xsl:template> 

<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
