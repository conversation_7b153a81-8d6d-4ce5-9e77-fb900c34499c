<?xml version="1.0"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
    <xsl:output method="html"/>
    <xsl:param name="listname"/>
    <!-- <xsl:param name="forceselection"/> -->
    <xsl:template match="/">	
        <!-- Only the root item has an .XSL transform defined. -->
        <select size="1" id="{$listname}" name="{$listname}" Class="Input" savelast="true">
        	<xsl:attribute name="onKeyPress">SearchListBox(this, window.event)</xsl:attribute>
            <xsl:for-each select="//items/item">
                <xsl:element name="option">
                    <xsl:choose>
			<xsl:when test="@selected">
			    <xsl:attribute name="selected"><xsl:value-of select="true"/></xsl:attribute>
			</xsl:when>
		    </xsl:choose>
                <xsl:attribute name="value"><xsl:value-of select="value"/></xsl:attribute>
                <xsl:value-of select="description"/>
                </xsl:element>
            </xsl:for-each>		
        </select>
    </xsl:template>
</xsl:stylesheet>