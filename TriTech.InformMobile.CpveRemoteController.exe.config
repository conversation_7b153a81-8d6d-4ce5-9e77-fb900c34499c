<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="TriTech.InformMobile.CpveRemoteController.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <applicationSettings>
      <TriTech.InformMobile.CpveRemoteController.Properties.Settings>
        <setting name="CpveIP" serializeAs="String">
          <value>*************</value>
        </setting>
        <setting name="ShowUI" serializeAs="String">
          <value>True</value>
        </setting>
        <setting name="CpvePort" serializeAs="String">
          <value>9111</value>
        </setting>
        <setting name="MessageButtonCaption" serializeAs="String">
          <value>Messages</value>
        </setting>
      </TriTech.InformMobile.CpveRemoteController.Properties.Settings>
    </applicationSettings>
</configuration>