<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Incident Times Results</TITLE>
		<LINK href="normalstyle.css" type="text/css" rel="stylesheet"></LINK>
		<style>
			.Small { font-Family:arial; font-Size:11px; text-align:center; }
			.Comment { font-Family:arial; font-Size:15px; font-weight:bold; text-align:left; }

		</style>
	</HEAD>
	<BODY>
		<form action="IncidentTimesResult.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
							  <H4 align="center">Incident Times </H4>
						<xsl:apply-templates select="/results/errormessage"/>
						<p>
						  	<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
									<tr style="font-weight:bold;color:white;">
									<td>Incident# </td>
<!-- Not Needed for FIRE
									<td>Case#</td>
-->
									<td>Problem</td>
									<td>Disp</td>
									<td align="center" class="Small">Date</td>
									<td align="center" class="Small">Assigned</td>
									<td align="center" class="Small">EnRoute</td>
									<td align="center" class="Small">OnScene</td>
									<td align="center" class="Small">Clear</td>
									<td align="center">Options</td>
								</tr>
								<xsl:apply-templates select="/results/exemel/NewDataSet/Table"/>
							</table>
						<BR></BR>
						</p>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
<xsl:variable name="recordid"><xsl:value-of select="RMID"/></xsl:variable>

   <tr style="color:windowtext;background-color:window;">

	<xsl:choose>

	<xsl:when test="COMMENT = ''">

      <td class="IncidentNum"><a href="SingleIncidentQuery.aspx?ID={$recordid}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="INC_NUM"/></a></td>

<!-- Not Needed for FIRE
      <td class="CaseNum"><a href="SingleIncidentQuery.aspx?ID={$recordid}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="CASENUMBER"/></a></td>
-->

      <td><xsl:value-of select="PROBLEM"/></td>
      <td><xsl:value-of select="DISPOSITION"/></td>
      <td class="Small"><xsl:value-of select="STARTTIME"/></td>
      <td class="Small"><xsl:value-of select="ASSIGNED"/></td>
      <td class="Small"><xsl:value-of select="ENROUTE"/></td>
      <td class="Small"><xsl:value-of select="ARRIVED"/></td>
      <td class="Small"><xsl:value-of select="CLEARED"/></td>

<!-- Add additional options similar to the incident search. Add comment and request case number. -->

		<td>
			<table>
				<tr><td class="Small">
				<a href="$" id="AddIncidentComment" executefunction="OpenForm" parameters="FormName=addincidentcomment&#38;IncidentID={$recordid}&#38;IncidentNumber={INC_NUM}&#38;Problem={PROBLEM}&#38;Address={ADDRESS}">Add Incident Comment</a>
				</td></tr>

				<xsl:if test="AllowRequestCaseNumber='true'">
					<tr><td class="Small">
					<a href="RequestCaseNumber.aspx?IncidentID={$recordid}&#38;queryfile=RequestCaseNumber.qry" location="remote">Request Case Number</a>
					</td></tr>
				</xsl:if>
			</table>

	 	</td>
	</xsl:when>

	<xsl:otherwise>
			  <td colspan="10" class="Comment">[<xsl:value-of select="STARTTIME"/>] <xsl:value-of select="COMMENT"/></td>
	</xsl:otherwise>

	</xsl:choose>

    </tr>

</xsl:template> 
<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
