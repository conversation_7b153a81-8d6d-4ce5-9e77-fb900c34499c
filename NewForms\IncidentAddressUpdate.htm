<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
        <TITLE>Mobile Enterprise - Modify Incident Address</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
		<LINK href="GeoValidate.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Modify Incident Address</H4>
                        
						<div style="text-align:right;">
							<input style="display:inline-block;float:right;" type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
						</div>
						
						<form action="IncidentAddressUpdate.aspx?queryfile=IncidentAddressUpdate.qry" method="post" id="Form"
							name="Form">
							<table ID="Table2">

								<tr>
									<td>Incident Number:</td>
									<td><span id="incidentnumber" name="incidentnumber" formvalue="true"></td>
									<td><input type="hidden" name="incidentid" id="incidentid"></td>
								</tr>
							
								<tr>
									<td valign="top"><b>Location:</b></td>
								</tr>
								<tr>
									<td align="right">Current<input type="radio" class="bigradio" name="location" id="currentlocation" value="currentlocation" onclick="radioChange()" executefunction="ReverseGeoCode" parameters="Latitude=curlat&Longitude=curlong&Address=Address&City=City"></td>
									<td width="300"><span id="curlat" name="curlat" formvalue="true"></span></td>
								</tr>
								<tr>
									<td></td>
									<td width="300"><span id="curlong" name="curlong" formvalue="true"></span></td>
								</tr>
								<tr>
									<td align="right" width="185">Selected<input type="radio" class="bigradio" name="location" id="selectedlocation" value="selectedlocation" onclick="radioChange()" executefunction="ReverseGeoCode" parameters="Latitude=sellat&Longitude=sellong&Address=Address&City=City"></td>
									<td>Lat.&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="sellat" name="sellat" fillbutton="selectbutton" size="9">&nbsp;&nbsp;<INPUT type="image" align="absMiddle" enablefill="true" value="Select from map" alt="Select from map"
											src="map.gif" id="selectbutton" name="selectbutton" simulateclickcontrolid="selectedlocation"></td>
								</tr>
								<tr>
									<td></td>
									<td>Long.&nbsp;<input type="text" id="sellong" name="sellong" fillbutton="selectbutton" size="9"></td>
								</tr>
								<tr>
									<td align="right" valign="top">Address<input type="radio" class="bigradio" name="location" id="addresslocation" value="addresslocation" onclick="radioChange()"></td>
									<td>
<table id="innertable">
    <tr><td>Address:</td><td><input type="text" name="Address" id="Address"></td></tr>
    <tr><td>City:</td><td><input type="text" name="City" id="City"></td></tr>
    <tr><td>State:</td><td><XML id="statestyle" src="genericcombo.xsl"></XML><SPAN type="selectlist" id="statevals" name="statevals"><XML id="statesource" src="state.xml"></XML></SPAN></td></tr>
    <tr><td>Zip:</td><td><input type="text" name="postalcode" id="postal_code"></td></tr>
    <tr><td>Room/Apt:</td><td><input type="text" name="roomapt" id="apartment"></td></tr>
    <tr><td>Building:</td><td><input type="text" name="building" id="building"></td></tr>
    <tr><td>Location:</td><td><input type="text" name="locationname" id="location_name"></td></tr>
</table>
<table ID="Table3">
								<tr>
									<td>(location name is not for  </td>  
								</tr>
								<tr>	
									<td>geoverification)</td>
</table>
									</td>
								</tr>
								<!-- GeoValidation start -->
								<tr>
									<td align="right">
										<label for="verifiedlocation" style="display:inline-block;vertical-align:middle;">
											Verified<br>Location
										</label>
										<input disabled type="radio" class="bigradio" name="location" id="verifiedlocation" value="verifiedlocation" onclick="radioChange()">
									</td>
									<td align="left">
										<input type="button" name="verifyLocationButton"
											id="verifyLocationButton" value="Verify Location" onkeydown="verifyLocationButtonKeyDown(event, false)"
											onclick="verifyLocationButtonClicked(false)" executefunction="VerifyLocation" parameters="">
                                        &nbsp;
                                        <input disabled type="button" name="verifyLocationButtonMore"
                                               id="verifyLocationButtonMore" value="More..." onkeydown="verifyLocationButtonKeyDown(event, true)"
                                               onclick="verifyLocationButtonClicked(true)" parameters="">
                                    </td>
								</tr>
								<tr>
									<td colspan="2" nowrap>
										<div id="verifyLocationContainer">
											<ul id="verifiedLocationsList"></ul>
										</div>
                                        <label id="lblVerifiedLocationsListCount" name="lblVerifiedLocationsListCount">0 record/s found</label>
									</td>
								</tr>
								<!-- GeoValidation end -->
							</table>
							<br>
							<input type="hidden" name="Lat" id="Lat">
							<input type="hidden" name="Long" id="Long">
							<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
							<input type="hidden" name="VerifyLocationResults" id="VerifyLocationResults" value="">
							<input type="hidden" name="SelectedAddressIndex" id="SelectedAddressIndex" value="-1">
                            <input type="hidden" name="VerifyMoreN" id="VerifyMoreN" value="0">
                            <input type="hidden" name="VerifyMaxResultsReturned" id="VerifyMaxResultsReturned" value="0">
							<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY>
        </TABLE>

	<script src="clientutilities.js"></script>
	<script src="GeoValidate.js"></script>
	<script language="javascript">

	function window.onload()
	{
		Form.Address.focus();
		Form.addresslocation.checked = true;
		statevals.innerHTML = GenerateComboBox("state", statesource, statestyle);
	}

    function OnAfterFormFill()
    {
        populateListOfAddressResults();
    }

	function window.validatepage()
	{
		if (Form.currentlocation.checked)
		{
			if (curlat != null)
			{
				if (curlat.innerText == null)
					Form.Lat.value = '';
				else
					Form.Lat.value = curlat.innerText;
            }
			else
				Form.Lat.value = '';

			if (curlong != null)
			{
				if (curlong.innerText == null)
					Form.Long.value = '';
				else
					Form.Long.value = curlong.innerText;
			}
			else
				Form.Long.value = '';

			// Form.Address.value = '';
		}
		else if (Form.selectedlocation.checked)
		{
			Form.Lat.value = Form.sellat.value;
			Form.Long.value = Form.sellong.value;
			// Form.Address.value = '';
		}
		else if (Form.addresslocation.checked)
		{
			Form.Lat.value = '';
			Form.Long.value = '';
		}

		if ((Form.selectedlocation.checked || Form.currentlocation.checked) && ((Form.Lat.value == 0) ||(Form.Long.value == 0)))
		{
			alert ("Latitude/Longitude cannot be zero");
			//Set focus
			if (Form.Lat.value == 0 )
			{
				Form.sellat.focus();
			}
			else
			{
				Form.sellong.focus();
			}
		}
		else if ((Form.addresslocation.checked) && (Form.Address.value.length <= 0))
		{
			alert("Please enter a location code or address in the Address field.");
			Form.Address.focus();
		}
        // GeoValidation start
        else if ((Form.verifiedlocation.checked) && (Form.SelectedAddressIndex.value < 0))
        {
            alert("Please select a location from the list.");
            Form.verifiedlocation.focus();
        }
        // GeoValidation end
        else
        {
            // clear this out - no need to send this data back to Mobile Server
            clearHiddenAddressResults();

            Form.Submit.click();
        }
	}

	</script>
	</body>
</HTML>
