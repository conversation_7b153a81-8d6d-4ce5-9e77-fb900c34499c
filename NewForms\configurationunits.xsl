<?xml version="1.0"?>
<xsl:stylesheet xmlns:cb="http://tempuri.org/Configuration_Units_Schema.xsd" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
    <xsl:output method="html"/>
    <xsl:param name="listname"/>
    <xsl:param name="mandatory"/>
    <xsl:param name="parent"/>
    <xsl:param name="savelast"/>
    <xsl:param name="size"/>
    <xsl:param name="multiple"/>
    <xsl:param name="print"/>
    <xsl:template match="/">
    <!-- Only the root item has an .XSL transform defined. -->
        <xsl:element name="select">
        	<xsl:attribute name="id"><xsl:value-of select="$listname"/></xsl:attribute>
        	<xsl:attribute name="name"><xsl:value-of select="$listname"/></xsl:attribute>
        	<xsl:attribute name="size"><xsl:value-of select="$size"/></xsl:attribute>
        	<xsl:attribute name="Class">Input</xsl:attribute>
        	<xsl:attribute name="onKeyPress">SearchListBox(this, window.event)</xsl:attribute>
		<xsl:if test="$mandatory">
			<xsl:attribute name="style">background-color:lightblue;</xsl:attribute>
			<xsl:attribute name="mandatory">true</xsl:attribute>
		</xsl:if>
        	<xsl:if test="$multiple">
        	    <xsl:attribute name="MULTIPLE">MULTIPLE</xsl:attribute>
        	</xsl:if>
        	<xsl:if test="$savelast">
			<xsl:attribute name="savelast">true</xsl:attribute>
		</xsl:if>
        	<xsl:if test="$parent != ''">
			<xsl:attribute name="parent"><xsl:value-of select="$parent"/></xsl:attribute>
		</xsl:if>	
		<xsl:if test="not($mandatory)">
			<xsl:if test="not($multiple)">
            			<xsl:element name="option"></xsl:element>
			</xsl:if>
		</xsl:if>
        	<xsl:if test="$print">
        	    <xsl:attribute name="print">true</xsl:attribute>
        	</xsl:if>
            	<xsl:for-each select="//cb:units/cb:unit">
                	<xsl:element name="option">
                		<xsl:choose>
					<xsl:when test="@cb:selected">
			    		<xsl:attribute name="selected"><xsl:value-of select="true"/></xsl:attribute>
					</xsl:when>
		    		</xsl:choose>
                <!-- The XML tag that will be used as the select value goes here. -->
                <xsl:attribute name="value"><xsl:value-of select="value"/></xsl:attribute>
                <!-- The XML tag that will be used as the option text goes here. -->
                <xsl:value-of select="cb:id"/>
                </xsl:element>
            </xsl:for-each>
        </xsl:element>
<!--
	<xsl:if test="$mandatory">
		<xsl:element name="mandatory">&#32;&#32;&#42;</xsl:element>
	</xsl:if>
-->
    </xsl:template>
</xsl:stylesheet>