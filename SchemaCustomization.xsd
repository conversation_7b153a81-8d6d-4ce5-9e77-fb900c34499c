<?xml version="1.0" encoding="utf-8" ?>
<xs:schema id="SchemaCustomization" targetNamespace="http://tempuri.org/SchemaCustomization.xsd" elementFormDefault="qualified" xmlns="http://tempuri.org/SchemaCustomization.xsd" xmlns:mstns="http://tempuri.org/SchemaCustomization.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="CustomList">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Text" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="name" type="xs:string" />
							<xs:element name="text" type="xs:string" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="Font" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="name" type="xs:string" />
							<xs:element name="size" type="xs:int" />
							<xs:element name="fontname" type="xs:string" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="Flag" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="name" type="xs:string" />
							<xs:element name="flag" type="xs:boolean" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="Color" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="name" type="xs:string" />
							<xs:element name="Red" type="xs:int" />
							<xs:element name="Green" type="xs:int" />
							<xs:element name="Blue" type="xs:int" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="Int" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="name" type="xs:string" />
							<xs:element name="val" type="xs:int" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>