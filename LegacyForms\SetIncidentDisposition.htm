<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Set Incident Disposition</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
	<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
	    <TBODY>
	    <TR>
	        <TD vAlign="top">
	            <H4 align="center">Set Incident Disposition</H4>
	            <form action="SetIncidentDisposition.aspx?queryfile=SetIncidentDisposition.qry" method="post" id="Form"
	                  name="Form">
	                <table ID="Table2">
	                    <tr>
	                        <td><b>Incident Number:</b></td>
	                        <td>
	                            <span id="incidentnumber" name="incidentnumber" formvalue="true"></span>
	                            <input type="hidden" name="incidentid" id="incidentid">
	                        </td>
	                    </tr>
	                    <tr>
	                        <td><input type="hidden" name="problemnature" id="problemnature"></td>
	                    </tr>
	                    <tr>
	                        <td><b>Response Disposition:</b></td>
	                    </tr>
	                    <tr>
	                        <td>
	                            <!-- Root item requires a SPAN element to contain it. -->                                    
	                            <SPAN type="selectlist" id="dispositionvals" name="dispositionvals">                     
										</SPAN>
	                        </td>
	                    </tr>
	                    <tr>
	                        <td>&nbsp;</td>
	                    </tr>
	                    <tr>
	                        <td><b>Comments:</b></td>
	                    </tr>
	                    <tr>
	                        <td>
	                            <textarea style="width:500px;" id="comments" name="comments" rows="8" onkeyup="CheckTextAreaLength(this.form.comments,200);"></textarea>
	                        </td>
	                    </tr>
	                </table>
	                <br>
                    <input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
	                <input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
	                <input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
	            </form>
	        </TD>
	    </TR>
	    </TBODY>
    </TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<script language="javascript">

    function OnAfterFormFill() {
        
        var problemnature = document.getElementById("problemnature").value;				
        var styleAndSource = getDispositionStyleAndSource(problemnature);		
        // Parameter order for GenerateSelectBoxForProblemNature is: listname, source, style, mandatory, parent, savelast, size, multiple, print, problemnature.
        dispositionvals.innerHTML = GenerateSelectBox("ResponseDisposition", styleAndSource.dispositionsource, styleAndSource.dispositionstyle, true, false, true, 8, false, false, problemnature);
        	        
        Form.ResponseDisposition.focus();
    }

    function getDispositionStyleAndSource(problemnature) {
        if (problemnature) {		
            return {
                dispositionstyle: { src: "dispositionbyproblemnatureselect.xsl" },
                dispositionsource: { src: "DispositionCodesByProblemNature.xml" }
            };
        } else {		
            return {
                dispositionstyle: { src: "genericselect.xsl" },
                dispositionsource: { src: "DispositionCodes.xml" }
            };
        }
    }

    function window.validatepage()
    {
        if (Form.incidentid.value == '')
        {
            alert('You are not assigned to an incident. Request was not submitted.');
        }
        else
        {
            Form.Submit.click();
        }
    }

</script>
</html>
