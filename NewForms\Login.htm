<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Login</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="5" valign="top" align="center" border="0" ID="Table1">
			<TBODY>

				<TR>
					<TD class="bottombordercell" align="center" vAlign="top" width="100%" colSpan="3">
						<IMG width="900px" height="112px" src="../Bitmaps/DakotaBanner.png" border="0">
					</TD>
				</TR>

				<TR>
					<TD vAlign="top">
						
						<form action="LoginQuery.aspx?queryfile=login.qry" method="post" id="Form" name="Form">
							<table align="center" ID="Table2">
								<tr>
									<td valign="top">Unit:
										<XML id="radiostyle" src="configurationunits.xsl"></XML>
										<SPAN type="selectlist" id="radiovals" name="radiovals">
											<XML id="radiosource" src="../Configuration_Units.xml"></XML>
										</SPAN>
									</td>
									<td valign="top">Password:
										<input size="12" type="password" id="password" name="password" mandatory="true" >
								 	</td>
									<td width="200px" valign="top" align="right">

							 			<input style="width:175px;height:60px;" type="button" name="Query" id="Query" value="LOGIN" onkeypress="validatepage()" onclick="validatepage()">
									</td>
						 		</tr>
					 </table>
					 <table>

<!-- COMMENT OUT ORIGINAL CAPABILITY SELECTION -->

								<tr style="display:none">
									<td valign="top">Hidden Capability:
										<br> 
									    <XML id="capstylex" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="capvalsx" name="capvalsx">
											<XML id="capsourcex" src="Capabilities.xml"></XML>
										</SPAN>
									</td>
								</tr>
<!-- -->

<!-- NEW SELECT / UNSELECT CAPS -->
								<tr style="DISPLAY:none">
									<td>
										<SELECT ID="capsmember" NAME="capsmember" MULTIPLE size="2">
										</SELECT>
									</td>
								</tr>

								<tr>
									<td colspan="3">
										<table>
											<tr>
												<td valign="top">Capability: Unselected
													<br>
													<XML id="capstyle" src="genericselect.xsl"></XML>
													<SPAN type="selectlist" id="capvals" name="capvals">
														<XML id="capsource" src="Capabilities.xml"></XML>
													</SPAN>
												</td>
												<td align="center" width="120px">
													<input style="width:100px;heights:50px;"type="button" value=" -> " onclick="MoveItems( Form.CapUnselected, Form.CapSelected)" />
													<br/>
													<input style="width:100px;heights:50px;"type="button" value=" <- " onclick="MoveItems( Form.CapSelected, Form.CapUnselected)" />
												</td>
												<td valign="top">Selected
													<br>
													<SELECT ID="CapSelected" NAME="CapSelected" SIZE="3" style="WIDTH:350px">
													</SELECT>
												</td>
											</tr>
										</table>
									</td>
								</tr>

								<tr> <td> <hr> </td> </tr>

<!-- No comments from Fire login
								<tr>
									<td valign="top">Comments:</td>
									<td><textarea style="WIDTH:350px" id="activitylog" name="activitylog" rows="2"></textarea></td>
								</tr>
-->
								<tr style="DISPLAY:none">
									<td>
										<SELECT ID="crewmember" NAME="crewmember" MULTIPLE size="5">
										</SELECT>
									</td>
								</tr>

								<tr>
									<td colspan="3">
										<table>
											<tr>
												<td valign="top">Staff: Unselected
													<br>
													<XML id="crewstyle" src="configurationusers.xsl"></XML>
													<SPAN type="selectlist" id="crewvals" name="crewvals">
														<XML id="crewsource" src="../Configuration_CrewMembers.xml"></XML>
													</SPAN>
												</td>
												<td align="center" width="120px">
													<input style="width:100px;heights:50px;"type="button" value=" -> " onclick="MoveItems( Form.Unselected, Form.Selected)" />
													<br/>
													<input style="width:100px;heights:50px;"type="button" value=" <- " onclick="MoveItems( Form.Selected, Form.Unselected)" />
												</td>
												<td valign="top">Selected
													<br>
													<SELECT ID="Selected" NAME="Selected" SIZE="4" style="WIDTH:350px">
													</SELECT>
												</td>
											</tr>
										</table>
									</td>
								</tr>
									<!--<input type="hidden" name="VehicleID" id="VehicleID">-->
								</table>
								<input style="display:none;" TABINDEX="-1" type="hidden" name="username" id="username">
								<input style="display:none;" TABINDEX="-1" type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</tr>
			</TBODY>
		</TABLE>
		<script src="clientutilities.js"></script>
		<SCRIPT language="javascript">
		// Move items between the selected and unselected lists
		function MoveItems( From, To)
		{
			// move items from listbox to listbox
			var i = From.selectedIndex;
			if (i == -1) return;
			var sItem = From.options[i].text;
			From.remove(i);
			var oOption = document.createElement("OPTION");
			oOption.text = sItem;
			// add the item into the right place (to keep the list sorted)
			var oOptions = To.options;
			var iLength = oOptions.length;
			for (i = 0; i < iLength; i++)
			{
				if (oOptions[i].text > sItem) break;
			}
			To.add( oOption, i);
		}
		
		// select all items in selected crewmembers list
		function SelectOptions()
		{
			var oOptions = Form.crewmember.options;
			// clear the hidden listbox
			var i;
			for ( i=oOptions.length-1; i>=0; i--)
			{
				oOptions.remove(i);
			}
			// copy options from Selected to hidden, and select them all
			var oSelectedOptions = Form.Selected.options;
			var iSelectedLength = oSelectedOptions.length;
			for ( i=0; i<iSelectedLength; i++)
			{
				var oOption = document.createElement("OPTION");
				oOption.text = oSelectedOptions[i].text;
				oOption.selected = true;
				oOptions.add(oOption);
			}
		}

		function window.validatepage()
		{
			if (Form.Selected.options.length > 7)
			{
				Form.Selected.focus();
				alert('Too many crew members selected.');
			}
			
			else if (Form.Selected.options.length < 0)
			{
				Form.Selected.focus();
				alert('You need to select at least one crew.');
			}
			else
			{
				if (Form.Selected.options.length > 0 && Form.username.selectedIndex >= 0)
				{
					//Get the Username
					var vLoginName = (Form.username.options[Form.username.selectedIndex].text);
					//Get selected options in crew member list and compare with login name
					//If the Login name matches with any of selected crew members
					//then remove that crew member from select list and proceed with login
					var oSelectedOptions = Form.Selected.options;
					var iSelectedLength = oSelectedOptions.length;
					for ( i=0; i<iSelectedLength; i++)
					{
						if (oSelectedOptions(i).text == vLoginName)
						{
							Form.Selected.remove(i);
							break;
						}
					}

				}
				SelectOptions();
				if (Form.unit.selectedIndex > -1)
				{
					Form.username.value = Form.unit.options(Form.unit.selectedIndex).text;
				}
				
				//Append the session start message to the comment so that it will be saved in the
				//Activity Log.
				//Form.activitylog.value += 'Login Comments: \n <<VisiNET Mobile Session Logon (Session Started)>> ';

				//reset hidden list
				var elements = document.getElementById("Capabilities").options;
				for(var i = 0; i < elements.length; i++)
					elements[i].selected = false;

				//copy capabilities from Selected to hidden, and select what's needed
				var capSelectedOptions = Form.CapSelected.options;
				var savedCaps = document.getElementById('Capabilities');

				for ( i=0; i< capSelectedOptions.length; i++) {
					for (j=0; j < savedCaps.length; j++) {
						if(savedCaps[j].text == capSelectedOptions[i].text) {
							savedCaps[j].setAttribute("selected","true");
						}
					}
				}

				Form.Submit.click();
			}
		}
		
		function window.onload()
		{
			PrepareValidation(Form);
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			radiovals.innerHTML = GenerateSelectBox("unit", radiosource, radiostyle, true, false, true, 1, false, false);
			//vehvals.innerHTML = GenerateSelectBox("VehicleID", vehsource, vehstyle, false, false, true, 1, false, false);
			crewvals.innerHTML = GenerateSelectBox("Unselected", crewsource, crewstyle, false, false, true, 4, true, false);
			capvalsx.innerHTML=GenerateSelectBox("Capabilities", capsourcex, capstylex, false, false, true, 4, true, false);
			capvals.innerHTML=  GenerateSelectBox("CapUnselected", capsource, capstyle, false, false, true, 3, true, false);

//			Form.Unselected.multiple = false;
			Form.Unselected.style.width = 350;

			Form.CapUnselected.multiple = false;
			Form.CapUnselected.style.width = 350;
			Form.password.focus();

		}


	setTimeout(function(){ // C4G anonymous function to capture variable after window loads
	var savedCaps = document.getElementById('Capabilities');
	var unselCaps = document.getElementById('CapUnselected');
	// update hidden list. it will change as items are moved
	for(i=0; i < savedCaps.length; i++)
	{
		if ( savedCaps[i].getAttribute('selected') ) {
			for(j=0; j < unselCaps.length; j++) {
				if (savedCaps[i].text == unselCaps[j].text) {
					unselCaps[j].setAttribute("selected", "true");
	  				MoveItems (document.getElementById('CapUnselected'), document.getElementById('CapSelected') );
					break;
				}
			}
		}
	}
 	}, 350);

	// Added functionality to double click item to move to other list. Marvin C4G
	document.getElementById('crewvals').ondblclick = function(e){
		 MoveItems (document.getElementById('Unselected'), document.getElementById('Selected') );
	}
	document.getElementById('Selected').ondblclick = function(e){
		 MoveItems (document.getElementById('Selected'), document.getElementById('Unselected') );
	}
	document.getElementById('capvals').ondblclick = function(e){
		 MoveItems (document.getElementById('CapUnselected'), document.getElementById('CapSelected') );
	}
	document.getElementById('CapSelected').ondblclick = function(e){
		 MoveItems (document.getElementById('CapSelected'), document.getElementById('CapUnselected') );
	}

		</SCRIPT>
	</body>
</HTML>
