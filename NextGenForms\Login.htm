﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Login</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <link rel="stylesheet" type="text/css" href="icons.css" />

    <!-- Compiled and minified JavaScript -->
    <script src="jquery.min.js" type="text/javascript"></script>
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>
    <script src="login.js" type="text/javascript"></script>

    <script language="javascript">
        $(document).ready(function () {

            $('select').formSelect();

            $('.timepicker').timepicker({
                twelveHour: false
            });
            
            $("#Form").submit(function () {
                if ($(this)[0].checkValidity() == true) {
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }

                return false;
            });

            $("#Form").on('keydown', function (e) {
                //Makes tabbing cycle through inputs
                if (e.keyCode == 9) {
                    var $focused = $(':focus');
                    var $lastInput = $("input:not(:hidden):not(:disabled)").last();
                    if ($focused.attr('tabindex') == $lastInput.attr('tabindex')) {
                        $("input").first().focus();
                        e.preventDefault();
                    }
                }
            });

            //focus on password field
            $("#password").focus();
        });

        function AfterFillForm() {

            // M.updateTextFields();
            SetUserPositions($("#username"), $('#userPosition'));

            $('input.autocomplete').autocomplete({
                data: {
                },
            });

            $('input.autocomplete').on('input', function () {
                GetDataList($(this));
            });

            GenerateSelectBox("Capabilities", "Capabilities.xml", "genericselect.xsl", false, false, true, 8, true, false).then(function (result) {
                $("#capvals").prepend(result);
                SetMultiSelectBoxFromParameters($("#Capabilities"));
                GenerateSelectBox("sector", "Configuration_Sectors.xml", "configurationusers.xsl", true, false, true, 1, false, false).then(function (result) {
                    $("#sectorvals").prepend(result);
                    SetSelectBoxFromParameters($("#sector"));
                    GenerateSelectBox("beat", "BeatCapabilities.xml", "genericselect.xsl", true, false, false, 3, false, false).then(function (result) {
                        $("#beatcapvals").prepend(result);
                        SetSelectBoxFromParameters($("#beat"));
                        SetTabIndex();
                    });
                });
            });
        }

    </script>

</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <img style="display:block;margin-left:auto;margin-right:auto" src="logo.png" />
                </div>
            </div>
        </div>
    </div>
    <form class="Flex-Form" action="LoginQuery.aspx?queryfile=login.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <!--login title-->
            <div class="row" style="padding-top:20px">
                <div>
                    <h5 class="center-align">Sign in to Mobile Enterprise Client</h5>
                </div>
            </div>
            <!--user name & password-->
            <div class="row">
                <div class="input-field col s10 offset-s1 m4 offset-m2">
                    <input type="text" placeholder="" name="username" id="username" onchange="SetUserPositions($('#username'), $('#userPosition'));" class="autocomplete" source="Configuration_Users.xml" searchField="item" savelast="true">
                    <label for="autocomplete-input" class="active">User Name</label>
                </div>
                <div class="input-field col s10 offset-s1 m4">
                    <input placeholder="" id="password" type="password" name="password" required>
                    <label for="password" class="active">Password</label>
                </div>
            </div>
            <!--user position-->
            <!--Uncomment to use User Position Field. Commented out by default.-->
            <!--<div class="row">
                <div class="input-field col s10 offset-s1 m4 offset-m2" type="selectlist">
                    <select name="userPosition" id="userPosition">
                        <option value="-1"></option>
                    </select>
                    <label for="userPosition">Position for User</label>
                </div>
            </div>-->
            <!--Primary device -->
            <!--Uncomment to use Primary Device Field. Commented out by default.-->
            <!--<div class="row">
                <label class="col s10 offset-s1 m4 offset-m2">
                    <input type="checkbox" name="isPrimaryDevice" id="isPrimaryDevice" />
                    <span>Primary Device</span>
                </label>
            </div>-->
            <!--radio name & vehicle number-->
            <div class="row">
                <div class="input-field col s10 offset-s1 m4 offset-m2">
                    <input placeholder="" type="text" name="unit" id="unit" class="autocomplete" source="Configuration_Units.xml" searchField="id" savelast="true">
                    <label for="autocomplete-input" class="active">Radio Name</label>
                </div>
                <div class="input-field col s10 offset-s1 m4">
                    <input placeholder="" type="text" name="vehicleid" id="vehicleid" class="autocomplete" source="NewForms/Vehicle.xml" searchField="description" savelast="true">
                    <label for="autocomplete-input" class="active">Vehicle Number</label>
                </div>
            </div>
            <!--capabilities & supervisor radio name-->
            <div class="row">
                <div class="input-field col s10 offset-s1 m4 offset-m2" type="selectlist" id="capvals" name="capvals">
                    <label for="capvals">Capabilities</label>
                </div>
                <!--Uncomment to use Supervisor Radio Name Field.  Commented out by default.-->
                <!--<div class="input-field col s10 offset-s1 m4">
            <input placeholder="" type="text" name="SupervisorRadioName" id="SupervisorRadioName" class="autocomplete" source="Configuration_Units.xml" searchField="id" savelast="true">
            <label for="autocomplete-input" class="active">Supervisor Radio Name</label>
        </div>-->
            </div>
            <!--crew members-->
            <div class="row">
                <div id="crewMembersDiv">
                    <div class="row crew-member">
                        <div class="input-field col s6 offset-s1 m4 offset-m2" style="padding-left:10px">
                            <input type="text" onchange="SetUserPositions(null, null, event);" placeholder="" name="crewmember" id="crewmember" class="autocomplete" source="Configuration_CrewMembers.xml" searchField="item">
                            <label for="autocomplete-input" class="active">Crew Member</label>
                        </div>
                        <!--Position will go here-->
                        <!--Uncomment to use Crew Member Position Field.  Commented out by default.-->
                        <!--<div class="input-field col s6 offset-s1 m4 crewPosition" type="selectlist">
                    <select name="crewmemberPosition" id="crewmemberPosition">
                        <option value="-1"></option>
                    </select>
                    <label for="crewmemberPosition">Position</label>
                </div>-->
                        <div class="col" style="margin-top:30px;">
                            <a class="btn-flat btn-icon-18" onclick="DeleteCrewMemberField(event);">
                                <i class="icon-delete icon-18"></i>
                            </a>
                        </div>
                        <div class="col addCrewBtn" style="padding:0px;margin-top:30px;">
                            <a class="btn-flat btn-icon-18" onclick="AddCrewMemberField();">
                                <i class="icon-add icon-18"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!--sector & beat-->
            <!--Uncomment to use Sector and/or Beat Field(s).  Commented out by default.-->
            <!--<div class="row">
        <div class="input-field col s10 offset-s1 m4 offset-m2" type="selectlist" id="sectorvals" name="sectorvals" required>
            <label for="sectorvals">Sector</label>
        </div>
        <div class="input-field col s10 offset-s1 m4" type="selectlist" id="beatcapvals" name="beatcapvals" required>
            <label for="beatcapvals">Beat</label>
        </div>
    </div>-->
            <!--shift end time-->
            <div class="row">
                <div class="input-field col s10 offset-s1 m4 offset-m2">
                    <input placeholder="" class="timepicker" type="text" id="shiftendtime" name="shiftendtime" savelast="true" required />
                    <label for="shiftendtime">Shift End Time: </label>
                </div>
            </div>
            <!--temporary pager-->
            <!--Uncomment to use temporary pager fields.  Commented out by default.-->
            <!--<div class="row">
        <div>
            <h5 class="center-align">TEMPORARY PAGER</h5>
        </div>
    </div>
    <div class="row">
        <div id="temporaryPagerDiv">
            <div class="row temporary-pager">
                <div class="input-field col s6 offset-s1 m4 offset-m2">
                    <input type="text" maxlength="30" placeholder="" name="Pin1" id="Pin1" class="pagers-control" mandatory="false">
                    <label for="Pin1" class="active">Temporary PIN</label>
                </div>
                <div class="col" style="margin-top:30px;">
                    <a class="btn-flat btn-icon-18 pagers-control" onclick="DeleteTemporaryPagerField(event);">
                        <i class="icon-delete icon-18"></i>
                    </a>
                </div>
                <div class="col addTemporaryPagerBtn" style="padding:0px;margin-top:30px;">
                    <a class="btn-flat btn-icon-18 pagers-control" onclick="AddTemporaryPagerField();">
                        <i class="icon-add icon-18"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>-->
            <!--temprorty radios-->
            <div class="row">
                <div>
                    <h5 class="center-align">TEMPORARY RADIOS</h5>
                </div>
            </div>
            <div class="row">
                <div id="temporaryRadiosDiv">
                    <div class="row temporary-radio">
                        <div class="input-field col s6 offset-s1 m2 offset-m2">
                            <input type="text" maxlength="30" placeholder="" name="radioID1" id="radioID1" class="radios-control" mandatory="false">
                            <label for="radioid" class="active">Radio ID</label>
                        </div>
                        <div class="input-field col s6 offset-s1 m2">
                            <input type="text" maxlength="30" placeholder="" name="radioCode1" id="radioCode1" class="radios-control" mandatory="false">
                            <label for="radiocode" class="active">Radio Code</label>
                        </div>
                        <div class="input-field col s6 offset-s1 m4">
                            <input type="text" maxlength="30" placeholder="" name="radioDescription1" id="radioDescription1" class="radios-control" mandatory="false">
                            <label for="radioid" class="active">Radio Description</label>
                        </div>
                        <div class="col" style="margin-top:30px;">
                            <a class="btn-flat btn-icon-18  radios-control" onclick="DeleteTemporaryRadioField(event);">
                                <i class="icon-delete icon-18"></i>
                            </a>
                        </div>
                        <div class="col addTemporaryRadioBtn" style="padding:0px;margin-top:30px;">
                            <a class="btn-flat btn-icon-18 radios-control" onclick="AddTemporaryRadioField();">
                                <i class="icon-add icon-18"></i>
                            </a>
                        </div>-
                    </div>
                </div>
            </div>

            <!--submit-->
            <div class="row show-on-small" style="display: none;">
                <input class="col s10 offset-s1 btn" type="submit" value="Submit" style="margin-bottom:10px;">
            </div>

            <!--hidden values-->
            <input type="hidden" name="maxCrew" id="maxCrew" value="7" disabled />
            <input type="hidden" name="maxRadios" id="maxRadios" value="8" disabled />
            <input type="hidden" name="maxPins" id="maxPins" value="3" disabled />
        </div>
    </form>

</body>
</html>


