<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <title>VisiNET Mobile - Tow Request</title>
    <link href="bigstyle.css" type="text/css" rel="stylesheet" />
</head>
<body>
    <table class="base" cellpadding="10" align="center" border="0" id="Table1">
        <tbody>
            <tr>
                <form action="TowRequest.aspx?queryfile=TowRequest.qry" method="post" id="TowRequest" name="TowRequest">
                    <td valign="top">
                        <h4 align="center">Tow Request</h4>
                    </td>
                    <input type="hidden" name="IncidentNumber" id="IncidentNumber" readonly>
                    <input type="hidden" name="IncidentID" id="IncidentID" readonly>
            <tr>
                <td valign="top">
                    Tow Category:&nbsp;&nbsp;
                    <xml id="categorystyle" src="genericselect.xsl"></xml>
                    <span type="selectlist" id="category" name="category">
                        <xml id="categorysource" src="RotationCategoryByAgency.xml"></xml>
                    </span>
                </td>
            </tr>
            <tr>
                <td valign="top">
                    <table class="base" align="center" cellpadding="5" id="Table2" width="95%">
                        <tbody>
                            <tr>
                                <td>
                                    <h4 align="left">Vehicle Info</h4>
                            <tr>
                                <td width="50%">
                                    Make:
                                    <xml id="makestyle" src="genericselect.xsl"></xml>
                                    <span type="selectlist" id="makevals" name="makevals" style="margin-left:13px">
                                        <xml id="makesource" src="VehicleMake.xml"></xml>
                                    </span>
                                </td>
                                <td width="45%">
                                    <input type="checkbox" name="HoldForEvidence" id="HoldForEvidence" value="1">  Hold for Evidence<br />
                                </td>
                            </tr>
                            <tr width="100%">
                                <td>
                                    Model:
                                    <input type="text" name="Model" id="Model" style=" width:50%; margin-left:5px;" />
                                </td>
                                <td>
                                    Year:
                                    <input type="text" name="VehicleYear" id="VehicleYear" style=" width:50%; margin-left:22px;" />
                                </td>
                            </tr>
                            <tr width="100%">
                                <td>
                                    Style:
                                    <input type="text" name="Style" id="Style" style=" width:50%; margin-left:16px;" />
                                </td>
                                <td>
                                    Color:
                                    <input type="text" name="Color" id="Color" style=" width:50%; margin-left:12px;" />
                                </td>
                            </tr>
                </td>
            </tr>
        </tbody>
    </table>
    </td>
    </tr>
    <tr>
        <td valign="top">
            <table class="base" align="center" cellpadding="5" id="Table3" width="95%">
                <tbody>
                    <tr>
                        <td>
                            <h4 align="left">License Plate Info</h3>

                    <tr width="100%">
                        <td valign="top" width="50%">
                            Plate:
                            <input type="text" name="Plate" id="Plate" style="width:50%; margin-left:17px;" />
                        </td>
                        <td width="50%">
                            Year:
                            <input type="text" name="LicenseYear" id="LicenseYear" style="width:50%; margin-left:21px;" />
                        </td>
                    </tr>
                    <tr width="100%">
                        <td>
                            State:

                            <xml id="statestyle" src="genericselect.xsl"></xml>
                            <span type="selectlist" id="statevals" name="statevals" style="margin-left:12px">
                                <xml id="statesource" src="state.xml"></xml>
                            </span>
                    </tr>
                    <tr>
                        <td>
                            VIN:
                            <input type="text" name="VIN" id="VIN" style=" width:50%; margin-left:30px;" />
                        </td>
                    </tr>
        </td>
    </tr>
    </tbody>
    </table>
    </td>
    </tr>
    <tr>
        <td valign="top">
            <table height="6" width="491">
                <td valign="top" height="2" width="69">
                    Comment:&nbsp;&nbsp;
                </td>
                <td width="408" height="2">
                    <textarea cols="30" name="Comment" id="Comment" style=" height:70px; margin-left:48px"></textarea>
                </td>
            </table>
        </td>
    </tr>
    <tr>
        <td valign="top">
            Requested by:&nbsp;&nbsp;
            <input type="text" name="RequestedByUser" id="RequestedByUser" />
        </td>
    </tr>

    <tr>
        <td valign="top" align="right">
            <input type="button" id="SumitButton" name="SumitButton" value="Submit" onclick="validate()" onkeypress="validate()" style="width:150px; height:40px" />
            <input style="width:0px;" tabindex=-1 type="submit" name="Submit" id="Submit" value="Submit">
        </td>
    </tr>
    </form>
    </tr>
    </tbody>
    </table>
</body>
<script src="clientutilities.js"></script>
<script language="javascript">

    var date = new Date();
    var CurrentYear = parseInt(date.getFullYear());
    var NextAllowedYear = CurrentYear + 1;
    var StartingYear = CurrentYear - 100;

    window.onload = function () {
        category.innerHTML = GenerateSelectBox("TowCategoryID", categorysource, categorystyle, false, false, true, 1, false, false);
        document.getElementById("TowCategoryID").style.width = "50%";
        statevals.innerHTML = GenerateSelectBox("State", statesource, statestyle, false, false, true, 1, false, false);
        document.getElementById("State").style.width = "60%";
        makevals.innerHTML = GenerateSelectBox("Make", makesource, makestyle, false, false, true, 1, false, false);
    }

    function validate() {
        var vehicleyear = document.getElementById("VehicleYear").value;
        var licenseyear = document.getElementById("LicenseYear").value;

        if (vehicleyear === '' && licenseyear === '') {
            document.getElementById("Submit").click();
            return;
        }
        if (vehicleyear != '' && !isValid(vehicleyear)) {
            alert("Invalid Vehicle Year")
            return;
        }
        if (licenseyear != '' && !isValid(licenseyear)) {
            alert("Invalid License Year")
            return;
        }
        document.getElementById("Submit").click();
        return;
    }

    function isValid(year) {
        if (year.length == 4) {
            year = parseInt(year);
            return (year != 'NaN' && year >= StartingYear && year <= NextAllowedYear)
        }
        else {
            return false;
        }
    }
</script>
</html>