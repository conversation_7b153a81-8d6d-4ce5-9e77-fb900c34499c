﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Nature Unknown</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <link rel="stylesheet" type="text/css" href="icons.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>
    <script src="GeoValidate.js"></script>

    <script language="javascript">
        $(document).ready(function () {
            //Set up Tab control
            $('.tabs').tabs();
            var instance = M.Tabs.getInstance($('.tabs'));
            instance.select($("#location").val());

            //handle form submition
            $("#Form").submit(function () {
                if (ValidateLocationInformation() && $(this)[0].checkValidity() == true) {

                    $(':disabled').each(function (e) {
                        $(this).removeAttr('disabled');
                    })

                    if ($("#location").val() == "currentlocation") {
                        $("#Lat").val($("#curlat").val());
                        $("#Long").val($("#curlong").val());
                    }
                    else {
                        $("#Lat").val($("#sellat").val());
                        $("#Long").val($("#sellong").val());
                    }
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });

            $("#Address").focus();
        });
    </script>

</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">NATURE UNKNOWN</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="Flex-Form" action="Form.aspx?queryfile=natureunknown.qry" method="post" id="Form" name="Form">

        <div class="Flex-Form-MainContent">
            <div class="col m8" style="margin-top:20px">
                <div class="row">
                    <div class="input-field col s6">
                        <input disabled name="problemnature" id="problemnature" type="text" value="NATURE UNKNOWN" placeholder="">
                        <label for="problemnature" class="active">Problem Nature</label>
                    </div>
                </div>
                <div class="row">
                    <ul class="tabs" id="radio_tabs" style="margin-bottom: 30px; margin-left: 8px;">
                        <li class="tab col s2" id="tab1"><a href="#currentlocation" onclick="setLocation('currentlocation')">Current</a></li>
                        <li class="tab col s2" id="tab2"><a href="#selectedlocation" onclick="setLocation('selectedlocation')">Selected</a></li>
                        <li class="tab col s2" id="tab3"><a href="#addresslocation" onclick="setLocation('addresslocation')">Address</a></li>
                    </ul>
                    <!--CurrentLocation-->
                    <div class="row" id="currentlocation">
                        <div class="row">
                            <div class="input-field col s4">
                                <input disabled name="curlat" id="curlat" type="text" placeholder="">
                                <label for="curlat" class="active">Latitude</label>
                            </div>
                            <div class="input-field col s4">
                                <input disabled name="curlong" id="curlong" type="text" placeholder="">
                                <label for="curlong" class="active">Longitude</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s8">
                                <input disabled id="curaddress" name="curaddress" type="text" placeholder="">
                                <label for="curaddress" class="active">Address</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s4">
                                <input disabled id="curcity" name="curcity" type="text" placeholder="">
                                <label for="curcity" class="active">City</label>
                            </div>
                            <div class="input-field col s4">
                                <input disabled id="curzip" name="curzip" type="text" placeholder="">
                                <label for="curzip" class="active">Zip</label>
                            </div>
                        </div>
                    </div>
                    <!--SelectedLocation-->
                    <div class="row" id="selectedlocation">
                        <div class="row">
                            <button class="mdc-button btn Select_From_Map col s3" type="button" style="margin-left: 10px; min-width:170px">
                                <div class="mdc-button__ripple"></div>
                                <span class="mdc-button__label">Select From Map</span>
                            </button>
                        </div>
                        <div class="row">
                            <div class="row">
                                <div class="input-field col s4">
                                    <input name="sellat" id="sellat" type="text" placeholder="" maxlength="15">
                                    <label for="sellat" class="active">Latitude</label>
                                </div>
                                <div class="input-field col s4">
                                    <input name="sellong" id="sellong" type="text" placeholder="" maxlength="15">
                                    <label for="sellong" class="active">Longitude</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="input-field col s8">
                                    <input disabled id="seladdress" nanme="seladdress" type="text" placeholder="">
                                    <label for="seladdress" class="active">Address</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="input-field col s4">
                                    <input disabled id="selcity" name="selcity" type="text" placeholder="">
                                    <label for="selcity" class="active">City</label>
                                </div>
                                <div class="input-field col s4">
                                    <input disabled id="selzip" name="selzip" type="text" placeholder="">
                                    <label for="selzip" class="active">Zip</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--AddressLocation-->
                    <div class="row" id="addresslocation">
                        <div class="row">
                            <div class="input-field col s8" mandatory="true">
                                <input placeholder="" id="Address" name="Address" type="text" maxlength="400" oninput="GeoButtonVisibility()">
                                <label for="adaddress" class="active">Address</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s4" mandatory="true">
                                <input placeholder="" id="City" name="City" type="text" maxlength="35">
                                <label for="adcity" class="active">City</label>
                            </div>
                            <div class="input-field col s4" mandatory="true">
                                <input placeholder="" id="Zip" name="Zip" type="text" maxlength="10">
                                <label for="Zip" class="active">Zip</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s6">
                                <button class="mdc-button btn Geo_Validate_Address disabled col s4" type="button" style="min-width:170px">
                                    <div class="mdc-button__ripple"></div>
                                    <span class="mdc-button__label">GeoValidate</span>
                                    <i class="icon-check icon-24" id="geovalid_address_selected" style="display:none"></i>
                                </button>
                                <div class="col s2"></div>
                                <div class="preloader-wrapper small active" id="preloader_container" style="display:none">
                                    <div class="spinner-layer spinner-blue-only">
                                        <div class="circle-clipper left">
                                            <div class="circle"></div>
                                        </div><div class="gap-patch">
                                            <div class="circle"></div>
                                        </div><div class="circle-clipper right">
                                            <div class="circle"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col s2 right-align">
                                <button disabled class="mdc-button btn More_Button" type="button" style="display:none">
                                    <div class="mdc-button__ripple"></div>
                                    <span class="mdc-button__label">More..</span>
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="responsive-table col s8" style="margin-top: 20px;">
                                <table class="highlight">
                                    <thead>
                                        <tr>
                                            <th class="center">
                                                <x>Geo Validated Addresses</x>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="verifiedLocationsList"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="Flex-Form-Comment">
            <div class="comment">
                <div class="row">
                    <div class="input-field col s12">
                        <textarea id="Comment" placeholder="Enter Comment Here" class="materialize-textarea" name="Comment"></textarea>
                        <label for="Comment" class="active">Comments</label>
                    </div>
                </div>            
            </div>        
        </div>
        <!--Hidden Inputs that are passed to the query server-->
        <input type="hidden" name="CallTaking_Performed_By" id="CallTaking_Performed_By">
        <input type="hidden" name="SelfAssign" id="SelfAssign" value="true" size="10">
        <input type="hidden" name="Lat" id="Lat" size="15">
        <input type="hidden" name="Long" id="Long" size="15">
        <input type="hidden" name="VerifyLocationResults" id="VerifyLocationResults" value="">
        <input type="hidden" name="SelectedAddressIndex" id="SelectedAddressIndex" value="-1">
        <input type="hidden" name="VerifyMoreN" id="VerifyMoreN" value="0">
        <input type="hidden" name="VerifyMaxResultsReturned" id="VerifyMaxResultsReturned" value="0">
        <input type="hidden" name="location" id="location" value="currentlocation">
    </form>
</body>
</html>


