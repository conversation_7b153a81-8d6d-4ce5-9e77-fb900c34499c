﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>Mobile Enterprise - Responding (Second Location)</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <link rel="stylesheet" type="text/css" href="icons.css" />
    <script src="jquery.min.js" type="text/javascript"></script>

    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>
    <script src="GeoValidate.js"></script>

    <script language="javascript">

        var State;

        $(document).ready(function () {

            //Set up Tab control
            $('.tabs').tabs();
            var instance = M.Tabs.getInstance($('.tabs'));
            instance.select($("#SelectedAddressTab").val());

            /* Location Search */
            $("#locationsTextSearch").on("input", GetTableData);
            $("#locationsTextSearch").on("focus", function () {
                $("#icon-search").addClass("icon-search-blue");
            });
            $("#locationsTextSearch").on("focusout", function () {
                $("#icon-search").removeClass("icon-search-blue");
            });

            //handle form submition
            $("#Form").submit(function () {
                if (ValidateLocationForms() && $(this)[0].checkValidity() == true) {
                    $(':disabled').each(function (e) {
                        $(this).removeAttr('disabled');
                    })
                    if ($("#SelectedAddressTab").val() == "locationtab") {
                        ClearAddress();
                    }
                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });

            $("#Address").focus();
        });

        function AfterFillForm() {

            CheckUnitsForPreFillFields($("#unitname1").val(), $("#address1").val(), $("#city1").val(), $("#postalcode1").val(), $("#roomapt1").val(), $("#building1").val(), $("#state1").val());
            CheckUnitsForPreFillFields($("#unitname2").val(), $("#address2").val(), $("#city2").val(), $("#postalcode2").val(), $("#roomapt2").val(), $("#building2").val(), $("#state2").val());
            CheckUnitsForPreFillFields($("#unitname3").val(), $("#address3").val(), $("#city3").val(), $("#postalcode3").val(), $("#roomapt3").val(), $("#building3").val(), $("#state3").val());
            CheckUnitsForPreFillFields($("#unitname4").val(), $("#address4").val(), $("#city4").val(), $("#postalcode4").val(), $("#roomapt4").val(), $("#building4").val(), $("#state4").val());

            GenerateSelectBox("transportstate", "state.xml", "genericselectvalue.xsl", false, false, false, 1, false, false).then(function (result) {
                $("#statevals").prepend(result);
                $('select').formSelect();
                var $option = $("#transportstate").find("option[value='" + State + "']");
                $option.prop("selected", true);
                $("#transportstate").formSelect();
            });

            setDefaultLocation();
        }
        function CheckUnitsForPreFillFields(unitname, address, city, postalcode, roomapt, building, state) {
            //Check for the unit name and prefill the location/ Address details
            //with the hidden fields value. If location is present, location value should be
            //pre selected with location radio checked.
            if ($("#unitname").val() == unitname) {
                //Fill Address fields
                $("#Address").val(address);
                $("#City").val(city);
                $("#postalcode").val(postalcode);
                $("#roomapt").val(roomapt);
                $("#building").val(building);
                //Check for State
                if (state.length > 0) {
                    State = state;
                }
            }
        }

    </script>

</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">RESPONDING - SECOND LOCATION</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="Flex-Form" action="SecondLocationQuery.aspx?queryfile=secondlocation.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <div class="col m12" style="margin-top:20px">
                <div class="row">
                    <ul class="tabs" id="radio_tabs" style="margin-bottom: 30px; margin-left: 10px">
                        <li class="tab col s3" id="tab2"><a href="#locationtab" onclick="setTab('locationtab')">Location</a></li>
                        <li class="tab col s3" id="tab3"><a href="#addresstab" onclick="setTab('addresstab')">Address</a></li>
                    </ul>
                    <!--Location-->
                    <div class="row" id="locationtab">
                        <div class="row">
                            <div class="responsive-table col s8" style="margin-top: 20px;">
                                <table class="tableSearch highlight">
                                    <thead>
                                        <tr>
                                            <th class="center" style="padding:0">
                                                <div class="row">
                                                    <div class="input-field col">
                                                        <i class="icon-search icon-20" id="icon-search"></i>
                                                    </div>
                                                    <div class="input-field col">

                                                        <input class="searchStyle" placeholder="Search Locations"
                                                               id="locationsTextSearch" type="text"
                                                               source="Respond2ndLocation.xml" tbody="locationbody" searchField="description">
                                                    </div>
                                                </div>

                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="locationbody"></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="row">
                            <label class="table-validate-required col s8" style="display:none">Field is Required</label>
                        </div>
                    </div>
                    <!--StreetAddress-->
                    <div class="row" id="addresstab">
                        <div class="row">
                            <div class="input-field col s8" mandatory="true">
                                <input placeholder="" id="Address" name="Address" type="text" maxlength="400" oninput="GeoButtonVisibility()">
                                <label for="Address" class="active">Address</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s4" mandatory="true">
                                <input placeholder="" id="City" name="City" type="text" maxlength="35">
                                <label for="City" class="active">City</label>
                            </div>
                            <div class="input-field col s2" mandatory="true">
                                <input placeholder="" id="postalcode" name="postalcode" type="text" maxlength="10">
                                <label for="postalcode" class="active">Zip</label>
                            </div>
                            <div class="input-field col s2" type="selectlist" id="statevals" name="statevals">
                                <label>State</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s2" mandatory="true">
                                <input placeholder="" id="building" name="building" type="text" maxlength="10">
                                <label for="building" class="active">Building</label>
                            </div>
                            <div class="input-field col s2" mandatory="true">
                                <input placeholder="" id="roomapt" name="roomapt" type="text" maxlength="10">
                                <label for="roomapt" class="active">Room/Apt</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s6">
                                <button class="mdc-button btn Geo_Validate_Address disabled col s4" type="button" style="min-width:170px">
                                    <div class="mdc-button__ripple"></div>
                                    <span class="mdc-button__label">GeoValidate</span>
                                    <i class="icon-check icon-24" id="geovalid_address_selected" style="display:none"></i>
                                </button>
                                <div class="col s2"></div>
                                <div class="preloader-wrapper small active" id="preloader_container" style="display:none">
                                    <div class="spinner-layer spinner-blue-only">
                                        <div class="circle-clipper left">
                                            <div class="circle"></div>
                                        </div><div class="gap-patch">
                                            <div class="circle"></div>
                                        </div><div class="circle-clipper right">
                                            <div class="circle"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col s2 right-align">
                                <button disabled class="mdc-button btn More_Button" type="button" style="display:none">
                                    <div class="mdc-button__ripple"></div>
                                    <span class="mdc-button__label">More..</span>
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="responsive-table col s8" style="margin-top: 20px;">
                                <table class="highlight">
                                    <thead>
                                        <tr>
                                            <th class="center">
                                                <x>Geo Validated Addresses</x>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="verifiedLocationsList"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="Flex-Form-Comment">
            <div class="comment">
                <div class="row">
                    <div class="input-field col s12">
                        <textarea id="Comments" placeholder="Enter Comment Here" class="materialize-textarea" name="Comments"></textarea>
                        <label for="Comments" class="active">Comments</label>
                    </div>
                </div>
            </div>
        </div>

        <!--Hidden Inputs that are passed to the query server-->
        <input type="hidden" name="SelectedAddressTab" id="SelectedAddressTab" value="addresstab">

        <input type="hidden" name="unitname" id="unitname">

        <input type="hidden" name="SelectedAddressIndex" id="SelectedAddressIndex">

        <input type="hidden" name="location" id="location" />

        <!--These are necessary for when an incident has several units tied to the call that need to be updated-->
        <input type="hidden" name="unitname1" id="unitname1">
        <input type="hidden" name="location1" id="location1">
        <input type="hidden" name="address1" id="address1">
        <input type="hidden" name="city1" id="city1">
        <input type="hidden" name="state1" id="state1">
        <input type="hidden" name="postalcode1" id="postalcode1">
        <input type="hidden" name="roomapt1" id="roomapt1">
        <input type="hidden" name="building1" id="building1">

        <input type="hidden" name="unitname2" id="unitname2">
        <input type="hidden" name="location2" id="location2">
        <input type="hidden" name="address2" id="address2">
        <input type="hidden" name="city2" id="city2">
        <input type="hidden" name="state2" id="state2">
        <input type="hidden" name="postalcode2" id="postalcode2">
        <input type="hidden" name="roomapt2" id="roomapt2">
        <input type="hidden" name="building2" id="building2">

        <input type="hidden" name="unitname3" id="unitname3">
        <input type="hidden" name="location3" id="location3">
        <input type="hidden" name="address3" id="address3">
        <input type="hidden" name="city3" id="city3">
        <input type="hidden" name="state3" id="state3">
        <input type="hidden" name="postalcode3" id="postalcode3">
        <input type="hidden" name="roomapt3" id="roomapt3">
        <input type="hidden" name="building3" id="building3">

        <input type="hidden" name="unitname4" id="unitname4">
        <input type="hidden" name="location4" id="location4">
        <input type="hidden" name="address4" id="address4">
        <input type="hidden" name="city4" id="city4">
        <input type="hidden" name="state4" id="state4">
        <input type="hidden" name="postalcode4" id="postalcode4">
        <input type="hidden" name="roomapt4" id="roomapt4">
        <input type="hidden" name="building4" id="building4">


    </form>
</body>
</html>