<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Unit Status</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="UnitStatus.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Unit Status</H4>
						<P>
	<xsl:apply-templates select="results/exemel/NewDataSet"/>


						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>
	<xsl:template match="/results/exemel/NewDataSet">

        
	
        <table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="color:white;background-color:#0000C0;border-width:1px;border-style:None;">
                <tr style="font-weight:bold;"><td>Unit</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Code"/>
				<xsl:element name="input">
			                <xsl:attribute name="type">button</xsl:attribute>
			                <xsl:attribute name="id">MapIt</xsl:attribute>
			                <xsl:attribute name="executefunction">MapIt</xsl:attribute>
		        	        <xsl:attribute name="parameters">Latitude=<xsl:apply-templates select="Table[1]/Current_Lat"/>&amp;Longitude=<xsl:apply-templates select="Table[1]/Current_Lon"/></xsl:attribute>
		        	        <xsl:attribute name="value">Map It</xsl:attribute>
		                </xsl:element>
		</td></tr>
		

       		 <xsl:if test="Table/IncidentNumber!=''">
       		 <tr style="font-weight:bold;">
       		 <td> Incident Assigned </td>
       		 <td style="background-color:window;color:windowtext;">
        	 <xsl:for-each select="Table">
        	 <xsl:variable name="v_rec"><xsl:value-of select="IncidentID"/></xsl:variable>
			<a href="SingleIncidentQuery.aspx?ID={$v_rec}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="IncidentNumber"/></a> 
			<!--Place separator only if not the last record  -->
			<xsl:if test="position()!=last()">
				<xsl:text> ; </xsl:text>
			</xsl:if>
		</xsl:for-each>
        	</td> </tr>
        	</xsl:if>		
		
		<tr style="font-weight:bold;"><td>Vehicle</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/VehicleId"/></td></tr>
		<tr style="font-weight:bold;"><td>Personnel</td><td style="background-color:window;color:windowtext;"> <xsl:value-of select="Table[1]/Emp_Name"/></td></tr>
		<tr style="font-weight:bold;"><td>Status</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Description"/></td></tr>
		<xsl:if test="Table[1]/Osreason!=''">
			<tr style="font-weight:bold;">
			<td>OOS Reason</td>
			<td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Osreason"/></td>
			</tr>
		</xsl:if>		
		<tr style="font-weight:bold;"><td>Home Station</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/StationName"/></td></tr>	
                <tr style="font-weight:bold;"><td>Shift Start</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/StartShiftDate"/></td></tr>
		<tr style="font-weight:bold;"><td>Shift End</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/EndShiftDate"/></td></tr>
		<xsl:if test="Table[1]/Division!=''">
			<tr style="font-weight:bold;">
			<td>Current Division</td>
			<td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Division"/></td>
			</tr>
		</xsl:if>
		<xsl:if test="Table[1]/Beat!=''">
			<tr style="font-weight:bold;">
			<td>District</td>
			<td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Beat"/></td>
			</tr>
		</xsl:if>		
		<tr style="font-weight:bold;"><td>Current Location</td><td style="background-color:window;color:windowtext;"><xsl:apply-templates select="Table[1]/Current_Location"/></td></tr>  
		<xsl:if test="Table[1]/Destination!=''">
			<tr style="font-weight:bold;">
			<td>Current Destination</td>
			<td style="background-color:window;color:windowtext;"><xsl:value-of select="Table[1]/Destination"/></td>
			</tr>
		</xsl:if>		
		<!--
		<tr style="font-weight:bold;"><td>Current Latitude</td><td style="background-color:window;color:windowtext;"><xsl:apply-templates select="Table[1]/Current_Lat"/></td></tr>  
		<tr style="font-weight:bold;"><td>Current Longitude</td><td style="background-color:window;color:windowtext;"><xsl:apply-templates select="Table[1]/Current_Lon"/></td></tr>  
		-->
        </table>
        
        
        <p> </p>
        <xsl:if test="Table/IncidentNumber!=''">
        <xsl:for-each select="Table">
        	<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;color:white;border-width:1px;border-style:None;">
        		<tr style="font-weight:bold;"><td>Incident</td>
        		<td style="background-color:window;color:windowtext;">
        		<xsl:variable name="v_rec"><xsl:value-of select="IncidentID"/></xsl:variable>
        		<a href="SingleIncidentQuery.aspx?ID={$v_rec}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="IncidentNumber"/></a>
        		</td></tr>
        		<tr style="font-weight:bold;"><td>Status</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="VehicleStatusForIncident"/></td></tr>
        		<tr style="font-weight:bold;"><td>Problem/Nature</td><td style="background-color:window;color:windowtext;"><xsl:value-of select="Problem"/></td></tr>
        		<tr style="font-weight:bold;"><td>Incident Location</td>
        		<td style="background-color:window;color:windowtext;">
        			<!--If Location is specified, diplaying location.Else Address is displayed. -->
        			<xsl:if test="Location_Name!=''">
        				<xsl:value-of select="Location_Name"/>
        			</xsl:if>
        			<xsl:if test="Location_Name=''">
        				<xsl:value-of select="Address"/>
        				<xsl:text> </xsl:text>
        				<xsl:if test="Building!=''">
        					<xsl:text>Bldg: </xsl:text>
        					<xsl:value-of select="Building"/>
        				</xsl:if>
        				<xsl:if test="Apartment!=''">
        					<xsl:text> Apt: </xsl:text>
        					<xsl:value-of select="Apartment"/>
        				</xsl:if>        				
        				<xsl:text> </xsl:text>
        				<xsl:value-of select="City"/>
        				<xsl:text> </xsl:text>
        				<xsl:value-of select="State"/>
        			</xsl:if>				
        		</td></tr>
        	</table>
        	<p> </p>
        </xsl:for-each>
        </xsl:if>
        
</xsl:template> 


</xsl:transform>