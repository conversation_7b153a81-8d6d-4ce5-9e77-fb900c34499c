<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>Mobile Enterprise - Incident Supplement Person</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Supplement Person</H4>
						<P><h3>Person Supplement Info for Incident Number : <xsl:value-of select="/results/exemel/NewDataSet/Table/IncidentNumber"/></h3>

	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
							
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
		
	<table cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;color:white;border-width:1px;border-style:None;">
		<tr style="font-weight:bold;" colspan="3">			
			<xsl:if test="LastName != '' or FirstName != '' or MiddleName != '' or Suffix != ''">				
				<td style="color:windowtext;background-color:window;" colspan="3" >
				<xsl:value-of select="concat(LastName,' ',FirstName, ' ', MiddleName, ' ', Suffix)"/>			
				<xsl:if test="PersonType != ''">
					<xsl:value-of select="concat(' - ', PersonType)"/>
				</xsl:if>
				</td>
			</xsl:if>		
		</tr>
		<tr style="font-weight:bold;">	
			<xsl:if test="Race != ''">
				<td>Race</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Race"/></td>
			</xsl:if>
			<xsl:if test="Gender != ''">
				<td>Gender</td><td style="color:windowtext;background-color:window;" colspan="3" >
				<xsl:value-of select="Gender"/></td>
			</xsl:if>
			<xsl:choose>
				<xsl:when test="Age != 0">
					<td>Age</td><td style="color:windowtext;background-color:window;" colspan="3">
					<xsl:value-of select="Age"/></td>
				</xsl:when>
				<xsl:otherwise>
					<xsl:if test="AgeMin != 0 and AgeMax != 0">
						<td>Age</td><td style="color:windowtext;background-color:window;" colspan="3">
						<xsl:value-of select="concat(string(AgeMin),'-', string(AgeMax))"/>
						</td>
					</xsl:if>
				</xsl:otherwise>
			</xsl:choose>
			<xsl:choose>
				<xsl:when test="Weight != 0">
					<td>Weight</td><td style="color:windowtext;background-color:window;" colspan="3">
					<xsl:value-of select="Weight"/></td>
				</xsl:when>
				<xsl:otherwise>
					<xsl:if test="WeightMin != 0 and WeightMax != 0">
						<td>Weight</td><td style="color:windowtext;background-color:window;" colspan="3" >
						<xsl:value-of select="concat(string(WeightMin),'-',string(WeightMax))"/>
						</td>
					</xsl:if>
				</xsl:otherwise>
			</xsl:choose>				
			<xsl:variable name="apos" select='"&apos;"'/>
			<xsl:choose>
				<xsl:when test="Height != 0">
					<td>Height</td>
					<td style="color:windowtext;background-color:window;" colspan="3">
					<xsl:value-of select="concat(string(floor(Height div 12)),$apos,string(Height mod 12))"/>					
					</td>
				</xsl:when>				
				<xsl:otherwise>
					<xsl:if test="HeightMin != 0 and HeightMax != 0">
						<td>Height</td>
						<td style="color:windowtext;background-color:window;" colspan="3">
						<xsl:value-of select="concat(string(floor(HeightMin div 12)),$apos,string(HeightMin mod 12), '-', string(floor(HeightMax div 12)),$apos,string(HeightMax mod 12)) "/>
						</td>
					</xsl:if>
				</xsl:otherwise>
			</xsl:choose>
		</tr>			
		<tr style="font-weight:bold;">
			<xsl:if test="OperatorLicenseNumber != ''">
				<td>OLN</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="OperatorLicenseNumber"/></td>
			</xsl:if>
			<xsl:if test="LicenseState != ''">
				<td>OLS</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="LicenseState"/></td>
			</xsl:if>			
			<xsl:if test="DateOfBirth != ''">
				<td>DOB</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="substring(DateOfBirth, 1, 10)"/></td>
			</xsl:if>
		</tr>
		<tr style="font-weight:bold;">
			<xsl:if test="SSN != ''">
				<td>SSN</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="SSN"/></td>
			</xsl:if>
			<xsl:if test="Hair != ''">
				<td>Hair</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Hair"/></td>
			</xsl:if>
			<xsl:if test="Eyes != ''">
				<td>Eyes</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Eyes"/></td>
			</xsl:if>
			<xsl:if test="Build != ''">
				<td>Build</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Build"/></td>
			</xsl:if>
			<xsl:if test="Facial != ''">
				<td>Facial</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Facial"/></td>
			</xsl:if>
			<xsl:if test="Glasses != ''">
				<td>Glasses</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Glasses"/></td>
			</xsl:if>		
		</tr>	
		<tr style="font-weight:bold;">
			
			<xsl:if test="Hat != ''">
				<td>Hat</td><td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Hat"/></td>
			</xsl:if>
			<xsl:if test="Jacket != ''">
				<td>Jacket</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Jacket"/></td>
			</xsl:if>		
			<xsl:if test="Shirt != ''">
				<td>Shirt</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Shirt"/></td>
			</xsl:if>
			<xsl:if test="Pant != ''">
				<td>Pant</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Pant"/></td>
			</xsl:if>
			<xsl:if test="Shoes != ''">
				<td>Shoes</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Shoes"/></td>
			</xsl:if>
		</tr>
		<tr style="font-weight:bold;">
			<xsl:if test="Weapon != ''">
				<td>Weapon</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="Weapon"/></td>
			</xsl:if>
			
			<xsl:if test="FlightDirection != ''">
				<td>Flight Direction</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="FlightDirection"/></td>
			</xsl:if>
			<xsl:if test="FlightMode != ''">
				<td>Flight Mode</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="FlightMode"/></td>
			</xsl:if>		
		</tr>			
		<tr style="font-weight:bold;">
			<xsl:if test="AddressStreet != '' or AddressApt != '' or City != '' or State != '' or ZipCode != '' ">
				<td>Address</td>
				<td style="color:windowtext;background-color:window;" colspan="3">
				<xsl:value-of select="concat(AddressStreet,' ',AddressApt , ' ', City, ' ', State, ' ', ZipCode)"/></td>
			</xsl:if>		
			<!--<xsl:if test="AddressStreet != ''">
			<xsl:if test="AddressApt != ''">
				<td>Apt</td><td style="color:windowtext;background-color:window;" colspan="3"><xsl:value-of select="AddressApt"/></td>
			</xsl:if>
			<xsl:if test="City != ''">
				<td>City</td><td style="color:windowtext;background-color:window;" colspan="3"><xsl:value-of select="City"/></td>
			</xsl:if>
			<xsl:if test="State != ''">
				<td>State</td><td style="color:windowtext;background-color:window;" colspan="3"><xsl:value-of select="State"/></td>
			</xsl:if>
			<xsl:if test="ZipCode != ''">
				<td>Zip</td><td style="color:windowtext;background-color:window;" colspan="3"><xsl:value-of select="ZipCode"/></td>
			</xsl:if>-->				
			<xsl:if test="Phone != ''">
				<td>Phone</td><td style="color:windowtext;background-color:window;" colspan="3"><xsl:value-of select="Phone"/></td>
			</xsl:if>
		</tr>
		<tr style="font-weight:bold;">
			<xsl:if test="Characteristics != ''">
				<td>Characteristics</td><td style="color:windowtext;background-color:window;" colspan="3"><xsl:value-of select="Characteristics"/></td>
			</xsl:if>
			<xsl:if test="Comments != ''">
				<td>Comments</td><td style="color:windowtext;background-color:window;" colspan="3"><xsl:value-of select="Comments"/></td>
			</xsl:if>			
		</tr>
	</table>
	<p></p>

</xsl:template> 

</xsl:transform>