<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Station Viewer</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Station Viewer</H4>
						<form action="StationViewer.aspx?queryfile=StationViewer.qry" method="post" id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td>Jurisdiction:</td>
									<td>
										<XML id="sectorstyle" src="sectors.xsl"></XML>
										<SPAN type="selectlist" id="sectorvals" name="sectorvals">
											<XML id="sectorsource" src="sectors.xml"></XML>
										</SPAN>
									</td>
								</tr>
							</table>
							<br>
							<input type="hidden" name="level" id="level">
							<input type="hidden" name="itemid" id="itemid">
							<input type="submit" name="Query" id="Query" value="Query" onclick="setvalue()">
							<br>Note: Select one or more. Select nothing to get all stations.
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
	    
		function window.onload()
		{
			Form.Query.focus();
			PrepareValidation(Form);

			// Note: setting savelast to true causes problems because the onchanged event doesn't get fired
			// and the divisions are out of synch with the secotors.
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			sectorvals.innerHTML = GenerateSelectBox("sector", sectorsource, sectorstyle, false, false, false, 5, true, false);
		}

		function setvalue()
		{
			Form.level.value = 'jurisdiction';
			//Form.level.value = 'sector';
			Form.itemid.value = '';
			var i;
			var values = '';
			var bAll = false;
			if (Form.sector.selectedIndex == -1) bAll = true;
			for (i=0; i<Form.sector.length; i++)
			{
				if (Form.sector.options[i].selected || bAll)
				{
					if (values != '') values += ',';
					values += Form.sector.options[i].value;
				}
			}
			Form.itemid.value = values;
		}

		function sectorchanged()
		{
			// do nothing. Called when a jurisdiction is selected
		}

	</script>
</HTML>
