﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="SchemaMapConfigArcGISRT"
    targetNamespace="http://tritech.com/InformMobile/SchemaMapConfigArcGISRT.xsd"
    elementFormDefault="qualified"
    xmlns="http://tritech.com/InformMobile/SchemaMapConfigArcGISRT.xsd"
    xmlns:mstns="http://tritech.com/InformMobile/SchemaMapConfigArcGISRT.xsd"
    xmlns:xs="http://www.w3.org/2001/XMLSchema"
>
  <xs:element name="MapConfig">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="GeneralConstants" type="GeneralConstants" />
        <xs:element name="DefaultExtent" type="Extent" minOccurs="0" maxOccurs="1" />
        <xs:element name="OverviewMapSettings" minOccurs="0">
          <xs:complexType>
            <xs:sequence>
							<xs:element name="BackgroundColor" type="ColorType" />
							<xs:element name="BorderColor" type="ColorType" minOccurs="0" />
            </xs:sequence>
            <xs:attribute name="PackageName" type="xs:string"/>
            <xs:attribute name="MapVisibilityGroupName" type="xs:string"/>
          </xs:complexType>
        </xs:element>
        <xs:element name="MapItemsInfoBoxSettings" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Opacity" type="xs:float" default="0.90"/>
              <xs:element name="SuppressIdentifyItemsBeyondScale" type="xs:double" default="100000" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name ="AcceleratedDisplayOptions" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:attribute name="MapAcceleratedDisplay" type="mstns:MapAcceleratedDisplayOption" default="DisableAcceleratedDisplay" />
            <!-- The remaining attributes being true are ignored unless MapAcceleratedDisplay is EnableAcceleratedDisplayLayersGroup -->
            <xs:attribute name="DisplayOtherTrackedItemsInAcceleratedDisplayLayersGroup" type="xs:boolean" default="false" />
            <xs:attribute name="DisplayGpsLocationInAcceleratedDisplayLayersGroup" type="xs:boolean" default="false" />
          </xs:complexType>
        </xs:element>
        <xs:element name="ClickedLocationOutputFields">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="OutputFieldName" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:simpleContent>
                    <xs:extension base="xs:string">
                      <xs:attribute name="DataType" use="optional" default="string">
                        <xs:simpleType>
                          <xs:restriction base="xs:string">
                            <xs:enumeration value="string" />
                          </xs:restriction>
                        </xs:simpleType>
                      </xs:attribute>
                    </xs:extension>
                  </xs:simpleContent>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>        
        <xs:element name="GeocodingPackage" minOccurs="1" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ReverseGeocodeToleranceInMeters"  type="xs:double" default="0.0"/>
              <xs:element name="ReverseGeocodeFirstXStreetSearchRadiusMeters" type="xs:double" minOccurs="0" maxOccurs="1" default="402.25" />
              <xs:element name="ReverseGeocodeSecondXStreetSearchRadiusMeters" type="xs:double" minOccurs="0" maxOccurs="1" default="3218" />
              <xs:element name="ReverseGeocodeStreetOutput" type="ReverseGeocodeAddressFields" minOccurs="1" maxOccurs="1"/>
              <xs:element name="ReverseGeocodeZoneOutput" type="ReverseGeocodeAddressFields"     minOccurs="1" maxOccurs="1"/>
              <xs:element name="ReverseGeocodeCrossStreetOutput" type="ReverseGeocodeCrossStreetFields" minOccurs="0" maxOccurs="1" />          
              <xs:element name="FindAddressSimpleOutput" type="xs:string" minOccurs="0" maxOccurs="1"/>
              <xs:element name="FindAddressFromAddressNumberFieldName" type="xs:string" minOccurs="0" maxOccurs="1"/>
              <xs:element name="FindAddressToAddressNumberFieldName" type="xs:string" minOccurs="0" maxOccurs="1"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="UserLayerVisibilityGroups" minOccurs="1" maxOccurs="1">
          <xs:complexType>          
            <xs:sequence>
              <xs:element name="UserLayerVisibilityGroup" minOccurs="1" maxOccurs="unbounded">
                <xs:complexType>
                  <!-- Name is the text for the Configuration_MapButtons.xml ButtonLayerToggle/LayerName." -->
                  <xs:attribute name="Name" type="xs:string" use="required" />
                  <xs:attribute name="InitiallyVisible" type="xs:boolean" use="optional" default="true" />
                  <xs:attribute name="IsBaseMap" type="xs:boolean" use="required" />
                  <xs:attribute name ="MapTrackingDisplay" type="mstns:MapTrackingDisplay" use="optional" default="DisplayRegardless"/>
                  <xs:attribute name ="DayNightModeDisplay" type ="mstns:DayNightModeDisplay" use="optional" default="DisplayOnlyInDayMode" />
                </xs:complexType>
              </xs:element>
            </xs:sequence>
            <xs:attribute name="InitialBaseMapForWhenNotTrackingLocation" type="xs:string" use="required" />
            <xs:attribute name="InitialBaseMapForWhenTrackingLocation" type="xs:string" use="required" />
            <xs:attribute name="InitialBaseMapForWhenNotTrackingLocationInNightMode" type="xs:string" use="required" />
            <xs:attribute name="InitialBaseMapForWhenTrackingLocationInNightMode" type="xs:string" use="required" />
          </xs:complexType>
          <xs:keyref name ="InitialBaseMapForWhenNotTrackingLocationToBaseMapVisibilityGroupKeyRef" refer="BaseMapVisibilityGroupKey" >
            <xs:selector xpath="." />
            <xs:field xpath="@InitialBaseMapForWhenNotTrackingLocation" />
          </xs:keyref>
          <xs:keyref name ="InitialBaseMapForWhenTrackingLocationToBaseMapVisibilityGroupKeyRef" refer="BaseMapVisibilityGroupKey" >
            <xs:selector xpath="." />
            <xs:field xpath="@InitialBaseMapForWhenTrackingLocation" />
          </xs:keyref>
        </xs:element>
        <xs:element name ="DisplayLayers">
          <xs:complexType>
            <!-- TODO: see of keyrefs below can be put into one, but this works as is. -->
            <xs:choice minOccurs="1" maxOccurs="unbounded">
              <xs:element name="ArcGISLocalTiledLayer" type="ArcGISLocalTiledLayerOptions" minOccurs="1" maxOccurs="1">
                <xs:keyref name ="ArcGISLocalTiledLayerToUserLayerVisibilityGroupRef" refer="UserLayerVisibilityGroupKey" >
                  <xs:selector xpath="." />
                  <xs:field xpath="@UserLayerVisibilityGroup" />
                </xs:keyref>
              </xs:element>
              <xs:element name="ArcGISLocalDynamicMapServiceLayer" type ="ArcGISLocalDynamicMapServiceLayerOptions" minOccurs="1" maxOccurs="1" >
                <xs:keyref name ="ArcGISLocalDynamicMapServiceLayerToUserLayerVisibilityGroupRef" refer="UserLayerVisibilityGroupKey" >
                  <xs:selector xpath="." />
                  <xs:field xpath="@UserLayerVisibilityGroupName" />
                </xs:keyref>
              </xs:element>
              <xs:element name="ArcGISLocalFeatureLayer" type ="ArcGISLocalFeatureLayerOptions" minOccurs="1" maxOccurs="1" >
                <xs:keyref name ="ArcGISLocalFeatureLayerToUserLayerVisibilityGroupRef" refer="UserLayerVisibilityGroupKey" >
                  <xs:selector xpath="." />
                  <xs:field xpath="@UserLayerVisibilityGroupName" />
                </xs:keyref>                
              </xs:element>
            </xs:choice>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
    <xs:key name ="UserLayerVisibilityGroupKey" >
      <xs:selector xpath="mstns:UserLayerVisibilityGroups/mstns:UserLayerVisibilityGroup" />
      <xs:field xpath="@Name" />
    </xs:key>
    <xs:key name ="BaseMapVisibilityGroupKey" >
      <!-- TODO: apply filter requiring [@IsBaseMap=true] xsd parser says its not valid. -->
      <xs:selector xpath="mstns:UserLayerVisibilityGroups/mstns:UserLayerVisibilityGroup" />
      <xs:field xpath="@Name" />
    </xs:key>
  </xs:element>
  <xs:complexType name="ColorType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" />
      <xs:element name="Alpha" type="xs:unsignedByte" />
      <xs:element name="Red" type="xs:unsignedByte" />
      <xs:element name="Green" type="xs:unsignedByte" />
      <xs:element name="Blue" type="xs:unsignedByte" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ReverseGeocodeAddressFields">
    <xs:sequence>    
      <xs:element name="Label" type="xs:string" default="" minOccurs="1" maxOccurs="1"/>
      <xs:element name="Pattern" type="xs:string" default="" minOccurs="1" maxOccurs="1"/>      
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ReverseGeocodeCrossStreetFields">
    <xs:complexContent>
      <xs:extension base="ReverseGeocodeAddressFields">
        <xs:sequence>
          <!-- SearchSource, when present will cause the search to happen separate from the geocoder packaage
           on a sub layer in a map pacakage.  This is the only way to do cross streets in as of version 5.6.2
           but it was made optional because if and when Esri provides the capabaility from the geocoder package it will not be needed -->
          <xs:element name="SearchSource" minOccurs="0" maxOccurs="1">
            <xs:complexType>
              <xs:attribute name="MapPackageDisplayName" type="xs:string" use="required" />
              <xs:attribute name ="LayerIdentfierType" type="mstns:LayerIdentfierType" use="required" />
              <!-- (identifies which layer in the local dynamic map service - may be either LayerID or LayerName depending on LayerIdentfierType) -->
              <xs:attribute name="Layer" type="xs:string" use="required" />
              <!-- If this is specified two cross streets will be found - one from each direction separated by this delimeter -->
              <!-- This is meant for experimental use at this time and is optional -->
              <xs:attribute name="DelimiterForTwoCrossStreets" type="xs:string" use="optional" />
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>  
  <xs:complexType name="GeneralConstants">
    <xs:sequence>
      <!-- TODO: Revise for ArcGIS Runtime - remove, modifiy, add?  -->
      <xs:element name="RecenterTolerance" type="xs:float" minOccurs="0" maxOccurs="1"/>
      <xs:element name="RotationTolerance" type="xs:float" minOccurs="0" maxOccurs="1"/>
      <xs:element name="MinAutoZoomMiles" type="xs:float" minOccurs="0" maxOccurs="1"/>
      <!-- Added for ArcGIS Runtime Map -->
      <xs:element name ="GeographicCoordinateSystemWKID" type="xs:int" minOccurs="0" maxOccurs="1" default="4326" />
      <!-- Assumed GCS for lat/lon Defaults to 4326 GCS_WGS_1984 -->
      <!-- Unit and other agency unit opacity -->
      <xs:element name="TrackingLayerOpacity" type="xs:double" minOccurs="0" maxOccurs="1" default="0.65"/>
      <xs:element name="MaximumZoomResolutionRange" type="xs:double" minOccurs="0" maxOccurs="1" />
      <xs:element name="MinimumZoomResolutionRange" type="xs:double" minOccurs="0" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name ="MapAcceleratedDisplayOption">
    <xs:restriction base="xs:string">
      <xs:enumeration value="DisableAcceleratedDisplay" />
      <xs:enumeration value="UseAcceleratedDisplayForEntireMap" />
      <xs:enumeration value="EnableAcceleratedDisplayLayersGroup" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="LinearUnit">
    <xs:restriction base="xs:string">
      <xs:enumeration value="StatuteMile" />
      <xs:enumeration value="Kilometer" />
      <!-- Add as other units are supported -->
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="Extent">
    <xs:choice minOccurs="1" maxOccurs="1">
      <!-- GeographicCircle allows setting the map extent to fit a circle defined by latitude, longitude and radius -->
      <xs:element name="GeographicCircle" minOccurs="1" maxOccurs="1">
        <xs:complexType>
          <xs:attribute name="RadiusUnits" type="LinearUnit" use="required" />
          <xs:attribute name ="CenterLatitude" type="xs:double" use="required" />
          <xs:attribute name ="CenterLongitude" type ="xs:double" use="required" />
          <xs:attribute name ="Radius" type ="xs:double" use ="required" />
        </xs:complexType>
      </xs:element>
      <!-- MapEnvelop X/Y values are in map units.  Allows setting the extent in the coordinates of the map. -->
      <xs:element name="MapEnvelope" minOccurs="1" maxOccurs="1">
        <xs:complexType>
          <xs:attribute name="XMin" type="xs:double" use="required" />
          <xs:attribute name="YMin" type="xs:double" use="required" />
          <xs:attribute name="XMax" type="xs:double" use="required" />
          <xs:attribute name="YMax" type="xs:double" use="required" />
        </xs:complexType>
      </xs:element>
    </xs:choice>
  </xs:complexType>
  <xs:complexType name="ClickInfoDisplayFields">
    <xs:sequence>
      <xs:element name="ClickInfoDisplayField" type="ClickInfoField" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="ignoreWhenNotVisible" type="xs:boolean" use="required" />
  </xs:complexType>
  <xs:complexType name="ClickInfoField">
    <xs:sequence>
      <xs:element name="DisplayLabel" type="xs:string" />
      <xs:element name="WhenContentEmpty">
        <xs:complexType>
          <xs:simpleContent>
            <xs:extension base="xs:string">
              <xs:attribute name="action" use="optional" default="DisplayOnlyLabel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:enumeration value="DisplayDefault" />
                    <xs:enumeration value="SuppressLabel" />
                    <xs:enumeration value="DisplayOnlyLabel" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:attribute>
            </xs:extension>
          </xs:simpleContent>
        </xs:complexType>
      </xs:element>
      <xs:element name="DisplayContent">
        <xs:complexType>
          <xs:sequence>
            <xs:choice maxOccurs="unbounded">
              <xs:element name="Literal" type="ContentLiteral" />
              <xs:element name="Separator" type="ContentSeparator" />
              <xs:element name="Field" type="ContentField" />
              <xs:element name="NearestSideField" type="ContentNearestSideField" />
              <xs:element name="MinNum" type="ContentMinNumField" />
              <xs:element name="MaxNum" type="ContentMaxNumField" />
              <xs:element name="UniqueFieldListValues" type="ContentUniqueFieldsValuesList" />
              <xs:element name="LineRange" type="ContentLineRange" />
              <xs:element name="Group" type="ContentGroup" />
              <xs:element name="Space" type="ContentSpace" />
            </xs:choice>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
    <xs:attribute name="clickedLocationDisplayLabel" type="xs:string" use="optional" />
    <xs:attribute name="clickedLocationFallBack" use="optional" default="0">
      <xs:simpleType>
        <xs:restriction base="xs:unsignedByte">
          <xs:minInclusive value="0" />
          <xs:maxInclusive value="5" />
        </xs:restriction>
      </xs:simpleType>
    </xs:attribute>
    <xs:attribute name="displayLayerItem" type="xs:boolean" use="optional" default="true" />
  </xs:complexType>
  <xs:complexType name="ContentField">
    <xs:attribute name="tblField" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="ContentNearestSideField">
    <xs:attribute name="leftTblField" type="xs:string" />
    <xs:attribute name="rightTblField" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="ContentUniqueFieldsValuesList">
    <xs:sequence>
      <xs:element name="Field" type="ContentField" maxOccurs="unbounded" />
    </xs:sequence>
    <xs:attribute name="fieldSeparator" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="ContentMinNumField">
    <xs:sequence>
      <xs:choice maxOccurs="unbounded">
        <xs:element name="Field" type="ContentField" maxOccurs="unbounded" />
        <xs:element name="Number" type="xs:double" />
      </xs:choice>
    </xs:sequence>
    <xs:attribute name="ignoreZero" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="ignoreBelow" type="xs:double" use="optional" />
    <xs:attribute name="ignoreAbove" type="xs:double" use="optional" />
  </xs:complexType>
  <xs:complexType name="ContentMaxNumField">
    <xs:sequence>
      <xs:choice maxOccurs="unbounded">
        <xs:element name="Field" type="ContentField" maxOccurs="unbounded" />
        <xs:element name="Number" type="xs:double" />
      </xs:choice>
    </xs:sequence>
    <xs:attribute name="ignoreZero" type="xs:boolean" use="optional" default="false" />
    <xs:attribute name="ignoreBelow" type="xs:double" use="optional" />
    <xs:attribute name="ignoreAbove" type="xs:double" use="optional" />
  </xs:complexType>
  <xs:complexType name="ContentLineRange">
    <xs:sequence>
      <xs:choice maxOccurs="unbounded">
        <xs:element name="MinRange" type="ContentMinRange" />
        <xs:element name="MaxRange" type="ContentMaxRange" />
        <xs:element name="MinNearestSide" type="ContentMinNearestSide" />
        <xs:element name="MaxNearestSide" type="ContentMaxNearestSide" />
        <xs:element name="Literal" type="ContentLiteral" />
        <xs:element name="Separator" type="ContentSeparator" />
        <xs:element name="Space" type="ContentSpace" />
      </xs:choice>
    </xs:sequence>
    <xs:attribute name="leftFromTblField" type="xs:string" use="required" />
    <xs:attribute name="rightFromTblField" type="xs:string" use="required" />
    <xs:attribute name="leftToTblField" type="xs:string" use="required" />
    <xs:attribute name="rightToTblField" type="xs:string" use="required" />
  </xs:complexType>
  <xs:complexType name="ContentGroup">
    <xs:sequence>
      <xs:choice maxOccurs="unbounded">
        <xs:element name="Literal" type="ContentLiteral" />
        <xs:element name="Separator" type="ContentSeparator" />
        <xs:element name="Field" type="ContentField" />
        <xs:element name="NearestSideField" type="ContentNearestSideField" />
        <xs:element name="MinNum" type="ContentMinNumField" />
        <xs:element name="MaxNum" type="ContentMaxNumField" />
        <xs:element name="UniqueFieldListValues" type="ContentUniqueFieldsValuesList" />
        <xs:element name="LineRange" type="ContentLineRange" />
        <xs:element name="Space" type="ContentSpace" />
      </xs:choice>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ContentSeparator">
    <xs:attribute name="text" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="ContentLiteral">
    <xs:simpleContent>
      <xs:extension base="xs:string" />
    </xs:simpleContent>
  </xs:complexType>
  <xs:simpleType name="MapTrackingDisplay">
    <xs:restriction base="xs:string">
      <xs:enumeration value="DisplayRegardless" />
      <xs:enumeration value="EnableOnlyWhenTrackingLocation" />
      <xs:enumeration value="DisabledWhenTrackingLocation" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="DayNightModeDisplay">
    <xs:restriction base="xs:string">
      <xs:enumeration value="DisplayRegardless" />
      <xs:enumeration value="DisplayOnlyInDayMode" />
      <xs:enumeration value="DisplayOnlyInNightMode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="BaseMapsToDisplayWith">
    <xs:sequence>
      <!-- ENH: Enhancement would be to add key validation that BaseMap Names in this list
           come from UserLayerVisibilityGroups elements flagged IsBaseMap=true-->
      <xs:element name="BaseMap" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:attribute name="Name" type="xs:string" use="required" />
          <xs:attribute name="MapTrackingDisplay" type="mstns:MapTrackingDisplay" use="optional" default="DisplayRegardless" />
          <xs:attribute name="DayNightModeDisplay" type="mstns:DayNightModeDisplay" use="optional" default="DisplayRegardless" />
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="LayerOptions">
    <xs:sequence>
      <xs:element name="BaseMapsToDisplayWith" type="mstns:BaseMapsToDisplayWith" minOccurs="0" maxOccurs="1"/>
      <xs:element name="ClickInfoDisplayFields" type="ClickInfoDisplayFields" minOccurs="0" />
    </xs:sequence>
    <xs:attribute name ="LayerIdentfierType" type="mstns:LayerIdentfierType" use="required" />
    <!-- (identifies which layer in the local dynamic map service - may be either LayerID or LayerName depending on LayerIdentfierType) -->
    <xs:attribute name="Layer" type="xs:string" use="required" />
    <!-- (the text for the Configuration_MapButtons.xml ButtonLayerToggle/LayerName)-->    
    <xs:attribute name ="UserLayerVisibilityGroupName" type="xs:string" use="optional" />
    <xs:attribute name ="DisplayName" type="xs:string" use="required" />
  </xs:complexType>
  
  <xs:complexType name="ShapefileOptionsBase">
    <xs:sequence>
      <xs:element name="ShapefileName" type="xs:string"  minOccurs="1" maxOccurs="1" />
    </xs:sequence> 
  </xs:complexType>

  <xs:complexType name="PolylineShapefileOptions">
    <xs:complexContent>
      <xs:extension base="ShapefileOptionsBase">
        <xs:sequence>
          <xs:element name="lineColor" type="ColorType"  minOccurs="1" maxOccurs="1" />
          <xs:element name="lineSize" type="xs:int"  minOccurs="1" maxOccurs="1" />
          <xs:element name="lineStyle" type="xs:string"  minOccurs="1" maxOccurs="1" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="PolygonShapefileOptions">
    <xs:complexContent>
      <xs:extension base="ShapefileOptionsBase">
        <xs:sequence>
          <xs:element name="FillColor" type="ColorType"  minOccurs="1" maxOccurs="1" />
          <xs:element name="FillStyle" type="xs:string"  minOccurs="1" maxOccurs="1" />
          <xs:element name="DrawOutline" type="xs:boolean" minOccurs="1" maxOccurs="1" />
          <xs:element name="OutlineSize" type="xs:int"  minOccurs="1" maxOccurs="1" />
          <xs:element name="OutlineColor" type="ColorType"  minOccurs="1" maxOccurs="1" />          
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MarkerShapefileOptions">
    <xs:complexContent>
      <xs:extension base="ShapefileOptionsBase">
        <xs:sequence>
          <xs:element name="MarkerColor" type="ColorType"  minOccurs="1" maxOccurs="1"/>
          <xs:element name="MarkerSize" type="xs:int"  minOccurs="1" maxOccurs="1"/>
          <xs:element name="MarkerStyle" type="xs:string"  minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ShapefilesWithOptions">
    <xs:choice minOccurs="1" maxOccurs="unbounded">
      <xs:element name="PolylineShapefile" type="PolylineShapefileOptions" minOccurs="1" maxOccurs="1"/>
      <xs:element name="PolygonShapefile" type="PolygonShapefileOptions" minOccurs="1" maxOccurs="1"/>
      <xs:element name="MarkerShapefile" type="MarkerShapefileOptions" minOccurs="1" maxOccurs="1"/>
    </xs:choice>
    <xs:attribute name="DirectoryPath" type="xs:string" use="required" />
  </xs:complexType>
  <xs:complexType name="ContentSpace" />
  <xs:complexType name="ContentMinRange" />
  <xs:complexType name="ContentMaxRange" />
  <xs:complexType name="ContentMinNearestSide" />
  <xs:complexType name="ContentMaxNearestSide" />
  <xs:complexType name="ArcGISLocalDataLayerOptionsBase">
    <xs:sequence>
      <xs:element name="BaseMapsToDisplayWith" type="mstns:BaseMapsToDisplayWith" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
    <xs:attribute name="ID" type="xs:string" use="required" />
    <xs:attribute name="DisplayName" type="xs:string" use="required" />
    <xs:attribute name="Path" type="xs:string" use="required" />
    <xs:attribute name="Opacity" type="xs:double" use="optional" default="1" />
    <!--(map units per pixel) -->
    <xs:attribute name="MinimumResolution" type="xs:double" use="optional" />
    <!--(map units per pixel) -->
    <xs:attribute name="MaximumResolution" type="xs:double" use="optional" />
    <xs:attribute name="ShowLegend" type="xs:boolean" use="optional" default="true" />
    <xs:attribute name="UserLayerVisibilityGroupName" type="xs:string" use="optional" />
    <xs:attribute name="DisplayInAcceleratedDisplayLayersGroup" type="xs:boolean" use="optional" default="false" />
  </xs:complexType>
  <xs:complexType name="ArcGISLocalTiledLayerOptions">
    <xs:complexContent>
      <xs:extension base="ArcGISLocalDataLayerOptionsBase">
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArcGISLocalDynamicMapServiceLayerOptions">
    <xs:complexContent>
      <xs:extension base="ArcGISLocalDataLayerOptionsBase">
        <xs:sequence>
          <xs:element name="LayersWithOptions" minOccurs="0" maxOccurs="1">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="LayerOptions" minOccurs="1" maxOccurs="unbounded" type="mstns:LayerOptions" />
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="ShapefileOptions" minOccurs="0" maxOccurs="1" type="ShapefilesWithOptions"/>         
        </xs:sequence>
        <xs:attribute name="EnableDynamicLayers" type="xs:boolean" use="optional" default="false" />        
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="LayerIdentfierType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="LayerID" />
      <xs:enumeration value="LayerName" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="ArcGISLocalFeatureLayerOptions">
    <xs:complexContent>
      <xs:extension base ="ArcGISLocalDataLayerOptionsBase">
        <xs:sequence>
          <xs:element name="ClickInfoDisplayFields" type="ClickInfoDisplayFields" minOccurs="0" />
        </xs:sequence>
        <xs:attribute name="LayerIdentfierType" type="mstns:LayerIdentfierType" use="required" />
        <xs:attribute name="Layer" type="xs:string" use="required" /> <!-- (identifies which layer in the local dynamic map service - may be either LayerID or LayerName depending on LayerIdentfierType) -->
        <xs:attribute name="OutFields" type="xs:string" use="optional" />
        <xs:attribute name="Filter" type="xs:string" use="optional" /> <!-- {Where property} -->
        <xs:attribute name="Editable" type="xs:boolean" use ="optional" default="false" />
        <xs:attribute name="AutoSave" type="xs:boolean" use="optional" default="true" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
</xs:schema>
