document.addEventListener('click', e => {

	var item = e.target;
	if (item && item.nodeName == "IMG")
	{
		if (item.parentElement && item.parentElement.nodeName == "A")
		{
			item = item.parentElement;
		}
		else
		{
			item = null;		
		}
	}
	if (item)
	{
		var result = {
			_method: 'documentClicked',
			id: item.id,
			nodeName: item.nodeName,
			attributes: {}
		};
		
		for (var i=0; i < item.attributes.length; i++)
		{
			result.attributes[item.attributes[i].nodeName] = item.attributes[i].nodeValue;
		}
		
		invokeCSharp(JSON.stringify(result));
	}
});

$(document).ready(function () {

	$('form').first().submit(function () {
		$("input").each(function (e) {
			$(this).attr('name', $(this).attr('id'));
		});

		var values = $(this).serialize();
		SubmitQuery(values, $(this).attr('action'));
	});
});

