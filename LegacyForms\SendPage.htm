<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Send Page</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Send Page</H4>
						<form action="SendPage.aspx?queryfile=SendPage.qry" method="post" id="Form" name="Form">
							<table>
								<tr>
									<td valign="top" colspan=2>Personnel Name(s)</td>
									<input type="hidden" name="PersonnelList" id="PersonnelList">
								</tr>
								<tr>
									<td colspan=2><table><tr>
									<td valign="top">Unselected
										<br>
										<XML id="PersonnelWithPagerstyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="PersonnelWithPagervals" name="PersonnelWithPagervals">
											<XML id="PersonnelWithPagersource" src="PersonnelWithPager.xml"></XML>
										</SPAN>
									</td>
									<td valign="top">Selected
										<br>
										<SELECT ID="Selected" NAME="Selected" SIZE="4" style="width:320">
										</SELECT>
									</td>
									</tr></table></td>
								</tr>
								<tr>
									<td/>
									<td>
										<input type="button" value="  ->  " onclick="MoveItems( Form.Unselected, Form.Selected)">
									</td>
								</tr>
								<tr>
									<td/>
									<td>
										<input type="button" value="  <-  " onclick="MoveItems( Form.Selected, Form.Unselected)">
									</td>
								</tr>

								<tr>
									<td valign="top">Units</td>
									<td><input type="text" name="Units" id="Units" maxlength = 100></td>
								</tr>
								<tr>
									<td valign="top">Pagers</td>
									<td><input type="text" name="Pagers" id="Pagers" maxlength = 100></td>
								</tr>
								<tr>
									<td colspan=2>&nbsp;&nbsp;(Separate Units and Pagers with commas)</td>
								</tr>
								<tr>
									<td valign="top" colspan=2><b>Message:</b></td>
								</tr>
								<tr>
									<td colspan=2>
										<textarea style="width:500px;" id="Message" name="Message" rows="4" onkeyup="CheckTextAreaLength(this.form.Message,200);"></textarea>
									</td>
								</tr>
							</table>
							<br>
							<input type="hidden" name="SenderUnit" id="SenderUnit">
		<input type="button" name="Query" id="Query" value="Send" onkeypress="validatepage()" onclick="validatepage()">
		<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
        <script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
		// Move items between the selected and unselected lists
		function MoveItems( From, To)
		{
			// move items from listbox to listbox
			var i = From.selectedIndex;
			if (i == -1) return;
			var sItem = From.options[i].text;
			var oOption = document.createElement("OPTION");
			oOption.text = sItem;
			oOption.value = From.options[i].value;
			From.remove(i);
			// add the item into the right place (to keep the list sorted)
			var oOptions = To.options;
			var iLength = oOptions.length;
			for (i = 0; i < iLength; i++)
			{
				if (oOptions[i].text > sItem) break;
			}
			To.add( oOption, i);
		}

		// Conevert Selected list into a comma separated value
		function GetSelected()
		{
			var sSelectedList = "";
			var oSelectedOptions = Form.Selected.options;
			var iSelectedLength = oSelectedOptions.length;
			for ( i=0; i<iSelectedLength; i++)
			{
				if (sSelectedList != "") sSelectedList += ",";
				sSelectedList += oSelectedOptions[i].value;
			}
			return sSelectedList;
		}

		function window.validatepage()
		{
			Form.PersonnelList.value = GetSelected();
			if (Form.PersonnelList.value == "" && Form.Units.value == "" && Form.Pagers.value == "")
			{
				Form.Units.focus();
				alert('Please enter a recipient.');
			}
			else if (Form.Message.value == "")
			{
				Form.Message.focus();
				alert('Please enter a Message.');
			}
			else if (Form.Message.value.length > 500)
			{
				Form.Message.focus();
				alert('max Message length is 500.');
			}
			else
			{
				Form.Submit.click();
			}
		}
		function window.onload()
		{
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.Units.focus();
			PrepareValidation(Form);
			PersonnelWithPagervals.innerHTML = GenerateSelectBox("Unselected", PersonnelWithPagersource, PersonnelWithPagerstyle, false, false, true, 4, true, false);
			Form.Unselected.multiple = false;
			Form.Unselected.style.width = 300;
		}
	</SCRIPT>
</HTML>
