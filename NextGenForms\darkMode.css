﻿/*Region NewForms*/

B {
    color: #BFC3C9;
}

input, input[type="password"],input[type="text"],textarea {
    color: #BFC3C9;
    border: 1px solid #303846;
    background: #040910;
}

.select-wrapper + label {
    color: #9FA3AB !important;
}

input:hover {
    background: #151e2e;
}

select {
    background: #2964a6;
    color: #fcfaf3;
}
option {
    background: #040910;
    color: #BFC3C9;
}
option:hover {
    background: #151e2e;
    color: #BFC3C9;
}

/* width */
::-webkit-scrollbar {
    background: #050d1a;
}

/* Track */
::-webkit-scrollbar-track {
    background: #050d1a;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #111b2b;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #192231;
}

H2 {
    FONT-SIZE: 24px;
    COLOR: #BFC3C9;
    FONT-FAMILY: Verdana;
}

H3 {
    COLOR: #BFC3C9;
}

H4 {
    COLOR: #BFC3C9;
}

H5 {
    COLOR: #BFC3C9;
}

BODY {
    COLOR: #BFC3C9;
    background: #040910;
}

HR {
    COLOR: #BFC3C9;
}

.base {
    BORDER-RIGHT: #050D1A thin solid;
    BORDER-TOP: #050D1A thin solid;
    BORDER-LEFT: #050D1A thin solid;
    BORDER-BOTTOM: #050D1A thin solid;
}

.topbottombordercell {
    BORDER-RIGHT: #050D1A;
    BORDER-TOP: #050D1A 1px solid;
    BORDER-LEFT: #050D1A;
    COLOR: #050D1A;
    BORDER-BOTTOM: #050D1A 1px solid;
}

.bottombordercell {
    BORDER-BOTTOM: #050D1A thin solid;
}

.bottombordercell img {
    width: 0;
    height: 0;
    padding: 76px 0 0 444px;
    background: url(logo.png) no-repeat;
    background-size: 444px 76px
}

.bottomborderpanel {
    BORDER-RIGHT: #050D1A 1px;
    BORDER-TOP: #050D1A 1px;
    BORDER-LEFT: #050D1A 1px;
    BORDER-BOTTOM: #050D1A 1px solid;
}

.topbordercell {
    BORDER-TOP: #050D1A thin solid;
}

.rightbordercell {
    BORDER-RIGHT: #050D1A 1px solid;
}

.leftbordercell {
    BORDER-LEFT: #050D1A 1px solid;
}

.presentationdelimiter {
    color: #ccffff;
}


BODY TD .title {
    COLOR: #FFBFC3C9;
}


BODY TD .InputNames {
    COLOR: #FFBFC3C9;
}


TABLE {
    COLOR: #BFC3C9;
    background-color: #040910;
}

HR {
    COLOR: #BFC3C9;
}

.base {
    BORDER-RIGHT: #050D1A thin solid;
    BORDER-TOP: #050D1A thin solid;
    BORDER-LEFT: #050D1A thin solid;
    BORDER-BOTTOM: #050D1A thin solid;
    BACKGROUND-COLOR: #040910;
}

.topbottombordercell {
    BORDER-RIGHT: #050D1A;
    BORDER-TOP: #050D1A 1px solid;
    BORDER-LEFT: #050D1A;
    COLOR: #BFC3C9;
    BORDER-BOTTOM: #050D1A 1px solid;
}

.bottombordercell {
    BORDER-BOTTOM: #050D1A thin solid;
    BACKGROUND-COLOR: #040910;
}

.bottomborderpanel {
    BORDER-RIGHT: #050D1A 1px;
    BORDER-TOP: #050D1A 1px;
    BORDER-LEFT: #050D1A 1px;
    BORDER-BOTTOM: #050D1A 1px solid;
    BACKGROUND-COLOR: #040910;
}

.topbordercell {
    BORDER-TOP: #050D1A thin solid;
    BACKGROUND-COLOR: #040910;
}

.rightbordercell {
    BORDER-RIGHT: #050D1A 1px solid
}

.leftbordercell {
    BORDER-LEFT: #050D1A 1px solid
}

.presentationdelimiter {
    color: #050D1A
}



.tableStyle1 {
    border-color: #050D1A;
}

.trHeader {
    border-color: #050D1A;
    background-color: #040910;
    color: #BFC3C9;
}

.trSubHeader td {
    border-bottom: 1px solid #050D1A;
}

font {
    color: #BFC3C9;
}

a {
    color: #3B90EE;
}

/*EndRegion NewForms*/

html, body {
    COLOR: #BFC3C9;
    background: #040910;
}

.comment {
    border-left: 1px solid #303846;
    background: #020406 !important;
}

.input-field textarea:focus + label { 
    color: #1066C7 !important;
}

.input-field textarea:focus {
    border-bottom: 1px solid #303846 !important;
    box-shadow: 0 1px 0 0 #303846 !important
}

input[type=text], input[type=password] {
    COLOR: #BFC3C9 !important;
}

.btn {
    background-color: #3b90ee !important;
    color: white !important;
}

.btn.disabled {
    background-color: #040910 !important;
    color: #5f6670 !important
}

.header {
    border-bottom: 1px solid #19191a;
}

.input-field input:focus + label {
    color: #1066C7 !important;
}

.input-field input:focus {
    border-bottom: 1px solid #303846 !important;
    box-shadow: 0 1px 0 0 #303846 !important;
}

.dropdown-content.select-dropdown li span {
    color: #1066C7;
}

.dropdown-content li > a, .dropdown-content li > span {
    color: White !important;
}

.dropdown-content.select-dropdown li.active span {
    color: #1066C7;
}

.dropdown-content.select-dropdown li:hover {
    background: #0C1626;
}

.with-gap[type="radio"]:checked + span:after {
    background-color: #303846 !important;
    border-color: #303846 !important;
}

.with-gap[type="radio"]:checked + span:before {
    border-color: #303846 !important;
}

table {
    border: thin solid gray !important;
    background-color: #040910 !important;
}

    table.highlight tbody tr:hover {
        background-color: #192231;
    }

    table.highlight {
        background-color: white;
    }

    table thead {
        color: #9FA3AB !important;
        border-bottom: thin solid gray !important;
    }

tr:nth-child(even) {
    background-color: #071021;
}
.tabs {
    background-color: #040910 !important;
}

.tabs .tab a {
    color: #3b90ee !important;
    background-color: #050d1a !important;
    /*Custom Text Color*/
}

.tabs .tab a:hover {
    color: #3b90ee !important;
    /*Custom Color On Hover*/
}

.tabs .tab a:focus.active {
    color: #3b90ee !important;
    /*Custom Text Color While Active*/
}

.tabs .indicator {
    background-color: #1066C7;
}

.selected, .selected:hover {
    background-color: #1066C7 !important;
    color: white !important;
}

input:disabled {
    color: #BFC3C9 !important;
}

    input:disabled + label {
        color: #BFC3C9 !important;
}

input:disabled.autocomplete, input[type=text]:disabled {
    border-bottom: 1px dotted #BFC3C9 !important;
}

.select-wrapper .caret {
    fill: white;
}

.select-wrapper.disabled .caret {
    fill: #BFC3C9;
}

.dropdown-content {
    color: #3b90ee !important;
    background-color: #050d1a !important;
}

.checkbox-color[type="checkbox"].filled-in:checked + label:after {
    border: 2px solid #3f84d2 !important;
    background-color: #3f84d2 !important;
}

[type="checkbox"]:checked + span:not(.lever):before {
    border-right: 2px solid white !important;
    border-bottom: 2px solid white !important;
} 

.autoSubmitErrorMessage {
    color: #BFC3C9 !important;
}

.icon-search {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23b0b4b9'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
}

.icon-search-blue {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233583d8'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
}
.ac-dropdown li > a {
    color: #3b90ee !important;
    background-color: #050d1a !important;
}

.ac-dropdown li > a:hover {
    color: #3b90ee !important;
    background-color: #0C1626 !important;
}

.modal .modal-header.warning {
    background-color: #b98700 !important;
}

.modal .modal-header.warning h5 {
    color: #212e40 !important;
}

.modal, .modal-content {
    background-color: #040910 !important;
}

.modal, .modal-footer {
    background: #040910 !important;
}
.subheader {
    color: #BFC3C9;
    background-color: #040910;
}
.switch label {
    color: #BFC3C9;
}

.switch label input[type=checkbox]:checked + .lever:after {
    background-color: #3b90ee;
}

.btn-flat {
    color: #bfc3c9 !important;
    border-color: #303846 !important;
}

.btn-flat.disabled {
    background-color: #020406 !important;
    border: none;
}
.btn-flat i {
    background-color: #a7abb0;
}
.btn-flat.disabled i {
    background-color: #2a323e;
}

.border-left {
    border-left: 1px solid #303846;
}

#divert_modal_msg {
    color: #caced3 !important;
}

.Flex-Form-Diverts {
    border-left-color:  #303846 !important;
}

#divertLocationRow {
    border-bottom-color: #000000 !important;
}

#divertLocationName {
    color: #192231 !important;
    background-color: #b28800 !important;
}

#divertLocationRow > #proceedButton {
    color: #192231 !important;
    background-color: #b9941a !important;
}

#divertLocationRow > #proceedButton > i {
    background-color: transparent !important;
}

#divertChipsRow {
    background-color: #000000 !important;
    border-bottom-color: #303846 !important;
}

#divertChipsRow .chip {
    border-color: #bfc3c9 !important;
    background-color: #000000 !important;
    color: #bfc3c9 !important;
}

#divertListRow > .row {
    border-bottom-color: #303846 !important;
}

#divertListRow .divertHeader, #divertChipsRow > div:first-child, #divertListRow .divertLabel {
    color: #5f6670 !important;
}

#divertListRow .icon-clock {
    background-color: #5f6670 !important;
}

#divertListRow .divertValue, #divertListRow .divertComments {
    color: #caced3 !important;
}

