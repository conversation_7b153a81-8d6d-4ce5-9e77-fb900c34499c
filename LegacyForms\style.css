A:link {
    font-weight: bold;
    font-size: 12pt;
    font-family: Verdana;
    text-decoration: underline;
}

A:visited {
    font-weight: bold;
    font-size: 12pt;
    font-family: Verdana;
    text-decoration: underline;
}

A:hover {
    font-weight: bold;
    font-size: 12pt;
    font-family: Verdana;
    text-decoration: underline;
}

B {
    font-weight: bold;
    font-size: 12pt;
    color: #BFC3C9;
}

H2 {
    FONT-SIZE: 16pt;
    COLOR: sienna;
    FONT-FAMILY: Verdana
}

H3 {
    BORDER-RIGHT: medium none;
    PADDING-RIGHT: 2px;
    BORDER-TOP: medium none;
    PADDING-LEFT: 2px;
    FONT-SIZE: 14pt;
    PADDING-BOTTOM: 3px;
    TEXT-TRANSFORM: capitalize;
    BORDER-LEFT: medium none;
    COLOR: darkblue;
    PADDING-TOP: 3px;
    BORDER-BOTTOM: blue 1px solid;
    FONT-FAMILY: Verdana;
    TEXT-ALIGN: left
}

H4 {
    BORDER-RIGHT: medium none;
    BORDER-TOP: medium none;
    FONT-SIZE: 13pt;
    BORDER-LEFT: medium none;
    COLOR: blue;
    BORDER-BOTTOM: medium none;
    FONT-FAMILY: Verdana
}

H5 {
    FONT-SIZE: 13pt;
    COLOR: darkgoldenrod;
    FONT-FAMILY: Verdana
}

BODY {
    FONT-SIZE: 12pt;
    COLOR: #BFC3C9;
    FONT-FAMILY: Verdana;
    BACKGROUND-COLOR: #040910
}

LI {
    LIST-STYLE-TYPE: square
}

TABLE {
    FONT-SIZE: 12pt;
    FONT-FAMILY: Verdana;
    BACKGROUND-COLOR: #040910;
    color: #BFC3C9
}

HR {
    BORDER-RIGHT: medium none;
    BORDER-TOP: medium none;
    FONT-SIZE: 1px;
    FLOAT: left;
    BORDER-LEFT: medium none;
    COLOR: blue;
    BORDER-BOTTOM: medium none;
    POSITION: relative;
    HEIGHT: 1px
}

.base {
    BORDER-RIGHT: blue thin solid;
    PADDING-RIGHT: 10px;
    BORDER-TOP: blue thin solid;
    PADDING-LEFT: 10px;
    PADDING-BOTTOM: 10px;
    MARGIN: 10px;
    BORDER-LEFT: blue thin solid;
    PADDING-TOP: 10px;
    BORDER-BOTTOM: blue thin solid;
    BACKGROUND-COLOR: #040910
}

.rightaligncell {
    PADDING-RIGHT: 2px;
    PADDING-LEFT: 2px;
    FONT-WEIGHT: bold;
    PADDING-BOTTOM: 2px;
    PADDING-TOP: 2px;
    TEXT-ALIGN: right
}

.topbottombordercell {
    BORDER-RIGHT: blue;
    PADDING-RIGHT: 2px;
    BORDER-TOP: blue 1px solid;
    PADDING-LEFT: 2px;
    PADDING-BOTTOM: 2px;
    BORDER-LEFT: blue;
    COLOR: blue;
    PADDING-TOP: 2px;
    BORDER-BOTTOM: blue 1px solid;
    TEXT-ALIGN: right
}

.bottombordercell {
    PADDING-RIGHT: 2px;
    PADDING-LEFT: 2px;
    PADDING-BOTTOM: 2px;
    PADDING-TOP: 2px;
    BORDER-BOTTOM: blue thin solid;
    BACKGROUND-COLOR: #ccffff
}

.bottomborderpanel {
    BORDER-RIGHT: blue 1px;
    PADDING-RIGHT: 5px;
    BORDER-TOP: blue 1px;
    PADDING-LEFT: 5px;
    PADDING-BOTTOM: 10px;
    BORDER-LEFT: blue 1px;
    PADDING-TOP: 5px;
    BORDER-BOTTOM: blue 1px solid;
    BACKGROUND-COLOR: white;
    TEXT-ALIGN: left
}

.topbordercell {
    PADDING-RIGHT: 10px;
    BORDER-TOP: blue thin solid;
    PADDING-LEFT: 10px;
    PADDING-BOTTOM: 10px;
    PADDING-TOP: 10px;
    BACKGROUND-COLOR: #ccffff;
    TEXT-ALIGN: center
}

.rightbordercell {
    BORDER-RIGHT: blue 1px solid
}

.leftbordercell {
    BORDER-LEFT: blue 1px solid
}

.message {
    FONT-WEIGHT: bolder;
    FONT-SIZE: 12pt;
    TEXT-ALIGN: center
}

.presentationdelimiter {
    color: #ccffff
}

.xslsubheading {
    font-size: 12pt;
}

.tableStyle1 {
    border-collapse: collapse;
    border-color: #000000;
    border-width: 0px;
    padding: 3px;
    width: 100%;
}

    .tableStyle1 tr {
        vertical-align: top;
    }

.trHeader {
    border-style: solid;
    border-width: 1px;
    border-color: #000000;
    background-color: #0000C0;
    color: #FFFFFF;
    font-weight: bold;
}

.trSubHeader td {
    border-bottom: 1px solid #000000;
    font-weight: bold;
}
