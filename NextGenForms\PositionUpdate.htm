﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - PositionUpdate</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.min.css" />

    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />

    <link rel="stylesheet" type="text/css" href="icons.css" />

    <script src="jquery.min.js"></script>
    <!-- Compiled and minified JavaScript -->
    <script src="materialize.min.js"></script>

    <script src="clientutilities.js"></script>

    <script src="GeoValidate.js"></script>

    <script language="javascript">
        $(document).ready(function () {
            //Set up Tab control
            $('ul.tabs').tabs();
            $('ul.tabs').tabs('select_tab', $("#locationtype").val());

            //handle form submition
            $("#Form").submit(function () {
                if (ValidateLocationInformation($("#locationtype"), null, null, $("#lat"), $("#long"), $("#Address")) && $(this)[0].checkValidity() == true) {
                    $(':disabled').each(function (e) {
                        $(this).removeAttr('disabled');
                    })

                    if ($("#locationtype").val() == "selectedlocation") {
                        $("#Address").val($("#seladdress").val());
                        $("#cross_street").val($("#selcross_street").val());
                        $("#City").val($("#selcity").val());
                    }

                    var values = $(this).serialize();
                    SubmitQuery(values, $(this).attr('action'));
                }
                return false;
            });
            $("#Address").focus();
        });
    </script>

</head>
<body>
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">POSITION UPDATE</h5>
                </div>
            </div>
        </div>
    </div>
    <form class="main form-fill-height" action="PositionUpdateQuery.aspx?queryfile=positionupdate.qry" method="post" id="Form" name="Form">
        <div class="row main-row">
            <div class="col m12" style="margin-top:20px">
                <div class="row">
                    <ul class="tabs" id="radio_tabs" style="margin-bottom: 30px; margin-left: 10px">
                        <li class="tab col s2" id="tab2"><a href="#selectedlocation" onclick="setLocationType('selectedlocation')">Selected</a></li>
                        <li class="tab col s2" id="tab3"><a class="active" href="#addresslocation" onclick="setLocationType('addresslocation')">Address</a></li>
                    </ul>
                    <!--SelectedLocation-->
                    <div class="row" id="selectedlocation">
                        <div class="row">
                            <button class="mdc-button btn Select_From_Map col s2" type="button" style="margin-left: 10px; min-width:170px">
                                <div class="mdc-button__ripple"></div>
                                <span class="mdc-button__label">Select From Map</span>
                            </button>
                        </div>
                        <div class="row">
                            <div class="row">
                                <div class="input-field col s3">
                                    <input name="lat" id="lat" type="text" placeholder="" maxlength="15">
                                    <label for="lat" class="active">Latitude</label>
                                </div>
                                <div class="input-field col s3">
                                    <input name="long" id="long" type="text" placeholder="" maxlength="15">
                                    <label for="long" class="active">Longitude</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="input-field col s5">
                                    <input disabled id="seladdress" nanme="seladdress" type="text" placeholder="">
                                    <label for="seladdress" class="active">Address</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="input-field col s5">
                                    <input disabled id="selcross_street" name="selcross_street" type="text" placeholder="">
                                    <label for="selcross_street" class="active">Cross Street</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="input-field col s4">
                                    <input disabled id="selcity" name="selcity" type="text" placeholder="">
                                    <label for="selcity" class="active">City</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--AddressLocation-->
                    <div class="row" id="addresslocation">
                        <div class="row">
                            <div class="input-field col s5" mandatory="true">
                                <input placeholder="" id="Address" name="Address" type="text" maxlength="400">
                                <label for="adaddress" class="active">Address</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s5">
                                <input placeholder="" id="cross_street" name="cross_street" type="text" maxlength="400">
                                <label for="cross_street" class="active">Cross Street</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s3" mandatory="true">
                                <input placeholder="" id="City" name="City" type="text" maxlength="35">
                                <label for="adcity" class="active">City</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--Hidden Inputs that are passed to the query server-->
        <input type="hidden" name="snapped_latitude" id="snapped_latitude">
        <input type="hidden" name="snapped_longitude" id="snapped_longitude">
        <input type="hidden" name="snapped_distance" id="snapped_distance">
        <input type="hidden" name="locationtype" id="locationtype" value="addresslocation">
    </form>
</body>
</html>


