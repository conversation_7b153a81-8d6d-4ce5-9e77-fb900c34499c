<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<LINK href="normalstyle.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
							<p>
							
	<xsl:apply-templates select="results"/>	

							</p>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
	<SCRIPT language="javascript">
		function insertcomment()
		{
			var sResults;
			sResults = result.innerText;
			sResults = sResults.replace(/\x26/g,"%26");
			sResults = sResults.replace(/=/g,"%3D");
			form.hiddencopy.parameters = "copyvalue=" + sResults;
			form.hiddencopy.click();
		}
	</SCRIPT>
</HTML>

</xsl:template>

<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template>

<xsl:template match="/results/text">

    <xsl:apply-templates select="textitem"/>

</xsl:template>

<xsl:template match="/results/text/textitem">

    <p>
	<form action="recordcheck.aspx?queryfile=recordcheck.qry" id="form" name="form">
	<input type="hidden" id="hiddencopy" executefunction="CopyToIncidentComment"/>
	<DIV id="result">
		<xsl:call-template name="replace" />
	</DIV>
	<input value="Insert Comment" id="buttonInsertComment" type="button" onclick="insertcomment()"/>
	</form>
    </p>

</xsl:template>

<xsl:template match="/results/images">

    <xsl:apply-templates select="image"/>

</xsl:template>

<xsl:template match="/results/images/image">

    <p>
		<xsl:variable name="image-location"><xsl:value-of select="imagename"/></xsl:variable>
 		<img src="{$image-location}" />
    </p>

</xsl:template>

<xsl:template name="replace">
  <xsl:param name="string" select="." />
  <xsl:choose>
    <xsl:when test="not($string)" />
    <xsl:when test="contains($string, '&#10;')">
      <xsl:value-of select="substring-before($string, '&#10;')" />
      <br/>
      <xsl:call-template name="replace">
        <xsl:with-param name="string"
                        select="substring-after($string, '&#10;')" />
      </xsl:call-template>
    </xsl:when>
    <xsl:otherwise>
      <xsl:value-of select="$string" />
      <br/>
    </xsl:otherwise>
  </xsl:choose>
</xsl:template>


</xsl:transform>