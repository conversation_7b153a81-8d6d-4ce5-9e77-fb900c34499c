{"Buttons": [{"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 38, "Caption": "Calls", "MobileAction": 3, "IsFavorite": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 39, "Caption": "Units", "MobileAction": 1, "IsFavorite": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 9, "Caption": "Messages", "MobileAction": 5}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 40, "Caption": "Records", "MobileAction": 14}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 45, "Caption": "Maps", "MobileAction": 2, "IsFavorite": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 46, "Caption": "Activity Log", "FormName": "AddActivityLogComment", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "AutoSubmit", "Value": "IfFieldHasValue"}, {"Name": "location", "Value": "SelectedIncidentFieldlocation_name"}], "Identifier": 1, "Caption": "Premise Info", "FormName": "GetPremiseInfo", "MobileAction": 10, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 3, "Caption": "Location Check", "FormName": "LocationCheck", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 2, "Caption": "Person Search", "FormName": "PersonnelSearch", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 5, "Caption": "Phone Search", "FormName": "ReversePhoneSearch", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "curlat", "Value": "CurrentLocationLat"}, {"Name": "curlong", "Value": "CurrentLocationLong"}, {"Name": "sellat", "Value": "SelectedLocationLat"}, {"Name": "sellong", "Value": "SelectedLocationLong"}], "Identifier": 4, "Caption": "Location Search", "FormName": "Radius<PERSON><PERSON><PERSON>", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 33, "Caption": "Hazmat", "FormName": "HazmatInquiry", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ChildrenIdentifiers": [1, 2, 3, 4, 5], "ExternalProcesses": [{"$type": "VSI.Mobile.Client.Controls.Models.ExternalProcesses, InformMobile", "ProcessName": ""}], "Identifier": 42, "Caption": "General Info", "MobileAction": 42, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 6, "Caption": "Compose Mail", "MobileAction": 6}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 7, "Caption": "Deleted Items", "MobileAction": 9}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 8, "Caption": "Form Responses", "MobileAction": 7}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 11, "Caption": "Sent Items", "MobileAction": 8}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ChildrenIdentifiers": [6, 7, 8, 11], "Identifier": 10, "Caption": "Messages Menu", "MobileAction": 42}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 47, "Caption": "Current Call", "MobileAction": 4, "IsFavorite": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "PictometryLoadURL", "Value": "PictometryLoadURL"}, {"Name": "PictometryJavascriptURL", "Value": "PictometryJavascriptURL"}, {"Name": "PictometrySecretKey", "Value": "PictometrySecretKey"}, {"Name": "PictometryAPIKey", "Value": "PictometryAPIKey"}, {"Name": "PictometryHostURL", "Value": "PictometryHostURL"}, {"Name": "MapCenterLocationLat", "Value": "MapCenterLocationLat"}, {"Name": "MapCenterLocationLong", "Value": "MapCenterLocationLong"}], "Identifier": 48, "Caption": "Pictometry", "FormName": "Pictometry", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "SenderUnit", "Value": "CurrentUnit"}], "Identifier": 49, "Caption": "Send Page", "FormName": "SendPage", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 12, "Caption": "Division Unit Query", "FormName": "DivisionUnitQuery", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "radioname", "Value": "SelectedUnit"}], "Identifier": 13, "Caption": "Specific Unit Status", "FormName": "SpecificUnitStatus", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 14, "Caption": "Station Viewer", "FormName": "StationViewer", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "radioname", "Value": "SelectedUnit"}], "Identifier": 15, "Caption": "Unit Status", "FormName": "UnitStatus", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "radioname", "Value": "SelectedUnit"}], "Identifier": 16, "Caption": "Unit History", "FormName": "UnitHistory", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 17, "Caption": "Vehicle Search", "FormName": "VehicleSearch", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 51, "Caption": "Web RMS", "MobileAction": 13}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 31, "Caption": "Boat", "FormName": "BoatInquiry", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 32, "Caption": "Firearm", "FormName": "<PERSON><PERSON><PERSON><PERSON>", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 34, "Caption": "Free Form", "FormName": "FreeFormInquiry", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "ParseLastName", "Value": "lastname"}, {"Name": "ParseFirstName", "Value": "firstname"}, {"Name": "ParseMiddleName", "Value": "MiddleName"}, {"Name": "ParseDOB", "Value": "dob"}, {"Name": "ParseID", "Value": "dlnumber"}, {"Name": "ParseSex", "Value": "Sex"}, {"Name": "ParseState", "Value": "state"}], "Identifier": 35, "Caption": "Person Check", "FormName": "<PERSON><PERSON><PERSON><PERSON>", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 36, "Caption": "Plate Check", "FormName": "VehicleCheck", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 37, "Caption": "Property Check", "FormName": "PropertyCheck", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ChildrenIdentifiers": [31, 32, 33, 34, 35, 36, 37], "Identifier": 41, "Caption": "Records Check", "MobileAction": 42}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 52, "Caption": "Cardfile Search", "FormName": "CardFileSearch", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 53, "Caption": "Self-Assign", "MobileAction": 41, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 54, "Caption": "Map It", "MobileAction": 37, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 55, "Caption": "Directions", "MobileAction": 38, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 56, "Caption": "Auto Zoom", "MobileAction": 39, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 57, "Caption": "Call Zoom", "MobileAction": 40, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "unitname", "Value": "CurrentUnit"}, {"Name": "incidentnumber", "Value": "CurrentIncident"}, {"Name": "incidentid", "Value": "CurrentIncidentID"}, {"Name": "AutoSubmit", "Value": "IfFieldHasValue"}], "Identifier": 58, "Caption": "Primary Unit", "FormName": "PrimaryUnitRequest", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 59, "Caption": "Request Tow", "FormName": "TowRequest", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "incidentnumber", "Value": "CurrentIncident"}, {"Name": "incidentid", "Value": "CurrentIncidentID"}, {"Name": "AutoSubmit", "Value": "IfFieldHasValue"}, {"Name": "DisplaySituationFound", "Value": "DisplaySituationFound"}, {"Name": "SituationFoundIsRequired", "Value": "SituationFoundIsRequired"}], "Identifier": 60, "Caption": "Request Case Number", "FormName": "RequestCaseNumber", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "IncidentID", "Value": "CurrentIncidentID"}, {"Name": "ProblemNature", "Value": "CurrentIncidentFieldProblem_Nature"}, {"Name": "incidentnumber", "Value": "CurrentIncident"}], "Identifier": 61, "Caption": "Change Problem Nature", "FormName": "ChangeProblemNature", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "incidentnumber", "Value": "SelectedIncident"}, {"Name": "incidentid", "Value": "SelectedIncidentID"}, {"Name": "curlat", "Value": "CurrentLocationLat"}, {"Name": "curlong", "Value": "CurrentLocationLong"}, {"Name": "sellong", "Value": "SelectedLocationLong"}, {"Name": "sellat", "Value": "SelectedLocationLat"}, {"Name": "locationname", "Value": "SelectedIncidentFieldlocation_name"}, {"Name": "address", "Value": "SelectedIncidentFieldaddress"}, {"Name": "city", "Value": "SelectedIncidentFieldcity"}, {"Name": "state", "Value": "SelectedIncidentFieldstate"}, {"Name": "postalcode", "Value": "SelectedIncidentFieldzip"}, {"Name": "roomapt", "Value": "SelectedIncidentFieldapartment"}, {"Name": "building", "Value": "SelectedIncidentFieldBuilding"}], "Identifier": 62, "Caption": "Incident Address Update", "FormName": "IncidentAddressUpdate", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "incidentnumber", "Value": "SelectedIncident"}, {"Name": "incidentid", "Value": "SelectedIncidentID"}, {"Name": "AutoSubmit", "Value": "IfFieldHasValue"}], "Identifier": 63, "Caption": "Incident Personnel", "FormName": "GetIncidentPersonnel", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "incidentnumber", "Value": "CurrentIncident"}, {"Name": "incidentid", "Value": "CurrentIncidentID"}, {"Name": "ProblemNature", "Value": "CurrentIncidentFieldProblem_Nature"}], "Identifier": 64, "Caption": "Incident Disposition", "FormName": "SetIncidentDisposition", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 65, "Caption": "AFR", "MobileAction": 44}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 66, "Caption": "FBR"}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "incidentnumber", "Value": "SelectedIncident"}, {"Name": "incidentid", "Value": "SelectedIncidentID"}, {"Name": "AutoSubmit", "Value": "IfFieldHasValue"}], "Identifier": 67, "Caption": "Allied Agencies", "FormName": "AlliedAgencies", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "unitname", "Value": "CurrentUnit"}, {"Name": "AutoSubmit", "Value": "IfFieldHasValue"}], "Identifier": 68, "Caption": "Associate Radios", "FormName": "GetPersonnelRadios", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "CurrentIncidentID", "Value": "CurrentIncidentID"}, {"Name": "ProblemNature", "Value": "CurrentIncidentFieldProblem_Nature"}, {"Name": "incidentnumber", "Value": "CurrentIncident"}], "Identifier": 69, "Caption": "Clear All", "FormName": "ClearAllUnits", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "1"}, {"Name": "StatusRequestRequires", "Value": "NA"}, {"Name": "ProblemNature", "Value": "CurrentIncidentFieldProblem_Nature"}, {"Name": "incidentnumber", "Value": "CurrentIncident"}], "Identifier": 70, "Caption": "Clear Call", "FormName": "ClearCall", "MobileAction": 10, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "lat", "Value": "SelectedLocationLat"}, {"Name": "long", "Value": "SelectedLocationLong"}], "Identifier": 71, "Caption": "Update Request", "FormName": "PositionUpdate", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 109, "Caption": "View Personnel Avls", "MobileAction": 54, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 72, "Caption": "Pre Plan", "MobileAction": 48, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "incidentnumber", "Value": "CurrentIncident"}, {"Name": "incidentid", "Value": "CurrentIncidentID"}, {"Name": "AutoSubmit", "Value": "IfFieldHasValue"}], "Identifier": 73, "Caption": "Remove C4", "FormName": "RemoveC4", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 74, "Caption": "Supp Info", "MobileAction": 47}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "IncidentNumber", "Value": "CurrentIncident"}, {"Name": "IncidentID", "Value": "CurrentIncidentID"}], "Identifier": 75, "Caption": "Timestamp", "FormName": "SetUnitTimeStamp", "MobileAction": 10, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 76, "Caption": "Active Incidents", "FormName": "ActiveIncidents", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "incidentnumber", "Value": "SelectedIncident"}], "Identifier": 77, "Caption": "Get Incident", "FormName": "GetIncident", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 78, "Caption": "Incident Summary", "FormName": "Incident<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "curlat", "Value": "CurrentLocationLat"}, {"Name": "curlong", "Value": "CurrentLocationLong"}, {"Name": "sellong", "Value": "SelectedLocationLong"}, {"Name": "sellat", "Value": "SelectedLocationLat"}, {"Name": "CallTaking_Performed_By", "Value": "CurrentUser"}], "Identifier": 79, "Caption": "Nature Unknown", "FormName": "NatureUnknown", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "curlat", "Value": "CurrentLocationLat"}, {"Name": "curlong", "Value": "CurrentLocationLong"}, {"Name": "sellong", "Value": "SelectedLocationLong"}, {"Name": "sellat", "Value": "SelectedLocationLat"}, {"Name": "CallTaking_Performed_By", "Value": "CurrentUser"}], "Identifier": 80, "Caption": "On Site", "FormName": "OnSite", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 81, "Caption": "Pending Incidents", "FormName": "PendingIncidents", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 82, "Caption": "Prior Incident", "FormName": "IncidentSearch", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "curlat", "Value": "CurrentLocationLat"}, {"Name": "curlong", "Value": "CurrentLocationLong"}, {"Name": "sellong", "Value": "SelectedLocationLong"}, {"Name": "sellat", "Value": "SelectedLocationLat"}, {"Name": "CallTaking_Performed_By", "Value": "CurrentUser"}], "Identifier": 83, "Caption": "Subject Stop", "FormName": "SubjectStop", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "curlat", "Value": "CurrentLocationLat"}, {"Name": "curlong", "Value": "CurrentLocationLong"}, {"Name": "sellong", "Value": "SelectedLocationLong"}, {"Name": "sellat", "Value": "SelectedLocationLat"}, {"Name": "CallTaking_Performed_By", "Value": "CurrentUser"}], "Identifier": 84, "Caption": "Traffic Stop", "FormName": "TrafficStop", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "curlat", "Value": "CurrentLocationLat"}, {"Name": "curlong", "Value": "CurrentLocationLong"}, {"Name": "sellong", "Value": "SelectedLocationLong"}, {"Name": "sellat", "Value": "SelectedLocationLat"}, {"Name": "CallTaking_Performed_By", "Value": "CurrentUser"}], "Identifier": 85, "Caption": "View Incident", "FormName": "ViewIncident", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 20, "Caption": "Change Password", "MobileAction": 12}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 21, "Caption": "Day/Night", "MobileAction": 17, "IsFavorite": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 24, "Caption": "In/Out of Vehicle", "MobileAction": 16}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 97, "Caption": "Watchlist", "MobileAction": 49}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "Logged<PERSON><PERSON>ser", "Value": "CurrentUser"}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Value": "<PERSON><PERSON><PERSON>"}], "Identifier": 27, "Caption": "Modify Logon", "FormName": "ModifyLogon", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 19, "Caption": "Card Reader", "MobileAction": 23}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ChildrenIdentifiers": [22, 26, 20, 29, 28, 30], "Identifier": 44, "Caption": "Settings", "MobileAction": 45}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ChildrenIdentifiers": [107, 106, 105, 104, 103, 102, 101, 100, 99], "Identifier": 98, "Caption": "Preview Resolutions", "MobileAction": 45}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1024"}, {"Name": "MainWindowHeight", "Value": "768"}], "Identifier": 99, "Caption": "1024x768", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1280"}, {"Name": "MainWindowHeight", "Value": "720"}], "Identifier": 100, "Caption": "1280x720", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1280"}, {"Name": "MainWindowHeight", "Value": "800"}], "Identifier": 101, "Caption": "1280x800", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1280"}, {"Name": "MainWindowHeight", "Value": "1024"}], "Identifier": 102, "Caption": "1280x1024", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1366"}, {"Name": "MainWindowHeight", "Value": "768"}], "Identifier": 103, "Caption": "1366x768", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1440"}, {"Name": "MainWindowHeight", "Value": "900"}], "Identifier": 104, "Caption": "1440x900", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1600"}, {"Name": "MainWindowHeight", "Value": "900"}], "Identifier": 105, "Caption": "1600x900", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1680"}, {"Name": "MainWindowHeight", "Value": "1050"}], "Identifier": 106, "Caption": "1680x1050", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "MainWindowWidth", "Value": "1920"}, {"Name": "MainWindowHeight", "Value": "1080"}], "Identifier": 107, "Caption": "1920x1080", "MobileAction": 50}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 18, "Caption": "About", "MobileAction": 21}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 22, "Caption": "GPS Info", "MobileAction": 19}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 23, "Caption": "Help", "MobileAction": 22}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 25, "Caption": "Logout", "FormName": "Logout", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 26, "Caption": "Mag Stripe Info", "MobileAction": 24}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 28, "Caption": "Reset Password", "MobileAction": 11}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 29, "Caption": "Setup", "MobileAction": 20}, {"$type": "VSI.Mobile.Client.RecordsCheck.RecordsCheckButton, InformMobile", "Identifier": 86, "Caption": "Reset", "MobileAction": 29}, {"$type": "VSI.Mobile.Client.RecordsCheck.RecordsCheckButton, InformMobile", "Identifier": 87, "Caption": "Submit", "MobileAction": 26}, {"$type": "VSI.Mobile.Client.RecordsCheck.RecordsCheckButton, InformMobile", "Identifier": 88, "Caption": "Delete", "MobileAction": 30}, {"$type": "VSI.Mobile.Client.RecordsCheck.RecordsCheckButton, InformMobile", "Identifier": 89, "Caption": "Refine Request", "MobileAction": 32}, {"$type": "VSI.Mobile.Client.RecordsCheck.RecordsCheckButton, InformMobile", "Identifier": 90, "Caption": "Attach", "MobileAction": 28}, {"$type": "VSI.Mobile.Client.RecordsCheck.RecordsCheckButton, InformMobile", "Identifier": 91, "Caption": "Forward", "MobileAction": 31}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ExternalProcesses": [{"$type": "VSI.Mobile.Client.Controls.Models.ExternalProcesses, InformMobile", "ProcessName": "InformMobile.ConfigUtility.exe"}], "Identifier": 30, "Caption": "Configuration Utility", "MobileAction": 46}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "12"}, {"Name": "firstname", "Value": "CurrentIncidentFieldPatient First Name"}, {"Name": "lastname", "Value": "CurrentIncidentFieldPatient Last Name"}, {"Name": "location", "Value": "CurrentIncidentFieldTransport To Location"}, {"Name": "Address", "Value": "CurrentIncidentFieldTransport To Address"}, {"Name": "City", "Value": "CurrentIncidentFieldTransport To City"}, {"Name": "transportstate", "Value": "CurrentIncidentFieldTransport To State"}, {"Name": "Zip", "Value": "CurrentIncidentFieldTransport To Zip"}, {"Name": "transportationprotocol", "Value": "CurrentIncidentFieldTransport Protocol"}, {"Name": "transportationapartment", "Value": "CurrentIncidentFieldTransport To Apartment"}, {"Name": "transportationbuilding", "Value": "CurrentIncidentFieldTransport To Building"}], "Identifier": 92, "Caption": "Transport", "FormName": "Transport", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "13"}], "Identifier": 93, "Caption": "At Destination", "FormName": "AtDestination", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "20"}, {"Name": "unitname", "Value": "CurrentUnit"}, {"Name": "unitname1", "Value": "CurrentIncidentFieldSec1 Unit"}, {"Name": "location1", "Value": "CurrentIncidentFieldSec1 Location Name"}, {"Name": "address1", "Value": "CurrentIncidentFieldSec1 Location Address"}, {"Name": "city1", "Value": "CurrentIncidentFieldSec1 Location City"}, {"Name": "state1", "Value": "CurrentIncidentFieldSec1 Location State"}, {"Name": "postalcode1", "Value": "CurrentIncidentFieldSec1 Location Zip"}, {"Name": "roomapt1", "Value": "CurrentIncidentFieldSec1 Location Apartment"}, {"Name": "building1", "Value": "CurrentIncidentFieldSec1 Location Building"}, {"Name": "unitname2", "Value": "CurrentIncidentFieldSec2 Unit"}, {"Name": "location2", "Value": "CurrentIncidentFieldSec2 Location Name"}, {"Name": "address2", "Value": "CurrentIncidentFieldSec2 Location Address"}, {"Name": "city2", "Value": "CurrentIncidentFieldSec2 Location City"}, {"Name": "state2", "Value": "CurrentIncidentFieldSec2 Location State"}, {"Name": "postalcode2", "Value": "CurrentIncidentFieldSec2 Location Zip"}, {"Name": "roomapt2", "Value": "CurrentIncidentFieldSec2 Location Apartment"}, {"Name": "building2", "Value": "CurrentIncidentFieldSec2 Location Building"}, {"Name": "unitname3", "Value": "CurrentIncidentFieldSec3 Unit"}, {"Name": "location3", "Value": "CurrentIncidentFieldSec3 Location Name"}, {"Name": "address3", "Value": "CurrentIncidentFieldSec3 Location Address"}, {"Name": "city3", "Value": "CurrentIncidentFieldSec3 Location City"}, {"Name": "state3", "Value": "CurrentIncidentFieldSec3 Location State"}, {"Name": "postalcode3", "Value": "CurrentIncidentFieldSec3 Location Zip"}, {"Name": "roomapt3", "Value": "CurrentIncidentFieldSec3 Location Apartment"}, {"Name": "building3", "Value": "CurrentIncidentFieldSec3 Location Building"}, {"Name": "unitname4", "Value": "CurrentIncidentFieldSec4 Unit"}, {"Name": "location4", "Value": "CurrentIncidentFieldSec4 Location Name"}, {"Name": "address4", "Value": "CurrentIncidentFieldSec4 Location Address"}, {"Name": "city4", "Value": "CurrentIncidentFieldSec4 Location City"}, {"Name": "state4", "Value": "CurrentIncidentFieldSec4 Location State"}, {"Name": "postalcode4", "Value": "CurrentIncidentFieldSec4 Location Zip"}, {"Name": "roomapt4", "Value": "CurrentIncidentFieldSec4 Location Apartment"}, {"Name": "building4", "Value": "CurrentIncidentFieldSec4 Location Building"}], "Identifier": 94, "Caption": "Responding2nd", "FormName": "Responding2ndLocation", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "21"}, {"Name": "unitname", "Value": "CurrentUnit"}, {"Name": "unitname1", "Value": "CurrentIncidentFieldSec1 Unit"}, {"Name": "location1", "Value": "CurrentIncidentFieldSec1 Location Name"}, {"Name": "address1", "Value": "CurrentIncidentFieldSec1 Location Address"}, {"Name": "city1", "Value": "CurrentIncidentFieldSec1 Location City"}, {"Name": "state1", "Value": "CurrentIncidentFieldSec1 Location State"}, {"Name": "postalcode1", "Value": "CurrentIncidentFieldSec1 Location Zip"}, {"Name": "roomapt1", "Value": "CurrentIncidentFieldSec1 Location Apartment"}, {"Name": "building1", "Value": "CurrentIncidentFieldSec1 Location Building"}, {"Name": "unitname2", "Value": "CurrentIncidentFieldSec2 Unit"}, {"Name": "location2", "Value": "CurrentIncidentFieldSec2 Location Name"}, {"Name": "address2", "Value": "CurrentIncidentFieldSec2 Location Address"}, {"Name": "city2", "Value": "CurrentIncidentFieldSec2 Location City"}, {"Name": "state2", "Value": "CurrentIncidentFieldSec2 Location State"}, {"Name": "postalcode2", "Value": "CurrentIncidentFieldSec2 Location Zip"}, {"Name": "roomapt2", "Value": "CurrentIncidentFieldSec2 Location Apartment"}, {"Name": "building2", "Value": "CurrentIncidentFieldSec2 Location Building"}, {"Name": "unitname3", "Value": "CurrentIncidentFieldSec3 Unit"}, {"Name": "location3", "Value": "CurrentIncidentFieldSec3 Location Name"}, {"Name": "address3", "Value": "CurrentIncidentFieldSec3 Location Address"}, {"Name": "city3", "Value": "CurrentIncidentFieldSec3 Location City"}, {"Name": "state3", "Value": "CurrentIncidentFieldSec3 Location State"}, {"Name": "postalcode3", "Value": "CurrentIncidentFieldSec3 Location Zip"}, {"Name": "roomapt3", "Value": "CurrentIncidentFieldSec3 Location Apartment"}, {"Name": "building3", "Value": "CurrentIncidentFieldSec3 Location Building"}, {"Name": "unitname4", "Value": "CurrentIncidentFieldSec4 Unit"}, {"Name": "location4", "Value": "CurrentIncidentFieldSec4 Location Name"}, {"Name": "address4", "Value": "CurrentIncidentFieldSec4 Location Address"}, {"Name": "city4", "Value": "CurrentIncidentFieldSec4 Location City"}, {"Name": "state4", "Value": "CurrentIncidentFieldSec4 Location State"}, {"Name": "postalcode4", "Value": "CurrentIncidentFieldSec4 Location Zip"}, {"Name": "roomapt4", "Value": "CurrentIncidentFieldSec4 Location Apartment"}, {"Name": "building4", "Value": "CurrentIncidentFieldSec4 Location Building"}], "Identifier": 95, "Caption": "At Scene 2nd", "FormName": "AtScene2ndLocation", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "5"}, {"Name": "Reason", "Value": "CurrentOOSReason"}], "Identifier": 96, "Caption": "OOS", "FormName": "OutOfService", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "2"}], "Identifier": 108, "Caption": "In Quarters", "FormName": "InQuarters", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "3"}], "Identifier": 115, "Caption": "Local Area", "FormName": "LocalArea", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "StatusValue", "Value": "8"}], "Identifier": 116, "Caption": "Enroute To Post", "FormName": "EnrouteToPost", "MobileAction": 10}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 111, "Caption": "Reset", "MobileAction": 53}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 110, "Caption": "Submit", "MobileAction": 52}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 112, "Caption": "Reset", "MobileAction": 56}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 113, "Caption": "Submit", "MobileAction": 57}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 114, "Caption": "Supp Info", "MobileAction": 60}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Fields": [{"Name": "incidentnumber", "Value": "SelectedIncident"}, {"Name": "incidentid", "Value": "SelectedIncidentID"}, {"Name": "AutoSubmit", "Value": "IfFieldHasValue"}], "Identifier": 117, "Caption": "Request Case Number", "FormName": "RequestCaseNumber", "MobileAction": 10, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 119, "Caption": "Modify Incident", "MobileAction": 61}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 118, "Caption": "Supp Info", "MobileAction": 47}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 220, "Caption": "Back Up Unit", "MobileAction": 62}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 121, "Caption": "Map It", "MobileAction": 69, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 123, "Caption": "Back Up Unit", "MobileAction": 70, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 122, "MobileAction": 71, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 221, "Caption": "Send Incident Mail Message", "MobileAction": 74}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 126, "Caption": "Active Facility Diverts", "MobileAction": 75}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 222, "Caption": "Cameras", "MobileAction": 77}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ChildrenIdentifiers": [-1, -3, -4, -5, -6], "ExternalProcesses": [{"$type": "VSI.Mobile.Client.Controls.Models.ExternalProcesses, InformMobile", "ProcessName": ""}], "Caption": "Other", "MobileAction": 42, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": -1, "Caption": "Activity Log Search", "FormName": "UnitHistory", "MobileAction": 10, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ExternalProcesses": [{"$type": "VSI.Mobile.Client.Controls.Models.ExternalProcesses, InformMobile", "ProcessName": "https://www.weather.gov/mpx/"}], "Identifier": -3, "Caption": "National Weather Service", "MobileAction": 43, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "ExternalProcesses": [{"$type": "VSI.Mobile.Client.Controls.Models.ExternalProcesses, InformMobile", "ProcessName": "https://cadbrowser.logis.org/visinetbrowser/"}], "Identifier": -4, "Caption": "<PERSON><PERSON>er", "MobileAction": 43, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "CustomSearchItems": [], "Identifier": -5, "Caption": "AlliedAgencies", "FormName": "AlliedAgencies", "MobileAction": 10, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "CustomSearchItems": [], "Identifier": -6, "Caption": "GetIncidents_LOGIS", "FormName": "GetIncidents_LOGIS", "MobileAction": 10, "IsIncluded": true}, {"$type": "VSI.Mobile.Client.Controls.Models.MobileButton, InformMobile", "Identifier": 124, "Caption": "Search", "MobileAction": 55}], "IsEnabled": true, "VersionNumber": 4}