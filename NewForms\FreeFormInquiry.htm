<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Free Form Inquiry</TITLE><LINK href="normalstyle.css" type="text/css" rel="stylesheet"></HEAD>
	<body>
		<TABLE class="base" id="Table1" cellPadding="10" align="center" border="0">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Free Form Inquiry</H4>
						<form id="Form" name="Form" action="FreeFormInquiry.aspx?queryfile=FreeFormInquiry.qry" method="post">
							<TABLE>
								<TR><td>Line 1:</td>
									<td><input type="text" id="QueryText1" name="QueryText1" size="40" maxlength="40" mandatory="true" savelast="false"></td></TR>
								<TR><td>Line 2:</td>
									<td><input type="text" id="QueryText2" name="QueryText2" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>
								<TR><td>Line 3:</td>
									<td><input type="text" id="QueryText3" name="QueryText3" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>
								<TR><td>Line 4:</td>
									<td><input type="text" id="QueryText4" name="QueryText4" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>
								<TR><td>Line 5:</td>
									<td><input type="text" id="QueryText5" name="QueryText5" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>
								<TR><td>Line 6:</td>
									<td><input type="text" id="QueryText6" name="QueryText6" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>
								<TR><td>Line 7:</td>
									<td><input type="text" id="QueryText7" name="QueryText7" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>
									<TR><td>Line 8:</td>
									<td><input type="text" id="QueryText8" name="QueryText8" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>
									<TR><td>Line 9:</td>
									<td><input type="text" id="QueryText9" name="QueryText9" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>
									<TR><td>Line 10:</td>
									<td><input type="text" id="QueryText10" name="QueryText10" size="40" maxlength="40" mandatory="false" savelast="false"></td></TR>

							
							<input type="hidden" name="FREE" id="FREE">											
							</TABLE>
							<P align="Center">
							<input type="button" name="Query" id="Query" value="Query" onkeypress="AppendFields()" onclick="AppendFields()">
		<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">
							
							</P>
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
		<script src="clientutilities.js"></script>
		<SCRIPT language="javascript">
		function window.onload()
		{

			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.QueryText1.focus();
			PrepareValidation(Form);

			//statevals.innerHTML = GenerateSelectBox("state", statesource, statestyle, false, false, false, 1, false, false);
			//racevals.innerHTML = GenerateSelectBox("race", racesource, racestyle, false, false, false, 1, false, false);

		}
		
		function AppendFields()
		{
			//Put all the query text together.
			Form.FREE.value = 
				Form.QueryText1.value + '\n' +
				Form.QueryText2.value + '\n' +
				Form.QueryText3.value + '\n' +
				Form.QueryText4.value + '\n' +
				Form.QueryText5.value + '\n' +
				Form.QueryText6.value + '\n' +
				Form.QueryText7.value + '\n' +
				Form.QueryText8.value + '\n' +
				Form.QueryText9.value + '\n' +
				Form.QueryText10.value ;

				// submit the form
				Form.Submit.click();
		}

		</SCRIPT>
	</body>
</HTML>
