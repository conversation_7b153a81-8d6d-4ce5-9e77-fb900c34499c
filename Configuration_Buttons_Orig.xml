<?xml version="1.0" encoding="utf-8"?>
<Toolbar xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://tempuri.org/SchemaButtons.xsd">
  <Button>
    <Type>Run</Type>
    <Caption>Buttons_800 Info</Caption>
    <Open>
      <FileName>c:\Readme.txt</FileName>
      <Arguments>
				There are several kinds of buttons (that can be used in the Type element):
				1) Backward, Forward, NavBarToggle, DayNightToggle, Messages, NewMessages, Incidents, Map, Emergency
				For these types you basically just need to decide where to put these buttons in the toolbar hierarchy,
				and set their Function Key, using the elements: FKeyNumber, HoldCtrlKey, HoldAltKey and HoldShiftKey
				everything else is set automatically by the program.
				2) Form
				For these buttons you should also set the Caption, and the TreeNodeParent and the TreeNodeLeaf elemnts.
				3) Status
				For these buttons you should also set the StatusValue which contains the status value as appear in the CAD.
				and also the IconID. This should get one of the following values:
				1 = available
				2 = busy
				0 = unavailable
				If the Status requires a form to be displayed before it is sent,
				then you should use the TreeNodeParent and the TreeNodeLeaf elemnts to define the form location in the tree.
				You should also set the button color using either BackColor and ForeColor or BackColorRed, BackColorGreen, BackColorBlue, ForeColorRed, ForeColorGreen, ForeColorBlue.
				These colors will be used in the entire system when each status is shown (in lists, tree, and the map).
				4) OtherTreeNode
				You can use this type to define a button that gets you to any of the available tree nodes.
				For these buttons you should also set the Caption, and the TreeNodeParent and the TreeNodeLeaf elemnts.
				5) Toolbar
				Use this type to define a button that open a new toolbar.
				For such button you shuld also define the FKeyNumber, the Caption and of course, the Toolbar element - which include a full set of button.
				6) Separator
				Use this type (with no additional elements) when you want to break the toolbar to more than one row of buttons.

				Notes:
				1) You can also use FontSize for each button.
				2) Messages, and Incidents buttons must be in the first Hierarchy level.
				3) If you set the IsHidden tag to true - the button will not be visible. This is usually useful to define status that you don't want to allow the user to request.
			</Arguments>
    </Open>
    <Size>
      <Width>80</Width>
      <Height>85</Height>
    </Size>
    <IsHidden>true</IsHidden>
  </Button>
  <Button>
    <Type>Map</Type>
    <Size>
      <Width>120</Width>
      <Height>85</Height>
    </Size>
    <MoveCaret />
    <BackgroundImage>
      <ImageFile>\Bitmaps\MapColor.png</ImageFile>
    </BackgroundImage>
    <FontSize>13</FontSize>
    <FKeyNumber>1</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Status</Type>
    <Caption>ENROUTE</Caption>
    <StatusValue>7</StatusValue>
    <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
    <StatusRequestRequires>AnIncident</StatusRequestRequires>
    <Size>
      <Width>130</Width>
      <Height>85</Height>
    </Size>
    <MoveCaret />
    <IconID>-1</IconID>
    <FontSize>12</FontSize>
    <FKeyNumber>2</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <BackColor>
      <ColorRed>0</ColorRed>
      <ColorGreen>196</ColorGreen>
      <ColorBlue>49</ColorBlue>
    </BackColor>
    <ForeColor>
      <ColorRed>255</ColorRed>
      <ColorGreen>255</ColorGreen>
      <ColorBlue>255</ColorBlue>
    </ForeColor>
  </Button>
  <Button>
    <Type>Status</Type>
    <Caption>ARRIVE</Caption>
    <StatusValue>10</StatusValue>
    <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
    <StatusRequestRequires>AnIncident</StatusRequestRequires>
    <Size />
    <IconID>-1</IconID>
    <FontSize>12</FontSize>
    <FKeyNumber>3</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <BackColor>
      <ColorRed>217</ColorRed>
      <ColorGreen>0</ColorGreen>
      <ColorBlue>0</ColorBlue>
    </BackColor>
    <ForeColor>
      <ColorRed>255</ColorRed>
      <ColorGreen>255</ColorGreen>
      <ColorBlue>255</ColorBlue>
    </ForeColor>
  </Button>
  <Button>
    <Type>Status</Type>
    <Caption>CLEAR Available</Caption>
    <StatusValue>1</StatusValue>
    <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
    <TreeNodeParent>Queries</TreeNodeParent>
    <TreeNodeLeaf>ClearCall</TreeNodeLeaf>
    <Size />
    <FontSize>12</FontSize>
    <FKeyNumber>4</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Toolbar</Type>
    <Caption>Status</Caption>
    <Size>
      <Width>120</Width>
      <Height>85</Height>
    </Size>
    <MoveCaret>
      <HorizontalDistance>0</HorizontalDistance>
    </MoveCaret>
    <BackgroundImage>
      <ImageFile>\Bitmaps\Green.Down.Arrow.png</ImageFile>
    </BackgroundImage>
    <IconID>0</IconID>
    <FontSize>14</FontSize>
    <FKeyNumber>5</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <Toolbar>
      <RelativeLocationX>-365</RelativeLocationX>
      <RelativeLocationY>120</RelativeLocationY>
      <Button>
        <Type>Run</Type>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>100</HorizontalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>FIRE</Caption>
        <Size>
          <Width>680</Width>
          <Height>30</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>65</HorizontalDistance>
          <VerticalDistance>25</VerticalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <FontSize>14</FontSize>
        <BackColor>
          <ColorRed>128</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>EMS</Caption>
        <Size>
          <Width>273</Width>
          <Height>30</Height>
        </Size>
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <FontSize>14</FontSize>
        <BackColor>
          <ColorRed>128</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Run</Type>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>100</HorizontalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>IN COMMAND 
 </Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PrimaryUnitRequest</TreeNodeLeaf>
        <Field>
          <Name>unitname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <Field>
          <Name>incidentnumber</Name>
          <Value>CurrentIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>CurrentIncidentID</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <Size>
          <Width>175</Width>
          <Height>90</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>2</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>IN QUARTERS 
 </Caption>
        <StatusValue>2</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <Size>
          <Width>175</Width>
          <Height>90</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>4</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>OOS             BUSY UNAVAILABLE</Caption>
        <StatusValue>5</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>OutOfService.BusyUnavail</TreeNodeLeaf>
        <Size>
          <Width>175</Width>
          <Height>90</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>135</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>1</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>TRANSPORT 
 </Caption>
        <StatusValue>12</StatusValue>
        <SubmitStatusAndFormRequest>false</SubmitStatusAndFormRequest>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>Transport</TreeNodeLeaf>
        <Size />
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>5</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Run</Type>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>100</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>C4 - NO PAR 
 </Caption>
        <StatusValue>11</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <Size>
          <Width>175</Width>
          <Height>90</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>6</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>C5 - PAR 
 </Caption>
        <StatusValue>9</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <MoveCaret />
        <FontSize>14</FontSize>
        <FKeyNumber>7</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AT DEST AVAIL 
 </Caption>
        <StatusValue>14</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <Size>
          <Width>175</Width>
          <Height>90</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>135</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>8</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <BackColor>
          <ColorRed>128</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AT DESTINATION 
 </Caption>
        <StatusValue>13</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AtDestination</TreeNodeLeaf>
        <Field>
          <Name>location</Name>
          <Value>CurrentIncidentFieldTransport To Location</Value>
        </Field>
        <Field>
          <Name>Address</Name>
          <Value>CurrentIncidentFieldTransport To Address</Value>
        </Field>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>9</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Run</Type>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>100</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>OTHER STATUS 
 </Caption>
        <StatusValue>5</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>OutOfService</TreeNodeLeaf>
        <Size>
          <Width>175</Width>
          <Height>90</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>10</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>ENROUTE 2ND LOCATION</Caption>
        <StatusValue>20</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>Responding2ndLocation</TreeNodeLeaf>
        <Field>
          <Name>unitname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <Field>
          <Name>unitname1</Name>
          <Value>CurrentIncidentFieldSec1 Unit</Value>
        </Field>
        <Field>
          <Name>location1</Name>
          <Value>CurrentIncidentFieldSec1 Location Name</Value>
        </Field>
        <Field>
          <Name>address1</Name>
          <Value>CurrentIncidentFieldSec1 Location Address</Value>
        </Field>
        <Field>
          <Name>city1</Name>
          <Value>CurrentIncidentFieldSec1 Location City</Value>
        </Field>
        <Field>
          <Name>state1</Name>
          <Value>CurrentIncidentFieldSec1 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode1</Name>
          <Value>CurrentIncidentFieldSec1 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt1</Name>
          <Value>CurrentIncidentFieldSec1 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building1</Name>
          <Value>CurrentIncidentFieldSec1 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname2</Name>
          <Value>CurrentIncidentFieldSec2 Unit</Value>
        </Field>
        <Field>
          <Name>location2</Name>
          <Value>CurrentIncidentFieldSec2 Location Name</Value>
        </Field>
        <Field>
          <Name>address2</Name>
          <Value>CurrentIncidentFieldSec2 Location Address</Value>
        </Field>
        <Field>
          <Name>city2</Name>
          <Value>CurrentIncidentFieldSec2 Location City</Value>
        </Field>
        <Field>
          <Name>state2</Name>
          <Value>CurrentIncidentFieldSec2 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode2</Name>
          <Value>CurrentIncidentFieldSec2 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt2</Name>
          <Value>CurrentIncidentFieldSec2 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building2</Name>
          <Value>CurrentIncidentFieldSec2 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname3</Name>
          <Value>CurrentIncidentFieldSec3 Unit</Value>
        </Field>
        <Field>
          <Name>location3</Name>
          <Value>CurrentIncidentFieldSec3 Location Name</Value>
        </Field>
        <Field>
          <Name>address3</Name>
          <Value>CurrentIncidentFieldSec3 Location Address</Value>
        </Field>
        <Field>
          <Name>city3</Name>
          <Value>CurrentIncidentFieldSec3 Location City</Value>
        </Field>
        <Field>
          <Name>state3</Name>
          <Value>CurrentIncidentFieldSec3 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode3</Name>
          <Value>CurrentIncidentFieldSec3 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt3</Name>
          <Value>CurrentIncidentFieldSec3 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building3</Name>
          <Value>CurrentIncidentFieldSec3 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname4</Name>
          <Value>CurrentIncidentFieldSec4 Unit</Value>
        </Field>
        <Field>
          <Name>location4</Name>
          <Value>CurrentIncidentFieldSec4 Location Name</Value>
        </Field>
        <Field>
          <Name>address4</Name>
          <Value>CurrentIncidentFieldSec4 Location Address</Value>
        </Field>
        <Field>
          <Name>city4</Name>
          <Value>CurrentIncidentFieldSec4 Location City</Value>
        </Field>
        <Field>
          <Name>state4</Name>
          <Value>CurrentIncidentFieldSec4 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode4</Name>
          <Value>CurrentIncidentFieldSec4 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt4</Name>
          <Value>CurrentIncidentFieldSec4 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building4</Name>
          <Value>CurrentIncidentFieldSec4 Location Building</Value>
        </Field>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>11</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>ARRIVED 2ND LOCATION</Caption>
        <StatusValue>21</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AtScene2ndLocation</TreeNodeLeaf>
        <Field>
          <Name>unitname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <Field>
          <Name>unitname1</Name>
          <Value>CurrentIncidentFieldSec1 Unit</Value>
        </Field>
        <Field>
          <Name>location1</Name>
          <Value>CurrentIncidentFieldSec1 Location Name</Value>
        </Field>
        <Field>
          <Name>address1</Name>
          <Value>CurrentIncidentFieldSec1 Location Address</Value>
        </Field>
        <Field>
          <Name>city1</Name>
          <Value>CurrentIncidentFieldSec1 Location City</Value>
        </Field>
        <Field>
          <Name>state1</Name>
          <Value>CurrentIncidentFieldSec1 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode1</Name>
          <Value>CurrentIncidentFieldSec1 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt1</Name>
          <Value>CurrentIncidentFieldSec1 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building1</Name>
          <Value>CurrentIncidentFieldSec1 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname2</Name>
          <Value>CurrentIncidentFieldSec2 Unit</Value>
        </Field>
        <Field>
          <Name>location2</Name>
          <Value>CurrentIncidentFieldSec2 Location Name</Value>
        </Field>
        <Field>
          <Name>address2</Name>
          <Value>CurrentIncidentFieldSec2 Location Address</Value>
        </Field>
        <Field>
          <Name>city2</Name>
          <Value>CurrentIncidentFieldSec2 Location City</Value>
        </Field>
        <Field>
          <Name>state2</Name>
          <Value>CurrentIncidentFieldSec2 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode2</Name>
          <Value>CurrentIncidentFieldSec2 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt2</Name>
          <Value>CurrentIncidentFieldSec2 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building2</Name>
          <Value>CurrentIncidentFieldSec2 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname3</Name>
          <Value>CurrentIncidentFieldSec3 Unit</Value>
        </Field>
        <Field>
          <Name>location3</Name>
          <Value>CurrentIncidentFieldSec3 Location Name</Value>
        </Field>
        <Field>
          <Name>address3</Name>
          <Value>CurrentIncidentFieldSec3 Location Address</Value>
        </Field>
        <Field>
          <Name>city3</Name>
          <Value>CurrentIncidentFieldSec3 Location City</Value>
        </Field>
        <Field>
          <Name>state3</Name>
          <Value>CurrentIncidentFieldSec3 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode3</Name>
          <Value>CurrentIncidentFieldSec3 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt3</Name>
          <Value>CurrentIncidentFieldSec3 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building3</Name>
          <Value>CurrentIncidentFieldSec3 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname4</Name>
          <Value>CurrentIncidentFieldSec4 Unit</Value>
        </Field>
        <Field>
          <Name>location4</Name>
          <Value>CurrentIncidentFieldSec4 Location Name</Value>
        </Field>
        <Field>
          <Name>address4</Name>
          <Value>CurrentIncidentFieldSec4 Location Address</Value>
        </Field>
        <Field>
          <Name>city4</Name>
          <Value>CurrentIncidentFieldSec4 Location City</Value>
        </Field>
        <Field>
          <Name>state4</Name>
          <Value>CurrentIncidentFieldSec4 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode4</Name>
          <Value>CurrentIncidentFieldSec4 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt4</Name>
          <Value>CurrentIncidentFieldSec4 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building4</Name>
          <Value>CurrentIncidentFieldSec4 Location Building</Value>
        </Field>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>12</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Run</Type>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>100</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>TIME STAMPS</Caption>
        <Size>
          <Width>1020</Width>
          <Height>30</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <VerticalDistance>30</VerticalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <FontSize>14</FontSize>
        <BackColor>
          <ColorRed>128</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Run</Type>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>40</HorizontalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>DelayMessage</Type>
        <Caption>Primary Search Completed 
 </Caption>
        <Size>
          <Width>110</Width>
          <Height>90</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>13</FontSize>
        <NotificationInfo>Primary Search Completed</NotificationInfo>
      </Button>
      <Button>
        <Type>DelayMessage</Type>
        <Caption>Secondary Search Completed

</Caption>
        <FontSize>13</FontSize>
        <NotificationInfo>Secondary Search Completed</NotificationInfo>
      </Button>
      <Button>
        <Type>DelayMessage</Type>
        <Caption>Water On Fire 
 </Caption>
        <FontSize>13</FontSize>
        <NotificationInfo>Water On Fire</NotificationInfo>
      </Button>
      <Button>
        <Type>DelayMessage</Type>
        <Caption>Fire Under Control 
 </Caption>
        <FontSize>13</FontSize>
        <NotificationInfo>Fire Under Control</NotificationInfo>
      </Button>
      <Button>
        <Type>DelayMessage</Type>
        <Caption>Patient Contact 
 </Caption>
        <FontSize>13</FontSize>
        <NotificationInfo>Patient Transferred Over</NotificationInfo>
      </Button>
      <Button>
        <Type>DelayMessage</Type>
        <Caption>Loss Stopped 
 </Caption>
        <FontSize>13</FontSize>
        <NotificationInfo>Loss Stopped</NotificationInfo>
      </Button>
      <Button>
        <Type>DelayMessage</Type>
        <Caption>Extrication Complete 
 </Caption>
        <FontSize>13</FontSize>
        <NotificationInfo>Extrication Complete</NotificationInfo>
      </Button>
      <Button>
        <Type>DelayMessage</Type>
        <Caption>Fire Out

 </Caption>
        <FontSize>13</FontSize>
        <NotificationInfo>Fire Out</NotificationInfo>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>.</Caption>
        <Size>
          <Width>1020</Width>
          <Height>530</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <VerticalDistance>-510</VerticalDistance>
        </MoveCaret>
        <FontSize>1</FontSize>
        <BackColor>
          <ColorRed>172</ColorRed>
          <ColorGreen>191</ColorGreen>
          <ColorBlue>179</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>** Unused Statuses - DO NOT REMOVE</Caption>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>STAGED 
 </Caption>
        <StatusValue>9</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <Size>
          <Width>175</Width>
          <Height>90</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>14</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>TO HOSPITAL</Caption>
        <StatusValue>12</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>Transport_NEW</TreeNodeLeaf>
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <FontSize>14</FontSize>
        <BackColor>
          <ColorRed>0</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Toolbar</Type>
        <Caption>TRANSPORTING TO</Caption>
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <FontSize>14</FontSize>
        <BackColor>
          <ColorRed>0</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
        <IsHidden>true</IsHidden>
        <Toolbar>
          <Button>
            <Type>Status</Type>
            <Caption>HOSP01</Caption>
            <StatusValue>12</StatusValue>
            <SubmitStatusAndFormRequest>true</SubmitStatusAndFormRequest>
            <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
            <StatusRequestRequires>AnIncident</StatusRequestRequires>
            <TreeNodeParent>Queries</TreeNodeParent>
            <TreeNodeLeaf>Transport01</TreeNodeLeaf>
            <Size>
              <Width>175</Width>
              <Height>90</Height>
            </Size>
            <MoveCaret>
              <NewLine>true</NewLine>
              <HorizontalDistance>10</HorizontalDistance>
            </MoveCaret>
            <BackgroundImage>
              <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
            </BackgroundImage>
            <FontSize>14</FontSize>
            <IsHidden>true</IsHidden>
          </Button>
          <Button>
            <Type>Status</Type>
            <Caption>HOSP02</Caption>
            <StatusValue>12</StatusValue>
            <SubmitStatusAndFormRequest>true</SubmitStatusAndFormRequest>
            <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
            <StatusRequestRequires>AnIncident</StatusRequestRequires>
            <TreeNodeParent>Queries</TreeNodeParent>
            <TreeNodeLeaf>Transport02</TreeNodeLeaf>
            <Size />
            <MoveCaret />
            <BackgroundImage>
              <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
            </BackgroundImage>
            <FontSize>14</FontSize>
            <IsHidden>true</IsHidden>
          </Button>
          <Button>
            <Type>Status</Type>
            <Caption>OTHER HOSPITAL</Caption>
            <StatusValue>12</StatusValue>
            <SubmitStatusAndFormRequest>true</SubmitStatusAndFormRequest>
            <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
            <StatusRequestRequires>AnIncident</StatusRequestRequires>
            <TreeNodeParent>Queries</TreeNodeParent>
            <TreeNodeLeaf>Transport</TreeNodeLeaf>
            <Size>
              <Width>730</Width>
              <Height>90</Height>
            </Size>
            <MoveCaret>
              <NewLine>true</NewLine>
              <VerticalDistance>10</VerticalDistance>
            </MoveCaret>
            <BackgroundImage>
              <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
            </BackgroundImage>
            <FontSize>14</FontSize>
            <IsHidden>true</IsHidden>
          </Button>
        </Toolbar>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>ARRIVED COQ / AVAIL ON RADIO</Caption>
        <StatusValue>3</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <IconID>0</IconID>
        <FontSize>14</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>Not Used Avial</Caption>
        <StatusValue>1</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <Size />
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <IconID>1</IconID>
        <FontSize>14</FontSize>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>OOS</Caption>
        <StatusValue>5</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>NA</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>OutOfService</TreeNodeLeaf>
        <Field>
          <Name>Reason</Name>
          <Value>CurrentOOSReason</Value>
        </Field>
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <IconID>0</IconID>
        <FontSize>14</FontSize>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>TRANSPORTING TO</Caption>
        <StatusValue>12</StatusValue>
        <SubmitStatusAndFormRequest>true</SubmitStatusAndFormRequest>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>Transport</TreeNodeLeaf>
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <FontSize>14</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AVAIL ON SCENE</Caption>
        <StatusValue>4</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <MoveCaret />
        <IconID>1</IconID>
        <FontSize>14</FontSize>
        <ForeColor>
          <ColorRed>128</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>PT Contact</Caption>
        <StatusValue>11</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <MoveCaret />
        <IconID>2</IconID>
        <FontSize>14</FontSize>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AVST form</Caption>
        <StatusValue>2</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>InQuarters</TreeNodeLeaf>
        <MoveCaret>
          <NewLine>true</NewLine>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
        <IconID>1</IconID>
        <FontSize>14</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AE</Caption>
        <StatusValue>15</StatusValue>
        <IconID>1</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>DISP</Caption>
        <StatusValue>6</StatusValue>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <IconID>2</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>DISP2</Caption>
        <StatusValue>19</StatusValue>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <IconID>2</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>MA</Caption>
        <StatusValue>18</StatusValue>
        <IconID>2</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>OFF</Caption>
        <StatusValue>0</StatusValue>
        <IconID>0</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>SP</Caption>
        <StatusValue>16</StatusValue>
        <IconID>0</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>Clear - Fire Log</Caption>
        <StatusValue>1</StatusValue>
        <DontShowStatusOverrideMsg>false</DontShowStatusOverrideMsg>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>FireLog</TreeNodeLeaf>
        <MoveCaret />
        <FontSize>14</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AVAIL ON AIR</Caption>
        <StatusValue>1</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <FontSize>14</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>ENROUTE CHANGE OF QUARTERS</Caption>
        <StatusValue>8</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <Size />
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <IconID>0</IconID>
        <FontSize>14</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
    </Toolbar>
  </Button>
  <Button>
    <Type>Toolbar</Type>
    <Caption>Other</Caption>
    <Size />
    <BackgroundImage>
      <ImageFile>\Bitmaps\Green.Down.Arrow.png</ImageFile>
    </BackgroundImage>
    <FontSize>14</FontSize>
    <FKeyNumber>6</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <Toolbar>
      <RelativeLocationX>-445</RelativeLocationX>
      <RelativeLocationY>120</RelativeLocationY>
      <Button>
        <Type>Run</Type>
        <Caption>-Spacer</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>109</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>ACTIVE / PENDING INCIDENT INFO</Caption>
        <Size>
          <Width>805</Width>
          <Height>35</Height>
        </Size>
        <FontSize>16</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>-Spacer</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>109</HorizontalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Prem Info</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GetPremiseInfo</TreeNodeLeaf>
        <Field>
          <Name>location</Name>
          <Value>SelectedIncidentFieldlocation_name</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <Size>
          <Width>130</Width>
          <Height>80</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>5</HorizontalDistance>
        </MoveCaret>
        <FontSize>13</FontSize>
        <FKeyNumber>1</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>198</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Responding Law Units</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AlliedAgencies</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>SelectedIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>SelectedIncidentID</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <FontSize>13</FontSize>
        <FKeyNumber>2</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>198</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Responding Fire Units</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GetIncidentPersonnel</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>SelectedIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>SelectedIncidentID</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <FontSize>13</FontSize>
        <FKeyNumber>3</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>198</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Active Calls</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ActiveIncidents</TreeNodeLeaf>
        <FontSize>13</FontSize>
        <FKeyNumber>4</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>198</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Pending Calls</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PendingIncidents</TreeNodeLeaf>
        <FontSize>13</FontSize>
        <FKeyNumber>5</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>198</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Query Selected Incident</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GetIncident</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>SelectedIncident</Value>
        </Field>
        <FontSize>13</FontSize>
        <FKeyNumber>6</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>198</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>-Spacer</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>109</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>MESSAGING</Caption>
        <Size>
          <Width>535</Width>
          <Height>35</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>0</HorizontalDistance>
        </MoveCaret>
        <FontSize>16</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>UNIT TIMES</Caption>
        <Size>
          <Width>270</Width>
          <Height>35</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>1</HorizontalDistance>
        </MoveCaret>
        <FontSize>16</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>-Spacer</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>109</HorizontalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>NewMessage</Type>
        <Size>
          <Width>130</Width>
          <Height>80</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>5</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\MsgYellow.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>7</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Query Rsp</Caption>
        <TreeNodeParent>Messages</TreeNodeParent>
        <TreeNodeLeaf>Query Rsp</TreeNodeLeaf>
        <Size />
        <MoveCaret />
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>8</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Sent Msgs</Caption>
        <TreeNodeParent>Messages</TreeNodeParent>
        <TreeNodeLeaf>Sent Msgs</TreeNodeLeaf>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>9</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Msg Trash</Caption>
        <TreeNodeParent>Messages</TreeNodeParent>
        <TreeNodeLeaf>Trash</TreeNodeLeaf>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>10</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Unit History</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>UnitHistory</TreeNodeLeaf>
        <Field>
          <Name>radioname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <FontSize>13</FontSize>
        <FKeyNumber>11</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Incident Times</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>IncidentTimes</TreeNodeLeaf>
        <Field>
          <Name>radioname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <FontSize>13</FontSize>
        <FKeyNumber>12</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>-Spacer</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>109</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>CAD QUERIES</Caption>
        <Size>
          <Width>805</Width>
          <Height>35</Height>
        </Size>
        <FontSize>16</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>-Spacer</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>109</HorizontalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>FindAddress</Type>
        <Caption>Find Address</Caption>
        <Size>
          <Width>130</Width>
          <Height>80</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>5</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>1</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Person Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PersonnelSearch</TreeNodeLeaf>
        <Size>
          <Width>130</Width>
          <Height>80</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>5</HorizontalDistance>
        </MoveCaret>
        <FontSize>13</FontSize>
        <FKeyNumber>2</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Vehicle Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>VehicleSearch</TreeNodeLeaf>
        <FontSize>13</FontSize>
        <FKeyNumber>3</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Prior Incidents Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>IncidentSearch</TreeNodeLeaf>
        <FontSize>13</FontSize>
        <FKeyNumber>4</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>All Incidents</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GetIncidents_LOGIS</TreeNodeLeaf>
        <Field>
          <Name>radioname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <FontSize>13</FontSize>
        <FKeyNumber>5</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Activity Log Entry</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AddActivityLogComment</TreeNodeLeaf>
        <FontSize>13</FontSize>
        <FKeyNumber>6</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>-Spacer</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>109</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>OTHER FUNCTIONS</Caption>
        <Size>
          <Width>805</Width>
          <Height>35</Height>
        </Size>
        <FontSize>16</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>-Spacer</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>109</HorizontalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Loc Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>RadiusSearch</TreeNodeLeaf>
        <Field>
          <Name>curlat</Name>
          <Value>CurrentLocationLat</Value>
        </Field>
        <Field>
          <Name>curlong</Name>
          <Value>CurrentLocationLong</Value>
        </Field>
        <Field>
          <Name>sellat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>sellong</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <Size>
          <Width>130</Width>
          <Height>80</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>5</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>7</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Change Crew</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ModifyLogon</TreeNodeLeaf>
        <Field>
          <Name>LoggedinUser</Name>
          <Value>CurrentUser</Value>
        </Field>
        <FontSize>13</FontSize>
        <FKeyNumber>8</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Fire Links</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>DF.Links</TreeNodeLeaf>
        <Size />
        <MoveCaret>
          <HorizontalDistance>5</HorizontalDistance>
        </MoveCaret>
        <FontSize>13</FontSize>
        <FKeyNumber>9</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>NavBarToggle</Type>
        <FontSize>13</FontSize>
        <FKeyNumber>10</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Logout</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>Logout</TreeNodeLeaf>
        <Size />
        <MoveCaret>
          <HorizontalDistance>5</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Log Off Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>12</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Toolbar</Type>
        <Caption>E             </Caption>
        <BackgroundImage>
          <ImageFile>\Bitmaps\EMERGENCY.bmp</ImageFile>
          <UseImageOnly>true</UseImageOnly>
        </BackgroundImage>
        <FontSize>14</FontSize>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </ForeColor>
        <Toolbar>
          <RelativeLocationX>-445</RelativeLocationX>
          <RelativeLocationY>-450</RelativeLocationY>
          <Button>
            <Type>Run</Type>
            <Caption>-Spacer</Caption>
            <Size>
              <Width>1</Width>
              <Height>1</Height>
            </Size>
            <MoveCaret>
              <NewLine>true</NewLine>
            </MoveCaret>
          </Button>
          <Button>
            <Type>Run</Type>
            <Caption>-Spacer</Caption>
            <Size>
              <Width>1</Width>
              <Height>1</Height>
            </Size>
            <MoveCaret>
              <NewLine>true</NewLine>
            </MoveCaret>
          </Button>
          <Button>
            <Type>Separator</Type>
          </Button>
          <Button>
            <Type>Emergency</Type>
            <Caption>PRESS TO CONFIRM</Caption>
            <Size>
              <Width>500</Width>
              <Height>620</Height>
            </Size>
            <MoveCaret>
              <VerticalDistance>-100</VerticalDistance>
            </MoveCaret>
            <FontSize>20</FontSize>
            <BackColor>
              <ColorRed>255</ColorRed>
              <ColorGreen>0</ColorGreen>
              <ColorBlue>0</ColorBlue>
            </BackColor>
            <ForeColor>
              <ColorRed>255</ColorRed>
              <ColorGreen>255</ColorGreen>
              <ColorBlue>255</ColorBlue>
            </ForeColor>
          </Button>
        </Toolbar>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>.</Caption>
        <Size>
          <Width>1020</Width>
          <Height>530</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <VerticalDistance>-510</VerticalDistance>
        </MoveCaret>
        <BackColor>
          <ColorRed>174</ColorRed>
          <ColorGreen>145</ColorGreen>
          <ColorBlue>148</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>Buttons Not Typically Used by Fire/EMS</Caption>
        <Size>
          <Width>1000</Width>
          <Height>30</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <VerticalDistance>50</VerticalDistance>
        </MoveCaret>
        <FontSize>12</FontSize>
        <BackColor>
          <ColorRed>128</ColorRed>
          <ColorGreen>128</ColorGreen>
          <ColorBlue>128</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Run</Type>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>39</HorizontalDistance>
        </MoveCaret>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>*** Hidden Below</Caption>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>On Site Call</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>OnSite</TreeNodeLeaf>
        <Field>
          <Name>curlat</Name>
          <Value>CurrentLocationLat</Value>
        </Field>
        <Field>
          <Name>curlong</Name>
          <Value>CurrentLocationLong</Value>
        </Field>
        <Field>
          <Name>sellat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>sellong</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <Field>
          <Name>CallTaking_Performed_By</Name>
          <Value>CurrentUser</Value>
        </Field>
        <Size>
          <Width>75</Width>
          <Height>65</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>0</HorizontalDistance>
        </MoveCaret>
        <FontSize>12</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Create Pending Call</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ViewIncident</TreeNodeLeaf>
        <Field>
          <Name>curlat</Name>
          <Value>CurrentLocationLat</Value>
        </Field>
        <Field>
          <Name>curlong</Name>
          <Value>CurrentLocationLong</Value>
        </Field>
        <Field>
          <Name>sellat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>sellong</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <Field>
          <Name>CallTaking_Performed_By</Name>
          <Value>CurrentUser</Value>
        </Field>
        <IconID>-1</IconID>
        <FontSize>12</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Actvty Log</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AddActivityLogComment</TreeNodeLeaf>
        <FontSize>12</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>Change Area</Caption>
        <StatusValue>3</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>LocalArea</TreeNodeLeaf>
        <Size />
        <MoveCaret />
        <IconID>1</IconID>
        <FontSize>12</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>ChangePassword</Type>
        <MoveCaret>
          <NewLine>true</NewLine>
        </MoveCaret>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>DayNightToggle</Type>
        <Size />
        <MoveCaret>
          <HorizontalDistance>5</HorizontalDistance>
        </MoveCaret>
        <FontSize>13</FontSize>
        <FKeyNumber>11</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Pos Update</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PositionUpdate</TreeNodeLeaf>
        <Field>
          <Name>lat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>long</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <FontSize>12</FontSize>
        <FKeyNumber>6</FKeyNumber>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Units by Area</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>DivisionUnitQuery</TreeNodeLeaf>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Inc Addr Update</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>IncidentAddressUpdate</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>SelectedIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>SelectedIncidentID</Value>
        </Field>
        <Field>
          <Name>curlat</Name>
          <Value>CurrentLocationLat</Value>
        </Field>
        <Field>
          <Name>curlong</Name>
          <Value>CurrentLocationLong</Value>
        </Field>
        <Field>
          <Name>sellat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>sellong</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <Field>
          <Name>locationname</Name>
          <Value>SelectedIncidentFieldlocation_name</Value>
        </Field>
        <Field>
          <Name>address</Name>
          <Value>SelectedIncidentFieldaddress</Value>
        </Field>
        <Field>
          <Name>city</Name>
          <Value>SelectedIncidentFieldcity</Value>
        </Field>
        <Field>
          <Name>state</Name>
          <Value>SelectedIncidentFieldstate</Value>
        </Field>
        <Field>
          <Name>postalcode</Name>
          <Value>SelectedIncidentFieldzip</Value>
        </Field>
        <Field>
          <Name>roomapt</Name>
          <Value>SelectedIncidentFieldapartment</Value>
        </Field>
        <Field>
          <Name>building</Name>
          <Value>SelectedIncidentFieldBuilding</Value>
        </Field>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Incd Disp</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>SetIncidentDisposition</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>CurrentIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>CurrentIncidentID</Value>
        </Field>
        <FontSize>12</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Send Page</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>SendPage</TreeNodeLeaf>
        <Field>
          <Name>SenderUnit</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <Size />
        <MoveCaret />
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Station Viewer</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>StationViewer</TreeNodeLeaf>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Unit Status</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>UnitStatus</TreeNodeLeaf>
        <Field>
          <Name>radioname</Name>
          <Value>SelectedUnit</Value>
        </Field>
        <MoveCaret />
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>InfoBarToggle</Type>
        <FontSize>12</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Units</Caption>
        <TreeNodeParent>Units</TreeNodeParent>
        <TreeNodeLeaf>Units</TreeNodeLeaf>
        <BackgroundImage>
          <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
        </BackgroundImage>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>OutOfVehicleToggle</Type>
        <FontSize>12</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Pos Update</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PositionUpdate</TreeNodeLeaf>
        <Field>
          <Name>lat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>long</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <FontSize>12</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>Code 4</Caption>
        <StatusValue>11</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <Size>
          <Width>80</Width>
          <Height>65</Height>
        </Size>
        <MoveCaret />
        <IconID>2</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Remov C4</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>RemoveC4</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>CurrentIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>CurrentIncidentID</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <FKeyNumber>2</FKeyNumber>
        <HoldCtrlKey>true</HoldCtrlKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Nature Unkn</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>NatureUnknown</TreeNodeLeaf>
        <Field>
          <Name>curlat</Name>
          <Value>CurrentLocationLat</Value>
        </Field>
        <Field>
          <Name>curlong</Name>
          <Value>CurrentLocationLong</Value>
        </Field>
        <Field>
          <Name>sellat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>sellong</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <Field>
          <Name>CallTaking_Performed_By</Name>
          <Value>CurrentUser</Value>
        </Field>
        <Field>
          <Name>CallTaking_Performed_By</Name>
          <Value>CurrentUser</Value>
        </Field>
        <FontSize>14</FontSize>
        <FKeyNumber>5</FKeyNumber>
        <HoldShiftKey>true</HoldShiftKey>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </ForeColor>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Reverse Phone</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ReversePhoneSearch</TreeNodeLeaf>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>Enroute Sta w/ Form</Caption>
        <StatusValue>8</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>EnrouteToPost</TreeNodeLeaf>
        <IsHidden>true</IsHidden>
      </Button>
    </Toolbar>
  </Button>
  <Button>
    <Type>Incidents</Type>
    <Caption>Calls</Caption>
    <Size />
    <MoveCaret />
    <BackgroundImage>
      <ImageFile>\Bitmaps\CallsIcon.png</ImageFile>
    </BackgroundImage>
    <IconID>-1</IconID>
    <FontSize>14</FontSize>
    <FKeyNumber>7</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Form</Type>
    <Caption>Units</Caption>
    <TreeNodeParent>Units</TreeNodeParent>
    <TreeNodeLeaf>Units</TreeNodeLeaf>
    <Size />
    <BackgroundImage>
      <ImageFile>\Bitmaps\Engine.gif</ImageFile>
    </BackgroundImage>
    <IconID>-1</IconID>
    <FontSize>14</FontSize>
    <FKeyNumber>8</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Messages</Type>
    <Size />
    <MoveCaret />
    <BackgroundImage>
      <ImageFile>\Bitmaps\MsgYellow.png</ImageFile>
    </BackgroundImage>
    <FontSize>14</FontSize>
    <FKeyNumber>9</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>ViewIncidentDetails</Type>
    <Caption>Current</Caption>
    <Size />
    <MoveCaret />
    <BackgroundImage>
      <ImageFile>\Bitmaps\CurrentCall.png</ImageFile>
    </BackgroundImage>
    <FontSize>14</FontSize>
    <FKeyNumber>10</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>DayNightToggle</Type>
    <Size />
    <FontSize>14</FontSize>
    <FKeyNumber>12</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Run</Type>
    <Caption>TRITECH FIRE MOBILE - DAKOTA COUNTY</Caption>
    <Size>
      <Width>1350</Width>
      <Height>35</Height>
    </Size>
    <MoveCaret>
      <NewLine>true</NewLine>
    </MoveCaret>
    <BackgroundImage>
      <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
    </BackgroundImage>
    <FontSize>16</FontSize>
    <BackColor>
      <ColorRed>183</ColorRed>
      <ColorGreen>0</ColorGreen>
      <ColorBlue>0</ColorBlue>
    </BackColor>
    <ForeColor>
      <ColorRed>223</ColorRed>
      <ColorGreen>223</ColorGreen>
      <ColorBlue>223</ColorBlue>
    </ForeColor>
  </Button>
  <Button>
    <Type>Run</Type>
    <Caption>*** Hidden Below ***</Caption>
    <IsHidden>true</IsHidden>
  </Button>
  <Button>
    <Type>Forward</Type>
    <Size>
      <Width>85</Width>
      <Height>85</Height>
    </Size>
    <MoveCaret>
      <HorizontalDistance>13</HorizontalDistance>
    </MoveCaret>
    <BackgroundImage>
      <ImageFile>\Bitmaps\btnFwd_clr.png</ImageFile>
    </BackgroundImage>
    <FontSize>14</FontSize>
    <FKeyNumber>0</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <IsHidden>true</IsHidden>
  </Button>
  <Button>
    <Type>Backward</Type>
    <Size>
      <Width>84</Width>
      <Height>85</Height>
    </Size>
    <MoveCaret />
    <BackgroundImage>
      <ImageFile>\Bitmaps\BackGrn.PNG</ImageFile>
    </BackgroundImage>
    <FontSize>14</FontSize>
    <FKeyNumber>0</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <IsHidden>true</IsHidden>
  </Button>
  <Button>
    <Type>DelayMessage</Type>
    <Caption>ACKNOW- LEDGED</Caption>
    <Size>
      <Width>120</Width>
      <Height>85</Height>
    </Size>
    <MoveCaret>
      <HorizontalDistance>5</HorizontalDistance>
    </MoveCaret>
    <BackgroundImage>
      <ImageFile>\Bitmaps\btnDefault_clr.png</ImageFile>
    </BackgroundImage>
    <FontSize>14</FontSize>
    <BackColor>
      <ColorRed>255</ColorRed>
      <ColorGreen>255</ColorGreen>
      <ColorBlue>128</ColorBlue>
    </BackColor>
    <IsHidden>true</IsHidden>
    <NotificationInfo>ACKNOWLEDGED</NotificationInfo>
  </Button>
</Toolbar>