<?xml version="1.0" ?>
<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>Mobile Enterprise - CardFile Info</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Cardfile Search Results</H4>
						<P>
	<xsl:apply-templates select="results/errormessage"/>
							<p>
								<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
									<tr style="font-weight:bold;color:white;">
										<td>Category</td>
										<td>Name</td>
										<td>Address</td>
										<td>City</td>
										<td>St</td>
										<td>Zip</td>
										<td>P1</td>
										<td>P2</td>
										<td>Fax</td>
										<td>Comment</td>
									</tr>
	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
								</table>
							</p>
						</P>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
	<xsl:variable name="SearchString"><xsl:value-of select="SearchString"/></xsl:variable>
		
		<tr style="background-color:window;color:windowtext;">
			<td><xsl:value-of select="Category"/></td>
			<td><xsl:value-of select="Name"/></td>
			<td><xsl:value-of select="Address"/></td>
			<td><xsl:value-of select="City"/></td>
			<td><xsl:value-of select="State"/></td>
			<td><xsl:value-of select="Zip"/></td>
			<td><xsl:value-of select="Phone1"/></td>
			<td><xsl:value-of select="Phone2"/></td>
			<td><xsl:value-of select="FaxNumber"/></td>
			<td><xsl:value-of select="Comment"/></td>
    		</tr>

</xsl:template> 

<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
