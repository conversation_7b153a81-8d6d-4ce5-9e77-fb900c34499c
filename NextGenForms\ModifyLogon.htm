﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <TITLE>VisiNET Mobile - Modify Logon</TITLE>
    <meta charset="utf-8" />
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" type="text/css" href="materialize.minv1.0.0.css" />

    <!-- Compiled and minified JavaScript -->
    <link rel="stylesheet" type="text/css" href="MobileStyle.css" />
    <link rel="stylesheet" type="text/css" href="icons.css" />
    <script src="jquery.min.js" type="text/javascript"></script>
    <!-- Compiled and minified JavaScript -->
    <script src="materialize.minv1.0.0.js" type="text/javascript"></script>
    <script src="ClientUtilities.js" type="text/javascript"></script>
    <script src="materialize_autocomplete.js" type="text/javascript"></script>
    <script src="login.js" type="text/javascript"></script>

    <style>
        #modal {
            overflow: hidden;
            width: 400px;
            height: 320px;
        }

        #modal .modal-header h5 {
            color: #192231;
            font-size: 20px;
            font-weight: 600 !important;
            font-family: 'Source Sans Pro Regular', serif !important;
        }

        .modal .modal-header.warning {
            background-color: #E8EAED;
        }

        #modal .modal-content {
            top: 61px;
            width: 100%;
            height: unset !important;
            overflow-y: auto;
            bottom: 80px;
            font-size: 20px;
            font-weight: 500 !important;
            font-family: 'Source Sans Pro Regular', serif !important;
        }

        #modal .modal-footer {
            height: 80px;
            padding: 16px;
        }

        #modal .modal-footer > a {
            margin: 0px !important;
            text-align: center;
            line-height: 46px;
            height: 48px;
            min-width: 150px;
            margin-left: 8px !important;
            padding-left: 20px !important;
            padding-right: 20px !important;
        }

        #modal .modal-footer > a.btn {
            line-height: 48px;
        }
    </style>

    <script language="javascript">
        $(document).ready(function () {

            $('#LoggedinUserPosition').formSelect();

            $("#Form").submit(function () {
                
                if ($(this)[0].checkValidity() == true) {
                    //set logged in user as crew
                    if ($('#modifyLoggedInUserPosition').is(':checked') || $('#modifyCrewmember').is(':checked')) {

                        $(".logged-in-user").val($("#LoggedinUser").val());
                        if ($(".logged-in-user-position").length > 0) {
                            $(".logged-in-user-position").val($("#LoggedinUserPosition").val());
                        }
                        $('#modifyCrewmember').prop('checked', true);
                        $('#modifyCrewmember').val("true");
                        var $controls = $($('#modifyCrewmember').attr("target"));
                        enableControls($controls);
                    }
                    else {
                        $(".logged-in-user").remove();
                        $(".logged-in-user-position").remove();
                    }

                    /*Check whether we need to show modal*/
                    ShowModal($(this));
                    
                }
                return false;
            });

            $(".toggle-disabled-reverse").change(function () {
                var $checkbox = $(this);
                if ($checkbox.is(':checked')) {
                    $checkbox.val("true");
                    if ($checkbox.attr("name") == 'removeTempCapabilities') {
                        $("#Capabilities").find("option:selected").prop("selected", false);
                        $("#Capabilities").prop('disabled', true);
                        $("#Capabilities").formSelect();
                    }
                    else if ($checkbox.attr("name") == 'removeTempRadios') {
                        var $temporaryRadioFields = $(".temporary-radio");
                        $temporaryRadioFields.each(function () {
                            DeleteTemporaryRadioFieldWithTarget($(this));
                        });
                        $temporaryRadioFields.last().find(".addTemporaryRadioBtn a").addClass("disabled");
                    }   
                }
            });
        });

        function AfterFillForm() {
            //set the logged in user as part of the crew
            //$(".logged-in-user").val($("#LoggedinUser").val());
            SetUserPositions($("#LoggedinUser"), $('#LoggedinUserPosition'));
            $("#crewMembersDiv #crewmemberPosition").formSelect();

            GenerateSelectBox("Capabilities", "capabilities.xml", "genericselect.xsl", false, false, true, 8, true, false).then(function (result) {
                $("#capvals").prepend(result);
                $("#Capabilities").attr("disabled", "disabled");
                $("#Capabilities").addClass("capabilities-control")
                // $('select').formSelect();
                SetMultiSelectBoxFromParameters($("#Capabilities"));
                // $(".select-dropdown").focus();

            });
            $('input.autocomplete').autocomplete({
                data: {
                }
            });

            $('input.autocomplete').on('input', function () {
                GetDataList($(this));
            });

            $('.modal').modal();

            UpdateCrew();
        }

        async function ShowModal($form) {

            var allowSubmit = true;
            var capmodal = false;
            var radmodal = false;

            /* modify capabilities checked, remove all capabilities unchecked, no capabilities selected*/
            if ($('#modifyCapabilities').is(':checked') && !$('#removeTempCapabilities').is(':checked') && $("#Capabilities").val().length < 1) {
                capmodal = true;
            }

            /* modify radios checked, remove all radios unchecked, all radios filled out*/
            if ($('#modifyradios').is(':checked') && !$('#removeTempRadios').is(':checked')) {
                var showmodal = true;
                var $temporaryRadioFields = $(".temporary-radio").find("input[type=text]");
                $temporaryRadioFields.each(function () {
                    var $control = $(this);
                    if ($control.val() != "") {
                        showmodal = false
                    }
                    else {
                        $control.prop("required", true);
                        allowSubmit = false;
                    }
                });

                if (showmodal) {
                    radmodal = true;
                }    
            }

            if (capmodal && radmodal) {
                allowSubmit = await openModal('Modify Logon', 'The Vehicle\'s Capabilities and Temporary Radios will not be modified since none were entered on the form.');
                if (allowSubmit) {
                    $('#modifyCapabilities').prop('checked', false);
                    $('#modifyradios').prop('checked', false);
                }
            }
            else if (capmodal) {
                allowSubmit = await openModal('Modify Logon', 'The Vehicle\'s Capabilities will not be modified since none were entered on the form.');
                if (allowSubmit) {
                    $('#modifyCapabilities').prop('checked', false);
                }
            }
            else if (radmodal) {
                allowSubmit = await openModal('Modify Logon', 'The Vehicle\'s Temporary Radios will not be modified since none were entered on the form.');
                if (allowSubmit) {
                    $('#modifyradios').prop('checked', false);
                }
            }

            if (allowSubmit) {

                var values = $form.serialize();
                SubmitQuery(values, $form.attr('action'));
            }           
        }

    </script>

</head>
<body class="FlexBody">
    <div class="header">
        <div class="row">
            <div class="s12">
                <div class="valign-wrapper">
                    <h5 style="margin-left: 20px;">MODIFY LOGON</h5>
                </div>
            </div>
        </div>
    </div>

    <form class="Flex-Form" action="ModifyLogonQuery.aspx?queryfile=ModifyLogon.qry" method="post" id="Form" name="Form">
        <div class="Flex-Form-MainContent">
            <div class="row s12 m12">
                <div class="col m12">
                    <!--LOGGED IN USER-->
                    <div class="row valign-wrapper subheader">
                        <h4 style="margin-left: 20px;">LOGGED IN USER</h4>
                    </div>
                    <!--<div class="row">
                    <div class="switch">
                        <label>
                            MODIFY
                            <input type="checkbox" name="modifyLoggedInUserPosition" id="modifyLoggedInUserPosition" class="toggle-disabled" target="#LoggedinUserPosition">
                            <span class="lever"></span>
                        </label>
                    </div>
                </div>-->
                    <div class="row">
                        <div class="s6">
                            <div class="input-field col s12 m5">
                                <input type="text" name="LoggedinUser" id="LoggedinUser" readonly>
                                <label for="LoggedinUser">Logged In User</label>
                            </div>

                            <!--Position will go here-->
                            <!--<div class="input-field col s2 m4" type="selectlist">
                            <select name="LoggedinUserPosition" id="LoggedinUserPosition" disabled>
                                <option value="-1"></option>
                            </select>
                            <label for="LoggedinUserPosition">Position</label>
                        </div>-->
                            <!--logged in user need to be added as part of the crew-->
                            <input type="hidden" name="crewmember" id="crewmember" class="logged-in-user">
                            <input type="hidden" name="crewmemberPosition" id="crewmemberPosition" class="logged-in-user-position">

                        </div>
                    </div>

                    <!--VEHICLE-->
                    <div class="row valign-wrapper subheader">
                        <h4 style="margin-left: 20px;">VEHICLE</h4>
                    </div>
                    <div class="row">
                        <div class="switch col">
                            <label>
                                MODIFY
                                <input type="checkbox" name="modifyCapabilities" id="modifyCapabilities" class="toggle-disabled" target=".capabilities-control">
                                <span class="lever"></span>
                            </label>
                        </div>
                        <div class="switch col">
                            <label>
                                Remove All Temporary Capabilities
                                <input disabled type="checkbox" name="removeTempCapabilities" id="removeTempCapabilities" class="toggle-disabled-reverse capabilities-control" target="#Capabilities">
                                <span class="lever"></span>
                            </label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="input-field col s12 m5" type="selectlist" id="capvals" name="capvals">
                            <label for="capvals">Capabilities</label>
                        </div>
                    </div>

                    <!--CREW MEMBERS-->
                    <div class="row valign-wrapper subheader">
                        <h4 style="margin-left: 20px;">CREW MEMBERS</h4>
                    </div>
                    <div class="row">
                        <div class="switch">
                            <label>
                                MODIFY
                                <input type="checkbox" name="modifyCrewmember" id="modifyCrewmember" class="toggle-disabled" target=".crew-toggle-disabled">
                                <span class="lever"></span>
                            </label>
                        </div>
                    </div>
                    <div id="crewMembersDiv">
                        <div class="row crew-member">
                            <div class="input-field col s12 m5">
                                <input type="text" onchange="SetUserPositions(null, null, event);" placeholder="--" name="crewmember" id="crewmember" class="autocomplete crew-toggle-disabled" source="Configuration_CrewMembers.xml" searchField="item" disabled>
                                <label for="autocomplete-input" class="active">Crew Member</label>
                            </div>
                            <!--Position will go here-->
                            <!--<div class="input-field col s2 m5 crewPosition" type="selectlist">
                            <select name="crewmemberPosition" class="crew-toggle-disabled" id="crewmemberPosition" disabled>
                                <option value="-1"></option>
                            </select>
                            <label for="crewmemberPosition">Position</label>
                        </div>-->
                            <div class="col" style="margin-top:30px;">
                                <a class="btn-flat btn-icon-18 disabled crew-toggle-disabled" onclick="DeleteCrewMemberField(event);">
                                    <i class="icon-delete icon-18"></i>
                                </a>
                            </div>
                            <div class="col addCrewBtn" style="padding:0px;margin-top:30px;">
                                <a class="btn-flat btn-icon-18 disabled crew-toggle-disabled" onclick="AddCrewMemberField();">
                                    <i class="icon-add icon-18"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!--TEMPORARY RADIOS-->
                    <div class="row valign-wrapper subheader">
                        <h4 style="margin-left: 20px;">TEMPORARY RADIOS</h4>
                    </div>
                    <div class="row">
                        <div class="switch col">
                            <label>
                                MODIFY
                                <input type="checkbox" name="modifyradios" id="modifyradios" class="toggle-disabled" target=".radios-control">
                                <span class="lever"></span>
                            </label>
                        </div>
                        <div class="switch col">
                            <label>
                                Remove All Temporary Radios
                                <input disabled type="checkbox" name="removeTempRadios" id="removeTempRadios" class="toggle-disabled-reverse radios-control" target=".temporary-radio-toggle-disabled">
                                <span class="lever"></span>
                            </label>
                        </div>
                    </div>
                    <div id="temporaryRadiosDiv">
                        <div class="row temporary-radio">
                            <div class="input-field col s12 m3">
                                <input type="text" maxlength="30" placeholder="--" name="radioID1" id="radioID1" class="temporary-radio-toggle-disabled radios-control" disabled>
                                <label for="radioid" class="active">Radio ID</label>
                            </div>
                            <div class="input-field col s12 m3">
                                <input type="text" maxlength="30" placeholder="--" name="radioCode1" id="radioCode1" class="temporary-radio-toggle-disabled radios-control" disabled>
                                <label for="radiocode" class="active">Radio Code</label>
                            </div>
                            <div class="input-field col s12 m3">
                                <input type="text" maxlength="30" placeholder="--" name="radioDescription1" id="radioDescription1" class="temporary-radio-toggle-disabled radios-control" disabled>
                                <label for="radioid" class="active">Radio Description</label>
                            </div>
                            <div class="col" style="margin-top:30px;">
                                <a class="btn-flat btn-icon-18 disabled temporary-radio-toggle-disabled radios-control" onclick="DeleteTemporaryRadioField(event);">
                                    <i class="icon-delete icon-18"></i>
                                </a>
                            </div>
                            <div class="col addTemporaryRadioBtn" style="padding:0px;margin-top:30px;">
                                <a class="btn-flat btn-icon-18 disabled temporary-radio-toggle-disabled radios-control" onclick="AddTemporaryRadioField();">
                                    <i class="icon-add icon-18"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="Flex-Form-Comment border-left">

        </div>

        <!--Modal control-->
        <div id="modal" class="modal modal-fixed-footer">
            <div class="header valign-wrapper modal-header warning">
                <h5>ADD TAG</h5>
            </div>
            <div class="modal-content">
                <p>add this tag</p>
            </div>
            <div class="modal-footer">
                <a id="closebtn" class="modal-close btn-flat" style="border: none; color: #1066C7;">RETURN TO FORM</a>
                <a id="addbtn" class="modal-close btn">OKAY - SUBMIT</a>
            </div>
        </div>

        <input type="hidden" name="LoggedinCrew" id="LoggedinCrew">
        <input type="hidden" name="maxCrew" id="maxCrew" value="7">
        <input type="hidden" name="maxRadios" id="maxRadios" value="8">
    </form>

</body>
</html>


