<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>Mobile Enterprise - Location Premise Details</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<form action="IncidentPremiseInfo.aspx?queryfile=">
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Location Premise Details</H4>
						<P>
	<xsl:apply-templates select="results/exemel/NewDataSet"/>
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
		</form>
	</BODY>
</HTML>
</xsl:template>
	<xsl:template match="/results/exemel/NewDataSet">

	<xsl:if test="//Table[Id1=1]/PremiseCode!=''">
        <table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
        <tr style="font-weight:bold;color:white;"><td colspan="2" align ="center">Premise</td> </tr>
		<xsl:for-each select="//Table[Id1=1]">		
			<xsl:if test="PremiseCode!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Premise Code </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="PremiseCode"/>
				</td> </tr>
			</xsl:if>		
			<xsl:if test="LocationName!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Premise Name </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="LocationName"/>
				</td> </tr>
			</xsl:if>       	
			<xsl:if test="LocationAddress!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Address </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="LocationAddress"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="LocationBuilding!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Building </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="LocationBuilding"/>
				</td> </tr>
			</xsl:if>        	
			<xsl:if test="LocationApartment!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Apartment </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="LocationApartment"/>
				</td> </tr>
			</xsl:if>        	
			<xsl:if test="LocationCity!='' or State!='' or Zip!=''">
				<tr style="font-weight:bold;color:white;">
				<td> 
				<xsl:if test="LocationCity!=''">
					<xsl:text>
					City,
					</xsl:text>
				</xsl:if>
				<xsl:if test="State!=''">
					<xsl:text>
					 State,
					</xsl:text>
				</xsl:if>     
				<xsl:if test="Zip!=''">
					<xsl:text>
					 Zip
					</xsl:text>
				</xsl:if>       		 	
				</td>
				<td style="background-color:window;color:windowtext;">
				<xsl:if test="LocationCity!=''">
					<xsl:value-of select="LocationCity"/>
					<xsl:text>
					,
					</xsl:text>
				</xsl:if>
				<xsl:if test="State!=''">
					<xsl:value-of select="State"/>
					<xsl:text>
					,
					</xsl:text>       		 		
				</xsl:if>
				<xsl:if test="Zip!=''">
					<xsl:value-of select="Zip"/>
				</xsl:if>       		 	
				</td> </tr>
			</xsl:if>
			<xsl:if test="County!=''">
				<tr style="font-weight:bold;color:white;">
				<td> County </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="County"/>
				</td> </tr>
			</xsl:if>        			
			<xsl:if test="Latitude!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Latitude </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="Latitude"/>
				</td> </tr>
			</xsl:if>        	
			<xsl:if test="Longitude!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Longitude </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="Longitude"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="CrossStreet!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Cross Street </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="CrossStreet"/>
				</td> </tr>
			</xsl:if>        	
			<xsl:if test="LocationPhone!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Phone </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="LocationPhone"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="Preplan!=''">
				<tr style="font-weight:bold;color:white;">
				<td>Preplan</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:element name="a">
				    	<xsl:attribute name="href">#</xsl:attribute>
	                		<xsl:attribute name="id">OpenPrePlanAttachment</xsl:attribute>
				       	<xsl:attribute name="executefunction">OpenPrePlanAttachment</xsl:attribute>
						<xsl:attribute name="parameters">Preplan=<xsl:value-of select="Preplan"/></xsl:attribute>
						<xsl:value-of select="Preplan"/>
	        		</xsl:element>
				</td> 
				</tr>
			</xsl:if>
			<xsl:if test="BillingCode!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Billing Code </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="BillingCode"/>
				</td> </tr>
			</xsl:if>   
			<xsl:if test="LocationTypeCode!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Location Type </td>
				<td style="background-color:window;color:windowtext;">
				<xsl:value-of select="LocationTypeCode"/>
				<xsl:text> : </xsl:text>
				<xsl:value-of select="LocationTypeDescription"/>       		 	
				</td> </tr>
			</xsl:if>		
		</xsl:for-each>
        </table>
	</xsl:if>
              
    <p> </p>
        
    <xsl:if test="//Table[Id2=2]/PersonnelName!=''">
		<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">        
		<tr style="font-weight:bold;color:white;"><td colspan="2" Align="center">Personnel</td> </tr>
		<xsl:for-each select="//Table[Id2=2]">
			<xsl:if test="PersonnelName!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Name </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PersonnelName"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="PersonnelPosition!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Position </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PersonnelPosition"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="PersonnelLocation!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Location </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PersonnelLocation"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="PersonnelPhone!=''">
			<tr style="font-weight:bold;color:white;">
				<td> Phone </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PersonnelPhone"/>
				</td> </tr>
			</xsl:if>	
			<xsl:if test="PersonnelPagerPin!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Pager PIN </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PersonnelPagerPin"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="PagingService!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Paging Service </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PagingService"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="PersonnelComments!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Comments </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PersonnelComments"/>
				</td> </tr>	
			</xsl:if>
			<xsl:if test="position()!=last()">
				<tr></tr>
			</xsl:if>
        </xsl:for-each>
        </table>
	</xsl:if>
        
    <p> </p>
        
    <xsl:if test="//Table[Id3=3]/PremiseComments!=''">
		<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
		<tr style="font-weight:bold;color:white;"><td colspan="2" Align="center">Premise Comments</td> </tr>
		<xsl:for-each select="//Table[Id3=3]">
			<xsl:if test="PremiseComments!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Comments </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PremiseComments"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="PremiseCommentsDateTime!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Date Time, Initials</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="PremiseCommentsDateTime"/>
					<xsl:text>-</xsl:text>
					<xsl:value-of select="PremiseCommentsInitial"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="position()!=last()">
				<tr></tr>
			</xsl:if>
		</xsl:for-each>
        </table>
	</xsl:if>
	
	<p> </p>		

    <xsl:if test="//Table[Id4=4]/AlarmContactName!=''">	
		<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">        
		<tr style="font-weight:bold;color:white;"><td colspan="2" Align="center">Premise Alarm</td> </tr>
		<xsl:for-each select="//Table[Id4=4]">
			<xsl:if test="AlarmTypeCode!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Alarm Type </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="AlarmTypeCode"/>
					<xsl:text>:</xsl:text>
					<xsl:value-of select="AlarmTypeDescription"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="AlarmPermit!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Alarm Permit</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="AlarmPermit"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="AlarmContactName!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Contact Name</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="AlarmContactName"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="AlarmContactPhone1!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Contact Phone1</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="AlarmContactPhone1"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="AlarmContactPhone2!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Contact Phone2</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="AlarmContactPhone2"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="position()!=last()">
				<tr></tr>
			</xsl:if>		
		</xsl:for-each>
	</table>
	</xsl:if>	

	<p> </p>

    <xsl:if test="//Table[Id5=5]/AlarmZoneCode!=''">
		<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">        
		<tr style="font-weight:bold;color:white;"><td colspan="2" Align="center">Alarm Zone Information</td> </tr>	
		<xsl:for-each select="//Table[Id5=5]">
			<xsl:if test="AlarmZoneCode!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Code</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="AlarmZoneCode"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="AlarmZoneDescription!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Zone Description</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="AlarmZoneDescription"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="AlarmZoneNote!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Note</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="AlarmZoneNote"/>
				</td> </tr>
			</xsl:if>		
			<xsl:if test="position()!=last()">
				<tr></tr>
			</xsl:if>		
		</xsl:for-each>
	</table>
	</xsl:if>
	
	<p> </p>

    <xsl:if test="//Table[Id6=6]/ChemicalName!=''">	
		<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">        
		<tr style="font-weight:bold;color:white;"><td colspan="2" Align="center">Premise Hazardous Materials</td> </tr>
		<xsl:for-each select="//Table[Id6=6]">
			<xsl:if test="ChemicalName!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Chemical Name</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="ChemicalName"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="ChemicalId!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Chemical Id</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="ChemicalId"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="ChemicalGuide!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Chemical Guide</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="ChemicalGuide"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="ChemicalQuantity!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Chemical Quantity</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="ChemicalQuantity"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="ChemicalNotifyDistance!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Chemical Notify Distance</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="ChemicalNotifyDistance"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="ChemicalStorageLocation!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Chemical Storage Location</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="ChemicalStorageLocation"/>
				</td> </tr>
			</xsl:if>		
			<xsl:if test="position()!=last()">
				<tr></tr>
			</xsl:if>		
		</xsl:for-each>
	</table>
	</xsl:if>
	
	<p></p>

    <xsl:if test="//Table[Id7=7]/FacilityDivertReason!=''">
		<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">        
		<tr style="font-weight:bold;color:white;"><td colspan="2" Align="center">Divert Status</td> </tr>
		<xsl:for-each select="//Table[Id7=7]">
			<xsl:if test="FacilityDivertReason!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Divert Reason</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="FacilityDivertReason"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="OnWarning!=''">
				<tr style="font-weight:bold;color:white;">
				<td> On Warning </td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="OnWarning"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="OffWarning!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Off Warning</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="OffWarning"/>
				</td> </tr>
			</xsl:if>
			<xsl:if test="LocationComments!=''">
				<tr style="font-weight:bold;color:white;">
				<td> Comments</td>
				<td style="background-color:window;color:windowtext;">
					<xsl:value-of select="LocationComments"/>
				</td> </tr>
			</xsl:if>		
			<xsl:if test="position()!=last()">
				<tr></tr>
			</xsl:if>		
		</xsl:for-each>
	</table>
	</xsl:if>
	
</xsl:template> 	
</xsl:transform>