{"UnitSearchItems": [{"HeaderText": "Agency", "SourcePropertyName": "Agency", "IsDefault": true, "IsUnitSegmentOnly": true, "IsItemEditable": 1}, {"HeaderText": "Station", "SourcePropertyName": "HomeStation", "IsUnitSegmentOnly": true, "IsEnabled": true}], "UnitSearchFields": [{"HeaderText": "Unit Name", "SourcePropertyName": "UnitName", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Vehicle ID", "SourcePropertyName": "VehicleID", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Sector", "SourcePropertyName": "CurrentSector", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Juris<PERSON>", "SourcePropertyName": "CurrentJurisdiction", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Division", "SourcePropertyName": "CurrentDivision", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Capabilities", "SourcePropertyName": "VehicleCapabilities", "Width": "Auto", "MinWidth": 50.0, "IsEnabled": true}, {"HeaderText": "Agency", "SourcePropertyName": "Agency", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Battalion", "SourcePropertyName": "HomeBattalion", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Beat", "SourcePropertyName": "Beat", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Personnel", "SourcePropertyName": "PersonnelAssigned", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Resource Type", "SourcePropertyName": "PrimaryResourceType", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Station", "SourcePropertyName": "HomeStation", "Width": "Auto", "MinWidth": 50.0}, {"HeaderText": "Status", "SourcePropertyName": "Status", "Width": "Auto", "MinWidth": 50.0}], "IsEnabled": true, "VersionNumber": 1}