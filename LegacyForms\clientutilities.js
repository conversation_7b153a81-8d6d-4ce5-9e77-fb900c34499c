/********************************************************************************************************
Include Global.js automatically for all HTML pages which inlcude clientutilities.js.

Global.js is to be generated by a function, CreateGlobalJS(), to be called during Mobile Client startup.
Global.js will contains variable and function definitions with global scope to allow code and page logics
    access information that is not readily accessible when running inside the security sandbox created by
    Internet Explorer Browser Control.
********************************************************************************************************/
function IncludeJavaScript(jsFile)
{
	document.write('<script type="text/javascript" src="' + jsFile + '"></scr' + 'ipt>'); 
}

IncludeJavaScript('Global.js');
IncludeJavaScript('PropertyCheck.js');
IncludeJavaScript('GunCheck.js');
IncludeJavaScript('PersonCheck.js');
IncludeJavaScript('VehicleCheck.js');
// HTMLQuery client utilities
//
// These utilities are currently used for encapsulating dynamic list
// generation functionality.  This functionality includes:
//
// * Assigning a name to the control.
// * The source from which to populate the combo / list with.
// * Specifying a stylesheet that controls the type of combo or list box that is generated.
// * The size of the list box.
// * Whether or not a selection is mandatory (for combos, having an 'empty' selection / value).
//   Note that this has an impact not just on functionality, but also on appearance (an asterisk,
//   or whatever).
// * Whether or not a listbox supports multiple selections.
// * Support for 'savelast' functionality in the combo or select box(es).
// * support for combo boxes that are 'chained' together (eg states & cities).
//
// More notes:
//
// * This routine can be used for both combo boxes and list boxes.
// * If 'mandatory is set to true, there is no empty / blank item at the top of the control.
// * The listname parameter is used for both the name and id of the control.
// * The source and style is used for the stylesheet and XML source that is used to generate 
//   the control and data to populate it.
// * The mandatory parameter is used to determine whether there is a 'blank' row for the control.
// * The parent parameter determines the parent of a 'chained' control.
// * Savelast determines whether the 'savelast' functionality is set for the control.
// * Size is simply the size (in rows) of the control.
// * Multiple is used to show if multiple items can be selected on a listbox.  This is automatically
//   set to false if the size is 1.
// * Print is set to true to initialize print functionality on the handheld.

function GenerateSelectBox(listname, source, style, mandatory, parent, savelast, size, multiple, print, problemnature) {
    var xslt = new ActiveXObject("Msxml2.XSLTemplate");
    var xslDoc = new ActiveXObject("Msxml2.FreeThreadedDOMDocument");
    var xslProc;

    //xslDoc.preserveWhiteSpace = true; 
    xslDoc.async = false;
    xslDoc.load(style.src);
    xslt.stylesheet = xslDoc;
    var xmlDoc = new ActiveXObject("Msxml2.DOMDocument");
    //xmlDoc.preserveWhiteSpace = true; 
    xmlDoc.async = false;
    xmlDoc.load(source.src);
    xslProc = xslt.createProcessor();
    xslProc.input = xmlDoc;
    if (size <= 1)
        multiple = false;
    xslProc.addParameter("listname", listname);
    xslProc.addParameter("mandatory", mandatory);
    xslProc.addParameter("parent", parent);
    xslProc.addParameter("savelast", savelast);
    xslProc.addParameter("size", size);
    xslProc.addParameter("multiple", multiple);
    xslProc.addParameter("print", print);

    if (problemnature) {
        xslProc.addParameter("problemnature", problemnature);
    }

    xslProc.transform();
    // To replace Space with &nbsp which will be display as space on HTML page.
    return xslProc.output.split('  ').join('&nbsp;&nbsp;');
}

function GenerateComboBox(listname, source, style)
{
	var xslt = new ActiveXObject("Msxml2.XSLTemplate");
	var xslDoc = new ActiveXObject("Msxml2.FreeThreadedDOMDocument");
		var xslProc;
	
	xslDoc.async = false;
	xslDoc.load(style.src);
	xslt.stylesheet = xslDoc;
	var xmlDoc = new ActiveXObject("Msxml2.DOMDocument");
	xmlDoc.async = false;
	xmlDoc.load(source.src);
	xslProc = xslt.createProcessor();
	xslProc.input = xmlDoc;
	xslProc.addParameter("listname", listname);
	xslProc.transform();

	return xslProc.output;
}

function GenerateLabel(source, style) {
    var xslt = new ActiveXObject("Msxml2.XSLTemplate");
    var xslDoc = new ActiveXObject("Msxml2.FreeThreadedDOMDocument");
    var xslProc;

    xslDoc.async = false;
    xslDoc.load(style.src);
    xslt.stylesheet = xslDoc;
    var xmlDoc = new ActiveXObject("Msxml2.DOMDocument");
    xmlDoc.async = false;
    xmlDoc.load(source.src);
    xslProc = xslt.createProcessor();
    xslProc.input = xmlDoc;
    xslProc.transform();
    
    return xslProc.output.split("\n").join("<br>");
}

function GenerateListBox(listname, size, source, style, problemnature) {
    var xslt = new ActiveXObject("Msxml2.XSLTemplate");
    var xslDoc = new ActiveXObject("Msxml2.FreeThreadedDOMDocument");
    var xslProc;

    xslDoc.async = false;
    xslDoc.load(style.src);
    xslt.stylesheet = xslDoc;
    var xmlDoc = new ActiveXObject("Msxml2.DOMDocument");
    xmlDoc.async = false;
    xmlDoc.load(source.src);
    xslProc = xslt.createProcessor();
    xslProc.input = xmlDoc;
    xslProc.addParameter("listname", listname);
    xslProc.addParameter("size", size);

    if (problemnature) {
        xslProc.addParameter("problemnature", problemnature);
    }
    xslProc.transform();

	return xslProc.output.split('  ').join('&nbsp;&nbsp;');
}

// The below routines are used for client validation of data entry.
function PrepareValidation(formname)
{
	// First, we check for mandatory fields.  For any
	// mandatory fields not already 'colored', we set
	// the background color.
	//var i = 0;
	
	//for (i = 0; i < formname.elements.length; i++)
	//{
	//	if (formname.elements[i].mandatory != null)
	//	{
	//		if (formname.elements[i].getAttribute('mandatory', 0) == 'true')
	//			formname.elements[i].style.backgroundColor = 'lightblue';
	//	}
	//}		
}


// What should the default functions *really* be?
function SetDefaultDateRange(start, end)
{
	// We're getting dates one day apart.
	var today = new Date();

	start.value = (today.getMonth() + 1) + '/' + (today.getDate() - 1) + '/' + today.getYear();
	end.value = (today.getMonth() + 1) + '/' + today.getDate() + '/' + today.getYear();
}

function SetDefaultDate(start)
{
	var today = new Date();
	start.value = (today.getMonth() + 1) + '/' + today.getDate() + '/' + today.getYear();
}

function SetDefaultTimeRange(start, end)
{
	start.value = '00:00';
	end.value = '23:59';
}

// get current datetime in standard format that fit all regional settings (YYYY-MM-DD HH:mm:SS
function GetCurrentDate()
{
    var dateToFormat = new Date();
    return dateToFormat.getFullYear() + '-' + _pad(dateToFormat.getMonth() + 1) + '-' + _pad(dateToFormat.getDate()) + ' '
    + _pad(dateToFormat.getHours()) + ':' + _pad(dateToFormat.getMinutes()) + ':' + _pad(dateToFormat.getSeconds());
}


/*
date.js: useful extensions to the JavaScript Date object.
Copyright (C) 1999-2000 Jan Wessely <<EMAIL>>

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
Version 2 as published by the Free Software Foundation.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA
or browse to http://www.gnu.org/copyleft/gpl.html.

created: 25 June 1998
last modified: 17 Jan 2000
*/

// literals *******************************************************************

// used as param unit in Date.add()
Date.MILLI = 1;
Date.SECOND = Date.MILLI * 1000;
Date.MINUTE = Date.SECOND * 60;
Date.HOUR = Date.MINUTE * 60;
Date.DAY = Date.HOUR * 24;
Date.MONTH = -1;
Date.YEAR = -2;

Date.DAYS_IN_MONTH = new Array(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);

// methods ********************************************************************

function _Date_toCanonString()
{
	return this.getFullYear() +
			 _pad(this.getMonth() + 1) + 
			 _pad(this.getDate());
}

function _Date_getFullYear()
{
	var y = this.getYear();
	if(y < 100 && y > 0)
		y += 1900;
	return y;
}

function _Date_setFullYear(val)
{
	this.setYear(val);
}

function _Date_compareTo(other)
{
	return Date.compare(this, other);
}

function _Date_isLeapYear()
{
	return Date.leapYear(this.getFullYear());
}

function _Date_add(date, unit, amount)
{
	return Date.addDate(this, date, unit, amount);
}

function _Date_getDaysInMonth()
{
	return Date.daysInMonth(this.getFullYear(), this.getMonth());
}

// utility functions **********************************************************

function _isLeapYear(year)
{
	return (year % 4 == 0 && year % 100 != 0) || year % 400 == 0;
}

function _compareDate(d1, d2)
{
	return (new Date(d1)).getTime() - (new Date(d2)).getTime();
}

function _addDate(date, unit, amount)
{
	if(unit == Date.MONTH)
		date.setMonth(date.getMonth() + amount);
	else if(unit == Date.YEAR)
		date.setFullYear(date.getFullYear() + amount);
	else
		date.setTime(date.getTime() + unit * amount);
	return date;
}

function _getDaysInMonth(year, month)
{
	return month == 1 && Date.leapYear(year) ? 29 : Date.DAYS_IN_MONTH[month];
}

function _pad(n)
{
	return (n < 10 ? "0" : "") + n;
}

// initialization *************************************************************

Date.prototype.toCanonString = _Date_toCanonString;
if(!Date.prototype.getFullYear)
{
	Date.prototype.getFullYear = _Date_getFullYear;
	Date.prototype.setFullYear = _Date_setFullYear;
}
Date.prototype.isLeapYear = _Date_isLeapYear;
Date.prototype.compareTo = _Date_compareTo;
Date.prototype.add = _Date_add;
Date.prototype.getDaysInMonth = _Date_getDaysInMonth;

Date.leapYear = _isLeapYear;
Date.compare = _compareDate;
Date.addDate = _addDate;
Date.daysInMonth = _getDaysInMonth;


////////

/**
 * DHTML date validation script. Courtesy of SmartWebby.com (http://www.smartwebby.com/dhtml/)
 */
// Declaring valid date character, minimum year and maximum year
var dtCh= "/";
var minYear=1900;
var maxYear=2100;

// Declaring valid time character
var timeCh = ":";

function isInteger(s){
	var i;
    for (i = 0; i < s.length; i++){   
        // Check that current character is number.
        var c = s.charAt(i);
        if (((c < "0") || (c > "9"))) return false;
    }
    // All characters are numbers.
    return true;
}

function stripCharsInBag(s, bag){
	var i;
    var returnString = "";
    // Search through string's characters one by one.
    // If character is not in bag, append to returnString.
    for (i = 0; i < s.length; i++){   
        var c = s.charAt(i);
        if (bag.indexOf(c) == -1) returnString += c;
    }
    return returnString;
}

function daysInFebruary (year){
	// February has 29 days in any year evenly divisible by four,
    // EXCEPT for centurial years which are not also divisible by 400.
    return (((year % 4 == 0) && ( (!(year % 100 == 0)) || (year % 400 == 0))) ? 29 : 28 );
}
function DaysArray(n) {
	for (var i = 1; i <= n; i++) {
		this[i] = 31
		if (i==4 || i==6 || i==9 || i==11) {this[i] = 30}
		if (i==2) {this[i] = 29}
   } 
   return this
}

function isTime(timeStr)
{
	// The following should accept a one- or two- digit hour, optionally followed by two-digit minutes
	if( timeStr != "" && ! /^([01]?[0-9]|[2][0-3])(:[0-5][0-9])?$/.test(timeStr) )
	{
		alert("Please enter a valid time value");;
		return false;
	}
	return true;
}

function ValidateForm(){
	var dt=document.frmSample.txtDate
	if (isDate(dt.value)==false){
		dt.focus()
		return false
	}
    return true
 }

/**
 * DHTML date validation script. Courtesy of SmartWebby.com (http://www.smartwebby.com/dhtml/)
 */
// Declaring valid date character, minimum year and maximum year
var dtCh= "/";
var minYear=1900;
var maxYear=2100;

function isInteger(s){
	var i;
    for (i = 0; i < s.length; i++){   
        // Check that current character is number.
        var c = s.charAt(i);
        if (((c < "0") || (c > "9"))) return false;
    }
    // All characters are numbers.
    return true;
}

function stripCharsInBag(s, bag){
	var i;
    var returnString = "";
    // Search through string's characters one by one.
    // If character is not in bag, append to returnString.
    for (i = 0; i < s.length; i++){   
        var c = s.charAt(i);
        if (bag.indexOf(c) == -1) returnString += c;
    }
    return returnString;
}

function daysInFebruary (year){
	// February has 29 days in any year evenly divisible by four,
    // EXCEPT for centurial years which are not also divisible by 400.
    return (((year % 4 == 0) && ( (!(year % 100 == 0)) || (year % 400 == 0))) ? 29 : 28 );
}
function DaysArray(n) {
	for (var i = 1; i <= n; i++) {
		this[i] = 31
		if (i==4 || i==6 || i==9 || i==11) {this[i] = 30}
		if (i==2) {this[i] = 29}
   } 
   return this
}

function isDate(dtStr){
    if (!IsDateVB(dtStr)){
        alert("Invalid date format, please enter a valid date.")
        return false
    }

    return true
}

function ValidateForm(){
	var dt=document.frmSample.txtDate
	if (isDate(dt.value)==false){
		dt.focus()
		return false
	}
    return true
 }



/******************************************************************************
Function for the search listbox/combobox string (same as implemented in windows forms).
******************************************************************************/
function SmartSelect(sInput, oSelect)
{
 var sInput = String(sInput).toUpperCase();
 var iLength = sInput.length;

 if (iLength <= 0)
  return -1;

 var oOptions = oSelect.options;
 var i, diff, bFound, sTemp;

 var iHigh = oOptions.length - 1;
 var iLow = 0;
 var iCurrent = Math.floor((iHigh + 1) / 2);

 bFound = false;
 do
 {
  // Get the current option
  sTemp = oOptions(iCurrent).text.toUpperCase();
  var sSubstr = sTemp.substr(0, iLength);

  if (sSubstr < sInput)
  {
   // Search the upper half of the branch
   iLow = iCurrent + 1;
  }
  else if (sSubstr > sInput)
  {
   // Search the lower half of the branch
   iHigh = iCurrent - 1;
  }
  else
  {
   bFound = true;
   break;
  }

  // Pick the middle of the branch again
  iCurrent = Math.floor(iLow + ((iHigh + 1) - iLow) / 2);

 } while (iHigh >= iLow)

 // Is there a better prefix match?
 if (iLength < sTemp.length)
 {
  // Store the current old value
  var iOld = iCurrent--;

  // Now go back until we find one that doesn't match the prefix
  while (iCurrent >= 0)
  {
   // Gone too far -- the prefix no longer matches.
   if (oOptions(iCurrent).text.toUpperCase().substr(0, iLength) != sInput)
    break;

   iOld = iCurrent--;
  }

  iCurrent = iOld;
 }

 if (bFound)
  return iCurrent;
 else
  return -1;
}

function DumbSelect(sInput, oSelect)
{
 var sInput = String(sInput).toUpperCase();
 var iLength = sInput.length;

 if (iLength <= 0)
  return -1;

 var oOptions = oSelect.options;
 var nElements = oOptions.length;

 for (var iCurrent = 0; iCurrent < nElements; iCurrent++)
 {
  if (oOptions(iCurrent).text.substr(0, iLength).toUpperCase() == sInput)
  {
   break;
  }
 }

 if (iCurrent < nElements)
  return iCurrent;
 else
  return -1;
}

function BinarySearch()
{
 var i = SmartSelect(document.all("binary").value, document.all("list"));
 document.all("list").selectedIndex = i;
}

function LinearSearch()
{
 var i = DumbSelect(document.all("linear").value, document.all("list"));
 document.all("list").selectedIndex = i;
}

var sSearched = "";
var dLastKeyPressedTime = 0;
var oLastListbox = null;
function SearchListBox(listbox, event)
{
	var d = new Date();
	if(d.getTime() -dLastKeyPressedTime > 1500 || listbox != oLastListbox) // search string is deleted after 1.5 seconds
	{
		sSearched = "";
	}
	sSearched += String.fromCharCode(event.keyCode);
	var i = DumbSelect(sSearched, listbox); // slow, but doesn't care if the items are sorted
	//var i = SmartSelect(sSearched, listbox); // fast, but assumes the items are sorted
	if (i!=-1) // do nothing if can't find the string
	{
		listbox.selectedIndex = i;
	}
	d = new Date();
	dLastKeyPressedTime = d.getTime();
	oLastListbox = listbox;
	event.returnValue=false; // cancel the event so the listbox will not scroll by itself
}

/******************************************************************************
End of Function for the search listbox/combobox string
******************************************************************************/

/*******************************************************************************
Function to limit the input in TextArea 
******************************************************************************/
function CheckTextAreaLength(limitField, limitNum)
{	
	//Compare the input length with the allowed length. If it exceeds then
	//limit the text to max allowed and raise an alert. 
	if (limitField.value.length > limitNum)
	{
		limitField.value = limitField.value.substring(0, limitNum);
		alert (limitField.name + ' Text has reached the maximum length.');
	}
}


function FormatDate(date)
{
	return FormatDateVB(date.getYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate());
}

function FormatToStandardDate(dateString) {
    if (IsDateVB(dateString)) {
        return FormatStandardDateVB(dateString);
    }

    return dateString;
}

/*****************************************************************************
Functions to support:
1. displaying the localized distance unit caption
2. converting localized distance to miles
3. Convert the miles to localized distance (km) for display

Note: These functions utilize gsDistanceUnit defined in Global.js.
*****************************************************************************/
function DisplayDistanceUnitCaption()
{
	document.write((typeof gsDistanceUnit == "undefined") ? "mi" : gsDistanceUnit);
}

function ConvertLocalizedDistanceToMiles(distance)
{
	var sDistanceUnit = (typeof gsDistanceUnit == "undefined") ? "mi" : gsDistanceUnit.toLowerCase();
	var dDistanceInMiles = distance;

	if (sDistanceUnit == "km")
	{
		dDistanceInMiles = distance * 0.621371192;	// 1 Kilometer = 0.621371192 Mile
	}
	// else if (sDistanceUnit == "...")
	// add additional distance unit to miles conversion if needed.


	return dDistanceInMiles;
}

function ConvertMilesToLocalizedDistance(distance)
{
	var sDistanceUnit = (typeof gsDistanceUnit == "undefined") ? "mi" : gsDistanceUnit.toLowerCase();
	var dDistance = distance;

	if (sDistanceUnit == "km")
	{
		dDistance = (distance / 0.621371192).toFixed(2);	// 1 Kilometer = 0.621371192 Mile
	}
	else if (sDistanceUnit == "mi")
	{
		dDistance = distance.toFixed(2);
	}

	return dDistance;
}

var failedElement;
var savedColor;
var flashCount;
var flashing = false;

function ShowInvalidField(msg, elm) 
{
	alert(msg);
	failedElement = elm;
	startFlash();
}

function ValidateRadioIDs() 
{
	var radioCount = 8;
	for( var i = 0; i < radioCount; i++ ) 
	{
		var idElement = document.getElementById('radioID' + (i + 1) + '');

		if (idElement.value != '') 
		{
			if (!isInteger(idElement.value)) 
			{
				ShowInvalidField('Radio ID field must be numeric.', idElement);
				return false;
			}		        
		}
	}

	return true;
}

function ValidateRadioRequiredFields() 
{

	var radioCount = 8;
	for (var i = 0; i < radioCount; i++) 
	{
		var idElement = document.getElementById('radioID' + (i + 1) + '');
		var codeElement = document.getElementById('radioCode' + (i + 1) + '');
		var descElement = document.getElementById('radioDescription' + (i + 1) + '');

		if (trim(idElement.value) != '' || trim(codeElement.value) != '' || trim(descElement.value) != '') 
		{

			var badField = null;

			// if any one of them are not blank, they must all be non-blank
			if (trim(idElement.value) == '') 
			{
				badField = idElement;
			} else if (trim(codeElement.value) == '') 
			{
				badField = codeElement;
			} else if (trim(descElement.value) == '') 
			{
				badField = descElement;
			}

			if (badField != null) 
			{
				ShowInvalidField('Value must not be blank.', badField);
				return false;
			}
		}
	}
	return true;
}

function ValidateRadios() 
{
	if( !ValidateRadioIDs() ) 
	{
		return false;
	}

	// now check for required fields
	return ValidateRadioRequiredFields();
}

function trim(s) 
{
	return s.replace(/^\s+|\s+$/g, "");
}

function isInteger(s) 
{
	return (s.toString().search(/^-?[0-9]+$/) == 0)
}

/* Starts the flash animation */
function startFlash() 
{
	if( !flashing ) 
	{
		flashCount = 0;

		failedElement.focus();
		savedColor = failedElement.style.backgroundColor;

		flashing = true;
		flashOnElement();
	}
}

/* sets text background to default */
function flashOffElement() 
{
	failedElement.style.backgroundColor = savedColor;

	if (++flashCount < 2) 
	{
		setTimeout('flashOnElement()', 500);
	} else 
	{
		flashing = false;
	}
}

/* sets text background to red */
function flashOnElement() 
{
	failedElement.style.backgroundColor = 0xff0000;
	setTimeout('flashOffElement()', 500);
}