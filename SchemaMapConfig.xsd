<?xml version="1.0" encoding="utf-8" ?>
<!-- edited with XMLSPY v2004 rel. 3 U (http://www.xmlspy.com) by <PERSON> (Personal) -->
<xs:schema targetNamespace="http://www.voyagersystemsinc.com/VoyagerMobile/SchemaMapConfig.xsd"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.voyagersystemsinc.com/VoyagerMobile/SchemaMapConfig.xsd"
	elementFormDefault="qualified" id="SchemaMapConfig">
	<xs:element name="MapConfig">
		<xs:complexType>
			<xs:sequence>
        <xs:element name="GeneralConstants" type="GeneralConstants_type" />
        <xs:element name="DefaultExtent" type="DefaultExtent_type" />
				<xs:element name="OverviewMapSettings" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="MinZoomMiles" type="xs:float" />
							<xs:element name="BackgroundColor" type="Color_type" />
							<xs:element name="BorderColor" type="Color_type" minOccurs="0" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="MapItemsInfoBoxSettings" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Opacity" type="xs:float" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="ClickedLocationOutputFields">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="OutputFieldName" maxOccurs="unbounded">
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="DataType" use="optional" default="string">
												<xs:simpleType>
													<xs:restriction base="xs:string">
														<xs:enumeration value="string" />
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="AddressOutputFields">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="AddrOutputFieldName" maxOccurs="unbounded">
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="AddrFieldType" use="required">
												<xs:simpleType>
													<xs:restriction base="xs:string">
														<xs:enumeration value="StAddress" />
														<xs:enumeration value="CrossStreet" />
														<xs:enumeration value="City" />
														<xs:enumeration value="Other" />
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
											<xs:attribute name="DataType" use="optional" default="string">
												<xs:simpleType>
													<xs:restriction base="xs:string">
														<xs:enumeration value="string" />
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="LayerSourceDbs">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="LayerSourceDb" type="LayerSourceDb_type" maxOccurs="unbounded" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="Layers" type="Layers_type" />
				<xs:element name="OverviewLayers" type="Layers_type" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="LayerSourceDb_type">
		<xs:sequence>
			<xs:element name="DatabaseType" type="DatabaseType_type" />
			<xs:element name="DatabaseRef">
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="type" type="DatabaseRefType_type" use="required" />
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="GeoDataSets">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="GeoDataSet" type="GeoDataSet_type" maxOccurs="unbounded" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GeoDataSet_type">
		<xs:sequence>
			<xs:element name="TableName" type="xs:string" />
		</xs:sequence>
		<xs:attribute name="name" type="xs:string" />
	</xs:complexType>
	<xs:complexType name="VectorLayer_type">
		<xs:sequence>
			<xs:element name="LayerName" type="xs:string" />
			<xs:element name="FilterExpression" type="IgnorableString_type" minOccurs="0" />
			<xs:element name="FilterFields" type="FilterFields_type" minOccurs="0" />
			<xs:element name="DisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" />
			<xs:element name="OverviewDisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="InitiallySuppressDisplay" type="xs:boolean" />
			<xs:element name="InvertForNightMode" type="xs:boolean" />
			<xs:element name="SetMapCoordsysSame" type="xs:boolean" default="false" minOccurs="0" />
			<xs:element name="DensificationTolerance" type="xs:float" />
			<xs:element name="LabelPlacer" type="LabelPlacer_Type" minOccurs="0" />
			<xs:element name="ClickInfoDisplayFields" type="ClickInfoDisplayFields_Type" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="sourceGeoDataSet" type="xs:string" />
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
		<xs:attribute name="forMainMap" type="xs:boolean" use="optional" default="true" />
		<xs:attribute name="forOverviewMap" type="xs:boolean" use="optional" default="false" />
	</xs:complexType>
	<!-- Want to derive all the vector layer types from VectorLayer_type but the way I tried it didn't work. -->
	<xs:complexType name="PolygonLayer_type">
		<xs:sequence>
			<xs:element name="LayerName" type="xs:string" />
			<xs:element name="FilterExpression" type="IgnorableString_type" minOccurs="0" />
			<xs:element name="FilterFields" type="FilterFields_type" minOccurs="0" />
			<xs:element name="DisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" />
			<xs:element name="OverviewDisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="InitiallySuppressDisplay" type="xs:boolean" />
			<xs:element name="InvertForNightMode" type="xs:boolean" />
			<xs:element name="SetMapCoordsysSame" type="xs:boolean" default="false" minOccurs="0" />
			<xs:element name="DensificationTolerance" type="xs:float" />
			<xs:element name="LabelPlacer" type="LabelPlacer_Type" minOccurs="0" />
			<xs:element name="ClickInfoDisplayFields" type="ClickInfoDisplayFields_Type" minOccurs="0" />
			<xs:element name="DefaultPolygonSymbol" type="PolygonSymbol_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="PolygonRenderers" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:choice minOccurs="0" maxOccurs="unbounded">
							<xs:element name="PolygonSymbol" type="PolygonSymbol_type" />
							<xs:element name="PolygonSymbolByFieldValue" type="PolygonSymbolByFieldValue_type" />
						</xs:choice>
					</xs:sequence>
					<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="sourceGeoDataSet" type="xs:string" />
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
		<xs:attribute name="forMainMap" type="xs:boolean" use="optional" default="true" />
		<xs:attribute name="forOverviewMap" type="xs:boolean" use="optional" default="false" />
	</xs:complexType>
	<xs:complexType name="LineLayer_type">
		<xs:sequence>
			<xs:element name="LayerName" type="xs:string" />
			<xs:element name="FilterExpression" type="IgnorableString_type" minOccurs="0" />
			<xs:element name="FilterFields" type="FilterFields_type" minOccurs="0" />
			<xs:element name="DisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" />
			<xs:element name="OverviewDisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="InitiallySuppressDisplay" type="xs:boolean" />
			<xs:element name="InvertForNightMode" type="xs:boolean" />
			<xs:element name="SetMapCoordsysSame" type="xs:boolean" default="false" minOccurs="0" />
			<xs:element name="DensificationTolerance" type="xs:float" />
			<xs:element name="LabelPlacer" type="LabelPlacer_Type" minOccurs="0" />
			<xs:element name="ClickInfoDisplayFields" type="ClickInfoDisplayFields_Type" minOccurs="0" />
			<xs:element name="AddrMatchSpec" type="AddrMatchSpec_Type" minOccurs="0" />
			<xs:element name="DefaultLineSymbol" type="LineSymbol_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="LineRenderers" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:choice minOccurs="0" maxOccurs="unbounded">
							<xs:element name="LineSymbol" type="LineSymbol_type" />
							<xs:element name="LineSymbolByFieldValue" type="LineSymbolByFieldValue_type" />
						</xs:choice>
					</xs:sequence>
					<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="sourceGeoDataSet" type="xs:string" />
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
		<xs:attribute name="forMainMap" type="xs:boolean" use="optional" default="true" />
		<xs:attribute name="forOverviewMap" type="xs:boolean" use="optional" default="false" />
	</xs:complexType>
	<xs:complexType name="PointLayer_type">
		<xs:sequence>
			<xs:element name="LayerName" type="xs:string" />
			<xs:element name="FilterExpression" type="IgnorableString_type" minOccurs="0" />
			<xs:element name="FilterFields" type="FilterFields_type" minOccurs="0" />
			<xs:element name="DisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" />
			<xs:element name="OverviewDisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="InitiallySuppressDisplay" type="xs:boolean" />
			<xs:element name="InvertForNightMode" type="xs:boolean" />
			<xs:element name="SetMapCoordsysSame" type="xs:boolean" default="false" minOccurs="0" />
			<xs:element name="DensificationTolerance" type="xs:float" />
			<xs:element name="LabelPlacer" type="LabelPlacer_Type" minOccurs="0" />
			<xs:element name="ClickInfoDisplayFields" type="ClickInfoDisplayFields_Type" minOccurs="0" />
			<xs:element name="DefaultMarkerSymbol" type="MarkerSymbol_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="PointRenderers" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:choice minOccurs="0" maxOccurs="unbounded">
							<xs:element name="MarkerSymbol" type="MarkerSymbol_type" />
							<xs:element name="MarkerSymbolByFieldValue" type="MarkerSymbolByFieldValue_type" />
						</xs:choice>
					</xs:sequence>
					<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="sourceGeoDataSet" type="xs:string" />
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
		<xs:attribute name="forMainMap" type="xs:boolean" use="optional" default="true" />
		<xs:attribute name="forOverviewMap" type="xs:boolean" use="optional" default="false" />
	</xs:complexType>
	<xs:complexType name="ImageLayer_type">
		<xs:sequence>
			<xs:element name="FileName">
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="pathType" type="FilePathType_type" use="required" />
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="LayerName" type="xs:string" />
			<xs:element name="DisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" />
			<xs:element name="OverviewDisplayZoomRange" type="DisplayZoomRange_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="InitiallySuppressDisplay" type="xs:boolean" />
			<xs:element name="Transparent" type="xs:boolean" />
			<xs:element name="TransparentColor" type="Color_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="UpdateWhileDrawing" type="xs:boolean" />
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
		<xs:attribute name="forMainMap" type="xs:boolean" use="optional" default="true" />
		<xs:attribute name="forOverviewMap" type="xs:boolean" use="optional" default="false" />
	</xs:complexType>
	<xs:complexType name="DisplayZoomRange_type">
		<xs:sequence>
			<xs:element name="MinZoomMiles" type="xs:float" default="0" minOccurs="0" />
			<xs:element name="MaxZoomMiles" type="xs:float" default="999999" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:simpleType name="FillStyle_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SolidFill" />
			<xs:enumeration value="TransparentFill" />
			<xs:enumeration value="HorizontalFill" />
			<xs:enumeration value="VerticalFill" />
			<xs:enumeration value="UpwardDiagonalFill" />
			<xs:enumeration value="DownwardDiagonalFill" />
			<xs:enumeration value="CrossFill" />
			<xs:enumeration value="DiagonalCrossFill" />
			<xs:enumeration value="LightGrayFill" />
			<xs:enumeration value="GrayFill" />
			<xs:enumeration value="DarkGrayFill" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="FilterFields_type">
		<xs:sequence>
			<xs:element name="FieldName" type="xs:string" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:simpleType name="ColorConstants_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RGB" />
			<xs:enumeration value="OLEColorVal" />
			<xs:enumeration value="Black" />
			<xs:enumeration value="Red" />
			<xs:enumeration value="Green" />
			<xs:enumeration value="Blue" />
			<xs:enumeration value="Magenta" />
			<xs:enumeration value="Cyan" />
			<xs:enumeration value="White" />
			<xs:enumeration value="LightGray" />
			<xs:enumeration value="DarkGray" />
			<xs:enumeration value="Gray" />
			<xs:enumeration value="PaleYellow" />
			<xs:enumeration value="LightYellow" />
			<xs:enumeration value="Yellow" />
			<xs:enumeration value="LimeGreen" />
			<xs:enumeration value="Teal" />
			<xs:enumeration value="DarkGreen" />
			<xs:enumeration value="Maroon" />
			<xs:enumeration value="Purple" />
			<xs:enumeration value="Orange" />
			<xs:enumeration value="Khaki" />
			<xs:enumeration value="Olive" />
			<xs:enumeration value="Brown" />
			<xs:enumeration value="Navy" />
			<xs:enumeration value="Bisque" />
			<xs:enumeration value="PapayaWhip" />
			<xs:enumeration value="PaleGoldenrod" />
			<xs:enumeration value="PaleGreen" />
			<xs:enumeration value="Aquamarine" />
			<xs:enumeration value="PaleTurquoise" />
			<xs:enumeration value="PowderBule" />
			<xs:enumeration value="LightSteelBlue" />
			<xs:enumeration value="Thistle" />
			<xs:enumeration value="Pink" />
			<xs:enumeration value="Violet" />
			<xs:enumeration value="Lavender" />
			<xs:enumeration value="GreenYellow" />
			<xs:enumeration value="LawnGreen" />
			<xs:enumeration value="LemonChiffon" />
			<xs:enumeration value="Cornsilk" />
			<xs:enumeration value="Wheat" />
			<xs:enumeration value="LightSalmon" />
			<xs:enumeration value="SandyBrown" />
			<xs:enumeration value="LightGoldenrodYellow" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PolygonSymbol_type">
		<xs:sequence>
			<xs:element name="FillStyle" type="FillStyle_type" />
			<xs:element name="FillColor" type="Color_type" />
			<xs:element name="DrawOutline" type="xs:boolean" />
			<xs:element name="OutlineSize" type="xs:unsignedShort" />
			<xs:element name="OutlineColor" type="Color_type" />
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:complexType name="LineSymbol_type">
		<xs:sequence>
			<xs:element name="LineStyle" type="LineStyle_type" />
			<xs:element name="Size" type="xs:unsignedShort" />
			<xs:element name="Color" type="Color_type" />
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:complexType name="MarkerSymbol_type">
		<xs:sequence>
			<xs:element name="MarkerStyle" type="MarkerStyle_type" />
			<xs:element name="Size" type="xs:unsignedShort" />
			<xs:element name="Color" type="Color_type" />
			<xs:element name="Font" type="Font_type" maxOccurs="1" minOccurs="0" />
			<xs:element name="CharacterIndex" type="xs:short" />
			<xs:element name="CenterOnAscent" type="xs:boolean" />
			<xs:element name="Rotation" type="xs:float" />
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:complexType name="PolygonSymbolForValue_type">
		<xs:sequence>
			<xs:element name="Value" type="xs:string" />
			<xs:element name="PolygonSymbol" type="PolygonSymbol_type" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LineSymbolForValue_type">
		<xs:sequence>
			<xs:element name="Value" type="xs:string" />
			<xs:element name="LineSymbol" type="LineSymbol_type" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MarkerSymbolForValue_type">
		<xs:sequence>
			<xs:element name="Value" type="xs:string" />
			<xs:element name="MarkerSymbol" type="MarkerSymbol_type" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LabelPlacer_Type">
		<xs:sequence>
			<xs:element name="LabelFieldName" type="xs:string" />
			<xs:element name="DefaultTextSymbol" type="TextSymbol_type" />
			<xs:element name="AllowDuplicates" type="xs:boolean" />
			<xs:element name="MaskLabels" type="xs:boolean" />
			<xs:element name="MaskColor" type="Color_type" />
			<xs:element name="PlaceAbove" type="xs:boolean" />
			<xs:element name="PlaceBelow" type="xs:boolean" />
			<xs:element name="PlaceOn" type="xs:boolean" />
			<xs:element name="SymbolWidth" type="xs:unsignedShort" />
			<xs:element name="SymbolHeight" type="xs:unsignedShort" />
			<xs:element name="TextSymbolByFieldValue" minOccurs="0" maxOccurs="1">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FieldName" type="xs:string" />
						<xs:element name="DefaultTextSymbol" type="TextSymbol_type" minOccurs="0" maxOccurs="1" />
						<xs:element name="ValueTextSymbols">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="TextSymbolForValue" type="TextSymbolForValue_type" maxOccurs="unbounded" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:complexType name="TextSymbol_type">
		<xs:sequence>
			<xs:element name="Font" type="Font_type" />
			<xs:element name="TextForeColor" type="Color_type" />
			<xs:element name="Fitted" type="xs:boolean" />
			<xs:element name="Height" type="xs:float" />
			<xs:element name="HorizontalAlignment" type="HorizontalAlignmentConstants_type" />
			<xs:element name="Rotation" type="xs:float" />
			<xs:element name="VerticalAlignment" type="VerticalAlignmentConstants_type" />
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:complexType name="Font_type">
		<xs:sequence>
			<xs:element name="FontName" type="xs:string" />
			<xs:element name="Size" type="xs:unsignedShort" />
			<xs:element name="Bold" type="xs:boolean" />
			<xs:element name="Italic" type="xs:boolean" />
			<xs:element name="Strikethrough" type="xs:boolean" />
			<xs:element name="Underline" type="xs:boolean" />
			<xs:element name="Weight" type="StdFontWeight_type" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Color_type">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="name" type="ColorConstants_type" use="optional" default="OLEColorVal" />
				<xs:attribute name="red" type="xs:unsignedByte" use="optional" default="0" />
				<xs:attribute name="green" type="xs:unsignedByte" use="optional" default="0" />
				<xs:attribute name="blue" type="xs:unsignedByte" use="optional" default="0" />
				<xs:attribute name="OLEColorVal" type="xs:unsignedLong" use="optional" default="0" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="IgnorableString_type">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="ignore" type="xs:boolean" use="optional" default="false" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="TextSymbolForValue_type">
		<xs:sequence>
			<xs:element name="Value" type="xs:string" />
			<xs:element name="TextSymbol" type="TextSymbol_type" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PolygonSymbolByFieldValue_type">
		<xs:sequence>
			<xs:element name="FieldName" type="xs:string" />
			<xs:element name="DefaultPolygonSymbol" type="PolygonSymbol_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="ValueSymbols">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="PolygonSymbolForValue" type="PolygonSymbolForValue_type" maxOccurs="unbounded" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:complexType name="LineSymbolByFieldValue_type">
		<xs:sequence>
			<xs:element name="FieldName" type="xs:string" />
			<xs:element name="DefaultLineSymbol" type="LineSymbol_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="ValueSymbols">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="LineSymbolForValue" type="LineSymbolForValue_type" maxOccurs="unbounded" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" default="false" use="optional" />
	</xs:complexType>
	<xs:complexType name="MarkerSymbolByFieldValue_type">
		<xs:sequence>
			<xs:element name="FieldName" type="xs:string" />
			<xs:element name="DefaultMarkerSymbol" type="MarkerSymbol_type" minOccurs="0" maxOccurs="1" />
			<xs:element name="ValueSymbols">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MarkerSymbolForValue" type="MarkerSymbolForValue_type" maxOccurs="unbounded" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ignore" type="xs:boolean" use="optional" default="false" />
	</xs:complexType>
	<xs:simpleType name="VerticalAlignmentConstants_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AlignTop" />
			<xs:enumeration value="AlignCenter" />
			<xs:enumeration value="AlignBottom" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="HorizontalAlignmentConstants_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AlignLeft" />
			<xs:enumeration value="AlignCenter" />
			<xs:enumeration value="AlignRight" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="LineStyle_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SolidLine" />
			<xs:enumeration value="DashLine" />
			<xs:enumeration value="DotLine" />
			<xs:enumeration value="DashDotLine" />
			<xs:enumeration value="DashDotDotLine" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="MarkerStyle_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CircleMarker" />
			<xs:enumeration value="SquareMarker" />
			<xs:enumeration value="TriangleMarker" />
			<xs:enumeration value="CrossMarker" />
			<xs:enumeration value="TrueTypeMarker" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatabaseType_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ESRI_Shape" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="StdFontWeight_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FW_DONTCARE" />
			<xs:enumeration value="FW_THIN" />
			<xs:enumeration value="FW_EXTRALIGHT" />
			<xs:enumeration value="FW_ULTRALIGHT" />
			<xs:enumeration value="FW_LIGHT" />
			<xs:enumeration value="FW_NORMAL" />
			<xs:enumeration value="FW_REGULAR" />
			<xs:enumeration value="FW_MEDIUM" />
			<xs:enumeration value="FW_SEMIBOLD" />
			<xs:enumeration value="FW_DEMIBOLD" />
			<xs:enumeration value="FW_BOLD" />
			<xs:enumeration value="FW_EXTRABOLD" />
			<xs:enumeration value="FW_ULTRABOLD" />
			<xs:enumeration value="FW_HEAVY" />
			<xs:enumeration value="FW_BLACK" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatabaseRefType_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="absolutePath" />
			<xs:enumeration value="appRelativePath" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FilePathType_type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="absolutePath" />
			<xs:enumeration value="appRelativePath" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="DefaultExtent_type">
		<xs:sequence>
			<xs:element name="Dimensions">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="HeightMiles" type="xs:float" />
						<xs:element name="WidthMiles" type="xs:float" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Center">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Latitude" type="xs:double" />
						<xs:element name="Longitude" type="xs:double" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
  <xs:complexType name="GeneralConstants_type">
    <xs:sequence>
      <xs:element name="RecenterTolerance" type="xs:float" minOccurs="0" maxOccurs="1"/>
      <xs:element name="RotationTolerance" type="xs:float" minOccurs="0" maxOccurs="1"/>
      <xs:element name="MinAutoZoomMiles" type="xs:float" minOccurs="0" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="AddrMatchSpec_Type">
		<xs:sequence>
			<xs:element name="MatchRules" type="xs:string" />
			<xs:element name="IntersectionMatchRules" type="xs:string" />
			<xs:element name="StandardizingRules" type="xs:string" />
			<xs:element name="IntersectionStandardizingRules" type="xs:string" />
			<xs:element name="GeocodingField" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FieldName" type="xs:string" />
						<xs:element name="MatchVariable">
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="xs:string">
										<xs:attribute name="indexType" use="required">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:enumeration value="None" />
													<xs:enumeration value="Normal" />
													<xs:enumeration value="Soundex" />
													<xs:enumeration value="Secondary" />
												</xs:restriction>
											</xs:simpleType>
										</xs:attribute>
										<xs:attribute name="withPrimaryFieldName" type="xs:string" use="optional" />
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
						<xs:element name="RevGcOutputField" minOccurs="0">
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="xs:string">
										<xs:attribute name="revGcFieldCat" use="required">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:enumeration value="LeftFrom" />
													<xs:enumeration value="RightFrom" />
													<xs:enumeration value="LeftTo" />
													<xs:enumeration value="RightTo" />
													<xs:enumeration value="Street" />
													<xs:enumeration value="BothSideFrom" />
													<xs:enumeration value="BothSideTo" />
													<xs:enumeration value="BothSideOtherField" />
													<xs:enumeration value="LeftOtherField" />
													<xs:enumeration value="RightOtherField" />
												</xs:restriction>
											</xs:simpleType>
										</xs:attribute>
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="MatchVariableIntersectionLink" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MatchVariable" type="xs:string" />
						<xs:element name="LinkGroup">
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:enumeration value="Primary" />
									<xs:enumeration value="Secondary" />
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="IntersectionMatchVariable" type="xs:string" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SearchQueries" type="xs:string" maxOccurs="unbounded" />
			<xs:element name="ZoneField">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="City" />
						<xs:enumeration value="Zip" />
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ClickInfoDisplayFields_Type">
		<xs:sequence>
			<xs:element name="ClickInfoDisplayField" type="ClickInfoField_type" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="ignoreWhenNotVisible" type="xs:boolean" use="required" />
	</xs:complexType>
	<xs:complexType name="ClickInfoField_type">
		<xs:sequence>
			<xs:element name="DisplayLabel" type="xs:string" />
			<xs:element name="WhenContentEmpty">
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="action" use="optional" default="DisplayOnlyLabel">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="DisplayDefault" />
										<xs:enumeration value="SuppressLabel" />
										<xs:enumeration value="DisplayOnlyLabel" />
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="DisplayContent">
				<xs:complexType>
					<xs:sequence>
						<xs:choice maxOccurs="unbounded">
							<xs:element name="Literal" type="ContentLiteral_type" />
							<xs:element name="Separator" type="ContentSeparator_type" />
							<xs:element name="Field" type="ContentField_type" />
							<xs:element name="NearestSideField" type="ContentNearestSideField_type" />
							<xs:element name="MinNum" type="ContentMinNumField_type" />
							<xs:element name="MaxNum" type="ContentMaxNumField_type" />
							<xs:element name="UniqueFieldListValues" type="ContentUniqueFieldsValuesList_type" />
							<xs:element name="LineRange" type="ContentLineRange_type" />
							<xs:element name="Group" type="ContentGroup_type" />
							<xs:element name="Space" type="ContentSpace_type" />
						</xs:choice>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="clickedLocationDisplayLabel" type="xs:string" use="optional" />
		<xs:attribute name="clickedLocationFallBack" use="optional" default="0">
			<xs:simpleType>
				<xs:restriction base="xs:unsignedByte">
					<xs:minInclusive value="0" />
					<xs:maxInclusive value="5" />
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="displayLayerItem" type="xs:boolean" use="optional" default="true" />
	</xs:complexType>
	<xs:complexType name="ContentField_type">
		<xs:attribute name="tblField" type="xs:string" />
	</xs:complexType>
	<xs:complexType name="ContentNearestSideField_type">
		<xs:attribute name="leftTblField" type="xs:string" />
		<xs:attribute name="rightTblField" type="xs:string" />
	</xs:complexType>
	<xs:complexType name="ContentUniqueFieldsValuesList_type">
		<xs:sequence>
			<xs:element name="Field" type="ContentField_type" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="fieldSeparator" type="xs:string" />
	</xs:complexType>
	<xs:complexType name="ContentMinNumField_type">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="Field" type="ContentField_type" maxOccurs="unbounded" />
				<xs:element name="Number" type="xs:double" />
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="ignoreZero" type="xs:boolean" use="optional" default="false" />
		<xs:attribute name="ignoreBelow" type="xs:double" use="optional" />
		<xs:attribute name="ignoreAbove" type="xs:double" use="optional" />
	</xs:complexType>
	<xs:complexType name="ContentMaxNumField_type">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="Field" type="ContentField_type" maxOccurs="unbounded" />
				<xs:element name="Number" type="xs:double" />
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="ignoreZero" type="xs:boolean" use="optional" default="false" />
		<xs:attribute name="ignoreBelow" type="xs:double" use="optional" />
		<xs:attribute name="ignoreAbove" type="xs:double" use="optional" />
	</xs:complexType>
	<xs:complexType name="ContentLineRange_type">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="MinRange" type="ContentMinRange_type" />
				<xs:element name="MaxRange" type="ContentMaxRange_type" />
				<xs:element name="MinNearestSide" type="ContentMinNearestSide_type" />
				<xs:element name="MaxNearestSide" type="ContentMaxNearestSide_type" />
				<xs:element name="Literal" type="ContentLiteral_type" />
				<xs:element name="Separator" type="ContentSeparator_type" />
				<xs:element name="Space" type="ContentSpace_type" />
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="leftFromTblField" type="xs:string" use="required" />
		<xs:attribute name="rightFromTblField" type="xs:string" use="required" />
		<xs:attribute name="leftToTblField" type="xs:string" use="required" />
		<xs:attribute name="rightToTblField" type="xs:string" use="required" />
	</xs:complexType>
	<xs:complexType name="ContentGroup_type">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="Literal" type="ContentLiteral_type" />
				<xs:element name="Separator" type="ContentSeparator_type" />
				<xs:element name="Field" type="ContentField_type" />
				<xs:element name="NearestSideField" type="ContentNearestSideField_type" />
				<xs:element name="MinNum" type="ContentMinNumField_type" />
				<xs:element name="MaxNum" type="ContentMaxNumField_type" />
				<xs:element name="UniqueFieldListValues" type="ContentUniqueFieldsValuesList_type" />
				<xs:element name="LineRange" type="ContentLineRange_type" />
				<xs:element name="Space" type="ContentSpace_type" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ContentSeparator_type">
		<xs:attribute name="text" type="xs:string" />
	</xs:complexType>
	<xs:complexType name="ContentLiteral_type">
		<xs:simpleContent>
			<xs:extension base="xs:string" />
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="ContentSpace_type" />
	<xs:complexType name="ContentMinRange_type" />
	<xs:complexType name="ContentMaxRange_type" />
	<xs:complexType name="ContentMinNearestSide_type" />
	<xs:complexType name="ContentMaxNearestSide_type" />
	<xs:complexType name="Layers_type">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="PolygonLayer" type="PolygonLayer_type" />
				<xs:element name="LineLayer" type="LineLayer_type" />
				<xs:element name="PointLayer" type="PointLayer_type" />
				<xs:element name="ImageLayer" type="ImageLayer_type" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
</xs:schema>