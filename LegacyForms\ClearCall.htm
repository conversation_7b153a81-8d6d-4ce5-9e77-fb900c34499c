<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Clear Call</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Clear Call</H4>
						<form action="ClearCallQuery.aspx?queryfile=clearcall.qry" method="post" id="ClearCallQuery"
							name="ClearCallQuery">
							<table ID="Table2">
								<tr>
									<td><b>Response Disposition:</b></td>
								</tr>
								<tr>
                                <td><input type="hidden" name="problemnature" id="problemnature"></td>
                            </tr>
                            <tr>
                                <td>
                                    <!-- Root item requires a SPAN element to contain it. -->
                                    <SPAN type="selectlist" id="dispositionvals" name="dispositionvals">
                                    </SPAN>
                                </td>
                            </tr>
                            <!--<tr>
                                <td NOWRAP><b>Patient(s) Seen:</b></td>
                            </tr>
                            <tr>
                                <td><input type="text" align="left" name="PatientsSeen" id="PatientsSeen" size="15" value="" numeric="true"></td>
                            </tr>
                                <td><b>Comments:</b></td>
                            </tr>-->
                            <tr>
                                <td>
                                    <textarea style="width:500px;" id="comments" name="comments" rows="8" onkeyup="CheckTextAreaLength(this.form.comments,200);"></textarea>
                                </td>
                            </tr>
                        </table>
                        <br>
                        <input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
                        <input type="submit" name="Query" id="Query" value="Submit">
                    </form>
                </TD>
            </TR>
        </TBODY>
    </TABLE>
</body>
<script src="clientutilities.js"></script>
<script language="javascript">

    function OnAfterFormFill() {
        var problemnature = document.getElementById("problemnature").value;
        var styleAndSource = getDispositionStyleAndSource(problemnature);
        dispositionvals.innerHTML = GenerateListBox("ResponseDisposition", "8", styleAndSource.dispositionsource, styleAndSource.dispositionstyle, problemnature);
        
        ClearCallQuery.ResponseDisposition.focus();
    }

    function getDispositionStyleAndSource(problemnature) {
        if (problemnature) {
            return {
                dispositionstyle: { src: "dispositionbyproblemnaturelist.xsl" },
                dispositionsource: { src: "DispositionCodesByProblemNature.xml" }
            };
        } else {
            return {
                dispositionstyle: { src: "genericlist.xsl" },
                dispositionsource: { src: "DispositionCodes.xml" }
            };
        }
    }
</script>
</html>
