<?xml version="1.0"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
    <xsl:output method="html"/>
    <xsl:param name="listname"/>
    <xsl:param name="savelast"/>
    <xsl:param name="size"/>
    <xsl:param name="multiple"/>
    <xsl:template match="/">	
        <!-- Only the root item has an .XSL transform defined. -->
        <select size="1" Class="InputSector" onchange="return sectorchanged()" >
            <!-- The node (with path) that we want to use as select items goes here. -->
            <xsl:attribute name="id"><xsl:value-of select="$listname"/></xsl:attribute>
            <xsl:attribute name="name"><xsl:value-of select="$listname"/></xsl:attribute>
        	<xsl:attribute name="onKeyPress">SearchListBox(this, window.event)</xsl:attribute>
        	<xsl:attribute name="size"><xsl:value-of select="$size"/></xsl:attribute>
        	<xsl:if test="$multiple">
        	    <xsl:attribute name="MULTIPLE">MULTIPLE</xsl:attribute>
        	</xsl:if>
            <xsl:if test="$savelast">
		<xsl:attribute name="savelast">true</xsl:attribute>
	    </xsl:if> 
            <xsl:for-each select="//sectors/item">
                <xsl:element name="option">
                    <xsl:choose>
			<xsl:when test="@selected">
				<xsl:attribute name="selected"><xsl:value-of select="true"/></xsl:attribute>
			</xsl:when>
		    </xsl:choose>
                <!-- The XML tag that will be used as the select value goes here. -->
                <xsl:attribute name="value"><xsl:value-of select="id"/></xsl:attribute>
                <!-- The XML tag that will be used as the option text goes here. -->
                <xsl:value-of select="description"/>
                </xsl:element>
            </xsl:for-each>		
        </select>
    </xsl:template>
</xsl:stylesheet>

