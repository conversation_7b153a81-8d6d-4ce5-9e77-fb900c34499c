<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Pending Incidents</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Pending Incidents</H4>
						<P>
	<xsl:apply-templates select="results/errormessage"/>
							<p>
								<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
									<tr style="font-weight:bold;color:white;">
										<td>Priority</td>
										<td>Incident #</td>
										<td>Problem Nature</td>
										<td>Address</td>
										<td>Response Date</td>
										<td>Map Info</td>
									</tr>
	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
								</table>
							</p>
						</P>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">

    <xsl:variable name="recordid"><xsl:value-of select="ID"/></xsl:variable>
		
	<tr style="background-color:window;color:windowtext;">
		<td><xsl:value-of select="Priority"/></td>
		<td><a href="SingleIncidentQuery.aspx?ID={$recordid}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="Incident_Number"/></a></td>
		<td><xsl:value-of select="Problem"/></td>
		<td><xsl:value-of select="Address"/>
		    <xsl:if test="Building!=''">	
		    <xsl:text> Bldg: </xsl:text><xsl:value-of select="Building"/>
		    </xsl:if>
		    <xsl:if test="Apartment!=''">
		    <xsl:text> Apt: </xsl:text><xsl:value-of select="Apartment"/>
		    </xsl:if>
		    <xsl:text> </xsl:text><xsl:value-of select="City"/>
		    <xsl:text> </xsl:text><xsl:value-of select="State"/>
		    <xsl:text> </xsl:text><xsl:value-of select="Postal_Code"/> </td>
		<td><xsl:value-of select="Response_Date"/></td>
		<td><xsl:value-of select="MapInfo"/></td>
    </tr>

</xsl:template> 

<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
