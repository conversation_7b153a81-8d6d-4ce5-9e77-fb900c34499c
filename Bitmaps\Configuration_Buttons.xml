<?xml version="1.0" encoding="utf-8"?>
<Toolbar xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://tempuri.org/SchemaButtons.xsd">
  <Button>
    <Type>Form</Type>
    <Caption>Plate</Caption>
    <TreeNodeParent>Queries</TreeNodeParent>
    <TreeNodeLeaf>VehicleCheck</TreeNodeLeaf>
    <Size>
      <Width>72</Width>
      <Height>72</Height>
    </Size>
    <BackgroundImage>
      <ImageFile>\Bitmaps\ColorPlate.MN.png</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
    <FKeyNumber>1</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Form</Type>
    <Caption>Person</Caption>
    <TreeNodeParent>Queries</TreeNodeParent>
    <TreeNodeLeaf>PersonCheck</TreeNodeLeaf>
    <BackgroundImage>
      <ImageFile>\Bitmaps\ColorName2.png</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
    <FKeyNumber>2</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Backward</Type>
    <Size />
    <MoveCaret />
    <BackgroundImage>
      <ImageFile>\Bitmaps\BackGrn.PNG</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
    <FKeyNumber>0</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Status</Type>
    <Caption>EN</Caption>
    <StatusValue>7</StatusValue>
    <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
    <StatusRequestRequires>AnIncident</StatusRequestRequires>
    <MoveCaret />
    <BackgroundImage>
      <ImageFile>\Bitmaps\ColorRoute2.png</ImageFile>
    </BackgroundImage>
    <IconID>-1</IconID>
    <FontSize>12</FontSize>
    <FKeyNumber>3</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Status</Type>
    <Caption>AR</Caption>
    <StatusValue>10</StatusValue>
    <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
    <StatusRequestRequires>AnIncident</StatusRequestRequires>
    <BackgroundImage>
      <ImageFile>\Bitmaps\ColorArrive2.png</ImageFile>
    </BackgroundImage>
    <IconID>-1</IconID>
    <FontSize>12</FontSize>
    <FKeyNumber>4</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Form</Type>
    <Caption>Traffic</Caption>
    <TreeNodeParent>Queries</TreeNodeParent>
    <TreeNodeLeaf>TrafficStop</TreeNodeLeaf>
    <Field>
      <Name>curlat</Name>
      <Value>CurrentLocationLat</Value>
    </Field>
    <Field>
      <Name>curlong</Name>
      <Value>CurrentLocationLong</Value>
    </Field>
    <Field>
      <Name>sellat</Name>
      <Value>SelectedLocationLat</Value>
    </Field>
    <Field>
      <Name>sellong</Name>
      <Value>SelectedLocationLong</Value>
    </Field>
    <Field>
      <Name>CallTaking_Performed_By</Name>
      <Value>CurrentUser</Value>
    </Field>
    <BackgroundImage>
      <ImageFile>\Bitmaps\StopRed.png</ImageFile>
    </BackgroundImage>
    <IconID>-1</IconID>
    <FontSize>12</FontSize>
    <FKeyNumber>6</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Incidents</Type>
    <BackgroundImage>
      <ImageFile>\Bitmaps\Phonered.png</ImageFile>
    </BackgroundImage>
    <IconID>-1</IconID>
    <FontSize>12</FontSize>
    <FKeyNumber>9</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Form</Type>
    <Caption>Units</Caption>
    <TreeNodeParent>Units</TreeNodeParent>
    <TreeNodeLeaf>Units</TreeNodeLeaf>
    <BackgroundImage>
      <ImageFile>\Bitmaps\SquadBlue2.png</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
    <FKeyNumber>11</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Messages</Type>
    <Size />
    <MoveCaret />
    <BackgroundImage>
      <ImageFile>\Bitmaps\MsgYellow2.png</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
    <FKeyNumber>7</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Form</Type>
    <Caption>Query</Caption>
    <TreeNodeParent>Messages</TreeNodeParent>
    <TreeNodeLeaf>Query Rsp</TreeNodeLeaf>
    <BackgroundImage>
      <ImageFile>\Bitmaps\QueryResponse.png</ImageFile>
    </BackgroundImage>
    <IconID>-1</IconID>
    <FontSize>12</FontSize>
    <FKeyNumber>8</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
  </Button>
  <Button>
    <Type>Toolbar</Type>
    <Caption>Status</Caption>
    <BackgroundImage>
      <ImageFile>\Bitmaps\orangechk2.gif</ImageFile>
    </BackgroundImage>
    <IconID>0</IconID>
    <FontSize>12</FontSize>
    <FKeyNumber>10</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <Toolbar>
      <RelativeLocationX>0</RelativeLocationX>
      <RelativeLocationY>0</RelativeLocationY>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AV</Caption>
        <StatusValue>1</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <AutomaticOutOfVehicle>false</AutomaticOutOfVehicle>
        <StatusRequestRequires>NA</StatusRequestRequires>
        <Size>
          <Width>75</Width>
          <Height>75</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <IconID>1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>10</FKeyNumber>
        <HoldCtrlKey>true</HoldCtrlKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>CLEAR</Caption>
        <StatusValue>1</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ClearCall</TreeNodeLeaf>
        <Size />
        <MoveCaret />
        <IconID>2</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>5</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>C4</Caption>
        <StatusValue>11</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <Size />
        <MoveCaret />
        <IconID>2</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>TP</Caption>
        <StatusValue>12</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>Transport</TreeNodeLeaf>
        <Field>
          <Name>firstname</Name>
          <Value>CurrentIncidentFieldPatient First Name</Value>
        </Field>
        <Field>
          <Name>lastname</Name>
          <Value>CurrentIncidentFieldPatient Last Name</Value>
        </Field>
        <Field>
          <Name>location</Name>
          <Value>CurrentIncidentFieldTransport To Location</Value>
        </Field>
        <Field>
          <Name>Address</Name>
          <Value>CurrentIncidentFieldTransport To Address</Value>
        </Field>
        <Field>
          <Name>City</Name>
          <Value>CurrentIncidentFieldTransport To City</Value>
        </Field>
        <Field>
          <Name>transportstate</Name>
          <Value>CurrentIncidentFieldTransport To State</Value>
        </Field>
        <Field>
          <Name>Zip</Name>
          <Value>CurrentIncidentFieldTransport To Zip</Value>
        </Field>
        <Field>
          <Name>transportationprotocol</Name>
          <Value>CurrentIncidentFieldTransport Protocol</Value>
        </Field>
        <Field>
          <Name>transportationapartment</Name>
          <Value>CurrentIncidentFieldTransport To Apartment</Value>
        </Field>
        <Field>
          <Name>transportationbuilding</Name>
          <Value>CurrentIncidentFieldTransport To Building</Value>
        </Field>
        <Size>
          <Width>75</Width>
          <Height>75</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <IconID>2</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>OOS</Caption>
        <StatusValue>5</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>OutOfService</TreeNodeLeaf>
        <Field>
          <Name>Reason</Name>
          <Value>CurrentOOSReason</Value>
        </Field>
        <Size />
        <MoveCaret />
        <IconID>0</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>E2</Caption>
        <StatusValue>20</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>Responding2ndLocation</TreeNodeLeaf>
        <Field>
          <Name>unitname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <Field>
          <Name>unitname1</Name>
          <Value>CurrentIncidentFieldSec1 Unit</Value>
        </Field>
        <Field>
          <Name>location1</Name>
          <Value>CurrentIncidentFieldSec1 Location Name</Value>
        </Field>
        <Field>
          <Name>address1</Name>
          <Value>CurrentIncidentFieldSec1 Location Address</Value>
        </Field>
        <Field>
          <Name>city1</Name>
          <Value>CurrentIncidentFieldSec1 Location City</Value>
        </Field>
        <Field>
          <Name>state1</Name>
          <Value>CurrentIncidentFieldSec1 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode1</Name>
          <Value>CurrentIncidentFieldSec1 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt1</Name>
          <Value>CurrentIncidentFieldSec1 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building1</Name>
          <Value>CurrentIncidentFieldSec1 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname2</Name>
          <Value>CurrentIncidentFieldSec2 Unit</Value>
        </Field>
        <Field>
          <Name>location2</Name>
          <Value>CurrentIncidentFieldSec2 Location Name</Value>
        </Field>
        <Field>
          <Name>address2</Name>
          <Value>CurrentIncidentFieldSec2 Location Address</Value>
        </Field>
        <Field>
          <Name>city2</Name>
          <Value>CurrentIncidentFieldSec2 Location City</Value>
        </Field>
        <Field>
          <Name>state2</Name>
          <Value>CurrentIncidentFieldSec2 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode2</Name>
          <Value>CurrentIncidentFieldSec2 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt2</Name>
          <Value>CurrentIncidentFieldSec2 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building2</Name>
          <Value>CurrentIncidentFieldSec2 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname3</Name>
          <Value>CurrentIncidentFieldSec3 Unit</Value>
        </Field>
        <Field>
          <Name>location3</Name>
          <Value>CurrentIncidentFieldSec3 Location Name</Value>
        </Field>
        <Field>
          <Name>address3</Name>
          <Value>CurrentIncidentFieldSec3 Location Address</Value>
        </Field>
        <Field>
          <Name>city3</Name>
          <Value>CurrentIncidentFieldSec3 Location City</Value>
        </Field>
        <Field>
          <Name>state3</Name>
          <Value>CurrentIncidentFieldSec3 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode3</Name>
          <Value>CurrentIncidentFieldSec3 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt3</Name>
          <Value>CurrentIncidentFieldSec3 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building3</Name>
          <Value>CurrentIncidentFieldSec3 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname4</Name>
          <Value>CurrentIncidentFieldSec4 Unit</Value>
        </Field>
        <Field>
          <Name>location4</Name>
          <Value>CurrentIncidentFieldSec4 Location Name</Value>
        </Field>
        <Field>
          <Name>address4</Name>
          <Value>CurrentIncidentFieldSec4 Location Address</Value>
        </Field>
        <Field>
          <Name>city4</Name>
          <Value>CurrentIncidentFieldSec4 Location City</Value>
        </Field>
        <Field>
          <Name>state4</Name>
          <Value>CurrentIncidentFieldSec4 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode4</Name>
          <Value>CurrentIncidentFieldSec4 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt4</Name>
          <Value>CurrentIncidentFieldSec4 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building4</Name>
          <Value>CurrentIncidentFieldSec4 Location Building</Value>
        </Field>
        <Size>
          <Width>75</Width>
          <Height>75</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <IconID>2</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>A2</Caption>
        <StatusValue>21</StatusValue>
        <DontShowStatusOverrideMsg>true</DontShowStatusOverrideMsg>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AtScene2ndLocation</TreeNodeLeaf>
        <Field>
          <Name>unitname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <Field>
          <Name>unitname1</Name>
          <Value>CurrentIncidentFieldSec1 Unit</Value>
        </Field>
        <Field>
          <Name>location1</Name>
          <Value>CurrentIncidentFieldSec1 Location Name</Value>
        </Field>
        <Field>
          <Name>address1</Name>
          <Value>CurrentIncidentFieldSec1 Location Address</Value>
        </Field>
        <Field>
          <Name>city1</Name>
          <Value>CurrentIncidentFieldSec1 Location City</Value>
        </Field>
        <Field>
          <Name>state1</Name>
          <Value>CurrentIncidentFieldSec1 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode1</Name>
          <Value>CurrentIncidentFieldSec1 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt1</Name>
          <Value>CurrentIncidentFieldSec1 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building1</Name>
          <Value>CurrentIncidentFieldSec1 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname2</Name>
          <Value>CurrentIncidentFieldSec2 Unit</Value>
        </Field>
        <Field>
          <Name>location2</Name>
          <Value>CurrentIncidentFieldSec2 Location Name</Value>
        </Field>
        <Field>
          <Name>address2</Name>
          <Value>CurrentIncidentFieldSec2 Location Address</Value>
        </Field>
        <Field>
          <Name>city2</Name>
          <Value>CurrentIncidentFieldSec2 Location City</Value>
        </Field>
        <Field>
          <Name>state2</Name>
          <Value>CurrentIncidentFieldSec2 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode2</Name>
          <Value>CurrentIncidentFieldSec2 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt2</Name>
          <Value>CurrentIncidentFieldSec2 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building2</Name>
          <Value>CurrentIncidentFieldSec2 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname3</Name>
          <Value>CurrentIncidentFieldSec3 Unit</Value>
        </Field>
        <Field>
          <Name>location3</Name>
          <Value>CurrentIncidentFieldSec3 Location Name</Value>
        </Field>
        <Field>
          <Name>address3</Name>
          <Value>CurrentIncidentFieldSec3 Location Address</Value>
        </Field>
        <Field>
          <Name>city3</Name>
          <Value>CurrentIncidentFieldSec3 Location City</Value>
        </Field>
        <Field>
          <Name>state3</Name>
          <Value>CurrentIncidentFieldSec3 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode3</Name>
          <Value>CurrentIncidentFieldSec3 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt3</Name>
          <Value>CurrentIncidentFieldSec3 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building3</Name>
          <Value>CurrentIncidentFieldSec3 Location Building</Value>
        </Field>
        <Field>
          <Name>unitname4</Name>
          <Value>CurrentIncidentFieldSec4 Unit</Value>
        </Field>
        <Field>
          <Name>location4</Name>
          <Value>CurrentIncidentFieldSec4 Location Name</Value>
        </Field>
        <Field>
          <Name>address4</Name>
          <Value>CurrentIncidentFieldSec4 Location Address</Value>
        </Field>
        <Field>
          <Name>city4</Name>
          <Value>CurrentIncidentFieldSec4 Location City</Value>
        </Field>
        <Field>
          <Name>state4</Name>
          <Value>CurrentIncidentFieldSec4 Location State</Value>
        </Field>
        <Field>
          <Name>postalcode4</Name>
          <Value>CurrentIncidentFieldSec4 Location Zip</Value>
        </Field>
        <Field>
          <Name>roomapt4</Name>
          <Value>CurrentIncidentFieldSec4 Location Apartment</Value>
        </Field>
        <Field>
          <Name>building4</Name>
          <Value>CurrentIncidentFieldSec4 Location Building</Value>
        </Field>
        <IconID>2</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>STATUS BUTTONS</Caption>
        <Size>
          <Width>350</Width>
          <Height>450</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <VerticalDistance>-400</VerticalDistance>
        </MoveCaret>
        <FontSize>16</FontSize>
        <BackColor>
          <ColorRed>3</ColorRed>
          <ColorGreen>128</ColorGreen>
          <ColorBlue>252</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Run</Type>
        <Size>
          <Width>100</Width>
          <Height>100</Height>
        </Size>
        <MoveCaret>
          <VerticalDistance>0</VerticalDistance>
        </MoveCaret>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>128</ColorBlue>
        </BackColor>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>********************</Caption>
        <BackColor>
          <ColorRed>0</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>128</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>*** Hidden Below ***</Caption>
      </Button>
      <Button>
        <Type>ViewIncidentDetails</Type>
        <Caption>CURR CALL</Caption>
        <MoveCaret />
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <BackColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>128</ColorBlue>
        </BackColor>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>PR</Caption>
        <StatusValue>13</StatusValue>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AtDestination</TreeNodeLeaf>
        <MoveCaret />
        <IconID>2</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AP</Caption>
        <StatusValue>17</StatusValue>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>TO</Caption>
        <StatusValue>8</StatusValue>
        <AutomaticOutOfVehicle>false</AutomaticOutOfVehicle>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>EnrouteToPost</TreeNodeLeaf>
        <Size>
          <Width>75</Width>
          <Height>75</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <IconID>0</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>LA</Caption>
        <StatusValue>3</StatusValue>
        <AutomaticOutOfVehicle>false</AutomaticOutOfVehicle>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <IconID>1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>9</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>C5</Caption>
        <StatusValue>14</StatusValue>
        <AutomaticOutOfVehicle>false</AutomaticOutOfVehicle>
        <StatusRequestRequires>AnIncident</StatusRequestRequires>
        <IconID>2</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>IQ</Caption>
        <StatusValue>2</StatusValue>
        <AutomaticOutOfVehicle>true</AutomaticOutOfVehicle>
        <StatusRequestRequires>NoIncident</StatusRequestRequires>
        <IconID>1</IconID>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>DI</Caption>
        <StatusValue>6</StatusValue>
        <AutomaticOutOfVehicle>false</AutomaticOutOfVehicle>
        <Size />
        <IconID>2</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AOS</Caption>
        <StatusValue>4</StatusValue>
        <IconID>1</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>OFF</Caption>
        <StatusValue>0</StatusValue>
        <IconID>0</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>AE</Caption>
        <StatusValue>15</StatusValue>
        <IconID>1</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>SP</Caption>
        <StatusValue>16</StatusValue>
        <IconID>0</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>BR</Caption>
        <StatusValue>17</StatusValue>
        <IconID>2</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>MA</Caption>
        <StatusValue>18</StatusValue>
        <IconID>2</IconID>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Status</Type>
        <Caption>D2</Caption>
        <StatusValue>19</StatusValue>
        <IconID>2</IconID>
        <IsHidden>true</IsHidden>
      </Button>
    </Toolbar>
  </Button>
  <Button>
    <Type>Map</Type>
    <Size />
    <BackgroundImage>
      <ImageFile>\Bitmaps\MapColor.png</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
  </Button>
  <Button>
    <Type>Toolbar</Type>
    <Caption>MORE</Caption>
    <BackgroundImage>
      <ImageFile>\Bitmaps\Down.Arrow.Blue.Large.png</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
    <FKeyNumber>12</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <Toolbar>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Phone Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ReversePhoneSearch</TreeNodeLeaf>
        <Size>
          <Width>95</Width>
          <Height>95</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>8</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Loc Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>RadiusSearch</TreeNodeLeaf>
        <Field>
          <Name>curlat</Name>
          <Value>CurrentLocationLat</Value>
        </Field>
        <Field>
          <Name>curlong</Name>
          <Value>CurrentLocationLong</Value>
        </Field>
        <Field>
          <Name>sellat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>sellong</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <Size />
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Incident Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>IncidentSearch</TreeNodeLeaf>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Get Incident</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GetIncident</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>SelectedIncident</Value>
        </Field>
        <Size />
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\Update Icon.png</ImageFile>
        </BackgroundImage>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>11</FKeyNumber>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Position Update</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PositionUpdate</TreeNodeLeaf>
        <Field>
          <Name>lat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>long</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Update Position Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>6</FKeyNumber>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Preimse Info</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GetPremiseInfo</TreeNodeLeaf>
        <Field>
          <Name>location</Name>
          <Value>SelectedIncidentFieldlocation_name</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <BackgroundImage>
          <ImageFile>\Bitmaps\House Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Request Case#</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>RequestCaseNumber</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>CurrentIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>CurrentIncidentID</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <BackgroundImage>
          <ImageFile>\Bitmaps\10-4 Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>On Site</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>OnSite</TreeNodeLeaf>
        <Field>
          <Name>curlat</Name>
          <Value>CurrentLocationLat</Value>
        </Field>
        <Field>
          <Name>curlong</Name>
          <Value>CurrentLocationLong</Value>
        </Field>
        <Field>
          <Name>sellat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>sellong</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <Field>
          <Name>CallTaking_Performed_By</Name>
          <Value>CurrentUser</Value>
        </Field>
        <Size />
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\ColorArrive2.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>********************</Caption>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Incident Persnnel</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GetIncidentPersonnel</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>SelectedIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>SelectedIncidentID</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <Size>
          <Width>95</Width>
          <Height>95</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>8</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Incident Summry</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>IncidentSummaryQuery</TreeNodeLeaf>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Person Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PersonnelSearch</TreeNodeLeaf>
        <Size />
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Unit History</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>UnitHistory</TreeNodeLeaf>
        <Field>
          <Name>radioname</Name>
          <Value>SelectedUnit</Value>
        </Field>
        <Size />
        <MoveCaret>
          <HorizontalDistance>8</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Unit History Icon.png</ImageFile>
        </BackgroundImage>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>7</FKeyNumber>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Change Problem</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ChangeProblemNature</TreeNodeLeaf>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Check List Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>FindAddress</Type>
        <Caption>Find Address</Caption>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Unit Times</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>IncidentTimes</TreeNodeLeaf>
        <Field>
          <Name>radioname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Radio Traffic Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>********************</Caption>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>********************</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>URLs</Caption>
        <Size>
          <Width>750</Width>
          <Height>24</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>12</FontSize>
        <BackColor>
          <ColorRed>128</ColorRed>
          <ColorGreen>128</ColorGreen>
          <ColorBlue>192</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>0</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Open>
          <FileName>http://www.google.com</FileName>
          <Arguments>Arguments0</Arguments>
        </Open>
        <Size>
          <Width>95</Width>
          <Height>65</Height>
        </Size>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Google.logo.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Open>
          <FileName>http://www.govtogovsolutions.org/Default.aspx?tabid=74</FileName>
          <Arguments>Arguments0</Arguments>
        </Open>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Pawn.System.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Open>
          <FileName>https://dps.mn.gov/divisions/bca/Pages/default.aspx</FileName>
          <Arguments>Arguments0</Arguments>
        </Open>
        <Size />
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\BCA.logo.gif</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Open>
          <FileName>https://por.state.mn.us</FileName>
          <Arguments>Arguments0</Arguments>
        </Open>
        <BackgroundImage>
          <ImageFile>\Bitmaps\POR.logo.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Open>
          <FileName>https://HENRAP.state.mn.us</FileName>
          <Arguments>Arguments0</Arguments>
        </Open>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Henn.RAP.logo.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Open>
          <FileName>https://mrap.x.state.mn.us/maint/index.html</FileName>
          <Arguments>Arguments0</Arguments>
        </Open>
        <BackgroundImage>
          <ImageFile>\Bitmaps\MN.MRAP.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>********************</Caption>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>OTHER</Caption>
        <Size>
          <Width>750</Width>
          <Height>24</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>12</FontSize>
        <BackColor>
          <ColorRed>128</ColorRed>
          <ColorGreen>128</ColorGreen>
          <ColorBlue>192</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>0</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Toolbar</Type>
        <Caption>Black Out Screen</Caption>
        <VoiceCommand>Black out Screen</VoiceCommand>
        <Size>
          <Width>95</Width>
          <Height>95</Height>
        </Size>
        <MoveCaret />
        <FontSize>13</FontSize>
        <FKeyNumber>1</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
        <BackColor>
          <ColorRed>0</ColorRed>
          <ColorGreen>0</ColorGreen>
          <ColorBlue>0</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
        <Toolbar>
          <RelativeLocationX>-175</RelativeLocationX>
          <RelativeLocationY>-375</RelativeLocationY>
          <Button>
            <Type>Run</Type>
            <Caption>CLICK ANYWHERE TO RETURN.</Caption>
            <Size>
              <Width>1050</Width>
              <Height>730</Height>
            </Size>
            <BackgroundImage>
              <ImageFile>\Bitmaps\Mtka.blackout.png</ImageFile>
            </BackgroundImage>
            <BackColor>
              <ColorRed>0</ColorRed>
              <ColorGreen>0</ColorGreen>
              <ColorBlue>0</ColorBlue>
            </BackColor>
            <ForeColor>
              <ColorRed>255</ColorRed>
              <ColorGreen>0</ColorGreen>
              <ColorBlue>0</ColorBlue>
            </ForeColor>
          </Button>
        </Toolbar>
      </Button>
      <Button>
        <Type>ResetPassword</Type>
        <Caption>Reset Passwd</Caption>
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\Change Password Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>NewMessage</Type>
        <Caption>New Msg</Caption>
        <Size />
        <MoveCaret />
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>NavBarToggle</Type>
        <Size />
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\Nav Bar Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>4</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>DayNightToggle</Type>
        <Size />
        <MoveCaret />
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Modfy Logon</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ModifyLogon</TreeNodeLeaf>
        <Field>
          <Name>LoggedinUser</Name>
          <Value>CurrentUser</Value>
        </Field>
        <Size />
        <MoveCaret />
        <BackgroundImage>
          <ImageFile>\Bitmaps\Key Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Log Info</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>MinnetonkaInfo</TreeNodeLeaf>
        <Field>
          <Name>radioname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Info Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>2</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>true</HoldCtrlKey>
        <HoldShiftKey>false</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Logout</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>Logout</TreeNodeLeaf>
        <Size>
          <Width>95</Width>
          <Height>95</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>8</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Log Off Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>10</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>********************</Caption>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>********************</Caption>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>0</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>.</Caption>
        <Size>
          <Width>865</Width>
          <Height>540</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>-890</HorizontalDistance>
          <VerticalDistance>-800</VerticalDistance>
        </MoveCaret>
        <FontSize>16</FontSize>
        <BackColor>
          <ColorRed>192</ColorRed>
          <ColorGreen>192</ColorGreen>
          <ColorBlue>192</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>192</ColorRed>
          <ColorGreen>192</ColorGreen>
          <ColorBlue>192</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>.</Caption>
        <Size>
          <Width>865</Width>
          <Height>175</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <VerticalDistance>-330</VerticalDistance>
        </MoveCaret>
        <BackColor>
          <ColorRed>121</ColorRed>
          <ColorGreen>187</ColorGreen>
          <ColorBlue>253</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>121</ColorRed>
          <ColorGreen>187</ColorGreen>
          <ColorBlue>253</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>.</Caption>
        <Size>
          <Width>865</Width>
          <Height>207</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <VerticalDistance>-210</VerticalDistance>
        </MoveCaret>
        <BackColor>
          <ColorRed>192</ColorRed>
          <ColorGreen>192</ColorGreen>
          <ColorBlue>192</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>192</ColorRed>
          <ColorGreen>192</ColorGreen>
          <ColorBlue>192</ColorBlue>
        </ForeColor>
      </Button>
      <Button>
        <Type>Separator</Type>
        <Caption>********************</Caption>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>*** Hidden Below ***</Caption>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Associate Radio</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GetPersonnelRadios</TreeNodeLeaf>
        <Field>
          <Name>unitname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>On View</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>OnView</TreeNodeLeaf>
        <Field>
          <Name>curlat</Name>
          <Value>CurrentLocationLat</Value>
        </Field>
        <Field>
          <Name>curlong</Name>
          <Value>CurrentLocationLong</Value>
        </Field>
        <Field>
          <Name>sellat</Name>
          <Value>SelectedLocationLat</Value>
        </Field>
        <Field>
          <Name>sellong</Name>
          <Value>SelectedLocationLong</Value>
        </Field>
        <Field>
          <Name>CallTaking_Performed_By</Name>
          <Value>CurrentUser</Value>
        </Field>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Send Page</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>SendPage</TreeNodeLeaf>
        <Field>
          <Name>SenderUnit</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Unit Status</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>SpecificUnitStatus</TreeNodeLeaf>
        <Field>
          <Name>radioname</Name>
          <Value>SelectedUnit</Value>
        </Field>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Person Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Actv Incd</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>ActiveIncidents</TreeNodeLeaf>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>9</FKeyNumber>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Station Viewer</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>StationViewer</TreeNodeLeaf>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Actvty Log</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AddActivityLogComment</TreeNodeLeaf>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Div Unit Query</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>DivisionUnitQuery</TreeNodeLeaf>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Allied Agencies</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>AlliedAgencies</TreeNodeLeaf>
        <Field>
          <Name>incidentnumber</Name>
          <Value>SelectedIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>SelectedIncidentID</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Other Unit Times</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>IncidentTimes_Sel</TreeNodeLeaf>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Vehicle Search</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>VehicleSearch</TreeNodeLeaf>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Sent Msgs</Caption>
        <TreeNodeParent>Messages</TreeNodeParent>
        <TreeNodeLeaf>Sent Msgs</TreeNodeLeaf>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>0</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Trash</Caption>
        <TreeNodeParent>Messages</TreeNodeParent>
        <TreeNodeLeaf>Trash</TreeNodeLeaf>
        <IconID>-1</IconID>
        <FontSize>13</FontSize>
        <FKeyNumber>9</FKeyNumber>
        <HoldAltKey>true</HoldAltKey>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Pend Incd</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PendingIncidents</TreeNodeLeaf>
        <BackgroundImage>
          <ImageFile>\Bitmaps\Search Icon.png</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>10</FKeyNumber>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>InfoBarToggle</Type>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Prim Unit</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>PrimaryUnitRequest</TreeNodeLeaf>
        <Field>
          <Name>unitname</Name>
          <Value>CurrentUnit</Value>
        </Field>
        <Field>
          <Name>incidentnumber</Name>
          <Value>CurrentIncident</Value>
        </Field>
        <Field>
          <Name>incidentid</Name>
          <Value>CurrentIncidentID</Value>
        </Field>
        <Field>
          <Name>AutoSubmit</Name>
          <Value>IfFieldHasValue</Value>
        </Field>
        <FontSize>13</FontSize>
        <FKeyNumber>5</FKeyNumber>
        <HoldShiftKey>true</HoldShiftKey>
        <IsHidden>true</IsHidden>
      </Button>
      <Button>
        <Type>OutOfVehicleToggle</Type>
        <Size>
          <Width>95</Width>
          <Height>95</Height>
        </Size>
        <FontSize>13</FontSize>
        <IsHidden>true</IsHidden>
      </Button>
    </Toolbar>
  </Button>
  <Button>
    <Type>ViewIncidentDetails</Type>
    <Caption>Current</Caption>
    <BackgroundImage>
      <ImageFile>\Bitmaps\CurrentCall.png</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
    <FKeyNumber>1</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>true</HoldShiftKey>
  </Button>
  <Button>
    <Type>Run</Type>
    <Caption>*** hidden Below ***</Caption>
    <IsHidden>true</IsHidden>
  </Button>
  <Button>
    <Type>Emergency</Type>
    <FontSize>9</FontSize>
    <IsHidden>true</IsHidden>
  </Button>
  <Button>
    <Type>Status</Type>
    <Caption>ST</Caption>
    <StatusValue>9</StatusValue>
    <StatusRequestRequires>AnIncident</StatusRequestRequires>
    <IconID>2</IconID>
    <FKeyNumber>0</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <IsHidden>true</IsHidden>
  </Button>
  <Button>
    <Type>Toolbar</Type>
    <Caption>NCIC</Caption>
    <Size>
      <Width>80</Width>
      <Height>80</Height>
    </Size>
    <BackgroundImage>
      <ImageFile>\Bitmaps\Down.Arrow.Blue.Large.png</ImageFile>
    </BackgroundImage>
    <FontSize>12</FontSize>
    <FKeyNumber>0</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <IsHidden>true</IsHidden>
    <Toolbar>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>GUN</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>GunCheck</TreeNodeLeaf>
        <Size>
          <Width>75</Width>
          <Height>75</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\icongunDN.gif</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
        <FKeyNumber>2</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>DNR</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>DNRCheck</TreeNodeLeaf>
        <Size>
          <Width>75</Width>
          <Height>75</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <FontSize>13</FontSize>
        <FKeyNumber>3</FKeyNumber>
        <HoldAltKey>false</HoldAltKey>
        <HoldCtrlKey>false</HoldCtrlKey>
        <HoldShiftKey>true</HoldShiftKey>
      </Button>
      <Button>
        <Type>Form</Type>
        <Caption>Free Form</Caption>
        <TreeNodeParent>Queries</TreeNodeParent>
        <TreeNodeLeaf>FreeForm</TreeNodeLeaf>
        <Size>
          <Width>75</Width>
          <Height>75</Height>
        </Size>
        <MoveCaret>
          <HorizontalDistance>10</HorizontalDistance>
        </MoveCaret>
        <BackgroundImage>
          <ImageFile>\Bitmaps\iconfreeform.gif</ImageFile>
        </BackgroundImage>
        <FontSize>13</FontSize>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>SPACER</Caption>
        <Size>
          <Width>1</Width>
          <Height>1</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>10</HorizontalDistance>
          <VerticalDistance>10</VerticalDistance>
        </MoveCaret>
      </Button>
      <Button>
        <Type>Run</Type>
        <Caption>NCIC QUERIES</Caption>
        <Size>
          <Width>275</Width>
          <Height>450</Height>
        </Size>
        <MoveCaret>
          <NewLine>true</NewLine>
          <HorizontalDistance>-800</HorizontalDistance>
          <VerticalDistance>-400</VerticalDistance>
        </MoveCaret>
        <FontSize>16</FontSize>
        <BackColor>
          <ColorRed>3</ColorRed>
          <ColorGreen>128</ColorGreen>
          <ColorBlue>252</ColorBlue>
        </BackColor>
        <ForeColor>
          <ColorRed>255</ColorRed>
          <ColorGreen>255</ColorGreen>
          <ColorBlue>255</ColorBlue>
        </ForeColor>
      </Button>
    </Toolbar>
  </Button>
  <Button>
    <Type>Forward</Type>
    <FKeyNumber>0</FKeyNumber>
    <HoldAltKey>false</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <IsHidden>true</IsHidden>
  </Button>
  <Button>
    <Type>Run</Type>
    <Caption>Email Reply</Caption>
    <Open>
      <FileName>C:\Program Files (x86)\TriTech Software Systems\VisiNet Mobile\Mobile Client\Email.Reply.vbs</FileName>
      <Arguments>Arguments0</Arguments>
    </Open>
    <FKeyNumber>1</FKeyNumber>
    <HoldAltKey>true</HoldAltKey>
    <HoldCtrlKey>false</HoldCtrlKey>
    <HoldShiftKey>false</HoldShiftKey>
    <IsHidden>true</IsHidden>
  </Button>
</Toolbar>