<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Clear Call</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Clear Call</H4>
						<form action="ClearCallQuery.aspx?queryfile=clearcall.qry" method="post" id="ClearCallQuery"
							name="ClearCallQuery">
							<table ID="Table2">

								<tr>
									<td><b>Response Disposition:</b></td>
									<td><b>Comments:</b></td>
								</tr>

								<tr>
									<td>
										<!-- Root item requires a SPAN element to contain it. -->
										<XML id="dispositionstyle" src="genericlist.xsl"></XML>
										<SPAN type="selectlist" id="dispositionvals" name="dispositionvals">
											<XML id="dispositionsource" src="DispositionCodes.xml"></XML>
										</SPAN>
									</td>
									<td valign="top">
										<textarea style="width:350px;" id="comments" name="comments" rows="6" onkeyup="CheckTextAreaLength(this.form.comments,200);"></textarea>
										<br>
										<input type="submit" name="Query" id="Query" value="Submit">
									</td>
								</tr>

								<!--<tr>
									<td NOWRAP><b>Patient(s) Seen:</b></td>
								</tr>
								<tr>
									<td><input type="text" align="left" name="PatientsSeen" id="PatientsSeen" size="15" value="" numeric="true"></td>
								</tr>
									<td><b>Comments:</b></td>
								</tr>-->

							</table>
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<script language="javascript">

	function window.onload()
	{
		dispositionvals.innerHTML = GenerateListBox("ResponseDisposition", "8", dispositionsource, dispositionstyle);
	    ClearCallQuery.ResponseDisposition.focus();
	}
	</script>
</HTML>
