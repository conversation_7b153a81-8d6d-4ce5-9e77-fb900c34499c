<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Unit Status Query</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Unit Status Query</H4>
						<form action="UnitStatusQuery.aspx?queryfile=unitstatus.qry" method="post" id="UnitStatusQuery"
							name="UnitStatusQuery">
							Get unit status for the selected unit:
							<p>
							</p>
							<table ID="Table2">
								<tr>
									<td valign="top"><b>Radio/ Unit Name:</b></td>
									<td>
										<XML id="radiostyle" src="configurationunits.xsl"></XML>
										<SPAN type="selectlist" id="radiovals" name="radiovals">
											<XML id="radiosource" src="../Configuration_Units.xml"></XML>
										</SPAN>
									</td>
								</tr>
							</table>
							<br>
							<input type="submit" name="Query" id="Query" value="Query">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">
		function window.onload()
		{
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			radiovals.innerHTML = GenerateSelectBox("radioname", radiosource, radiostyle, true, false, false, 4, false, false);
			UnitStatusQuery.radioname.focus();
		}
	</SCRIPT>
</HTML>
