<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <title>VisiNET Mobile - DNR Check</title>
    <link href="normalstyle.css" type="text/css" rel="stylesheet">
</head>
<body>
    <table id="Table1" class="base" cellpadding="10" align="center" border="0">
        <tbody>
            <tr>
                <td valign="top">
                    <h4 align="center">DNR Check</h4>
                    <form id="Form" name="Form" action="DNRCheck.aspx?queryfile=DNRCheck.qry" method="post">
                    <p>
                        <table>
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td width="150px" valign="middle" rowspan="2"><b>Check Requested:</b></td>
                                            <td valign="bottom"><center>Boat</center></td>
                                            <td width="150px" valign="bottom"><center>Snowmobile</center></td>
                                            <td valign="bottom"><center>Off-Road</center></td>
                                            <td width="50px"></td>
                                            <td valign="bottom"><center>Include<br>Images</center></td>
                                        </tr>
                                        <tr>
                                            <td><center><label for="doQBOT" onclick="toggleCheckbox('doQBOT')"><input id="doQBOT" name="MKE" type="radio" class="bigradio" value="doQBOT" checked title="This option performs the QBOT query" /></label></center></td>
                                            <td><center><label for="doQSNO" onclick="toggleCheckbox('doQSNO')"><input id="doQSNO" name="MKE" type="radio" class="bigradio" value="doQSNO" title="This option performs the QSNO query" /></label></center></td>
                                            <td><center><label for="doQORV" onclick="toggleCheckbox('doQORV')"><input id="doQORV" name="MKE" type="radio" class="bigradio" value="doQORV" title="This option performs the QORV query" /></label></center></td>
                                            <td></td>
                                            <td><center><input id="includeImages" name="includeImages" type="checkbox" class="bigcheckbox" title="Select this option to include images in the return" /></center></td>
                                        </tr>
										<tr>
											<td style="text-align:center">
												<input id="doSupplement" name="doSupplement" type="checkbox" checked style="display:none;" />
												<!--<label id="labelSupplement">Supplement</label>-->
											</td>
<!--
											<td style="text-align:center">
												<input id="doSupplementByVin" name="doSupplementByVin" type="checkbox" checked style="display:none;" />
												<!--<label id="labelSupplementByVin">Supplement By Vin</label>
											</td>
											<td style="text-align:center">
												<input id="doSupplementByLic" name="doSupplementByLic" type="checkbox" checked style="display:none;" />
												<label id="labelSupplementByLic">Supplement By Lic</label>
											</td>
-->
											<td  style="text-align:center">
												<input id="doRms" name="doRms" type="checkbox" checked style="display:none;" />
												<!--<label id="labelRms">RMS</label>-->
											</td>
<!--
											<td style="text-align:center">
												<input id="doRmsByLic" name="doRmsByLic" type="checkbox" checked style="display:none;" />
												<label id="labelRmsLic">RMS By Lic</label>
											</td>
											<td style="text-align:center">
												<input id="doRmsByVin" name="doRmsByVin" type="checkbox" checked style="display:none;" />
												<label id="labelRmsByVin">RMS By Vin</label>
											</td>
-->
										</tr>
                                    </table>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td width="150px" align="right" valign="middle">State:</td>
                                            <td>
                                                <xml id="statestyle" src="genericselect.xsl"></xml>
                                                <span id="statevals" name="statevals" type="selectlist">
                                                    <xml id="statesource" src="ncicstates.xml"></xml>
                                                </span>
								            </td>
								        </tr>
									</table>
                                    <table id="BoatTable" style="display:table">
                                        <tr>
                                            <td width="150px" align="right" valign="middle">Registration #:</td>
                                            <td><input id="REG1" name="REG1" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for MN DNR queries" /></td>
                                        </tr>
										<tr>
											<td align="right" valign="middle">Lic. Year:</td>
											<td><input id="LIY1" name="LIY1" type="text" size="4" maxlength="4" /><font size="1">&nbsp;(YYYY)</font></td>
										</tr>
                                        <tr>
                                            <td width="150px" align="right" valign="middle">Boat Hull #:</td>
                                            <td><input id="BHN1" name="BHN1" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for out-of-state vehicle queries" /></td>
                                        </tr>
                                        <tr>
                                            <td width="150px" align="right" valign="middle">Owner #:</td>
                                            <td><input id="OAN1" name="OAN1" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for out-of-state vehicle queries" /></td>
                                        </tr>
                                        <tr>
                                            <td width="150px" align="right" valign="middle">Owner Name:</td>
                                            <td><input id="LNAM1" name="LNAM1" type="text" size="15" title="Max 15 characters" placeholder="Last Name" />,&nbsp;
                                            	<input id="FNAM1" name="FNAM1" type="text" size="12" title="Max 12 characters" placeholder="First Name" />&nbsp;
                                            	<input id="MNAM1" name="MNAM1" type="text" size="3" title="Max 3 characters" placeholder="MI" /></td>
                                        </tr>
										<tr>
											<td width="150px" align="right" valign="middle">DOB:</td>
											<td colspan="3"><input id="DOB1" name="DOB1" type="text" size="8" maxsize="8" numeric="true" /><font size="1">&nbsp;(YYYYMMDD)</font></td>
										</tr>
									</table>
									<table id="SnowTable" style="display:none">
                                        <tr>
                                            <td width="150px" align="right" valign="middle">Registration #:</td>
                                            <td><input id="REG2" name="REG2" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for out-of-state snowmobile queries" /></td>
                                        </tr>
										<tr>
											<td align="right" valign="middle">Lic. Year:</td>
											<td><input id="LIY2" name="LIY2" type="text" size="4" maxlength="4" /><font size="1">&nbsp;(YYYY)</font></td>
										</tr>
										<tr>
                                            <td width="150px" align="right" valign="middle">VIN:</td>
                                            <td><input id="VIN2" name="VIN2" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for out-of-state vehicle queries" /></td>
                                        </tr>
                                        <tr>
                                            <td width="150px" align="right" valign="middle">Serial #:</td>
                                            <td><input id="SER2" name="SER2" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for out-of-state vehicle queries" /></td>
                                        </tr>
                                        <tr>
                                            <td width="150px" align="right" valign="middle">Owner Name:</td>
                                            <td><input id="LNAM2" name="LNAM2" type="text" size="15" title="Max 15 characters" placeholder="Last Name" />,&nbsp;
                                            	<input id="FNAM2" name="FNAM2" type="text" size="12" title="Max 12 characters" placeholder="First Name" />&nbsp;
                                            	<input id="MNAM2" name="MNAM2" type="text" size="3" title="Max 3 characters" placeholder="MI" /></td>
                                        </tr>
										<tr>
											<td width="150px" align="right" valign="middle">DOB:</td>
											<td colspan="3"><input id="DOB2" name="DOB2" type="text" size="8" maxsize="8" numeric="true" /><font size="1">&nbsp;(YYYYMMDD)</font></td>
										</tr>
                                    </table>
									<table id="ORVTable" style="display:none">
                                        <tr>
                                            <td width="150px" align="right" valign="middle">Registration #:</td>
                                            <td><input id="REG3" name="REG3" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for out-of-state vehicle queries" /></td>
                                        </tr>
										<tr>
											<td align="right" valign="middle">Lic. Year:</td>
											<td><input id="LIY3" name="LIY3" type="text" size="4" maxlength="4" /><font size="1">&nbsp;(YYYY)</font></td>
										</tr>
										<tr>
                                            <td width="150px" align="right" valign="middle">VIN:</td>
                                            <td><input id="VIN3" name="VIN3" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for out-of-state vehicle queries" /></td>
                                        </tr>
                                                                                <tr>
                                            <td width="150px" align="right" valign="middle">Serial #:</td>
                                            <td><input id="SER3" name="SER3" type="text" size="20" maxlength="20" style="FONT-SIZE:50px;TEXT-TRANSFORM:uppercase;HEIGHT:50px;" title="Maximum 20 characters -- use this field for out-of-state vehicle queries" /></td>
                                        </tr>
<tr>
                                            <td width="150px" align="right" valign="middle">Owner Name:</td>
                                            <td><input id="LNAM3" name="LNAM3" type="text" size="15" title="Max 15 characters" placeholder="Last Name" />,&nbsp;
                                            	<input id="FNAM3" name="FNAM3" type="text" size="12" title="Max 12 characters" placeholder="First Name" />&nbsp;
                                            	<input id="MNAM3" name="MNAM3" type="text" size="3" title="Max 3 characters" placeholder="MI" /></td>
                                        </tr>
										<tr>
											<td width="150px" align="right" valign="middle">DOB:</td>
											<td colspan="3"><input id="DOB3" name="DOB3" type="text" size="8" maxsize="8" numeric="true" /><font size="1">&nbsp;(YYYYMMDD)</font></td>
										</tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </p>
                    <!--when click on this button, it call the validation.-->
                    <input id="Button" name="Query" type="button" value="Submit" onkeypress="validatepage()" onclick="validatepage()" />
                    <!--when validation is ok, it will call this button to submit the page.  This button is not visible because of it width is zero.-->
                    <input id="Submit" name="Submit" type="submit" value="Submit" style="width: 0px;" tabindex="-1" />

					<!-- DoQBOT -->
					<input id="LIS1" name="LIS1" type="hidden">
					<input id="includeImages1" name="includeImages1" type="hidden">

					<!-- DoQSNO -->
					<input id="LIS2" name="LIS2" type="hidden">
					<input id="includeImages2" name="includeImages2" type="hidden">

					<!-- DoQORV -->
					<input id="LIS3" name="LIS3" type="hidden">
					<input id="includeImages3" name="includeImages3" type="hidden">

		            <!-- doSupplement -->
					<input id="LastName" name="LastName" type="hidden">
					<input id="FirstName" name="FirstName" type="hidden">
					<input id="MiddleName" name="MiddleName" type="hidden">

					</form>
                </td>
            </tr>
        </tbody>
    </table>
    <script type="text/javascript" src="clientutilities.js"></script>
	<script type="text/javascript" language="javascript">
		var currentTime = new Date();
		var year = currentTime.getFullYear();

		function window.onload()
		{
			Form.LIY1.value = year;
			Form.LIY2.value = year;
			Form.LIY3.value = year;
			
			// Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
			Form.REG1.focus();
			PrepareValidation(Form);

			statevals.innerHTML = GenerateSelectBox("State", statesource, statestyle, false, false, false, 1, false, false);
			var items = Form.State.options;
			var itemsCount = items.length;
			for (i = 0; i < itemsCount; i++)
			{
				if (items(i).text == "Minnesota")
				{
					items(i).selected = true;
					break;
				}
			}

			Form.innerHTML = Form.innerHTML + GenerateVehicleSupplementHiddenFields();   //ensure that the supplement is correctly setup.
			Form.innerHTML = Form.innerHTML + GenerateVehicleRmsHiddenFields();  //ensure that the rms is correctly setup.
		}

		function window.validatepage() 
		{
			// Run doBOT Query
				if (document.getElementById('doQBOT').checked == true) 
				{
					Form.LIS1.value = Form.State.value;
					if (Form.includeImages.checked)
					{
						Form.includeImages1.value = "Y";
					}
					else
					{
						Form.includeImages1.value = "N";
					}

					// Run doSupplement
					Form.LastName.value = Form.LNAM1.value;
					Form.FirstName.value = Form.FNAM1.value;
					Form.MiddleName.value = Form.MNAM1.value;
				}

			// Run doSNO Query
				if (document.getElementById('doQSNO').checked == true) 
				{
					Form.LIS2.value = Form.State.value;
					if (Form.includeImages.checked)
					{
						Form.includeImages2.value = "Y";
					}
					else
					{
						Form.includeImages2.value = "N";
					}

					// Run doSupplement
					Form.LastName.value = Form.LNAM2.value;
					Form.FirstName.value = Form.FNAM2.value;
					Form.MiddleName.value = Form.MNAM2.value;
				}

			// Run doSNO Query
				if (document.getElementById('doQORV').checked == true) 
				{
					Form.LIS3.value = Form.State.value;
					if (Form.includeImages.checked)
					{
						Form.includeImages3.value = "Y";
					}
					else
					{
						Form.includeImages3.value = "N";
					}

					// Run doSupplement
					Form.LastName.value = Form.LNAM3.value;
					Form.FirstName.value = Form.FNAM3.value;
					Form.MiddleName.value = Form.MNAM3.value;
				}

			if (document.getElementById('doQSNO').checked == true || document.getElementById('doQORV').checked == true)
			{
				if (!HandleVehicleRmsFieldsBeforeSubmit()) return;
				if (!HandleVehicleSupplementFieldsBeforeSubmit()) return;
			}

			Form.Submit.click();
		}
	</script>
	<script type="text/javascript" language="javascript">
		function toggleCheckbox(element) 
		{
		// This function ensures when the Boat, Snowmobile, or Off-Road radio buttons are selected,
		// the appropriate BHN or VIN field appears/disappears
			if (document.getElementById(element).id == "doQBOT")
			{
				document.getElementById('BoatTable').style.display = "table";
				document.getElementById('SnowTable').style.display = "none";
				document.getElementById('ORVTable').style.display = "none";
				document.getElementById('REG1').focus();
			}
			else if (document.getElementById(element).id == "doQSNO")
			{
				document.getElementById('BoatTable').style.display = "none";
				document.getElementById('SnowTable').style.display = "table";
				document.getElementById('ORVTable').style.display = "none";
				document.getElementById('REG2').focus();
			}
			else if (document.getElementById(element).id == "doQORV")
			{
				document.getElementById('BoatTable').style.display = "none";
				document.getElementById('SnowTable').style.display = "none";
				document.getElementById('ORVTable').style.display = "table";
				document.getElementById('REG3').focus();
			}
		}
	</script>
</body>
</html>
