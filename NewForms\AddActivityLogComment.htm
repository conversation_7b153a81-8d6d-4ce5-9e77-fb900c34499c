<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Add Activity Log Comment</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Add Activity Log Comment</H4>
						<form action="AddActivityLogComment.aspx?queryfile=AddActivityLogComment.qry" method="post"
							id="Form" name="Form">
							<table ID="Table2">
								<tr>
									<td>
										<textarea size="8" rows="8" name="Comment" id="Comment" mandatory="true" onkeyup="CheckTextAreaLength(this.form.Comment,200);"></textarea>
									</td>
								</tr>
							</table>
							<input type="submit" name="Query" id="Query" value="Submit">
						</form>
					</TD>
				</TR>
			</TBODY></TABLE>
	</body>
	<script src="clientutilities.js"></script>
	<script language="javascript">

		function window.onload()
		{
			PrepareValidation(Form);
			Form.Comment.focus();
		}

	</script>
</HTML>
