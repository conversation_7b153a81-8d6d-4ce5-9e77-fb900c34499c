<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Transport</TITLE>
		<LINK href="bigstyle.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<form action="TransportQuery.aspx?queryfile=person.qry" method="post" id="TransportQuery" name="TransportQuery">

							<table ID="Table2">
								<tr>
								  <td align="right">
											 <b>Transport</b>
								  </td>
								  <td align="center" >
										<input type="button" name="Query" id="Query" value="Submit" onkeypress="validatepage()" onclick="validatepage()">
								  </td>
								</tr>
								<tr>
										  <td>&nbsp;</td>
								</tr>
								<tr>

									<td valign="top" NOWRAP colspan="2">
										<span style="font-size:20px;font-weight:bold;">
										Odometer:
										<input type="number" name="currentodometer" id="currentodometer" size="9" mandatory="true" step="0.1" min="0" max="1000000" value="">
<!--										<input type="text" name="currentodometer" id="currentodometer" size="9" maxvalue = "10000000" mandatory="true" numeric="true">
-->
										&nbsp;&nbsp;&nbsp;
										Number Transported:
										<input type="text" name="PatientsTransported" id="PatientsTransported" size="2" value="1"  mandatory="true">
										&nbsp;&nbsp;&nbsp;
										</span>
									</td>

								</tr>
<!--
								<tr>
                       		<td colspan="2" NOWRAP>
										<span style="font-size:20px;font-weight:bold;">
										Is Juvenile:
										<input type="checkbox" class="bigcheckbox" name="isjuvenile" id="isjuvenile">
										Gender:
										<input type="radio" class="bigradio" name="gender" id="M" value="M">Male
										<input type="radio" class="bigradio" name="gender" id="F" value="F">Female
										</span>
									</td>
								</tr>
-->

								<tr>

									<td align="left" valign="top" >
										<input type="radio" class="bigradio" name="loc" id="L" value="L">
										<b>Location:</b>
										<br/>

										<!-- Root item requires a SPAN element to contain it. -->
										<XML id="locationstyle" src="genericlist.LOGIS.xsl"></XML>
										<SPAN type="selectlist" id="locationvals" name="locationvals">
											<XML id="locationsource" src="TransportLocation.xml"></XML>
										</SPAN>
									</td>

									<td align="left" valign="top" >
										<input type="radio" class="bigradio" name="loc" id="A" value="A">
										<b>Address:</b>
										<br/>
										<table id="innertable">
    										<tr>
												<td>Address:</td>
												<td><input type="text" name="Address" id="Address"></td>
											</tr>
    										<tr>
												<td>City:</td>
												<td><input type="text" name="city" id="city"></td>
											</tr>
											<tr>
											  <td>State: </td>
												<td>
													<XML id="statestyle" src="genericlist.xsl"></XML>
													<SPAN type="selectlist" id="statevals" name="statevals">
														<XML id="statesource" src="state.xml"></XML>
													 </SPAN>
												</td>
											 </tr>
    										<tr>
												<td>Zip:</td>
												<td><input type="text" name="Zip" id="Zip"></td>
											</tr>
											</table>
									</td>

								</tr>
<!--
								<tr>
									<td valign="top">
										<b>Transport Protocol:</b>
										<XML id="protocolstyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="protocolvals" name="protocolvals">
											<XML id="protocolsource" src="transportprotocol.xml"></XML>
										</SPAN>
									</td>

									<td valign="top">
										<b>Transport Priority:</b>
										<XML id="prioritystyle" src="genericselect.xsl"></XML>
										<SPAN type="selectlist" id="priorityvals" name="priorityvals">
											<XML id="prioritysource" src="transportpriority.xml"></XML>
										</SPAN>
									</td>
-->
				</TR>

				<!--
				<tr>
					<td valign="top"><b>Transport Mode:</b></td>
				</tr>
				<tr>
					<td width="300">
						<XML id="transmodestyle" src="genericlist.xsl"></XML>
						<SPAN type="selectlist" id="transmodevals" name="transmodevals">
							<XML id="transmodesource" src="transportmode.xml"></XML>
						</SPAN>
					</td>
				</tr>
				<tr>
					<td>&nbsp;</td>
				</tr>
				-->
				<tr>
					<td valign="top" colspan="2">
							  <b>Comments:</b>
					<textarea style="width:100%;" name="comments" id="comments" rows="2" onkeyup="CheckTextAreaLength(this.form.comments,200);"></textarea>
					</td>
				</tr>

		</TABLE>

<!-- Records Check not needed for DF
							<table>
								<tr><td colspan="4">Complete the following to run a records check:</td></tr>
								<tr><td>&nbsp;</td></tr>
								<tr>
									<td>Last Name:</td>
									<td colspan="3"><input type="text" name="lastname" id="lastname"></td>
								</tr>
								<tr>
									<td>First Name:</td>
									<td colspan="3"><input type="text" name="firstname" id="firstname"></td>
								</tr>
								<tr>
									<td>DOB<br>(MMDDYYYY):</td>
									<td colspan="3"><input type="text" name="dob" size="11" ID="dob"><a href="javascript:void(0)" id="callink" name="callink" onclick='calendar(document.TransportQuery.dob,"date");' HIDEFOCUS><img name="popcal" id="popcal" src="calbtn.gif" width="34" height="22" border="0" alt=""></a></td>
								</tr>
								<tr>
									<td>Race:</td>
									<td colspan="3">
		<XML id="racestyle" src="genericcombo.xsl"></XML>
		<SPAN type="selectlist" id="racevals" name="racevals">
			<XML id="racesource" src="race.xml"></XML>
		</SPAN>
									</td>
								</tr>
							</table>
							<br>
-->

		<input type="hidden" name="state" id="state" >
		<input type="hidden" name="genderr" id="genderr" >
		<input type="hidden" name="lastname" id="lastname" >


		<!--<input type="hidden" name="transportationprotocol" id="transportationprotocol" value="DEFAULT">-->
		<input type="hidden" name="transportationpriority" id="transportationpriority" value="DF1">
		<input type="hidden" name="transportationprotocol" id="transportationprotocol" value="DF1">
		<input type="hidden" name="PatientsSeen" id="PatientsSeen" value="1">
<!-- <input type="hidden" name="transportationpriority" id="transportationpriority" value="DEF"> -->
	

		<!-- Modified by Harshad on 6/23/05 for enahncement request -->
		<!-- Hidden button to call sub OnAfterFormfill routine to pre select the Address/location radio  -->
		<input type="button" style="display:none" name="AfterFormFill" id="AfterFormFill" onclick="OnAfterFormFill()">
		<!--End modifications by Harshad -->
		<input style="width:0px;" TABINDEX=-1 type="submit" name="Submit" id="Submit" value="Submit">


		</form>
		</TD>
		</TR>
		</TBODY></TABLE>

		<!--  PopCalendar(tag name and id must match) Tags should sit at the page bottom -->
		<iframe width="168" height="190" name="gToday:normal:agenda.js" id="gToday:normal:agenda.js"
			src="ipopeng.htm" scrolling="no" frameborder="0" style="border:2px ridge; visibility:visible; z-index:999; position:absolute; left:-500px; top:0px;"></iframe>

	</body>

	<script src="clientutilities.js"></script>
	<SCRIPT language="javascript">

		// var originalHTML;

		function window.onload()
		{
		    PrepareValidation(TransportQuery);
		    TransportQuery.currentodometer.focus();
			locationvals.innerHTML = GenerateSelectBox("location", locationsource, locationstyle, false, false, false, 6, false, false);
		    // Parameter order for GenerateSelectBox is: listname, source, style, mandatory, parent, savelast, size, multiple, print.
		    statevals.innerHTML = GenerateSelectBox("transportstate", statesource, statestyle, true, false, false, 1, false, false);
			//protocolvals.innerHTML = GenerateSelectBox("transportationprotocol", protocolsource, protocolstyle, true, false, true, 1, false, false);
			//priorityvals.innerHTML = GenerateSelectBox("transportationpriority", prioritysource, prioritystyle, true, false, true, 1, false, false);

		    //racevals.innerHTML = GenerateComboBox("race", racesource, racestyle);
		    // originalHTML = TransportQuery.removegroup.innerHTML;
		}

<!--Modified by Harshad on 6/23/05 for enahncement request -->
<!--Function call to determine pre selection of Location/Address radio -->
		function OnAfterFormFill()
		{

			var sLocation = "";
			if (TransportQuery.location.selectedIndex != -1)
			{

				sLocation = TransportQuery.location.options[TransportQuery.location.selectedIndex].text;
			}

			if (TransportQuery.Address.value.length > 0)
			{
				//Check Address radio
				TransportQuery.A.checked = true;
			}
			else
			{
				//Check Location Radio
				TransportQuery.L.checked = true;
			}

		}
<!--End Modifications by Harshad -->
		function calendar(ctrl, formattype)
		{
			gfPop.gDateFormat = "nosplit";
			gfPop.fPopCalendar(ctrl);
			return false;
		}

		function window.validatepage()
		{

/* Records check not needed for DF transport.
			if (TransportQuery.lastname.value == '')
			{
				TransportQuery.firstname.value = '';
				TransportQuery.dob.value = '';
				TransportQuery.race.selectedIndex = -1;
				TransportQuery.genderr.value = '';
				TransportQuery.state.value = ''
			}
			else
			{
				if (TransportQuery.M.checked)
					TransportQuery.genderr.value = 'M';
				else if (TransportQuery.F.checked)
					TransportQuery.genderr.value = 'F';
				else
					TransportQuery.genderr.value = '';
				TransportQuery.state.value = TransportQuery.transportstate.value;
			}
*/
			TransportQuery.genderr.value = '';
			TransportQuery.state.value = TransportQuery.transportstate.value;

			<!-- Determine the selection. If location radio is selected user must select location value	-->
			<!-- If Address radio is selected, user must enter the Address -->
			var sLoc = "";
			if (TransportQuery.L.checked)
			{
				if (TransportQuery.location.selectedIndex != -1)
				{
					sLoc = TransportQuery.location.options[TransportQuery.location.selectedIndex].text;
				}

				if (sLoc == "")
				{
					TransportQuery.location.focus();
					alert ("Please select a location");
				}
				else
				{
					<!-- As Location is selected, making Address field null -->
					TransportQuery.Address.value = "";
					TransportQuery.Submit.click();
					return;
				}

			}

			var sAddress = "";
			if (TransportQuery.A.checked)
			{
				sAddress = TransportQuery.Address.value;

				if (sAddress == "")
				{
					TransportQuery.Address.focus();
					alert ("Please enter an address");
				}
				else
				{
					<!-- As Address is selected, making Location field null -->
					TransportQuery.location.selectedIndex = -1;
					TransportQuery.Submit.click();
					return;
				}

			}
				<!--End Modifications by Harshad	-->
		}
	</SCRIPT>
</HTML>
