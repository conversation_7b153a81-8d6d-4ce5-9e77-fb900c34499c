<?xml version="1.0" ?>

<xsl:transform xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
<xsl:output method="xml" />
<xsl:preserve-space elements="*"/>

<xsl:template match="/">

<HTML>
	<HEAD>
		<TITLE>VisiNET Mobile - Incident Summary Results</TITLE>
		<LINK href="style.css" type="text/css" rel="stylesheet"></LINK>
	</HEAD>
	<BODY>
		<TABLE class="base" cellPadding="10" align="center" border="0" ID="Table1">
			<TBODY>
				<TR>
					<TD vAlign="top">
						<H4 align="center">Incident Summary Results</H4>
	<xsl:apply-templates select="results/errormessage"/>
							<p>
								<table handheld="byrow" cellspacing="2" cellpadding="3" rules="all" bordercolor="#80FFFF" border="1" style="background-color:#0000C0;border-width:1px;border-style:None;">
									<tr style="font-weight:bold;color:white;">
										<td>Time</td>
										<td>Incident Number</td>
										<td>Unit Name</td>
									</tr>
	<xsl:apply-templates select="results/exemel/NewDataSet/Table"/>	
								</table>
							</p>
					</TD>
				</TR>
			</TBODY></TABLE>
	</BODY>
<SCRIPT language="javascript">
    function hide()
    {
	event.srcElement.parentElement.parentElement.parentElement.parentElement.parentElement.parentElement.style.display="none";
    }
    function show()
    {
	event.srcElement.parentElement.parentElement.nextSibling.style.display="inline";
    }
</SCRIPT>
</HTML>
</xsl:template>

<xsl:template match="/results/exemel/NewDataSet/Table">
    <xsl:variable name="recordid"><xsl:value-of select="ID"/></xsl:variable>
		
    <tr style="color:windowtext;background-color:window;">
		<td><a href="#" onclick='show();return false;'><xsl:value-of select="SummaryTime"/></a></td>
                <td><a href="SingleIncidentQuery.aspx?ID={$recordid}&#38;queryfile=SingleIncident.qry" location="remote"><xsl:value-of select="Incident_Number"/></a></td>
		<td><xsl:value-of select="UnitName"/></td>
    </tr>
    <tr style="display:none">
        <td colspan="3" style="color:windowtext;background-color:window;">
            <table>
                <tr>
                    <td VALIGN="top"><input type="button" value="Collapse" onclick='hide()'/></td>
                    <td>
                        <table>
			<xsl:if test="Field1Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field1Name"/>:</B></td>
                                <td><xsl:value-of select="Field1"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field2Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field2Name"/>:</B></td>
                                <td><xsl:value-of select="Field2"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field3Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field3Name"/>:</B></td>
                                <td><xsl:value-of select="Field3"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field4Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field4Name"/>:</B></td>
                                <td><xsl:value-of select="Field4"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field5Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field5Name"/>:</B></td>
                                <td><xsl:value-of select="Field5"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field6Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field6Name"/>:</B></td>
                                <td><xsl:value-of select="Field6"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field7Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field7Name"/>:</B></td>
                                <td><xsl:value-of select="Field7"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field8Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field8Name"/>:</B></td>
                                <td><xsl:value-of select="Field8"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field9Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field9Name"/>:</B></td>
                                <td><xsl:value-of select="Field9"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field10Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field10Name"/>:</B></td>
                                <td><xsl:value-of select="Field10"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field11Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field11Name"/>:</B></td>
                                <td><xsl:value-of select="Field11"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field12Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field12Name"/>:</B></td>
                                <td><xsl:value-of select="Field12"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field13Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field13Name"/>:</B></td>
                                <td><xsl:value-of select="Field13"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field14Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field14Name"/>:</B></td>
                                <td><xsl:value-of select="Field14"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field15Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field15Name"/>:</B></td>
                                <td><xsl:value-of select="Field15"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field16Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field16Name"/>:</B></td>
                                <td><xsl:value-of select="Field16"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field17Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field17Name"/>:</B></td>
                                <td><xsl:value-of select="Field17"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field18Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field18Name"/>:</B></td>
                                <td><xsl:value-of select="Field18"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field19Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field19Name"/>:</B></td>
                                <td><xsl:value-of select="Field19"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field20Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field20Name"/>:</B></td>
                                <td><xsl:value-of select="Field20"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field21Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field21Name"/>:</B></td>
                                <td><xsl:value-of select="Field21"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field22Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field22Name"/>:</B></td>
                                <td><xsl:value-of select="Field22"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field23Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field23Name"/>:</B></td>
                                <td><xsl:value-of select="Field23"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field24Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field24Name"/>:</B></td>
                                <td><xsl:value-of select="Field24"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field25Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field25Name"/>:</B></td>
                                <td><xsl:value-of select="Field25"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field26Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field26Name"/>:</B></td>
                                <td><xsl:value-of select="Field26"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field27Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field27Name"/>:</B></td>
                                <td><xsl:value-of select="Field27"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field28Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field28Name"/>:</B></td>
                                <td><xsl:value-of select="Field28"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field29Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field29Name"/>:</B></td>
                                <td><xsl:value-of select="Field29"/></td>
                            </tr>
			</xsl:if>
			<xsl:if test="Field30Name!=''">
                            <tr>
                                <td><B><xsl:value-of select="Field30Name"/>:</B></td>
                                <td><xsl:value-of select="Field30"/></td>
                            </tr>
			</xsl:if>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</xsl:template> 

<xsl:template match="/results/errormessage">

	<br/>
	<p>
	<b>Too much data was requested.  Please narrow your search criteria.</b>
	</p>
	<br/>

</xsl:template> 

</xsl:transform>
